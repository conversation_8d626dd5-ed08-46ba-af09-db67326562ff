<template>
  <div class="loading" v-if="dataView === null">
    <a-spin tip="加载中..." />
  </div>
  <!-- 数据卡片 -->
  <div v-show="show_data_list" class="data-card-container" v-else>
    <!-- 车长数量 -->
    <div class="data-card" @click="openDataModal('车长拜访情况')">
      <div class="data-card-title">车长数量<ArrowRightOutlined /></div>

      <div class="data-card-content">
        <i>
          经销区总数：
          <span style="color: #1677ff">{{ dataView?.area_num }}</span>
        </i>
        <i>
          车长总数：
          <span style="color: #1677ff">{{ dataView?.dealer_total_num }}</span>
        </i>
        <!-- <i>
          当年新增：
          <span style="color: #1677ff">{{ dataView?.dealer_add_num }} </span>
        </i> -->
        <i>
          参与车长：
          <span style="color: #1677ff">{{ dataView?.dealer_join_num }} </span>
        </i>
        <i>
          参与比例：
          <span style="color: #1677ff">{{ dataView?.dealer_join_rate }}% </span>
        </i>
      </div>
    </div>
    <!-- 门店数 -->
    <div class="data-card" @click="openDataModal('门店')">
      <div class="data-card-title">门店数<ArrowRightOutlined /></div>
      <div class="data-card-content">
        <i>
          门店总数：
          <span style="color: #1677ff">{{ dataView?.store_total_num }} </span>
        </i>
        <i>
          已开发：
          <span style="color: #1677ff">{{ dataView?.store_develop_num }}</span>
        </i>
        <!-- <i>
          当年新增：
          <span style="color: #1677ff">{{ dataView?.store_add_num }}</span>
        </i> -->
        <i>
          参与门店：
          <span style="color: #1677ff">{{ dataView?.store_join_num }}</span>
        </i>
        <i>
          参与比例：
          <span style="color: #1677ff">{{ dataView?.store_join_rate }}%</span>
        </i>
      </div>
    </div>
    <!-- 人群覆盖率 -->
    <div
      class="data-card"
      style="height: 85px"
      @click="openDataModal('人群覆盖率')"
    >
      <div class="data-card-title" style="background-color: #4fb163">
        人群覆盖率
        <ArrowRightOutlined />
      </div>
      <div class="data-card-content">
        <i>
          总覆盖率：
          <span style="color: #4fb163">{{ dataView?.p_c_rate + "%" }}</span>
        </i>
        <i>
          品类覆盖率：
          <span style="color: #4fb163">{{ dataView?.p_p_rate + "%" }}</span>
        </i>
        <i>
          人口数：
          <span style="color: #4fb163">{{ dataView?.area_population }}</span>
        </i>
      </div>
    </div>
    <!-- 门店覆盖率 -->
    <div class="data-card" style="height: 85px">
      <div class="data-card-title" style="background-color: #4fb163">
        门店覆盖率
      </div>
      <div class="data-card-content">
        <i>
          总覆盖率：
          <span style="color: #4fb163">{{ dataView?.s_c_rate + "%" }}</span>
        </i>
        <i>
          品类覆盖率：
          <span style="color: #4fb163">{{ dataView?.p_s_rate + "%" }}</span>
        </i>
      </div>
    </div>

    <!-- 销售 -->
    <div class="data-card" @click="openDataModal('销售')">
      <div class="data-card-title" style="background-color: #fbc422">
        销售总数
        <ArrowRightOutlined />
      </div>
      <div class="data-card-content">
        <i>
          装车总额：
          <span style="color: #fbc422"
            >{{ dataView?.total_normal_delivery.toFixed(0) }}
          </span>
          元
        </i>
        <i>
          门店销售总额：
          <span style="color: #fbc422"
            >{{ dataView?.sale_total_num.toFixed(0) }}
          </span>
          元
        </i>
        <!-- <i>
          净销售额：
          <span style="color: #fbc422"
            >{{ dataView?.sale_total_num.toFixed(0) }}
          </span>
          万元
        </i>
        <i>
          目标销售额：
          <span style="color: #fbc422"
            >{{ dataView?.sale_total_num.toFixed(0) }}
          </span>
          %
        </i> -->
        <i>
          单店平均：
          <span style="color: #fbc422">
            {{ dataView?.sale_average_num.toFixed(0) }}
          </span>
          元
        </i>
        <i>
          潜力销售额：
          <span style="color: #fbc422">
            {{ dataView?.sale_potential_num.toFixed(0) }}
          </span>
          元
        </i>
      </div>
    </div>
    <!-- 返货情况 -->
    <div class="data-card">
      <div class="data-card-title" style="background-color: #fbc422">
        返货情况
      </div>
      <div class="data-card-content">
        <i>
          总返货额：
          <span style="color: #fbc422">{{ dataView?.total_normal_return }}</span
          >元
        </i>
        <i>
          总返货率：
          <span style="color: #fbc422">{{ dataView?.ratio.toFixed(0) }}%</span>
        </i>
        <i>
          门店退货额：
          <span style="color: #fbc422">{{ dataView?.retrun_total_price }}</span
          >元
        </i>
        <i>
          门店退货率：
          <span style="color: #fbc422">{{ dataView?.return_rate }}%</span>
        </i>
        <i>
          >20%返货门店数：
          <span style="color: #fbc422">{{ dataView?.high_return_store }}</span>
        </i>
      </div>
    </div>
    <!-- 物料数量 -->
    <div class="data-card" @click="openDataModal('物料数量')">
      <div class="data-card-title" style="background-color: #fbc422">
        物料数量
        <ArrowRightOutlined />
      </div>
      <div class="data-card-content">
        <!-- <i>
          物料总金额：
          <span style="color: #fbc422">{{
            dataView?.material_total_price
          }}</span
          >元
        </i> -->
        <!-- <i>
          物料总数量：
          <span style="color: #fbc422">{{ dataView?.material_total_num }}</span>
        </i> -->
        <i>
          柜体数量：
          <span style="color: #fbc422">{{ dataView?.cabinet_num }}</span>
        </i>
        <i>
          工字架数量：
          <span style="color: #fbc422">{{ dataView?.capned_num }}</span>
        </i>
        <i>
          其他物料数量：
          <span style="color: #fbc422">{{ dataView?.other_material_num }}</span>
        </i>
        <i>
          筐总金额：
          <span style="color: #fbc422">{{ dataView?.basket_total_price }}</span
          >元
        </i>
        <i>
          筐总数量：
          <span style="color: #fbc422">{{ dataView?.basket_total_num }}</span>
        </i>
      </div>
    </div>
    <!-- 产品总数 -->
    <div class="data-card" @click="openDataModal('产品种类总数')">
      <div class="data-card-title" style="background-color: #fbc422">
        产品种类总数
        <ArrowRightOutlined />
      </div>
      <div class="data-card-content">
        <i style="color: #fbc422" class="center">{{
          dataView?.product_type_num
        }}</i>
      </div>
    </div>
  </div>
  <div class="sync-time">最后计算时间: {{ dataView?.sync_time }}</div>
  <!-- 切换省市区显示数据卡片 -->
  <div v-show="!show_data_list" class="data-card-container">
    <!-- 门店数 -->
    <div class="data-card">
      <div class="data-card-title">门店数</div>
      <div class="data-card-content">
        <i class="center">
          <span style="color: #1677ff">{{ store.store_count }}</span>
        </i>
      </div>
    </div>
    <!-- 人口数 -->
    <div class="data-card">
      <div class="data-card-title" style="background-color: #4fb163">
        人口数
      </div>
      <div class="data-card-content">
        <i style="color: #4fb163" class="center">{{ store.people_count }}</i>
      </div>
    </div>
  </div>
</template>

<script setup>
import { toRefs } from "vue";
import { ArrowRightOutlined } from "@ant-design/icons-vue";
import { useMapStore } from "../../../../store/map";

const props = defineProps(["dataView", "show_data_list"]);
const emits = defineEmits(["openDataModal"]);
const { dataView } = toRefs(props);
const { show_data_list } = toRefs(props);
const store = useMapStore();

async function openDataModal(name) {
  emits("openDataModal", name);
}
</script>

<style lang="scss" scoped>
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 150px;
}

.data-card-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 5px;
  margin-top: 10px;
  font-size: 12px;
  font-weight: 700;

  .data-card {
    display: flex;
    flex-direction: column;
    height: 120px;
    border: 1px solid rgba(0, 0, 0, 0.3);
    cursor: pointer;
  }

  .data-card-title {
    font-weight: 700;
    padding: 5px;
    color: #fff;
    text-align: center;
    background: #1677ff;
  }

  .data-card-content {
    display: flex;
    gap: 4px;
    padding: 0 3px 0 0;
    flex: 1;
    flex-direction: column;
    justify-content: center;
    align-items: start;
    i {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
    }
    .center {
      justify-content: center;
    }
  }
}

.sync-time {
  margin-top: 10px;
  font-weight: bold;
}
</style>
