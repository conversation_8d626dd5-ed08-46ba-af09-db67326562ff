<template>
  <div class="container">
    <a-card title="物流分仓列表" class="main-card">
      <!-- 搜索区域 -->
      <div class="header-actions">
        <div class="search-section">
          <a-input-search
            v-model:value="keyword"
            placeholder="输入分仓名称进行搜索"
            enter-button="搜索"
            size="large"
            allow-clear
            @search="onSearch"
            @clear="onClear"
            class="search-input"
          />
        </div>
      </div>

      <!-- 卡片列表 -->
      <div class="card-list">
        <a-card
          v-for="item in list"
          :key="item.id"
          class="warehouse-card"
          hoverable
          @click="goDetail(item)"
        >
          <!-- 卡片头部 -->
          <template #title>
            <div class="card-header">
              <span class="warehouse-name">{{ item.warehouse_name || '未命名分仓' }}</span>
              <a-tag :color="getStatusColor(item.approve_status)" class="status-tag">
                {{ approveStatusMap[item.approve_status] || '未知状态' }}
              </a-tag>
            </div>
          </template>

          <!-- 卡片内容 -->
          <div class="card-content">
            <!-- 地理位置信息 -->
            <div class="location-section">
              <div class="section-title">
                <span class="icon">📍</span>
                地理位置
              </div>
              <div class="location-grid">
                <div class="location-item">
                  <span class="label">省份：</span>
                  <span class="value">{{ item.province || '未设置' }}</span>
                </div>
                <div class="location-item">
                  <span class="label">城市：</span>
                  <span class="value">{{ item.city || '未设置' }}</span>
                </div>
                <div class="location-item">
                  <span class="label">区县：</span>
                  <span class="value">{{ item.district || '未设置' }}</span>
                </div>
              </div>
              <div class="specific-location" v-if="item.specificLocation">
                <span class="label">具体位置：</span>
                <span class="value">{{ item.specificLocation }}</span>
              </div>
            </div>

            <!-- 坐标信息 -->
            <div class="coordinates-section">
              <div class="section-title">
                <span class="icon">🎯</span>
                坐标信息
              </div>
              <div class="coordinates-grid">
                <div class="coordinate-item">
                  <span class="label">纬度：</span>
                  <span class="value">{{ item.latitude || '未设置' }}</span>
                </div>
                <div class="coordinate-item">
                  <span class="label">经度：</span>
                  <span class="value">{{ item.longitude || '未设置' }}</span>
                </div>
              </div>
            </div>
          </div>
        </a-card>
      </div>

      <!-- 加载状态 -->
      <div class="loading-section" v-if="loading">
        <a-spin size="large">
          <div class="loading-text">正在加载数据...</div>
        </a-spin>
      </div>

      <!-- 空状态 -->
      <a-empty v-if="!loading && !list.length" class="empty-state">
        <template #description>
          <span>{{ keyword ? '未找到相关分仓信息' : '暂无分仓数据' }}</span>
        </template>
      </a-empty>

      <!-- 无更多数据提示 -->
      <div class="no-more" v-if="!hasMore && list.length && !loading">
        <a-divider>已显示全部数据</a-divider>
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { get_wlfc_wz } from '@/api/workbench/logisticsWarehouseBuild'
import { onReachBottom } from "@dcloudio/uni-app";

const keyword = ref('')
const list = ref([])
const page = ref(1)
const pageSize = 10
const hasMore = ref(true)
const loading = ref(false)

const approveStatusMap = {
	0: '审批中',
	1: '已通过',
	2: '未通过',
	3: '已关闭'
}

// 获取状态颜色
const getStatusColor = (status) => {
  const colorMap = {
    0: 'orange',   // 审批中
    1: 'green',    // 已通过
    2: 'red',      // 未通过
    3: 'gray',     // 已关闭
  }
  return colorMap[status] || 'default'
}

const fetchData = async (reset = false) => {
  if (loading.value || (!hasMore.value && !reset)) return
  loading.value = true

  if (reset) {
    page.value = 1
    list.value = []
    hasMore.value = true
  }

  const params = {
    page: page.value,
    page_size: pageSize,
    search: keyword.value?.trim() || null
  }

  try {
    const res = await get_wlfc_wz(params)
    const rows = res?.data?.result ?? []

    if (reset) {
      list.value = rows
    } else {
      list.value.push(...rows)
    }

    hasMore.value = rows.length === pageSize
    if (hasMore.value && !reset) page.value += 1

    // 如果是搜索且没有结果，显示友好提示
    if (reset && keyword.value?.trim() && rows.length === 0) {
      uni.showToast({
        title: '未找到相关分仓',
        icon: 'none',
        duration: 2000
      })
    }
  } catch (error) {
    console.error('获取数据失败:', error)
    uni.showToast({
      title: '网络异常，请稍后重试',
      icon: 'none',
      duration: 2000
    })

    // 如果是重置操作失败，保持原有数据
    if (reset) {
      list.value = []
    }
  } finally {
    loading.value = false
  }
}

const onSearch = () => {
  fetchData(true)
}

const onClear = () => {
  keyword.value = ''
  fetchData(true)
}



const goDetail = (item) => {
  uni.navigateTo({
    url: `/pages/workbench/logisticsList/detail?item=${encodeURIComponent(JSON.stringify(item))}`,
  })
}

// 使用onReachBottom代替scroll事件（保留原有的无限滚动功能）
onReachBottom(() => {
  if (!loading.value && hasMore.value) {
    fetchData()
  }
})

onMounted(() => fetchData())
</script>

<style lang="scss" scoped>
.container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.main-card {
  max-width: 1400px;
  margin: 0 auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 8px;

  :deep(.ant-card-head) {
    background: linear-gradient(135deg, #5C258D 0%, #4389A2 100%);
    border-radius: 8px 8px 0 0;

    .ant-card-head-title {
      color: white;
      font-size: 18px;
      font-weight: 600;
    }
  }

  :deep(.ant-card-body) {
    padding: 32px;
  }
}

.header-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 16px;

  .search-section {
    flex: 1;
    min-width: 300px;
    max-width: 500px;

    .search-input {
      width: 100%;
    }
  }
}

// 卡片列表样式
.card-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.warehouse-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  }

  :deep(.ant-card-head) {
    border-bottom: 1px solid #f0f0f0;
    padding: 16px 24px;

    .ant-card-head-title {
      font-size: 16px;
      font-weight: 600;
    }
  }

  :deep(.ant-card-body) {
    padding: 20px 24px;
  }
}

// 卡片头部样式
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .warehouse-name {
    font-size: 16px;
    font-weight: 600;
    color: #fff;
    flex: 1;
    margin-right: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .status-tag {
    flex-shrink: 0;
    font-weight: 500;
  }
}

// 卡片内容样式
.card-content {
  .section-title {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 600;
    color: #1890ff;
    margin-bottom: 12px;

    .icon {
      margin-right: 6px;
      font-size: 16px;
    }
  }

  .location-section {
    margin-bottom: 20px;

    .location-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: 8px;
      margin-bottom: 8px;
    }

    .location-item {
      display: flex;
      align-items: center;
      font-size: 13px;

      .label {
        color: #8c8c8c;
        margin-right: 4px;
        flex-shrink: 0;
      }

      .value {
        color: #262626;
        font-weight: 500;
      }
    }

    .specific-location {
      display: flex;
      align-items: flex-start;
      font-size: 13px;
      margin-top: 8px;

      .label {
        color: #8c8c8c;
        margin-right: 4px;
        flex-shrink: 0;
      }

      .value {
        color: #262626;
        font-weight: 500;
        line-height: 1.4;
      }
    }
  }

  .coordinates-section {
    .coordinates-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 8px;
    }

    .coordinate-item {
      display: flex;
      align-items: center;
      font-size: 13px;

      .label {
        color: #8c8c8c;
        margin-right: 4px;
        flex-shrink: 0;
      }

      .value {
        color: #262626;
        font-weight: 500;
        font-family: 'Courier New', monospace;
      }
    }
  }
}

// 加载状态样式
.loading-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;

  .loading-text {
    margin-top: 12px;
    color: #8c8c8c;
    font-size: 14px;
  }
}

// 空状态样式
.empty-state {
  margin: 60px 0;

  :deep(.ant-empty-description) {
    color: #8c8c8c;
    font-size: 14px;
  }
}

// 无更多数据样式
.no-more {
  margin: 40px 0 20px 0;
  text-align: center;

  :deep(.ant-divider-inner-text) {
    color: #8c8c8c;
    font-size: 14px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }

  .main-card {
    :deep(.ant-card-body) {
      padding: 16px;
    }
  }

  .header-actions {
    flex-direction: column;
    align-items: stretch;

    .search-section {
      min-width: auto;
      max-width: none;
    }
  }

  .card-list {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .warehouse-card {
    :deep(.ant-card-head) {
      padding: 12px 16px;
    }

    :deep(.ant-card-body) {
      padding: 16px;
    }

    .card-header {
      .warehouse-name {
        font-size: 15px;
      }
    }

    .card-content {
      .location-section {
        .location-grid {
          grid-template-columns: 1fr;
          gap: 6px;
        }
      }

      .coordinates-section {
        .coordinates-grid {
          grid-template-columns: 1fr;
          gap: 6px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .container {
    padding: 12px;
  }

  .main-card {
    :deep(.ant-card-body) {
      padding: 12px;
    }
  }

  .card-list {
    gap: 12px;
  }

  .warehouse-card {
    :deep(.ant-card-head) {
      padding: 10px 12px;
    }

    :deep(.ant-card-body) {
      padding: 12px;
    }
  }
}
</style>