<template>
  <div>
    <div class="map-container-wrap">
      <div class="search-card">
        <div class="search-box">
          <a-select
            show-search
            allowClear
            v-model:value="selectedLocation"
            placeholder="搜索位置"
            :default-active-first-option="false"
            :show-arrow="false"
            :filter-option="false"
            :not-found-content="null"
            :options="suggestionOptions"
            @search="handleSearchInput"
            @select="handleSelectChange"
            @clear="handleClear"
            style="flex: 1; margin-right: 10px"
          >
            <template #option="{ value, label }">
              <div>{{ label }}</div>
            </template>
          </a-select>
          <a-button type="primary" @click="searchLocation">搜索</a-button>
        </div>
        <div class="form-item">
          <div class="label">分仓名称：</div>
          <a-input v-model:value="warehouseName" placeholder="请输入分仓名称" />
        </div>
        <div class="form-item">
          <div class="label">所属经销区：</div>
          <a-select
            v-model:value="selectedArea"
            style="width: 100%"
            placeholder="请选择经销区"
            show-search
						:options="areaList"
						:filter-option="filterOption"
          >
          </a-select>
        </div>
        <div class="form-item">
          <div class="label">位置文本：</div>
          <a-input v-model:value="locationText" placeholder="请输入位置描述" />
        </div>
        <div class="form-item">
          <div class="label">经度：</div>
          <a-input v-model:value="longitude" disabled />
        </div>
        <div class="form-item">
          <div class="label">纬度：</div>
          <a-input v-model:value="latitude" disabled />
        </div>
        <div class="form-footer">
          <a-button type="primary" @click="saveWarehouse">保存</a-button>
        </div>
      </div>
      <div class="map-container" id="map"></div>
      <a-radio-group
        style="position: absolute; right: 10px; top: 190px; z-index: 999"
        v-model:value="mapType"
        @change="changeMapType"
      >
        <a-radio-button value="vector">地图</a-radio-button>
        <a-radio-button value="satellite">卫星</a-radio-button>
      </a-radio-group>
    </div>
  </div>
</template>

<script setup>
import { ref, nextTick, onMounted, onUnmounted } from "vue";
import {
  getAreaListApi,
  saveWarehouseApi,
  get_wlfc_wz,
} from "@/api/workbench/logisticsWarehouseBuild";
import { getMapKey } from "@/api/Map";
import dayjs from "dayjs";

// 地图相关变量
let map;
let marker; // 仓库列表标记集合
let selectionMarker; // 页面点击后用于选择/编辑的单个标记
let mapType = ref("vector");

// +++++++++++++++++++++ 新增：InfoWindow 及状态映射 +++++++++++++++++++++
let infoWindow = null;
const approveStatusMap = {
  0: "审批中",
  1: "已通过",
  2: "未通过",
  3: "已关闭",
};
// +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

// 表单数据
const warehouseName = ref("");
const selectedArea = ref("");
const areaList = ref([]);
const longitude = ref("");
const latitude = ref("");
const mapKey = ref("");
const province = ref("");
const city = ref("");
const district = ref("");
const selectedLocation = ref("");
const searchKeyword = ref(""); // 保留为搜索关键词
const suggestionOptions = ref([]); // 联想选项列表
const locationText = ref(""); // 位置文本描述

const get_wlfc_wz_list = async () => {
  const data = {
    page: 1,
    page_size: 9999,
    search: null,
  };
  const res = await get_wlfc_wz(data);
  console.log(res, "获取仓库列表");
  if (res?.data?.result?.length) {
    const geometries = res.data.result.map((item) => ({
      id: `warehouse-${item.id}`,
      position: new TMap.LatLng(Number(item.latitude), Number(item.longitude)),
      styleId: "marker",
      properties: {
        // 保存整条数据，点击时直接读取
        warehouse: item,
      },
    }));
    // 一次性设置所有标记
    marker.setGeometries(geometries);
  }
};

const getMapKeyApi = async () => {
  const res = await getMapKey();
  console.log(res, "获取地图key");
  mapKey.value = res.data.result[0].mapKeyValue;
};

// 初始化地图
const initMap = () => {
  const center = new TMap.LatLng(39.984104, 116.307503);
  map = new TMap.Map("map", {
    center: center,
    zoom: 10,
    viewMode: "2D",
    baseMap: {
      type: mapType.value,
    },
  });

  // 创建仓库列表标记
  marker = new TMap.MultiMarker({
    map: map,
    styles: {
      marker: new TMap.MarkerStyle({
        width: 25,
        height: 25,
        anchor: { x: 12.5, y: 35 },
        src: "/static/icon/map/warehouse-fill.png",
      }),
    },
    geometries: [],
  });

  // 创建"当前选中"单独标记，避免覆盖仓库标记
  selectionMarker = new TMap.MultiMarker({
    map: map,
    styles: {
      selected: new TMap.MarkerStyle({
        width: 30,
        height: 30,
        anchor: { x: 15, y: 35 },
        src: "/static/icon/map/storeTrack.png", // 如需高亮可换图标
      }),
    },
    geometries: [],
  });

  // 监听仓库标记点击
  marker.on("click", handleMarkerClick);

  // 获取经销区列表
  getAreaList();

  // 点击地图事件
  map.on("click", handleMapClick);
};

// 处理地图点击事件
const handleMapClick = (evt) => {
  const lat = evt.latLng.lat;
  const lng = evt.latLng.lng;
  updateMarker(lat, lng);

  // 获取点击位置的地址信息
  getAddressFromLocation(lat, lng);
};

// 根据经纬度获取地址信息
const getAddressFromLocation = (lat, lng) => {
  const script = document.createElement("script");
  const callbackName =
    "geocoder_callback_" + Math.round(100000 * Math.random());

  // 创建全局回调函数
  window[callbackName] = (res) => {
    document.body.removeChild(script);
    delete window[callbackName];

    if (res.status === 0 && res.result) {
      locationText.value = res.result.address;
      province.value = res.result.address_component.province;
      city.value = res.result.address_component.city;
      district.value = res.result.address_component.district;
    }
  };

  // 构建URL并添加script标签到DOM
  script.src = `https://apis.map.qq.com/ws/geocoder/v1/?key=${mapKey.value}&location=${lat},${lng}&output=jsonp&callback=${callbackName}`;
  document.body.appendChild(script);

  script.onerror = () => {
    document.body.removeChild(script);
    delete window[callbackName];
    console.error("获取地址信息失败");
  };
};

// 更新"当前选中"标记
const updateMarker = (lat, lng) => {
  latitude.value = lat.toFixed(6);
  longitude.value = lng.toFixed(6);

  // 仅更新 selectionMarker，不影响仓库列表标记
  selectionMarker.setGeometries([
    {
      id: "selected-marker",
      position: new TMap.LatLng(lat, lng),
      styleId: "selected",
    },
  ]);
};

// 清除"当前选中"标记
const clearMarker = () => {
  selectionMarker.setGeometries([]);
  latitude.value = "";
  longitude.value = "";
  locationText.value = "";
};

// 处理清除搜索框事件
const handleClear = () => {
  selectedLocation.value = "";
  searchKeyword.value = "";
  clearMarker();
};

// 输入关键词时获取联想结果
const handleSearchInput = (value) => {
  console.log("输入的关键词:", value);

  if (!value) {
    suggestionOptions.value = [];
    return;
  }

  // 防抖处理
  if (window.searchTimeout) {
    clearTimeout(window.searchTimeout);
  }

  window.searchTimeout = setTimeout(() => {
    console.log("正在请求联想API...");

    // 创建回调函数名称
    const callbackName = "jsonp_callback_" + Date.now();

    // 挂载回调函数到全局
    window[callbackName] = function (res) {
      console.log("收到联想结果:", res);

      if (res.status === 0 && res.data && res.data.length > 0) {
        // 转换为a-select需要的格式
        suggestionOptions.value = res.data.map((item) => ({
          value: item.id || item.title,
          label: item.title,
          location: item.location,
        }));
      } else {
        suggestionOptions.value = [];
      }

      // 清理回调函数
      document.head.removeChild(document.getElementById("jsonp_script"));
      delete window[callbackName];
    };

    // 创建script标签
    const script = document.createElement("script");
    script.id = "jsonp_script";
    script.src = `https://apis.map.qq.com/ws/place/v1/suggestion?key=${
      mapKey.value
    }&keyword=${encodeURIComponent(
      value
    )}&region=全国&output=jsonp&callback=${callbackName}`;

    // 添加错误处理
    script.onerror = function () {
      console.error("联想API请求失败");
      suggestionOptions.value = [];
      document.head.removeChild(document.getElementById("jsonp_script"));
      delete window[callbackName];
    };

    // 将script添加到head
    document.head.appendChild(script);
  }, 300);
};

// 搜索位置
const searchLocation = () => {
  if (!selectedLocation.value) return;

  // 使用JSONP方式请求腾讯地图API，避免CORS问题
  const script = document.createElement("script");
  const callbackName = "jsonp_" + Math.round(100000 * Math.random());

  // 创建全局回调函数
  window[callbackName] = (res) => {
    // 请求完成后，删除script标签和回调函数
    document.body.removeChild(script);
    delete window[callbackName];

    if (res.status === 0 && res.data && res.data.length > 0) {
      const location = res.data[0].location;
      map.setCenter(new TMap.LatLng(location.lat, location.lng));
      updateMarker(location.lat, location.lng);
      locationText.value = res.data[0].title; // 设置位置文本
      province.value = res.data[0].ad_info.province;
      city.value = res.data[0].ad_info.city;
      district.value = res.data[0].ad_info.district;
    } else {
      uni.showToast({
        title: "未找到相关位置",
        icon: "none",
      });
    }
  };

  // 构建URL并添加script标签到DOM
  script.src = `https://apis.map.qq.com/ws/place/v1/search?key=${
    mapKey.value
  }&keyword=${encodeURIComponent(
    selectedLocation.value
  )}&boundary=region(全国,0)&output=jsonp&callback=${callbackName}`;
  document.body.appendChild(script);

  // 处理错误情况
  script.onerror = () => {
    document.body.removeChild(script);
    delete window[callbackName];
    console.error("搜索位置失败");
    uni.showToast({
      title: "搜索位置失败",
      icon: "none",
    });
  };
};

// 切换地图类型
const changeMapType = (e) => {
  map.setBaseMap({ type: e.target.value });
};

// 获取经销区列表
const getAreaList = async () => {
  try {
    // 这里可以从 store 获取经销区列表或者调用 API
    const res = await getAreaListApi();
    console.log(res, "经销区列表");

    areaList.value = res.data.result.map((item) => {
      return {
        ...item,
        label: item.Organization_Name,
        value: item.id,
      };
    });
  } catch (error) {
    console.error("获取经销区列表失败:", error);
  }
};

const filterOption = (input, option) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};
// 保存仓库
const saveWarehouse = async () => {
  if (!warehouseName.value) {
    // 提示用户输入仓库名称
    uni.showToast({
      title: "请输入仓库名称",
      icon: "none",
    });
    return;
  }
  if (!selectedArea.value) {
    // 提示用户选择经销区
    uni.showToast({
      title: "请选择经销区",
      icon: "none",
    });
    return;
  }
  if (!latitude.value || !longitude.value) {
    // 提示用户在地图上选择位置
    uni.showToast({
      title: "请在地图上选择位置",
      icon: "none",
    });
    return;
  }

  // 这里调用 API 保存仓库信息
  console.log("保存仓库信息:", {
    name: warehouseName.value,
    areaId: selectedArea.value,
    latitude: latitude.value,
    longitude: longitude.value,
    locationText: locationText.value,
    logistics_code: `W${dayjs().format("YYYYMMDDHHmmss")}`,
    province: province.value,
    city: city.value,
    district: district.value,
  });
  await saveWarehouseApi({
    name: warehouseName.value,
    areaId: selectedArea.value,
    latitude: latitude.value,
    longitude: longitude.value,
    locationText: locationText.value,
    logistics_code: `W${dayjs().format("YYYYMMDDHHmmss")}`,
    province: province.value,
    city: city.value,
    district: district.value,
  });

  uni.showToast({
    title: "保存成功",
    icon: "success",
    duration: 1500
  });

  resetForm();

  // 延迟跳转到物流列表页面，让用户看到成功提示
  setTimeout(() => {
    uni.navigateTo({
      url: '/pages/workbench/logisticsList/index'
    });
  }, 1500);
};

const resetForm = () => {
  selectedLocation.value = "";
  searchKeyword.value = "";
  warehouseName.value = "";
  selectedArea.value = "";
  latitude.value = "";
  longitude.value = "";
  locationText.value = "";
};

// 处理选择变化
const handleSelectChange = (value, option) => {
  console.log("选中的值:", value);
  console.log("选中的选项:", option);
  searchKeyword.value = option.label;
  locationText.value = option.label; // 设置位置文本

  // 如果有位置信息
  if (option && option.location) {
    map.setCenter(new TMap.LatLng(option.location.lat, option.location.lng));
    updateMarker(option.location.lat, option.location.lng);
  } else {
    searchLocation(); // 没有位置信息，执行搜索
  }
};

// ==================== 处理标记点击事件 ====================
const handleMarkerClick = (evt) => {
  const data = evt.geometry.properties.warehouse;
  if (!data) return;

  const statusText = approveStatusMap[data.approve_status] ?? "";
  const html = `
		<div style="min-width:150px;padding:4px 6px;">
			<div style="font-weight:600;">${data.warehouse_name}</div>
			<div style="margin-top:4px;">${statusText}</div>
		</div>`;

  if (!infoWindow) {
    infoWindow = new TMap.InfoWindow({
      map: map,
      position: evt.geometry.position,
      content: html,
      offset: { x: 0, y: -32 },
    });
  } else {
    infoWindow.setPosition(evt.geometry.position);
    infoWindow.setContent(html);
    infoWindow.open && infoWindow.open(); // 若 SDK 版本较低可去掉
  }
};
// ==========================================================

onMounted(() => {
  nextTick(() => {
    initMap();
    get_wlfc_wz_list();
    getMapKeyApi();
  });
});

onUnmounted(() => {
  if (map) {
    map.off("click", handleMapClick);
  }
});
</script>

<style lang="scss" scoped>
.map-container-wrap {
  position: relative;
  height: calc(100vh - 120px);
  width: 100%;
}

.map-container {
  height: 100%;
  width: 100%;
  z-index: 1;
}

.search-card {
  position: absolute;
  top: 20px;
  left: 20px;
  width: 320px;
  background-color: white;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 999;

  .search-box {
    display: flex;
    margin-bottom: 15px;
  }

  .form-item {
    margin-bottom: 12px;

    .label {
      margin-bottom: 5px;
      font-weight: 500;
    }
  }

  .form-footer {
    margin-top: 20px;
    text-align: center;
  }
}

.title {
  height: 50px;
  display: flex;
  align-items: center;
  padding: 0 20px;
  background-color: #f5f5f5;

  span {
    font-size: 18px;
    font-weight: bold;
  }
}
</style>
