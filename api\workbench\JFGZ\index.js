import request from "../../index";

// 获取积分规则列表
export function getScoreRuleListApi(data) {
  return request({
    method: "POST",
    url: "/score/rule_list",
    data,
  });
}

// 新增积分规则
export function addScoreRuleApi(data) {
  return request({
    method: "POST",
    url: "/score/create_record",
    data,
  });
}

// 照片检查任务列表
export function photoCheckApi(data) {
  return request({
    method: "POST",
    url: "/score/photo_check",
    data,
  });
}

// 照片检查任务列表
export function materialRecordApi(data) {
  return request({
    method: "POST",
    url: "/score/material_record",
    data,
  });
}

// 编辑积分规则
export function editScoreRuleApi(data) {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        data: {
          success: true,
          message: '编辑成功'
        }
      });
    }, 300);
  });
}

// 删除积分规则
export function deleteScoreRuleApi(id) {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 从模拟数据中删除
      const index = mockData.findIndex(item => item.id === id);
      if (index > -1) {
        mockData.splice(index, 1);
      }

      resolve({
        data: {
          success: true,
          message: '删除成功'
        }
      });
    }, 300);
  });
}

// 启用/禁用积分规则
export function toggleScoreRuleApi(id, active) {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 更新模拟数据中的状态
      const item = mockData.find(item => item.id === id);
      if (item) {
        item.active = active;
      }

      resolve({
        data: {
          success: true,
          message: active === 1 ? '启用成功' : '禁用成功'
        }
      });
    }, 300);
  });
}
