import request from "../../index";

// 获取产品列表
export function getProductListApi() {
  return request({
    url: `/get/all_product_price`,
    method: "get",
  });
}

// 获取渠道列表
export function getChannelListApi(type) {
  return request({
    url: `/get/channel_list?type=${type}`,
    method: "get",
  });
}

// 提交价格价值变化
export function submitPriceValueChangeApi(data) {
  return request({
    url: `/approval/product_value_chain`,
    method: "post",
    data,
  });
}

// 获取价值链审批列表
export function getPriceValueChangeListApi(data) {
  return request({
    url: `/get/value_chain_approval_list `,
    method: "get",
    data,
  });
}

// 获取价值链审批列表-搜索
export function getPriceValueChangeListSearchApi(data) {
  return request({
    url: `/get/value_chain_approval_list_search`,
    method: "get",
    data,
  });
}
