<template>
  <div class="price-value-change">
    <!-- 表单容器 -->
    <div class="form-container">
      <a-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        layout="vertical"
        :scroll-to-first-error="true"
      >
        <!-- 模块1: 产品选择 -->
        <a-card class="form-section" :bordered="false">
          <template #title>
            <div class="section-title">
              <AppstoreOutlined class="title-icon" />
              <span class="title-text">发放信息</span>
            </div>
          </template>
          <a-form-item label="特渠费用名称" name="eventName" :required="true">
            <a-input
              v-model:value="formData.eventName"
              placeholder="请输入特渠费用名称"
              size="large"
            >
            </a-input>
          </a-form-item>

          <a-form-item label="发放经销商" name="personId" :required="true">
            <a-select
              v-model:value="formData.personId"
              placeholder="请选择发放经销商"
              size="large"
              :options="personOptions"
              @change="handlePersonChange"
              show-search
            />
          </a-form-item>
          <a-form-item label="发放金额/积分" name="moneyVal" :required="true">
            <a-input-number
              v-model:value="formData.moneyVal"
              placeholder="请输入发放金额/积分"
              size="large"
              :min="0"
              :precision="2"
              :step="1"
              style="width: 100%"
              @change="calculateManagerPoint"
            />
          </a-form-item>
          <a-form-item label="发放方式" name="grantType" :required="true">
            <a-radio-group
              v-model:value="formData.grantType"
              @change="handleChannelTypeChange()"
            >
              <a-space direction="vertical">
                <a-radio
                  v-for="item in channelTypes"
                  :key="item.value"
                  :value="item.value"
                >
                  {{ item.label }}
                </a-radio>
              </a-space>
            </a-radio-group>
          </a-form-item>
        </a-card>
        <!-- 提交按钮 -->
        <a-card class="submit-section" :bordered="false">
          <a-space style="width: 100%" direction="vertical">
            <a-button
              type="primary"
              size="large"
              :loading="submitLoading"
              @click="handleSubmit"
              style="
                width: 100%;
                height: 50px;
                font-size: 16px;
                font-weight: 600;
              "
            >
              <template #icon>
                <SendOutlined />
              </template>
              提交申请
            </a-button>

            <a-button size="large" @click="handleReset" style="width: 100%">
              <template #icon>
                <ReloadOutlined />
              </template>
              重置表单
            </a-button>
          </a-space>
        </a-card>
      </a-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from "vue";
import { message } from "ant-design-vue";
import {
  AppstoreOutlined,
  SendOutlined,
  ReloadOutlined,
} from "@ant-design/icons-vue";
import {
  getPersonApi,
  addGrantApi,
} from "/api/workbench/SpecialChannel/index.js";

// 响应式数据
const formRef = ref(null);
const submitLoading = ref(false);

// 表单数据
const formData = reactive({
  personId: undefined,
  personName: "",
  eventName: null,
  moneyVal: undefined,
  grantType: "",
});

// 渠道类型选项
const channelTypes = [
  { label: "货款", value: "货款" },
  { label: "积分", value: "积分" },
];

// 人员选项
const personOptions = ref([]);

// 获取人员数据
const getPersonList = async () => {
  const { data } = await getPersonApi();

  personOptions.value = data.result.map((item) => ({
    label: item.Master_data_person_name,
    value: item.id,
    ...item,
  }));
};

// 表单验证规则
const rules = {
  eventName: [
    { required: true, message: "请输入特渠费用名称", trigger: "change" },
  ],
  personId: [{ required: true, message: "请选择经销商", trigger: "change" }],
  moneyVal: [{ required: true, message: "请输入发放金额", trigger: "blur" }],
  grantType: [{ required: true, message: "请选择发放方式", trigger: "change" }],
};

const handlePersonChange = (value) => {
  const person = personOptions.value.find((p) => p.value === value);
  formData.personId = person.id;
  formData.personName = person.label ?? "";
};

const handleChannelTypeChange = async () => {};

const calculateManagerPoint = (e) => {
  formData.moneyVal = e >= 0 ? e.toFixed(2) : "0.00";
};

const handleSubmit = async () => {
  try {
    await formRef.value.validate();

    submitLoading.value = true;

    const userInfo = JSON.parse(sessionStorage.getItem("userInfo"));
    // 提交参数
    const params = {
      user_id: userInfo.id,
      grant_type: formData.grantType,
      grant_name: formData.eventName,
      grant_num: formData.moneyVal,
      grant_person: formData.personId,
      content:
        formData.grantType == "积分"
          ? `特渠费用名称：${formData.eventName}\n发放经销商：${formData.personName}\n发放方式：${formData.grantType}\n发放积分：${formData.moneyVal}积分`
          : formData.grantType == "货款"
          ? `特渠费用名称：${formData.eventName}\n发放经销商：${formData.personName}\n发放方式：${formData.grantType}\n发放货款：${formData.moneyVal}元`
          : "",
      user_name: userInfo.userName,
    };

    await addGrantApi(params);
    message.success("提交成功！");
    uni.navigateBack();
  } catch (error) {
    console.error("提交失败", error);
  } finally {
    submitLoading.value = false;
  }
};

const handleReset = () => {
  formRef.value.resetFields();
  Object.assign(formData, {
    personId: undefined,
    personName: "",
    eventName: null,
    moneyVal: undefined,
    grantType: "",
  });
  message.info("表单已重置");
};

onMounted(() => {
  // 初始化数据
  getPersonList();
});
</script>

<style lang="scss" scoped>
.price-value-change {
  height: 100vh;
  background: #f0f2f5;
  box-sizing: border-box;

  .page-header {
    position: sticky;
    top: 0;
    z-index: 100;
    background: #ffffff;
    border-bottom: 1px solid #e8e8e8;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    .header-content {
      padding: 16px 20px;
      max-width: 1200px;
      margin: 0 auto;

      .page-title {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        color: #262626;
      }
    }
  }

  .form-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 24px 20px;

    .form-section {
      margin-bottom: 24px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

      .section-title {
        display: flex;
        align-items: center;

        .title-icon {
          margin-right: 8px;
          font-size: 16px;
          color: #1890ff;
        }

        .title-text {
          font-size: 16px;
          font-weight: 600;
          color: #262626;
        }
      }

      :deep(.ant-card-head) {
        border-bottom: 1px solid #f0f0f0;
      }

      :deep(.ant-card-body) {
        padding: 24px;
      }
    }

    .submit-section {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

      :deep(.ant-card-body) {
        padding: 24px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .price-value-change {
    .page-header {
      .header-content {
        padding: 12px 16px;

        .page-title {
          font-size: 18px;
        }
      }
    }

    .form-container {
      padding: 16px;

      .form-section {
        margin-bottom: 16px;

        :deep(.ant-card-body) {
          padding: 16px;
        }
      }

      .submit-section {
        :deep(.ant-card-body) {
          padding: 16px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .price-value-change {
    .page-header {
      .header-content {
        padding: 10px 12px;

        .page-title {
          font-size: 16px;
        }
      }
    }

    .form-container {
      padding: 12px;

      .form-section {
        margin-bottom: 12px;

        :deep(.ant-card-body) {
          padding: 12px;
        }
      }

      .submit-section {
        :deep(.ant-card-body) {
          padding: 12px;
        }
      }
    }
  }
}

// 自定义样式
:deep(.ant-form-item-label) {
  font-weight: 500;
}

:deep(.ant-radio-group) {
  width: 100%;

  .ant-radio-wrapper {
    display: block;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

:deep(.ant-select-multiple) {
  .ant-select-selector {
    min-height: 40px;
  }
}

:deep(.ant-input-number) {
  .ant-input-number-input {
    height: 40px;
  }
}
</style>
