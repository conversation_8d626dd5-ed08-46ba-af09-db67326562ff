import request from "../../index";


export function get_jxyl(data,dq,sq,jxq) {
  return request({
    method: "get",
    url: `/boss/get_jxyl?data=${data}&dq=${dq}&sq=${sq}&jxq=${jxq}`,
  });
}
export function get_zzjg(dq,sq,jxq,jxkf) {
  return request({
    method: "get",
    url: `/boss/get_zzjg?dq=${dq}&sq=${sq}&jxq=${jxq}&jxkf=${jxkf}`,
  });
}

export function get_zzjg_cz(dq,sq,jxq,jxkf) {
  return request({
    method: "get",
    url: `/boss/get_zzjg_cz?dq=${dq}&sq=${sq}&jxq=${jxq}&jxkf=${jxkf}`,
  });
}

export function get_all_work(time,dq,sq,jxq,jxkf,id,Role_name) {
  return request({
    method: "get",
    url: `/boss/get_all_work?time=${time}&dq=${dq}&sq=${sq}&jxq=${jxq}&jxkf=${jxkf}&id=${id}&Role_name=${Role_name}`,
  });
}

// 查询组织架构-车长-新
export function get_zzjg_cz_new(jxkf) {
  return request({
    method: "get",
    url: `/boss/get_zzjg_cz_new?jxkf=${jxkf}`,
  });
}

// 获取回调数据
export function get_hdsj(time1,time2,jxqid,hhrid,fhjyzid) {
  return request({
    method: "get",
    url: `/boss/get_hdsj?time1=${time1}&time2=${time2}&jxqid=${jxqid}&hhrid=${hhrid}&fhjyzid=${fhjyzid}`,
  });
}

// 获取返货经营返货数据
export function get_fhjyzfhsj(time1,time2,jxqid,hhrid,fhjyzid) {
  return request({
    method: "get",
    url: `/boss/get_fhjyzfhsj?time1=${time1}&time2=${time2}&jxqid=${jxqid}&hhrid=${hhrid}&fhjyzid=${fhjyzid}`,
  });
}

// 获取返货经营销货数据
export function get_fhjyzxhsj(time1,time2,jxqid,hhrid,fhjyzid) {
  return request({
    method: "get",
    url: `/boss/get_fhjyzxhsj?time1=${time1}&time2=${time2}&jxqid=${jxqid}&hhrid=${hhrid}&fhjyzid=${fhjyzid}`,
  });
}

// 获取返货经营库存数据
export function get_fhjyzkcsj(time1,time2,jxqid,hhrid,fhjyzid) {
  return request({
    method: "get",
    url: `/boss/get_fhjyzkcsj?time1=${time1}&time2=${time2}&jxqid=${jxqid}&hhrid=${hhrid}&fhjyzid=${fhjyzid}`,
  });
}

export function get_tcdh_kc(jxqid,hhrid) {
  return request({
    method: "get",
    url: `/boss/get_tcdh_kc?jxqid=${jxqid}&hhrid=${hhrid}`,
  });
}