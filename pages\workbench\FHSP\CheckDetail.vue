<template>
	<u-loading-page :loading="bFirstLoading" bg-color="#b2b2b2" color="#fff" loading-mode="spinner"
		loading-text="加载中..."></u-loading-page>
	<view v-if="!bFirstLoading" class="all" :style="pageState == '新建' ? 'height:100vh' : 'min-height:100vh'">
		<scroll-view scroll-y :style="pageState == '新建' ? 'height:calc(100% - 210rpx)' : 'height:100%'">
			<!--  #region ============================= 主要信息 start ============================= -->
			<view class="main-info">
				
				<view class="basic-info">
					<view v-for="(item, index) in bBasicInfo" :key="index" class="info-item">
						<text class="info-item-title">{{ item.name }}：</text>
						<text class="info-item-content">{{ item.value }}</text>
					</view>
					<view v-if="returnBillInfo.billState == '3'" class="cancel-sign"> 返货单取消 </view>
					<view v-if="returnBillInfo.billState == '4'" class="turk-btn">
						<u-button type="primary" @click="toTurk(returnBillInfo)">装车单查看</u-button>
					</view>
				</view>
			</view>
			<!-- #endregion ============================= 主要信息 end ============================= -->

			<!--  #region ============================= 商品信息 start ============================= -->
			<view class="product-info">
				<ProductList :productInfoList="bProductInfo" />
			</view>
			<!-- #endregion ============================= 商品信息 end ============================= -->
			
			<!-- #region ============================= 审批模块 ============================= -->
			<view class="sug_box" v-if="bShowApprove">
				<view class="user-list">
					<view style="font-size: 30rpx; font-weight: 700; padding: 20rpx 0">审批流程</view>
					<view class="approve-info" >
						<view>审批人：{{ returnBillInfo.sq_name }}</view>
						<!-- <view class="approve-time">审批时间：{{ item.approveTime }}</view> -->
						<view>审批意见：</view>
						
						<view class="div5">
							<u--textarea placeholder="--暂无意见--" v-model="returnBillInfo.sq_spyj"
								:disabled="returnBillInfo.sq_status != '0' " autoHeight
								:maxlength="100"></u--textarea>
						</view>
					</view>
					
					<view class="approve-info" v-if="returnBillInfo.sq_status == '1' && returnBillInfo.dq_status == '0'">
						<view>审批人：{{ returnBillInfo.dq_name }}</view>
						<view class="approve-time">审批时间：{{ item.approveTime }}</view>
						<view>审批意见：</view>
						
						<view class="div5">
							<u--textarea placeholder="--暂无意见--" v-model="returnBillInfo.dq_spyj"
								:disabled="returnBillInfo.dq_status != '0'" autoHeight
								:maxlength="100"></u--textarea>
						</view>
					</view>
					
					
					
					
					<view class="div6" v-if="bIsEdit">
<!--						<u-button text="驳回" type="error" :loading="bRejectLoading" loadingText="驳回"-->
<!--							@click="onReject"></u-button>-->
						<u-button text="同意" type="primary" loadingText="通过" @click="onAgree"></u-button>
					</view>
				
				</view>
			</view>
		
	
			<!-- #endregion ============================= 审批模块 end ============================= -->
		</scroll-view>
		<!-- 只有新建进来显示 -->
		<view v-if="pageState == '新建'" class="btn_box">
			<!-- <view class="btn_box"> -->
			<view class="operate">
				<u-button shape="circle" color="#3398da" @click="onCopyBill">复制</u-button>
				<u-button shape="circle" color="#1bbf96e8" @click="backlist" v-if="truckIf">返回列表</u-button>
				<u-button shape="circle" color="#1bbf96e8" @click="backlist" v-if="!truckIf">返回返货列表</u-button>
			</view>
			<view class="tab_box">
				<view class="tabs" v-for="(item, index) in tabbar.datas" :key="index" @click="jumpTab(item)">
					<view class="tab_img">
						<image :src="item.iconPath" style="width: 40% !important; height: 80% !important"></image>
					</view>
					<view style="font-size: 26rpx">{{ item.name }}</view>
				</view>
			</view>
		</view>
		<!-- -------------------- 同意审批弹窗 -------------------- -->
		<u-popup :show="bAgreeApproveShow" mode="center" bgColor="transparent">
			<view class="agree-approve-model">
				<view class="title">请选择业务结算方式</view>
				<view class="content">
					<u-radio-group v-model="sSettleCountType" :disabled="
              (returnBillInfo.businessManagerCode
                ? returnBillInfo.businessManagerCode != userInfo.Master_data_person_code
                : false) || !returnBillInfo.isReturnFactory
            " iconPlacement="right" activeColor="#19be6b" placement="column" labelSize="28rpx" iconSize="24rpx"
						borderBottom>
						<u-radio name="返工厂结算" label="返工厂结算"></u-radio>
						<!-- 05-30 暂时只读 -->
						<u-radio name="经销商结算" label="经销商结算" disabled></u-radio>
					</u-radio-group>
				</view>
				<view class="operate">
					<view class="cancel" @click="onApproveCancel">取消</view>
					<view class="confirm" @click="onApproveConfirm">确定</view>
				</view>
			</view>
			<u-loading-page :loading="bAgreeLoading" bg-color="#616161eb" color="#fff" loading-mode="spinner"
				loading-text="提交中..."></u-loading-page>
		</u-popup>
	</view>
</template>

<script setup>
	import {
		reactive,
		ref,
		computed
	} from 'vue'
	import {
		onLoad,
		onUnload
	} from '@dcloudio/uni-app'
	
	import { useUserInfo } from "/store/user/userInfo";
	import {
		postGetReturnBasicInfo,
		postGetReturnGoodInfo,
		getApproveInfo,
		get_fhbz,
		sumit_fhsp,
		sumit_fhsp_dq
	} from '@/api/workbench/FHSP/index.js'
	

	import dayjs from 'dayjs'
	
	import ProductList from './common/ProductList.vue'

	const {
		userInfo
	} = useUserInfo()
	// 用户ID
	let userID = userInfo.id
	// 当前页面的进入状态，从新建来还是列表进来
	let pageState = ref('')

	// 基础信息
	let bBasicInfo = ref([])
	// 商品信息
	let bProductInfo = ref([])
	// 当前页面数
	let currentPagesLength = 0
	// 页面堆栈的第一页路由
	let firstPage = ''

	// 返货单基础信息
	let returnBillInfo = reactive({
		returnBillID: '', // 返货单ID(返货主表的每条id)
		did: '',
		returnBillCode: '', // 返货单编号
		productMoney: '', // 产品金额
		kuangMoney: '', // 筐金额
		startPersonID: '', // 返货单发起人ID
		startPersonName: '', // 返货单发起人姓名
		businessManagerName: '', // 业务经理姓名
		businessManagerCode: '', // 业务经理编码
		businessManagerState: '', // 业务经理审批状态
		salePersonName: '', // 经销商姓名
		salePersonCode: '', // 经销商编码
		salePersonState: '', // 经销商审批状态
		billState: '', // 返货单状态
		isReturnFactory: 0, // 发起人的经销商是否返工厂结算 0-否，1-是
		completePersonID: '', // 单据完成人ID
	})

	// 审批流程
	let aApproveProcess = ref([])
	// 当前审批意见,(可编辑，只有第一个才有可能编辑)
	const currentApproveOpinion = computed(() => {
		if (aApproveProcess.value.length > 0) {
			return aApproveProcess.value[0].approveOpinion
		} else {
			return ''
		}
	})

	// #region ============================= 获取订单信息 start =============================

	// 获取订单基础信息
	const fnGetReturnBasicInfo = async (mainId) => {
		try {
			let {
				data
			} = await postGetReturnBasicInfo(mainId)
			if (data.code == 200) {
				let info = data.result[0]
				returnBillInfo.returnBillCode = info.Return_goods_bills_code
				returnBillInfo.productMoney = info.Return_goods_return_good_money || 0
				returnBillInfo.kuangMoney = info.Return_goods_kuang_money || 0
				returnBillInfo.businessManagerName = info.Return_goods_business_manager_name
				returnBillInfo.businessManagerCode = info.Return_goods_business_manager_code
				returnBillInfo.businessManagerState = info.Return_goods_business_manager_state
				returnBillInfo.salePersonName = info.Return_goods_sale_person_name
				returnBillInfo.salePersonCode = info.Return_goods_sale_person_code
				returnBillInfo.superCode = info.Return_goods_business_super_code

				returnBillInfo.superState = info.Return_goods_business_super_state
				returnBillInfo.superName = info.Return_goods_business_super_name
				returnBillInfo.salePersonState = info.Return_goods_sale_person_state
				returnBillInfo.startPersonID = info.Return_goods_start_person_id
				returnBillInfo.startPersonName = info.Return_goods_start_person_name
				returnBillInfo.billState = info.Return_goods_bills_state
				returnBillInfo.sq_status = info.sq_status
				returnBillInfo.dq_status = info.dq_status
				returnBillInfo.sq_spyj = info.sq_spyj
				returnBillInfo.dq_spyj = info.dq_spyj
				returnBillInfo.sq_name = info.sq_name
				returnBillInfo.dq_name = info.dq_name
				if(returnBillInfo.sq_name == null){
					returnBillInfo.sq_name = userInfo.userName
				}
				if(returnBillInfo.dq_name == null){
					returnBillInfo.dq_name = userInfo.userName
				}
				returnBillInfo.isReturnFactory = info.is_return_factory
				returnBillInfo.completePersonID = info.Return_goods_bills_complete_person_id
				sSettleCountType.value = info.Return_goods_bill_type ?
					info.Return_goods_bill_type :
					info.is_return_factory ?
					'返工厂结算' :
					'经销商结算' // 返货类型
				bBasicInfo.value = [{
						name: '返货编号',
						value: info.Return_goods_bills_code,
					},
					{
						name: '申请人',
						value: info.Return_goods_start_person_name,
					},
					{
						name: '发起时间',
						value: dayjs(info.Return_goods_start_date).subtract(8, 'h').format(
							'YYYY-MM-DD HH:mm:ss'),
					},
					{
						name: '返货时间',
						value: info.Return_goods_date_time || '--',
					},
					// {
					//   name: '物流班次',
					//   value: info.Return_goods_material_flow_name,
					// },
					{
						name: '车辆牌照',
						value: info.Return_goods_car_id,
					},
					{
						name: '筐  数',
						value: info.Return_goods_kuang_number,
					},
					{
						name: '产品金额',
						value: '￥' + (info.Return_goods_return_good_money?.toFixed(2) ?? '￥0.00'),
					},
					{
						name: '筐总金额',
						value: '￥' + (info.Return_goods_kuang_money?.toFixed(2) ?? '0.00'),
					},
					{
						name: '业务结算方式',
						value: info.Return_goods_bill_type || '--',
					},
					{
						name: '返货总金额',
						value: '￥' + ((returnBillInfo.productMoney + returnBillInfo.kuangMoney).toFixed(2) ??
							'0.00'),
					},
				]
				// await fhbz(info.fhzxjk)
			}
		} catch (error) {
			console.log(error)
		}
	}

	// 获取订单商品信息
	const fnGetReturnGoodInfo = async (mainId) => {
		try {
			let {
				data
			} = await postGetReturnGoodInfo(mainId)
			if (data.code == 200) {
				bProductInfo.value = data.result.map((item) => {
					return {
						id: item.id,
						isProblem: item.Return_goods_child_is_problem, // 是否变质品
						name: item.Return_goods_child_goods_name, // 商品名称
						code: item.Return_goods_child_goods_code, // 商品编码
						barCode: item.Return_goods_child_bar_code, // 商品条形码
						specification: item.Return_goods_child_specification, // 商品规格
						productUrl: item.Upload_img_url, // 商品图片
						price: item.Return_goods_child_choose_tax_unit_price, // 商品价格
						number: item.Return_goods_child_number, // 商品数量
						unit: item.Return_goods_child_choose_unit, // 商品单位
						Excessive_information: item.Excessive_information, // 超标信息
						isSwitchOn: item.No_credited
					}
				})
			}
		} catch (error) {}
	}
	// #endregion ============================= 获取订单信息 end =============================

	// #region ============================= 审批模块 start =============================
	// 审批状态
	let sApproveState = computed(() => (state) => {
		switch (state) {
			case '0':
				return {
					text: '待审批',
						color: '#f2c037',
				}
			case '1':
				return {
					text: '同意',
						color: '#67c23a',
				}
			case '2':
				return {
					text: '驳回',
						color: '#f56c6c',
				}
			case '3':
				return {
					text: '已取消',
						color: '#909399',
				}
			default:
				return {
					text: '待审批',
						color: '#f2c037',
				}
		}
	})
	// 当前进度
	const currentSchedule = computed(() => {
		// 审批状态为1，则审批完成
		// if (returnBillInfo.billState == '1') {
		// 	return 4
		// }
		// // 业务经理为空，则业务经理审批状态为同意
		// if (!returnBillInfo.businessManagerName) {
		// 	let stateCode = returnBillInfo.salePersonState == '1' ? 4 : 2
		// 	return stateCode
		// } else {
		// 	// 业务经理为同意，经销商审批状态为同意
		// 	if (returnBillInfo.businessManagerState == '1' && returnBillInfo.salePersonState == '1') {
		// 		return 4
		// 	} else if (returnBillInfo.businessManagerState == '1') {
		// 		// 业务经理审批状态为同意，经销商审批状态为待审批或者驳回
		// 		return 2
		// 	} else if (returnBillInfo.billState == '3') {
		// 		// 订单已取消
		// 		return 0
		// 	} else {
		// 		// 业务经理审批状态为待审批或者驳回
		// 		return 1
		// 	}
		// }
		
		if (returnBillInfo.superState == '1') {
			return 3
		}else if (returnBillInfo.billState == '3') {
				// 订单已取消
				return 0
		} else {
				// 业务经理审批状态为待审批或者驳回
			return 1
		}
		
	})

	// 展示审批模块--新建或已取消都不显示，发起人和审批人相同也不显示（也就是经销商发起返货单）
	let bShowApprove = computed(() => {
		return (
			returnBillInfo.billState != '3' &&
			pageState.value != '新建' &&
			returnBillInfo.startPersonID != returnBillInfo.completePersonID
		)
	})
	// 展示取消订单按钮--登录人和发起人相等且无人审批时展示
	let bShowCancel = computed(() => {
		// 业务经理存在，且业务经理审批状态为待审批
		if (returnBillInfo.businessManagerName && !returnBillInfo.businessManagerState) {
			return userID == returnBillInfo.startPersonID
			// 业务经理不存在，且经销商审批状态为待审批
		} else if (!returnBillInfo.businessManagerName && !returnBillInfo.salePersonState) {
			return userID == returnBillInfo.startPersonID
		}
	})
	// 审批同意驳回按钮是否显示--登录人和审批人相等且待审批时可显示编辑
	let bIsEdit = computed(() => {
		// 登录用户是业务经理 且 业务经理审批状态为待审批
		
		console.log(returnBillInfo);
		console.log(returnBillInfo.billState);
		if(returnBillInfo.billState === 0){
			return true
		}
	})

	// #region ------------ 审批详情 start ------------
	const fnGetApproveInfo = async (mainID) => {
		try {
			let {
				data
			} = await getApproveInfo(mainID)
			// aApproveProcess.value = data.result.map((item) => {
			// 	let time = item.Todo_tasks_approval_date ?
			// 		dayjs(item.Todo_tasks_approval_date).subtract(8, 'h').format('YYYY-MM-DD HH:mm:ss') :
			// 		'--'
			// 	return {
			// 		approvePersonName: item.approver_name, // 审批人姓名
			// 		approvePersonID: item.approver_id, // 审批人id
			// 		approveTime: time, // 审批时间
			// 		approveOpinion: item.Todo_tasks_approval_opinion, // 审批意见
			// 		approveState: item.Todo_tasks_approval_status, // 审批状态
			// 	}
			// })
		} catch (error) {
			console.log(error)
			uni.showToast({
				title: '获取信息失败',
				icon: 'error',
				mask: true,
				duration: 2000,
			})
		}
	}
	// #endregion ------------ 审批详情 end ------------

	// #region ------------ 审批同意 start ------------
	let bAgreeLoading = ref(false)
	let bAgreeApproveShow = ref(false) // 同意审批弹窗
	let sSettleCountType = ref('返工厂结算') // 业务结算方式
	// 同意审批出现弹窗
	const onAgree = async() => {
		console.log('returnBillInfo',returnBillInfo.value);
		console.log('bProductInfo.value',bProductInfo.value);
		let isgo = 0
		for (var i = 0; i < bProductInfo.value.length; i++) {
			if(bProductInfo.value[i].isSwitchOn){
				bProductInfo.value[i].No_credited = 1
				isgo = 1
				
			}else{
				bProductInfo.value[i].No_credited = 0
			}
		}
		let res = ''
		if(userInfo.Role_grade == '省区总'){
			let temp = {
				id: returnBillInfo.returnBillID, // 返货单id
				note: returnBillInfo.sq_spyj, // 审批意见
				user: userInfo.id, // 审批描述
				status: '1', // 单据状态-- 2-驳回
				username: userInfo.userName,
				bProductInfo: bProductInfo.value,
				isgo: isgo
			}
			res = await sumit_fhsp(temp)
		}else if(userInfo.Role_grade == '大区总'){
			let temp = {
				id: returnBillInfo.returnBillID, // 返货单id
				note: returnBillInfo.dq_spyj, // 审批意见
				user: userInfo.id, // 审批描述
				status: '1', // 单据状态-- 2-驳回
				username: userInfo.userName,
				bProductInfo: bProductInfo.value,
        isgo: isgo
			}
			res = await sumit_fhsp_dq(temp)
		}
		if (res.data.code == 200) {
			setTimeout(() => {
				uni.showToast({
					title: '同意成功',
					icon: 'success',
					duration: 2000,
				})
			}, 10)
		}
		uni.navigateBack()
	}
	const onReject = async () => {
		let temp = {
			id: returnBillInfo.returnBillID, // 返货单id
			note: '驳回', // 审批意见
			user: userInfo.id, // 审批描述
			status: '2', // 单据状态-- 2-驳回
			
		}
		let res = ''
		if(userInfo.Role_grade == '省区总'){
			res = await sumit_fhsp(temp)
		}else if(userInfo.Role_grade == '大区总'){
			res = await sumit_fhsp_dq(temp)
		}
		if (res.data.code == 200) {
			setTimeout(() => {
				uni.showToast({
					title: '驳回成功',
					icon: 'success',
					duration: 2000,
				})
			}, 10)
		}
		uni.navigateBack()
	}
	const onApproveCancel = () => {
		bAgreeApproveShow.value = false
	}


	// ----------------- 审批同意确认 -----------------
	

	// #endregion ------------ 审批同意 end ------------

	// #region ============================= 根据不同角色执行不同审批的同意函数 start =============================

	

	// 点击确认同意
	const onApproveConfirm = async () => {
		bAgreeLoading.value = true
		try {
			
			
				let data = await fnSalePersonApprove_sj()
				console.log(data, '----当前用户是经销商')
				if (data.code == 200 && data.msg.includes('error')) {
					uni.showToast({
						title: '经销商余额不足此次返货金额，请确认充值，谢谢！',
						icon: 'none',
						duration: 2000,
					})
					return
				}
			setTimeout(() => {
				uni.showToast({
					title: '提交成功',
					icon: 'success',
					duration: 2000,
				})
			}, 10)
			uni.navigateBack()
		} catch (error) {
			console.log(error)
			uni.showToast({
				title: '提交失败',
				icon: 'error',
				duration: 2000,
			})
		} finally {
			bAgreeLoading.value = false
			bAgreeApproveShow.value = false
		}
	}
	// #endregion ============================= 根据不同角色执行不同审批的同意函数 end =============================

	// #region ------------ 审批驳回 start ------------
	let bRejectLoading = ref(false)
	
	// #endregion ------------ 审批驳回 end ------------

	// #region ------------ 取消订单 start ------------
	// 取消订单loading
	let bCancelLoading = ref(false)
	
	// #endregion ------------ 取消订单 end ------------
	// #endregion ============================= 审批模块 end =============================

	// #region ============================= 新建返货单后菜单 start =============================
	//导航数据
	//tabbar数组
	const tabbar = reactive({
		datas: [{
				name: '首页',
				iconPath: '/static/icon/home.png',
			},
			{
				name: '任务',
				iconPath: '/static/icon/task.png',
			},
			{
				name: '工作台',
				iconPath: '/static/icon/classroom2.png',
			},
			{
				name: '数据',
				iconPath: '/static/icon/information.png',
			},
			{
				name: '我的',
				iconPath: '/static/icon/mine.png',
			},
		],
	})
	//返回列表按钮
	const backlist = () => {
		if (currentPagesLength > 3) {
			uni.navigateBack({
				delta: currentPagesLength - 2,
			})
			currentPagesLength = 0
			// 从首页或者工作台进入，返回到历史页
		} else if (firstPage == 'pages/index/index' || firstPage == 'pages/Workbench/index') {
			uni.redirectTo({
				url: '/pages/Workbench/components/ReturnGoodsMag/OrderHistory',
			})
			firstPage = ''
		}
	}
	// 复制返货单
	const onCopyBill = () => {
		uni.redirectTo({
			url: '/pages/Workbench/components/ReturnGoodsMag/OrderDetails',
		})
	}
	//跳转到导航栏页面
	const jumpTab = (data) => {
		if (data.name == '首页') {
			uni.switchTab({
				url: '/pages/index/index',
			})
		}
		if (data.name == '任务') {
			uni.switchTab({
				url: '/pages/TaskList/NewIndex',
			})
		}
		if (data.name == '工作台') {
			uni.switchTab({
				url: '/pages/Workbench/index',
			})
		}
		if (data.name == '数据') {
			uni.switchTab({
				url: '/pages/Data/index',
			})
		}
		if (data.name == '我的') {
			uni.switchTab({
				url: '/pages/PersonalCenter/index',
			})
		}
		// 页面数清零，防止触发onUnload
		currentPagesLength = 0
		firstPage = ''
	}
	let currentStep = ref(0)
	let currentStep2 = ref(0)
	let stepList = ref()
	let stepList2 = ref()
	
	const fhbz2 =async (id) => {
		// const res =  await get_fhbz(id)
		// console.log('res',res);
		// stepList2.value = res.data.result
		
		
	}
	// #endregion ============================= 新建返货单后菜单 end =============================
	let bFirstLoading = ref(false)
	//是否显示再来一单按钮
	const againBtn = ref(false)
	//是否是装车页面进入
	const truckIf = ref(false)
	onLoad(async (option) => {
		try {
			truckIf.value = false
			console.log('option', option)
			if (option.type == '列表') {
				againBtn.value = true
			}
			if(option.type == '装车'){
				truckIf.value = true
			}
			
			bFirstLoading.value = true
			returnBillInfo.returnBillID = option.mainId
			returnBillInfo.did = option.billCode
			pageState.value = option.status // 当前页面状态，新建或者其他

			let pages = getCurrentPages()
			console.log('pages:', pages)

			currentPagesLength = pages.length
			firstPage = pages[0].route

			//待办表数据
			fnGetApproveInfo(returnBillInfo.returnBillID)
			fnGetReturnBasicInfo(returnBillInfo.returnBillID)
			await fnGetReturnGoodInfo(returnBillInfo.returnBillID)
			// await fhbz2(returnBillInfo.returnBillID)
		} catch (error) {
			uni.showToast({
				title: '加载失败',
				icon: 'error',
				mask: true,
				duration: 2000,
			})
		} finally {
			bFirstLoading.value = false
		}
	})

	onUnload(() => {
		if(truckIf.value == true){
			// uni.navigateBack()
			// let pages = getCurrentPages()
			// console.log('pages:', pages)
		}else{
			if (currentPagesLength > 3) {
				uni.navigateBack({
					delta: currentPagesLength - 3,
				})
			} else if (firstPage == 'pages/index/index') {
				uni.redirectTo({
					url: '/pages/Workbench/components/ReturnGoodsMag/OrderHistory',
				})
			}
		}
	})
	// #region ------------ 打印 start ------------
	const onPrintBill = () => {
		let temp = {
			bBasicInfo: bBasicInfo.value,
			bProductInfo: bProductInfo.value,
			statuss: 'fanhuo',
			statue: returnBillInfo.billState, // 返货单状态
		}
		uni.navigateTo({
			url: `/pages/Workbench/components/print/index?datas=${JSON.stringify(temp)}`,
		})
	}
	// #endregion ------------ 打印 end ------------

	//------------------跳转到发货单页面--------------
	//查看发车单
	const toTurk = (ordernum) => {
		console.log('查看装车单')
		console.log(returnBillInfo.did)
		uni.navigateTo({
			url: `/pages/OthersGoods/TruckDetail?returnDetail=${returnBillInfo.did}`,
		})
	}


</script>

<style lang="scss" scoped>
	.all {
		// border: 1px solid #e5e5e5;
		position: relative;
		height: 100vh;
		width: 70%;
		box-sizing: border-box;
		margin: 0 auto; /* 这将使元素在父容器中水平居中 */
		padding: 40px; /* 替换为您想要的padding值 */

		.go-back {
			position: absolute;
			bottom: 10rpx;
			left: 0;
			width: 100%;
			// transform: translateX(-50%);
		}
	}

	.main-info {
		padding: 10rpx 20rpx;
		box-shadow: 0rpx 10rpx 20rpx rgba(215, 215, 215, 0.34);
		box-sizing: border-box;
	}

	.product-info {
		margin-top: 20rpx;
		// margin-bottom: 90rpx;
		// height: 500rpx;
		// overflow: auto;
		// overflow-y: scroll;
	}

	// ----------------- 主要信息 -----------------
	.deal-state {
		margin-bottom: 20rpx;

		.title {
			display: flex;
			justify-content: space-between;
			margin-bottom: 10rpx;
			font-weight: bold;
			font-size: 30rpx;
		}

		.print {
			width: 100rpx;
			height: 36rpx;
			font-size: 26rpx;
			background-color: #a7c9d6;
			text-align: center;
			border-radius: 10rpx;
			padding: 8rpx;
			color: white;
			font-weight: bold;
			transform: translateY(-10rpx);
		}
	}

	.basic-info {
		position: relative;

		.info-item {
			font-size: 26rpx;
			padding-bottom: 10rpx;

			.info-item-title {
				color: #606266;
			}

			.info-item-content {
				color: #000;
				font-weight: bold;
			}
		}

		.cancel-sign {
			position: absolute;
			right: 36rpx;
			top: 70rpx;
			width: 200rpx;
			height: 200rpx;
			line-height: 200rpx;
			font-size: 32rpx;
			color: #f25f5c;
			font-weight: bold;
			text-align: center;
			border: 4rpx dashed #ff6f61;
			border-radius: 100rpx;
		}

		//装车单按钮样式
		.turk-btn {
			width: 200rpx;
			height: 80rpx;
			position: absolute;
			bottom: 10rpx;
			right: 0;
		}
	}

	//再来一单按钮样式
	.againclass {
		margin: 30rpx 0;
		width: 100%;
		display: flex;
		justify-content: center;

		:deep(.u-button) {
			width: 90% !important;
			background-color: green !important;
			color: #fff !important;
			font-size: 30rpx !important;
			border-radius: 10rpx !important;
		}
	}

	//意见模块
	//意见
	.sug_box {
		padding-bottom: 2rpx;

		.user-list {
			padding: 0 10px;
			box-shadow: 0rpx 10rpx 20rpx rgba(215, 215, 215, 0.34);

			//标签样式
			:deep(.u-tag--mini) {
				height: 30rpx !important;
				line-height: 30rpx !important;
				padding: 5rpx !important;
			}

			:deep(.u-tag__text--mini) {
				font-size: 26rpx !important;
				line-height: 26rpx !important;
			}

			.approve-info {
				position: relative;
				font-size: 28rpx;
				margin-bottom: 20rpx;

				.approve-time {
					margin: 10rpx 0;
				}

				.approve-state {
					position: absolute;
					right: 0;
					top: 0;
				}
			}

			.participant {
				display: flex;
				align-items: center;
				box-sizing: border-box;
				padding: 10rpx;
				border-radius: 10rpx;
				margin-top: 20rpx;
				// margin-left: 100rpx;
				border: 2rpx solid #dadbde;
			}

			.div5 {
				:deep(.u-textarea__field) {
					font-size: 26rpx !important;
					max-height: 150rpx !important;
				}
			}

			.div6 {
				margin-top: 20rpx;
				padding-bottom: 20rpx;
				display: flex;
				justify-content: space-between;

				:deep(.u-button) {
					font-size: 50rpx !important;
					height: 100rpx !important;
					width: 210rpx !important;
					margin: 0;
				}

				:deep(.u-button__text) {
					font-size: 36rpx !important;
				}
			}
		}
	}

	//返货成功按钮盒子
	.btn_box {
		position: absolute;
		bottom: 0;
		left: 0;
		height: 210rpx;
		width: 100%;
		padding-bottom: 30rpx;
		overflow: hidden;
		box-sizing: border-box;

		// background-color: pink;
		.operate {
			display: flex;
			justify-content: space-around;
			// height: 140rpx;
			// padding: 40rpx 0 30rpx;
			padding: 10rpx;
			box-sizing: border-box;

			// box-shadow: 0px 1px 8px 0px #d4dcdaab;
			:deep(.u-button) {
				width: 230rpx;
				height: 70rpx;
				font-size: 26rpx;
				box-shadow: 0px 1px 8px 0px #8acebd;

				&:nth-child(2) {
					box-shadow: 0px 1px 8px 0px #3398da;
				}
			}
		}

		// :deep(.u-button) {
		//   width: 80% !important;
		//   height: 80rpx !important;
		//   margin-bottom: 10rpx;
		//   background-color: #4ea28d !important;
		//   color: #fff !important;
		//   font-size: 28rpx !important;
		//   border-radius: 50rpx !important;
		// }
		//tab盒子
		.tab_box {
			display: flex;
			justify-content: space-around;
			height: 90rpx;
			margin-bottom: 10rpx;

			.tabs {
				width: 20%;
				height: 100%;
				display: flex;
				flex-wrap: wrap;
				justify-content: center;

				.tab_img {
					width: 70%;
					height: 45rpx;
					text-align: center;
				}
			}
		}
	}

	.approval-process {
		:deep(.u-steps) {
			align-items: center !important;
		}

		//连接线
		:deep(.u-steps-item__line--row) {
			top: 25rpx !important;
		}

		//步骤圆圈
		:deep(.u-steps-item__wrapper__circle) {
			width: 50rpx !important;
			height: 50rpx !important;
		}

		:deep(.u-steps-item__wrapper__circle__text) {
			font-size: 26rpx !important;
		}

		:deep(.u-text__value--main) {
			font-size: 26rpx !important;
		}

		:deep(.u-text__value--content) {
			font-size: 26rpx !important;
		}

		:deep(.u-steps-item__wrapper--row) {
			width: 50rpx !important;
			height: 50rpx !important;
		}

		:deep(.u-icon__icon) {
			font-size: 26rpx !important;
		}

		:deep(.u-steps-item__content) {
			margin-left: 0 !important;

			.u-text {
				justify-content: center !important;
			}
		}
	}
</style>