import request from "../../index";

// 查询相册
export function get_user_xiangce(data) {
  return request({
    url: `/boss/get_user_xiangce`,
    method: "post",
    data
  });
}

// 获取所有大区
export function getAllRegionApi() {
  return request({
    url: `/get/all_region_qd`,
    method: "get",
  });
}

export function getAllRegionApi_dqz(userId) {
  return request({
    method: "get",
    url: `/get/all_region_qd_dqz?userId=${userId}`,
  });
}

// 获取所有省区
export function getAllProvincesApi() {
  return request({
    url: `/get/all_provinces_new`,
    method: "get",
  });
}
export function getAllProvincesApi_sqz(userId) {
  return request({
    method: "get",
    url: `/get/all_provinces_sqz_new_cl?userId=${userId}`,
  });
}


// 获取所有经销区
export function getAllJxqApi() {
  return request({
    url: `/get/all_jxq_new`,
    method: "get",
  });
}
export function getAllJxqApiCS(userId) {
  return request({
    url: `/get/all_jxq_new_csjl?userId=${userId}`,
    method: "get",
  });
}

// 获取车长列表
export function getAllCarApi(id) {
  return request({
    url: `/boss/get_cz_by_jxq?jxq=${id}`,
    method: "get",
  });
}

// 获取照片任务
export function getAllTaskApi(id) {
  return request({
    url: `/get/photo_task`,
    method: "get",
  });
}

// 除车长外过滤
export function get_all_xc(data) {
  return request({
    url: `/boss/get_all_xc`,
    method: "post",
    data
  });
}

// 车长过滤
export function get_user_xiangce_user(data) {
  return request({
    url: `/boss/get_user_xiangce_user`,
    method: "post",
    data
  });
}

// 批量审批照片接口
export function change_photo_status(data) {
  return request({
    url: `/change/photo_status`,
    method: "post",
    data
  });
}
