<template>
  <a-config-provider :locale="zhCN">
    <div style="position: relative">
      <div class="legend-list" v-show="showLegend">
        <div v-for="(item, index) in legendData" :key="index">
          <img :src="item.img" alt="" />
          <span>{{ item.name }}</span>
        </div>
      </div>
      <SelectList
        @selectChange="selectChange"
        @setLayerVisible="setLayerVisible"
        @drawFCMarkers="drawFCMarkers"
        @changeMapCenter="changeMapCenter"
        :carList="carList"
        :fcPointList="fcPointList"
      />
      <div class="map-container" id="map"></div>
      <div class="spin" v-show="showSpin">
        <a-spin size="large" tip="加载中..." />
      </div>
    </div>
    <a-modal
      v-model:open="list_show"
      style="width: 1000px; top: 20px"
      title="驾驶数据"
      :footer="null"
    >
      <DriverData :currentClickCar="currentClickCar" v-if="list_show" />
    </a-modal>
    <a-modal
      v-model:open="fcinfo_show"
      style="width: 1000px; top: 20px"
      title="最近30批到货"
      :footer="null"
    >
      <FenChangData v-if="fcinfo_show" :branchCode="branchCode" />
    </a-modal>
  </a-config-provider>
</template>

<script setup>
import { nextTick, ref, onMounted, onUnmounted, reactive } from "vue";
import { legendData, CarStatus } from "./data";
import {
  getAllCars,
  getAllFCs,
  getCarFreight,
  getCarPlan,
  getCarTracks,
  getSeparateCost,
} from "../../api/vehiclemonitoring";
import { onShow, onHide } from "@dcloudio/uni-app";
import { message } from "ant-design-vue";
import dayjs from "dayjs";
import zhCN from "ant-design-vue/es/locale/zh_CN";
import "dayjs/locale/zh-cn";
dayjs.locale("zh-cn");
import SelectList from "./SelectList.vue";
import DriverData from "./DriverData.vue";
import FenChangData from "./FenChangData.vue";

nextTick(() => {
  initMap();
});

onUnmounted(() => {
  clearInterval(timer);
});

onMounted(async () => {
  await getCarList();
  await drawCar();
  const date = dayjs().format("YYYY-MM-DD");
  await drawFCMarkers(date);
});

let map; //地图对象
let carMarker; //车辆点对象
let S_E_markers; //起点终点图层对象
let FC_markers; //分仓对象
let InfoWindow; //信息窗对象
let carLine; //线路对象

const showSpin = ref(false);
const showLegend = ref(true);
const list_show = ref(false);
let plan_date = null;
const fcinfo_show = ref(false);

function initMap() {
  const center = new TMap.LatLng(39.984104, 116.307503);
  map = new TMap.Map("map", {
    center: center,
    zoom: 6.5,
    viewMode: "2D",
    baseMap: {
      type: "vector", //类型：失量底图
    },
  });
  carMarker = new TMap.MultiMarker({
    id: "marker-layer", //图层唯一标识
    map: map,
    zIndex: 3,
  });
  FC_markers = new TMap.MultiMarker({
    id: "marker-FC_markers", //图层唯一标识
    map: map,
    zIndex: 1,
    styles: {
      FC: new TMap.MarkerStyle({
        width: 20,
        height: 20,
        src: "/static/icon/vehiclemonitoring/red.png",
        offset: { x: 0, y: 25 },
      }),
    },
  });

  FC_markers.on("click", event => {
    drawFCInfoWindow(event.geometry.id);
  });
  S_E_markers = new TMap.MultiMarker({
    id: "marker-points", //图层唯一标识
    map: map,
    zIndex: 2,
    styles: {
      start: new TMap.MarkerStyle({
        width: 35,
        height: 45,
        src: "https://mapapi.qq.com/web/lbs/javascriptGL/demo/img/start.png", //图片路径
      }),
      end: new TMap.MarkerStyle({
        width: 35,
        height: 45,
        src: "https://mapapi.qq.com/web/lbs/javascriptGL/demo/img/end.png", //图片路径
      }),
      fc: new TMap.MarkerStyle({
        offset: { x: 0, y: -30 },
      }),
    },
  });
  carMarker.on("click", event => {
    drawInfoWindow(event.geometry.id);
  });

  InfoWindow = new TMap.InfoWindow({
    map: map,
    position: center, //设置信息框位置
    content: "", //设置信息框内容
    offset: {
      x: 0,
      y: -20,
    },
  });
  InfoWindow.on("closeclick", async () => {
    await clearLines();
    // map.easeTo({ zoom: 7 });
  });
  InfoWindow.close(); //初始时不显示
  carLine = new TMap.MultiPolyline({
    id: "polyline-layer", //图层唯一标识
    map: map, //绘制到目标地图
    styles: {
      style_blue_s: new TMap.PolylineStyle({
        color: "#3777FF", //线填充色
        width: 6, //折线宽度
        borderWidth: 5, //边线宽度
        borderColor: "#FFF", //边线颜色
        lineCap: "butt", //线端头方式
        showArrow: true,
      }),
      style_blue_x: new TMap.PolylineStyle({
        color: "#3777FF", //线填充色
        width: 2, //折线宽度
        lineCap: "round", //线端头方式
        dashArray: [10, 10],
      }),
    },
  });
}

// 修改地图中心点
function changeMapCenter(lat, lng) {
  const center = new TMap.LatLng(lat, lng);
  map.setCenter(center);
  map.setZoom(16);
}

const carList = ref([]); //处理显示地图列表信息
async function getCarList() {
  carList.value = []; //清空数据
  const res = await getAllCars();
  const data1 = res.data.result[0]; //干线车辆信息
  const data2 = res.data.result[1]; // gps信息

  // 处理数据，合并两个信息
  data1.forEach(item => {
    const gps_msg = data2.find(item2 => item2.imei === "0" + item.Tms_car_imei);
    if (gps_msg) {
      const obj = {
        ...item,
        ...gps_msg,
      };
      carList.value.push(obj);
    }
  });
}

const freightData = ref([]);
// 绘制地图车辆
async function drawCar() {
  let styles = {};

  carList.value.forEach(item => {
    // 状态不对默认给个空闲图标
    const png =
      CarStatus[item.Tms_car_status] ||
      "/static/icon/vehiclemonitoring/free_car.png";
    styles[item.imei] = new TMap.MarkerStyle({
      color: "#3876ff",
      width: 40, // 点标记样式宽度（像素）
      height: 35, // 点标记样式高度（像素）
      offset: { x: 0, y: -25 },
      rotate: item.gps_direction_value,
      src: png, //图片路径
      backgroundColor: "rgba(204, 134, 108,0.6)",
      backgroundBorderColor: "rgba(227, 200, 171,1.0)",
      backgroundBorderRadius: 20,
      backgroundBorderWidth: 4,
      padding: "5px 15px",
    });
  });

  // 处理每辆车的运费/公里
  if (freightData.value.length === 0) {
    const carNumberList = carList.value.map(item => item.Tms_car_number);
    const carNumberListStr = carNumberList.join(",");
    const { data } = await getCarFreight(carNumberListStr);
    freightData.value = data.result;
  }

  carList.value.forEach(item => {
    const freight = freightData.value.find(
      it => it.Licence_number === item.Tms_car_number
    );
    item.freight_km = freight?.freight_km;
  });

  const geometries = carList.value.map(item => {
    return {
      id: item.Tms_car_imei,
      content: `${item.Tms_car_number}（${item.freight_km}元/公里）`,
      styleId: item.imei, //指定样式id
      position: new TMap.LatLng(item.lat_tx, item.lng_tx), //点标记坐标位置
    };
  });
  carMarker.setStyles(styles);
  carMarker.setGeometries(geometries);
}

// 绘制分仓点
const fcPointList = ref([]);
async function drawFCMarkers(date) {
  // 获取分仓点数据
  plan_date = dayjs(date);
  const oneDayBefore = plan_date.subtract(1, "day");
  const finallyDate = oneDayBefore.format("YYYY-MM-DD");
  await clearLines();
  map.easeTo({ zoom: 7 });
  const res = await getAllFCs({ plan_date: finallyDate });
  const data =
    res.data.result.filter(
      item => item.order_num > 0 && item.order_num !== null
    ) || [];
  fcPointList.value = data;

  // 获取分仓每筐运费
  const code_list = fcPointList.value
    .map(item => `'${item.LW_number}'`)
    .join(",");
  const { data: costData } = await getSeparateCost(code_list);
  fcPointList.value.forEach(item => {
    const cost = costData.result.find(
      _item => _item.warehouse_code === item.LW_number
    );
    item.per_freight = cost?.per_freight;
  });

  console.log("fcPointList", fcPointList.value);

  //解决分仓重叠问题start
  const dataListA = data.map((item, index) => {
    return {
      ...item,
      index: index,
    };
  });

  // 使用groupbyFields进行分组
  let groupedItems = groupItemsByFields(
    dataListA,
    "LW_latitude",
    "LW_longitude"
  ).filter(item => item.length > 1);
  groupedItems.forEach(itemF => {
    itemF.forEach((item, index) => {
      if (index !== 0) {
        dataListA[item.index].LW_longitude =
          index * 0.001 + Number(item.LW_longitude);
      }
    });
  });
  //解决分仓重叠问题end
  const geometries = dataListA.map(item => {
    return {
      id: item.LW_number,
      styleId: "FC",
      position: new TMap.LatLng(item.LW_latitude, item.LW_longitude),
      content: `${item.LW_name_short ? item.LW_name_short : item.LW_name}(${
        item.order_num ? item.order_num : 0
      }|${item.per_freight}元/筐)`,
    };
  });
  FC_markers.setGeometries(geometries);
}

function groupItemsByFields(items, ...fields) {
  const groups = items.reduce((acc, item) => {
    const key = fields.map(field => item[field]).join("-");
    if (!acc[key]) {
      acc[key] = [];
    }
    acc[key].push(item);
    return acc;
  }, {});

  // 将分组对象转换为二维数组
  return Object.values(groups);
}

// 改变选择类型
async function selectChange(value) {
  await clearLines();
  if (value === "全部") {
    await getCarList();
    await drawCar();
  } else {
    await getCarList();
    const filterData = carList.value.filter(
      item => item.Tms_car_status === value
    );
    carList.value = filterData;
    await drawCar();
  }
}

const currentClickCar = ref(null);

async function drawInfoWindow(imei) {
  // 找出点击车辆信息
  const carInfo = carList.value.find(item => item.Tms_car_imei === imei);
  currentClickCar.value = carInfo;
  const center = new TMap.LatLng(carInfo.lat_tx, carInfo.lng_tx); //设置中心点坐标
  let content;

  content = `
    <div style="display: flex; flex-direction: column; gap: 5px; z-index: 2; align-items: start;">
      <span>车辆编号：${carInfo.Tms_car_number}</span>
      <span>司机名称：${carInfo.Small_boss_car_carrier_driver}</span>
      <span id="see-btn" style="color: blue; cursor: pointer;">查看运输路线</span>
      <span id="car-btn" style="color: blue; cursor: pointer;">查看车辆驾驶信息</span>
    </div>
  `;

  // if (["空闲"].includes(carInfo.Tms_car_status)) {
  //   content = `
  //   <div style="display: flex; flex-direction: column; gap: 5px; z-index: 2; align-items: start;">
  //     <span>车辆编号：${carInfo.Tms_car_number}</span>
  //     <span>司机名称：${carInfo.Small_boss_car_carrier_driver}</span>
  //     <span id="car-btn" style="color: blue; cursor: pointer;">查看车辆驾驶信息</span>
  //   </div>
  // `;
  // } else {
  //   content = `
  //   <div style="display: flex; flex-direction: column; gap: 5px; z-index: 2; align-items: start;">
  //     <span>车辆编号：${carInfo.Tms_car_number}</span>
  //     <span>司机名称：${carInfo.Small_boss_car_carrier_driver}</span>
  //     <span id="see-btn" style="color: blue; cursor: pointer;">查看运输路线</span>
  //     <span id="car-btn" style="color: blue; cursor: pointer;">查看车辆驾驶信息</span>
  //   </div>
  // `;
  // }

  showInfoWindows(center, content);
}

const branchCode = ref("");
async function drawFCInfoWindow(fc_code) {
  branchCode.value = fc_code;
  fcinfo_show.value = true;
}

async function showInfoWindows(center, content) {
  await clearLines();
  InfoWindow.close();
  InfoWindow.setPosition(center);
  InfoWindow.setContent(content);
  InfoWindow.open();
  const btn = document.querySelector("#see-btn");
  btn.addEventListener("click", async () => {
    showSpin.value = true;
    await seeLine(currentClickCar.value);
    showSpin.value = false;
  });

  const btn1 = document.querySelector("#car-btn");
  btn1.addEventListener("click", async () => {
    list_show.value = true;
  });
}

async function seeLine(carInfo) {
  try {
    const res = await getCarPlan({ car_number: carInfo.Tms_car_number });
    const data = res.data.result || [];
    if (data[0].length === 0 || data[0][0].Out_time === null) {
      message.info("暂无运输记录");
      return;
    }

    let nowDate = dayjs().format("YYYY-MM-DD");
    let dataParams = {};
    if (nowDate === dayjs(plan_date).format("YYYY-MM-DD")) {
      dataParams = {
        imei: "0" + carInfo.Tms_car_imei,
        start_time: data[0][0].Out_time,
        end_time: data[0][0].Back_time,
      };
    } else {
      dataParams = {
        imei: "0" + carInfo.Tms_car_imei,
        start_time: `${dayjs(plan_date).format("YYYY-MM-DD")} 00:00:00`,
        end_time: `${dayjs(plan_date).format("YYYY-MM-DD")} 23:59:59`,
      };
    }
    console.log(dataParams);

    if (data[0][0].Back_time === null) {
      dataParams.end_time = dayjs().format("YYYY-MM-DD HH:mm:ss");
    }
    const linesPoints = await getCarTracks(dataParams);
    // 处理线数据
    // 车辆实际路线
    const paths = linesPoints.data.result.map(item => {
      return new TMap.LatLng(item.lat_tx, item.lng_tx);
    });

    if (nowDate == dayjs(plan_date).format("YYYY-MM-DD")) {
      // 分仓起点到终点虚线
      const paths1 = [
        new TMap.LatLng(data[0][0].origin_lat, data[0][0].origin_long),
        new TMap.LatLng(
          data[1][data[1].length - 1].LW_latitude,
          data[1][data[1].length - 1].LW_longitude
        ),
      ];
      const geometries = [
        {
          id: "style_blue_s", //图层唯一标识
          styleId: "style_blue_s", //指定样式id
          paths: paths, //折线路径
        },
        {
          id: "style_blue_x", //图层唯一标识
          styleId: "style_blue_x", //指定样式id
          paths: paths1, //折线路径
        },
      ];
      carLine.setGeometries(geometries);
      // 添加起点和终点
      let SE_POINTS = [
        {
          id: "start",
          styleId: "start",
          position: new TMap.LatLng(
            data[0][0].origin_lat,
            data[0][0].origin_long
          ),
        },
      ];

      // 处理其他途经点
      console.log("data[1]", data[1]);
      const way_arr = data[1].map(item => {
        return {
          id: item.Branch_code,
          styleId: "fc",
          position: new TMap.LatLng(item.LW_latitude, item.LW_longitude),
          content: "(" + item.Transport_sequence + ")" + item.LW_name,
        };
      });

      SE_POINTS = SE_POINTS.concat(way_arr);
      S_E_markers.setGeometries(SE_POINTS);
    } else {
      const geometries = [
        {
          id: "style_blue_s", //图层唯一标识
          styleId: "style_blue_s", //指定样式id
          paths: paths, //折线路径
        },
      ];
      carLine.setGeometries(geometries);
    }
  } catch (err) {
    console.log(err);

    message.error(err.message);
  }
}

// 设置图层是否可见
async function setLayerVisible(type, checked) {
  if (type === "car") {
    await clearLines();
    carMarker.setVisible(checked);
    showLegend.value = checked;
  }
  if (type === "fc") {
    FC_markers.setVisible(checked);
  }
}

// 清除轨迹数据
async function clearLines() {
  InfoWindow.close();
  S_E_markers.setGeometries([]);
  carLine.setGeometries([]);
}

const timer = setInterval(async () => {
  await getCarList();
  drawCar();
}, 60000);
</script>

<style lang="scss">
.map-container {
  height: calc(100vh - 100rpx);
  width: 100vw;
  z-index: 1;
}
.spin {
  position: absolute;
  height: calc(100vh - 60px);
  width: 100vw;
  top: 0;
  left: 0;
  background-color: rgba(255, 255, 255, 0.5);
  z-index: 5;
  display: flex;
  align-items: center;
  justify-content: center;
}
.legend-list {
  position: absolute;
  top: 400rpx;
  right: 20px;
  z-index: 2;
  img {
    width: 40px;
  }
}
.select {
  position: absolute;
  display: flex;
  align-items: center;
  top: 20px;
  left: 20px;
  z-index: 2;
}
.info-content {
  width: 180px;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  gap: 5px;
  align-items: start;
  z-index: 2;
}
</style>
