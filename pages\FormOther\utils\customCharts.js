/**
 *
 * @param {*x轴配置项} xAxis
 * @param {*y轴配置项} yAxis
 * @param {*所用数据} series
 * @param {*图表标题} title
 * @param {*图例}legend
 */

// 柱状图配置
export function setBarOptions(title, xAxis, yAxis, series, dataZoom) {
  let option = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        // Use axis to trigger tooltip
        type: "shadow", // 'shadow' as default; can also be 'line' or 'shadow'
      },
    },
    legend: {
      top: "10%",
    },
    grid: {
      left: "0%",
      right: "4%",
      bottom: "0%",
      containLabel: true,
      height: "75%",
    },
    xAxis: {
      ...xAxis,
    },
    yAxis: {
      ...yAxis,
      axisLabel: {
        interval: 0,
        rotate: -20,
        formatter: function (value) {
          var ret = ""; //拼接加\n返回的类目项
          var maxLength = 7; //每项显示文字个数
          var valLength = value.length; //X轴类目项的文字个数
          var rowN = Math.ceil(valLength / maxLength); //类目项需要换行的行数
          if (rowN > 1) {
            //如果类目项的文字大于5,
            for (var i = 0; i < rowN; i++) {
              var temp = ""; //每次截取的字符串
              var start = i * maxLength; //开始截取的位置
              var end = start + maxLength; //结束截取的位置
              temp = value.substring(start, end) + "\n";
              ret += temp; //凭借最终的字符串
            }
            return ret;
          } else {
            return value;
          }
        },
      },
    },
    dataZoom: {
      type: "slider",
      // xAxisIndex: 0, // 控制x轴
      // yAxisIndex: 0, // 控制y轴
      ...dataZoom,
      show: true,
      start: 0,
      end: 100,
      realtime: true,
    },
    series: [...series],
    title: {
      text: `${title}`,
    },
  };

  return option;
}

// 折线图配置
export function setLineOptions(title, xAxis, yAxis, series, legend) {
  let option = {
    title: {
      ...title,
    },
    tooltip: {
      trigger: "axis",
    },
    legend: {
      ...legend,
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
    },
    toolbox: {
      feature: {
        saveAsImage: {},
      },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      ...xAxis,
    },
    yAxis: {
      type: "value",
      ...yAxis,
    },
    series: [...series],
  };

  return option;
}

// 饼状图配置
export function setPieOptions(title, series) {
  let option = {
    title: {
      text: `${title}`,
      left: "center",
    },
    tooltip: {
      trigger: "item",
    },
    legend: {
      orient: "vertical",
      left: "left",
    },
    series: [...series],
  };

  return option;
}
