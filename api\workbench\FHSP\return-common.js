import Big from 'big.js'

// 使用Big.js计算金额
export function fnBigCalculation(type, num1 = 0, num2 = 0) {
  let result = 0
  let _num1 = new Big(num1)
  let _num2 = new Big(num2)
  switch (type) {
    case '+':
      result = _num1.plus(_num2)
      break
    case '-':
      result = _num1.minus(_num2)
      break
    case '*':
      result = _num1.times(_num2)
      break
    case '/':
      result = _num1.div(_num2)
      break
    default:
      result = _num1
      break
  }
  // 使用Big 的toNumber方法将Big对象转换为数字,并向下取整，保留两位小数
  return result.round(2, 0).toNumber()
}
// 用于显示金额保留两位小数
export function fnFloorToFixed2(num) {
  return num?.toFixed(2)
}

// // 用于存储数字，最多保留两位小数，向下取整
// export function fnFloorTwoPoint(num) {
//   let _num = fnBigCalculation('*', num, 100)
//   return Math.floor(num * 100) / 100
// }
