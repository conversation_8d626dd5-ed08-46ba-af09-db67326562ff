import request from "../index";

//获取区域内无负责人的门店信息

export function getStoreNotPerson(PCR) {
  return request({
    method: "get",
    url: `/bi/getStoreNotPerson`,
    data: {
      PCR,
    },
  });
}

//经销商获取自己下属用户
export function getSubordinateUser(jxq_id) {
  return request({
    method: "get",
    url: `/bi/getSubordinateUser`,
    data: {
      jxq_id,
    },
  });
}

// 创建市场规划记录
export function createPlanRecord(data) {
  return request({
    method: "post",
    url: `/bi/createPlanRecord`,
    data,
  });
}

// 获取正在执行规划列表
export function getPlanList(data) {
  return request({
    method: "get",
    url: `/bi/getPlanList`,
    data,
  });
}

// 获取超期规划列表
export function getPlanListPast(data) {
  return request({
    method: "get",
    url: `/bi/getPlanListPast`,
    data,
  });
}

// 获取规划区域的门店信息
export function getPlanStoreList(plan_id) {
  return request({
    method: "get",
    url: `/bi/getInPlanStores`,
    data: {
      plan_id,
    },
  });
}

// 删除规划列表
export function deletedPlanArea(plan_id) {
  return request({
    method: "get",
    url: `/bi/deletedPlanArea`,
    data: {
      plan_id,
    },
  });
}

// 获取所有的绑定经销区的区域
export function getNationalAreas() {
  return request({
    method: "get",
    url: "/national/getAllAreas",
  });
}

// 获取经销区的经销商
export function getThisAreaPersons(jxqId) {
  return request({
    method: "get",
    url: "/bi/getThisAreaPersons",
    data: {
      jxqId,
    },
  });
}

// 获取时间范围内规划区域

export function getIndatePlanMessage(data) {
  return request({
    method: "get",
    url: "/bi/getIndatePlanMessage",
    data,
  });
}

// 获取当前门店最新的预谈信息
export function getStoreLastPreTalk(storeId) {
  return request({
    method: "get",
    url: "/bi/getStorePrenegotiation",
    data: {
      storeId,
    },
  });
}

// 获取用户对应的crm中的id
export function getUserCrmId(areaId, reportUserId) {
  return request({
    method: "get",
    url: "/bi/getUserIdFromCrm",
    data: {
      areaId,
      reportUserId,
    },
  });
}

// 获取经销区内的所有的街道信息
export function getAreaStreets(areaCode) {
  return request({
    method: "get",
    url: "/bi/getAreaStreets",
    data: {
      areaCode,
    },
  });
}
