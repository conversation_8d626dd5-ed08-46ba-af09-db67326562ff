<template>
  <div class="table-container">
    <!-- 筛选条件区域 -->
    <div class="filter-container">

      <el-date-picker
          v-model="filterDate"
          type="date"
          placeholder="选择日期"
          @change="handleDateFilter">
      </el-date-picker>
      <el-input
          v-model="dq"
          placeholder="请输入大区"
          @click="dqclick('dq')"
          style="flex: 2;width: 200px;margin-left: 20px;">
      </el-input>
<!--      <el-button-->
<!--          type="primary"-->
<!--          @click="dqclick">-->
<!--        大区筛选-->
<!--      </el-button>-->
      <el-input
          v-model="sq"
          placeholder="请输入省区"
          @click="dqclick('sq')"
          style="flex: 2;width: 200px;margin-left: 20px;">
      </el-input>
<!--      <el-button-->
<!--          type="primary"-->
<!--          @click="showRegionDialog = true">-->
<!--        省区筛选-->
<!--      </el-button>-->
      <el-input
        v-model="jxq"
        placeholder="请输入经销库房"
        @click="dqclick('jxq')"
        style="flex: 2;width: 200px;margin-left: 20px;">
       </el-input>
<!--      <el-button-->
<!--          type="primary"-->
<!--          @click="showRegionDialog = true">-->
<!--        经销库房筛选-->
<!--      </el-button>-->


      <el-button
          type="info"
          @click="resetFilters">
        重置筛选
      </el-button>
    </div>

    <!-- 大区筛选弹出框 -->
    <el-dialog
        title="选择大区"
        :visible.sync="showRegionDialog"
        width="30%"
        append-to-body>
      <el-checkbox-group v-model="selectedRegions">
        <el-checkbox
            v-for="region in regions"
            :key="region.value"
            :label="region.value">
          {{ region.label }}
        </el-checkbox>
      </el-checkbox-group>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showRegionDialog = false">取消</el-button>
        <el-button type="primary" @click="handleRegionFilter">确定</el-button>
      </span>
    </el-dialog>

    <!-- 表格区域 -->
    <el-table
        :data="filteredTableData"
        style="width: 100%"
        border
        stripe
        @row-click="handleRowClick">

      <el-table-column
          prop="Region"
          label="大区"
          width="120">
      </el-table-column>

      <el-table-column
          prop="Province"
          label="省区"
          width="120">
      </el-table-column>

      <el-table-column
          prop="DistributionOrganizationName"
          label="经销库房"
          width="150">
      </el-table-column>

      <el-table-column
          prop="datetime"
          label="日期"
          width="180"
          sortable>
      </el-table-column>

      <el-table-column
          prop="cl_num"
          label="车辆数 (辆)"
          width="120"
          align="right">
      </el-table-column>

      <el-table-column
          prop="target"
          label="目标"
          width="120"
          align="right">
      </el-table-column>

      <el-table-column
          prop="target_month"
          label="当月目标"
          width="120"
          align="right">
      </el-table-column>

      <el-table-column
          prop="target_rate"
          label="管理目标达成率"
          width="150"
          align="right">
      </el-table-column>

      <el-table-column
          prop="last_year_target"
          label="去年同期目标"
          width="150"
          align="right">
      </el-table-column>

      <el-table-column
          prop="last_year_target_month"
          label="去年上月累计 (辆)"
          width="150"
          align="right">
      </el-table-column>

      <el-table-column
          prop="last_year_growth_rate"
          label="较去年同期成长率"
          width="180"
          align="right">
      </el-table-column>

      <el-table-column
          prop="previous_month_rate"
          label="较上月环比成长率"
          width="180"
          align="right">
      </el-table-column>

      <el-table-column
          prop="Deviation_standard_progress"
          label="较标准进度差异"
          width="180"
          align="right">
      </el-table-column>

      <el-table-column
          prop="Actual_Goal"
          label="实际-目标 (数字量化)"
          width="180"
          align="right">
      </el-table-column>

      <el-table-column
          prop="bicycle_sales_per"
          label="单车均销 (辆)"
          width="120"
          align="right">
      </el-table-column>

      <el-table-column
          prop="ranking"
          label="管理目标达成排名 (大区内)"
          width="200"
          align="right">
      </el-table-column>

      <el-table-column
          prop="placed_month"
          label="本月应下单批次数"
          width="180"
          align="right">
      </el-table-column>

      <el-table-column
          prop="placed_now"
          label="累计目前应下单批次"
          width="200"
          align="right">
      </el-table-column>

      <el-table-column
          prop="placed_batches"
          label="实际下单批次数"
          width="150"
          align="right">
      </el-table-column>

      <el-table-column
          prop="Absenteeism_Batch"
          label="缺勤批次 (辆)"
          width="120"
          align="right">
      </el-table-column>

      <el-table-column
          prop="Regular_amount"
          label="常规品到货金额"
          width="150"
          align="right">
      </el-table-column>

      <el-table-column
          prop="Regular_amount_rate"
          label="常规品到货金占比"
          width="180"
          align="right">
      </el-table-column>

      <el-table-column
          prop="Time_amount"
          label="档期品到货金额"
          width="150"
          align="right">
      </el-table-column>

      <el-table-column
          prop="Time_amount_rate"
          label="档期品到货占比"
          width="150"
          align="right">
      </el-table-column>

      <el-table-column
          prop="Return_rate_kf"
          label="库房业务返货率 (辆)"
          width="180"
          align="right">
      </el-table-column>

      <el-table-column
          prop="Return_rate_cw"
          label="财务返货率"
          width="120"
          align="right">
      </el-table-column>

      <el-table-column
          prop="Return_money_kf"
          label="本月业务返货金额"
          width="180"
          align="right">
      </el-table-column>

      <el-table-column
          prop="Return_money_cw"
          label="财务上账金额"
          width="150"
          align="right">
      </el-table-column>

      <el-table-column
          prop="no_return_money"
          label="未上账金额 (辆)"
          width="150"
          align="right">
      </el-table-column>
    </el-table>
  </div>
  <Modal
      v-if="showModal"
      :show="showModal"
      title="请选择"
      @close="showModal = false"
      @confirm="handleConfirm"
  >
    <MultiSelectListOne
        :items="allItems"
        :items2="allItems2"
        @update:selected-items="update($event)"
        @update:close="close($event)"
        @update:confirm="confirm($event)"
        v-model:selected-items="tempSelectedItems"
        :items-per-page="pageSize"
        :current-page="currentPage"
        :total-pages="totalPages"
        :single-select="true"
    />
  </Modal>
</template>

<script>
import { GetAllDqNew,GetAllSqNew } from "/api/Map/index.js";
import { get_rzkf, } from "/api/workbench/trainmastersigning/index.js";
import { get_jxyl } from "../../../api/workbench/JXYL/index.js";
import Modal from "@/pages/workbench/mod/Modal.vue";
import MultiSelectListOne from "@/pages/workbench/mod/MultiSelectListOne.vue";
import {ref} from "vue";
import dayjs from "dayjs";
export default {
  components: {
    Modal,
    MultiSelectListOne
  },
  data() {
    const today = new Date();
    const formattedDate = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;

    return {
      tableData: [], // 初始为空数组，将在created钩子中填充数据
      filterDate: formattedDate, // 单日期筛选值
      dq: null, // 大区
      sq: null, // 省区
      jxq: null, // 经销区
      dqid: '', // 大区
      sqid: '', // 省区
      jxqid: '', // 经销区
      selectedRegions: [], // 选中大区
      allItems: [],
      allItems2: [],
      pageType: 1,
      currentPage: 1,
      pageSize: 999,
      totalPages: 1,
      showModal: false,
      showRegionDialog: false, // 是否显示大区筛选弹窗
      regions: [ // 大区选项
        { label: '华东', value: '华东' },
        { label: '华北', value: '华北' },
        { label: '华南', value: '华南' },
        { label: '西南', value: '西南' },
        { label: '西北', value: '西北' },
        { label: '东北', value: '东北' }
      ],
      activeFilters: { // 当前生效的筛选条件
        date: null,
        regions: []
      },
      tempSelectedItems: [],
      loading: false // 新增加载状态
    }
  },

  // 新增的生命周期钩子
  created() {
    this.fetchJxylData();
  },

  computed: {
    // 筛选后的表格数据
    filteredTableData() {
      return this.tableData;
    }
  },

  methods: {
    // 新增的获取数据方法
    async fetchJxylData() {
      try {
        this.loading = true;
        const response = await get_jxyl(this.filterDate,this.dqid,this.sqid,this.jxqid);
        // 假设API返回的数据结构是{ data: [...] }
        this.tableData = response.data.result
      } catch (error) {
        console.error('获取经销一览数据失败:', error);
        // 可以根据需要添加错误处理，如显示提示消息
        this.$message.error('获取数据失败，请稍后重试');
      } finally {
        this.loading = false;
      }
    },

    // 日期筛选
    async handleDateFilter(val) {
      // console.log(val)
      // console.log(this.filterDate)
      const year = val.getFullYear();
      const month = String(val.getMonth() + 1).padStart(2, '0');
      const day = String(val.getDate()).padStart(2, '0');
      const formattedDate = `${year}-${month}-${day}`;

      this.activeFilters.date = formattedDate;
      this.filterDate= formattedDate;
      try {
        this.loading = true;
        const response = await get_jxyl(this.filterDate,this.dqid,this.sqid,this.jxqid);
        // 假设API返回的数据结构是{ data: [...] }
        console.log('response.data.result',response.data.result)
        this.tableData = response.data.result
      } catch (error) {
        console.error('获取经销一览数据失败:', error);
        // 可以根据需要添加错误处理，如显示提示消息
        this.$message.error('获取数据失败，请稍后重试');
      } finally {
        this.loading = false;
      }

    },

    // 大区筛选
    handleRegionFilter() {
      this.activeFilters.regions = [...this.selectedRegions];
      this.showModal = false;
    },
    async dqclick(value){
      console.log('this.showRegionDialog',this.showRegionDialog)
      this.pageType = value
      if(value == 'dq'){
        let res = await GetAllDqNew();
        this.allItems = [];
        for (var i = 0; i < res.data.result.length; i++) {
          this.allItems.push({
            value: res.data.result[i].id,
            label: res.data.result[i].DistributionOrganizationName,
            label2: res.data.result[i].DistributionOrganizationName2,
            label3: res.data.result[i].DistributionOrganizationName3,
            radio: 0,
          });
        }
        this.allItems2 = [];
        this.allItems2.push({
          value: 1,
          label: "大区名称",


          radio: 0,
        });
        console.log("ssss");

      }else if(value == 'sq'){
        let res = await GetAllSqNew();
        this.allItems = [];
        for (var i = 0; i < res.data.result.length; i++) {
          this.allItems.push({
            value: res.data.result[i].id,
            label: res.data.result[i].DistributionOrganizationName,
            label2: res.data.result[i].DistributionOrganizationName2,
            label3: res.data.result[i].DistributionOrganizationName3,
            radio: 0,
          });
        }
        this.allItems2 = [];
        this.allItems2.push({
          value: 1,
          label: "省区名称",
          radio: 0,
        });
        console.log("ssss");
      }else if(value == 'jxq'){
        let res = await get_rzkf();
        this.allItems = [];
        for (var i = 0; i < res.data.result.length; i++) {
          this.allItems.push({
            value: res.data.result[i].id,
            label: res.data.result[i].DistributionOrganizationName,
            label2: res.data.result[i].DistributionOrganizationName2,
            label3: res.data.result[i].DistributionOrganizationName3,
            radio: 0,
          });
        }
        this.allItems2 = [];
        this.allItems2.push({
          value: 1,
          label: "名称",
          label2: "所属省区",
          label3: "所属大区",
          radio: 0,
        });
        console.log("ssss");
      }


      this.showModal = false;
      this.$nextTick(() => {
        this.showModal = true;
      });
    },
    // 重置筛选
    resetFilters() {
      this.filterDate = null;
      this.dq = null;
      this.sq = null;
      this.jxq = null;

      this.selectedRegions = [];
      this.activeFilters = {
        date: null,
        regions: []
      };
    },

    handleEdit(index, row) {
      console.log(index, row);
      // 这里可以添加编辑逻辑
    },

    handleDelete(index, row) {
      console.log(index, row);
      // 这里可以添加删除逻辑
    },

    handleRowClick(row, column, event) {
      console.log('行点击事件', row);
    },
    handleConfirm(row) {
      console.log('行点击事件', row);
    },
    confirm(item) {
      console.log('行点击事件', item);

      if(this.pageType == 'dq'){
        this.dq = item.label
        this.dqid = item.value
      }else if(this.pageType == 'sq'){
        this.sq = item.label
        this.sqid = item.value
      }else if(this.pageType == 'jxq'){
        this.jxq = item.label
        this.jxqid = item.value
      }
      this.showModal = false;
    },
    //
    // const confirm = (item) => {
    //   console.log("item", item);
    //   formData[info.value] = item.label;
    //   if (info.value == "rzkf") {
    //     rzkfid.value = item.value;
    //     formData["sssq"] = item.label2;
    //     formData["ssdq"] = item.label3;
    //   } else if (info.value == "wlfcmc") {
    //     // 存储物流分仓ID，以便后续使用
    //     formData["wlfcId"] = item.value;
    //     formData["wlfcbm"] = `WLFC${dayjs().format("YYYYMMDDHHmmss")}`;
    //   }
    //
    //   showModal.value = false;
    // };
  }
}
</script>

<style scoped>
.table-container {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.filter-container > * {
  margin-right: 10px;
}
</style>