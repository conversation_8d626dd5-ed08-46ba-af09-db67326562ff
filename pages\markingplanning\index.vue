<template>
  <a-config-provider :locale="zhCN">
    <div style="position: relative">
      <div class="map-container" id="map"></div>
      <div class="edit-form">
        <div
          style="
            display: grid;
            grid-template-columns: repeat(2, minmax(0, 1fr));
          "
        >
          <div class="map-type">
            <span>卫星地图</span>
            <a-switch
              v-model:checked="openSatellite"
              @change="changeSatellite"
            />
          </div>
          <div class="map-type" v-if="activeKey === '1'">
            <span>浏览模式</span>
            <a-switch v-model:checked="openView" @change="changeView" />
          </div>
          <div class="map-type" v-if="activeKey === '1'">
            <span>门店规划</span>
            <a-switch
              v-model:checked="storePlanOpen"
              @change="changeClickStore"
            />
          </div>
          <div class="map-type" v-if="activeKey === '1'">
            <span>显示街镇</span>
            <a-switch v-model:checked="openStreet" @change="changeOpenStreet" />
          </div>
        </div>
        <a-tabs v-model:activeKey="activeKey" type="card" @change="changeTabs">
          <a-tab-pane key="1" tab="新建规划">
            <AddProject
              :messageMap="messageMap"
              @drawArea="drawArea"
              @drawStreet="drawStreet"
              @drawStore="drawStore"
              @resetPlan="resetPlan"
              @drawHistoryArea="drawHistoryArea"
            />
            <div class="click-store">
              <div
                class="area-message"
                v-for="item in messageMap.clickStoreList"
              >
                <div>
                  <span style="font-weight: bold">{{ item.Store_name }} </span>
                  <span>
                    {{
                      item.Master_data_person_name_crm_really ?? "[无负责人]"
                    }}
                  </span>
                </div>
                <span
                  style="color: red; cursor: pointer"
                  @click="removeThisStore(item)"
                >
                  移出
                </span>
              </div>
            </div>

            <div class="detail-message" v-if="showDetail">
              <div class="list">
                <span>门店总数</span>
                <span>{{ detailMessage.storeNum }}</span>
              </div>
              <div class="list">
                <span>已开门店</span>
                <span>{{ detailMessage.openStoreNum }}</span>
              </div>
              <div class="list">
                <span>未开门店</span>
                <span>{{ detailMessage.closeStoreNum }}</span>
              </div>

              <div class="area-message">
                <span style="font-weight: bold">范围面积：</span>
                <span>{{ detailMessage.acreage + "㎡" }}</span>
              </div>
              <div class="user-store-list">
                <div
                  class="item-list"
                  v-for="(key, value) in detailMessage.userStoreList"
                >
                  <span>{{ value + "：" }}</span>
                  {{ key + "门店" }}
                </div>
              </div>
            </div>
          </a-tab-pane>
          <a-tab-pane key="2" tab="规划列表">
            <ProjectList
              v-if="activeKey === '2'"
              @drawPlanArea="drawPlanArea"
              @changeViewStore="changeViewStore"
              @deletedPlan="deletedPlan"
            />
          </a-tab-pane>
        </a-tabs>
      </div>
      <div class="map-legend" v-if="activeKey === '1'">
        <div>
          <img src="../../static/icon/map/store77.png" alt="" />
          已拜访
        </div>
        <div>
          <img src="../../static/icon/map/store44.png" alt="" />
          已开门店
        </div>
        <div>
          <img src="../../static/icon/map/store11.png" alt="" />
          未开门店
        </div>
        <div>
          <img src="../../static/icon/map/store55.png" alt="" />
          有意向有柜体
        </div>
        <div>
          <img src="../../static/icon/map/store33.png" alt="" />
          无意向
        </div>
        <div>
          <img src="../../static/icon/map/store22.png" alt="" />
          超期未谈
        </div>
      </div>
      <div class="map-legend" v-if="activeKey === '2'">
        <div>
          <img src="../../static/icon/map/store44.png" alt="" />
          已开门店
        </div>
        <div>
          <img src="../../static/icon/map/store11.png" alt="" />
          未开门店
        </div>
      </div>
    </div>
  </a-config-provider>
  <a-modal
    v-model:open="modalShow"
    title="详细信息"
    width="800px"
    :footer="null"
  >
    <StoreMessageModal :curStore="curStoreMessage" />
  </a-modal>
</template>

<script setup>
import { nextTick, reactive, ref } from "vue";
import AddProject from "./AddProject.vue";
import ProjectList from "./ProjectList.vue";
import StoreMessageModal from "./StoreMessageModal.vue";

import { useUserInfo } from "/store/user/userInfo";
import { useMarking } from "/store/markingplanning/index";

import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
import zhCN from "ant-design-vue/es/locale/zh_CN";
import { getStoreLastPreTalk } from "../../api/workingplanning";
import { message } from "ant-design-vue";
dayjs.locale("zh-cn");

const userStore = useUserInfo();
const useMarkingStore = useMarking();
//传给子组的信息
const messageMap = reactive({
  editAreaPoints: [], //编辑区域的点坐标
  inEditAreaStore: [], //在片区内的门店id列表
  clickStoreList: [],
});
const openSatellite = ref(false);
const openView = ref(false);
const storePlanOpen = ref(false);
const openStreet = ref(false);

const showDetail = ref(false);
const detailMessage = reactive({
  storeNum: 0,
  openStoreNum: 0,
  closeStoreNum: 0,
  acreage: 0,
  userStoreList: {},
});

const modalShow = ref(false);
const curStoreMessage = ref();

let map;
let mapCenter;
let storeLayer; //门店图层
let editLayer; //规划图层
let editViewLayer; //时间范围内规划历史图层
let editLabelLayer; //时间范围内规划历史文本图层
let areaLayer; //区域图层
let areaEditLayer; //编辑区域图层
let streetLayer; //街道图层
let infoWindow;

let storeHistoryLayer; //列表门店图层
let editHistoryLayer; //列表规划图层
let areaHistoryLayer; //列表区域图层
function initMap() {
  mapCenter = new TMap.LatLng(39.984104, 116.307503);
  map = new TMap.Map("map", {
    center: mapCenter,
    zoom: 12,
  });
  streetLayer = new TMap.MultiMarker({
    id: "street-layer",
    map,
    styles: {
      label: new TMap.MarkerStyle({
        width: 16, // 宽度
        height: 16, // 高度
        anchor: { x: 17, y: 23 }, // 标注点图片的锚点位置
        src: "/static/icon/map/redStar.png", // 标注点图片url或base64地址
        color: "#fb410e",
        size: 20, // 标注点文本文字大小
        direction: "bottom", // 标注点文本文字相对于标注点图片的方位
        offset: { x: 0, y: 8 }, // 标注点文本文字基于direction方位的偏移属性
        strokeColor: "#fff", // 标注点文本描边颜色
        strokeWidth: 2, // 标注点文本描边宽度
      }),
    },
    zIndex: 0,
  });
  streetLayer.setVisible(false); //默认关闭街道数据显示
  areaLayer = new TMap.MultiPolygon({
    id: "area-layer",
    map: map,
    styles: {
      bgColor: new TMap.PolygonStyle({
        color: "rgba(60, 156, 255,0.05)",
        showBorder: true,
        borderColor: "rgba(0,0,0,0.8)",
        borderWidth: 2,
      }),
    },
    zIndex: 1,
  });

  editViewLayer = new TMap.MultiPolygon({
    id: "edit-view-layer",
    map: map,
    zIndex: 2,
  });

  editLabelLayer = new TMap.MultiLabel({
    id: "edit-label-layer",
    map: map,
    collisionOptions: {
      sameSource: true,
    },
    styles: {
      label: new TMap.LabelStyle({
        color: "rgba(0,0,0,0.2)",
        strokeColor: "#FFFFFF",
        size: 28, //文字大小属性
        offset: { x: 0, y: 0 },
        angle: 25,
        alignment: "center",
        verticalAlignment: "middle",
      }),
    },
    zIndex: 3,
  });

  storeLayer = new TMap.MultiMarker({
    id: "store-layer",
    map,
    styles: {
      // 已拜访黄色
      marker1: new TMap.MarkerStyle({
        width: 15, // 样式宽
        height: 15, // 样式高
        src: "../../static/icon/map/store77.png",
      }),
      // 有意向浅绿色
      marker2: new TMap.MarkerStyle({
        width: 15, // 样式宽
        height: 15, // 样式高
        src: "../../static/icon/map/store44.png",
      }),
      // 有意向有柜体深绿色
      marker3: new TMap.MarkerStyle({
        width: 15, // 样式宽
        height: 15, // 样式高
        src: "../../static/icon/map/store55.png",
      }),
      // 无意向红色
      marker4: new TMap.MarkerStyle({
        width: 15, // 样式宽
        height: 15, // 样式高
        src: "../../static/icon/map/store33.png",
      }),
      // 未谈蓝色
      marker5: new TMap.MarkerStyle({
        width: 15, // 样式宽
        height: 15, // 样式高
        src: "../../static/icon/map/store11.png",
      }),
      // 超期未谈深蓝色
      marker6: new TMap.MarkerStyle({
        width: 15, // 样式宽
        height: 15, // 样式高
        src: "../../static/icon/map/store22.png",
      }),
    },
    zIndex: 4,
  });

  storeLayer.on("mouseover", e => {
    if (openView.value || storePlanOpen.value) {
      const curStore = useMarkingStore.storeList.find(
        item => item.id === e.geometry.id
      );
      infoWindow.setPosition(e.latLng);
      infoWindow.setContent(
        `${curStore.Store_name} | ${
          curStore.estimated_sales ? curStore.estimated_sales : 0
        }`
      );
      infoWindow.open();
    }
  });

  storeLayer.on("click", async e => {
    if (openView.value && !storePlanOpen.value) {
      const { data } = await getStoreLastPreTalk(e.geometry.id);
      if (data.result.length === 0) {
        return message.info("该门店没有更进记录");
      }
      curStoreMessage.value = data.result[0];
      modalShow.value = true;
      return;
    }
    if (storePlanOpen.value) {
      console.log("该门店进入列表", e);
      let geometry = e.geometry;
      let curStore = useMarkingStore.storeList.find(
        item => item.id === e.geometry.id
      );
      Object.assign(curStore, e.geometry);
      let arr = messageMap.clickStoreList;
      arr.push(curStore);
      geometry.styleId = "";
      messageMap.clickStoreList = [...new Set(arr)];
      console.log("messageMap.clickStoreList", messageMap.clickStoreList);

      storeLayer.updateGeometries([{ ...geometry }]);
    }
  });

  areaEditLayer = new TMap.MultiPolygon({
    map: map,
    styles: {
      highlight: new TMap.PolygonStyle({
        color: "rgba(255, 255, 0, 0.2)",
      }),
    },
    id: "areaEditLayer",
    zIndex: 5,
  });

  editLayer = new TMap.tools.GeometryEditor({
    map: map,
    overlayList: [
      {
        overlay: areaEditLayer,
        id: "areaEditLayer",
        selectedStyleId: "highlight",
      },
    ],
    actionMode: TMap.tools.constants.EDITOR_ACTION.DRAW, // 编辑器的工作模式
    activeOverlayId: "areaEditLayer", // 激活图层
    snappable: true, // 开启吸附
    selectable: true, //选点功能
  });

  editLayer.on("draw_complete", geometry => {
    disposeEditMapMessage(geometry);
  });
  editLayer.on("adjust_complete", geometry => {
    disposeEditMapMessage(geometry);
  });

  editHistoryLayer = new TMap.MultiPolygon({
    id: "edit-history-layer",
    map: map,
    styles: {
      bgColor: new TMap.PolygonStyle({
        color: "rgba(255, 255, 0,0.2)",
        showBorder: true,
        borderColor: "rgba(0,0,0,0.8)",
        borderWidth: 2,
      }),
    },
  });

  areaHistoryLayer = new TMap.MultiPolygon({
    id: "area-history-layer",
    map: map,
    styles: {
      bgColor: new TMap.PolygonStyle({
        color: "rgba(60, 156, 255,0.05)",
        showBorder: true,
        borderColor: "rgba(0,0,0,0.8)",
        borderWidth: 2,
      }),
    },
    zIndex: 1,
  });

  storeHistoryLayer = new TMap.MultiMarker({
    id: "store-history-layer",
    map,
    styles: {
      // 已开门店
      marker1: new TMap.MarkerStyle({
        width: 15, // 样式宽
        height: 15, // 样式高
        src: "../../static/icon/map/store44.png",
      }),
      // 未开门店
      marker2: new TMap.MarkerStyle({
        width: 15, // 样式宽
        height: 15, // 样式高
        src: "../../static/icon/map/store11.png",
      }),
    },
    zIndex: 4,
  });

  storeHistoryLayer.on("mouseover", e => {
    const curStore = useMarkingStore.inAreaEditStore.find(
      item => item.id === e.geometry.id
    );
    console.log("curStore", curStore);

    infoWindow.setPosition(e.latLng);
    infoWindow.setContent(
      `${curStore.Store_name} | ${
        curStore.estimated_sales ? curStore.estimated_sales : 0
      }`
    );
    infoWindow.open();
  });

  storeHistoryLayer.on("click", async e => {
    curStoreMessage.value = useMarkingStore.inAreaEditStore.find(
      item => item.id === e.geometry.id
    );
    modalShow.value = true;
  });

  infoWindow = new TMap.InfoWindow({
    map: map,
    position: mapCenter,
    content: "",
    offset: { x: 0, y: -10 },
  });
  infoWindow.close();
}

function disposeEditMapMessage(geometry) {
  messageMap.editAreaPoints = geometry.paths;
  editLayer.setActionMode(TMap.tools.constants.EDITOR_ACTION.INTERACT);
  editLayer.select([geometry.id]);

  // 处理在区域内的门店
  messageMap.inEditAreaStore = [];
  storeLayer.geometries.forEach(item => {
    const distance = TMap.geometry.isPointInPolygon(
      item.position,
      geometry.paths,
      true
    );
    if (distance) {
      // 在自定区域内添加进门店id列表
      messageMap.inEditAreaStore.push(item.id);
    }
  });

  // 处理门店数量
  showDetail.value = true;

  detailMessage.acreage = Number(
    TMap.geometry.computeArea(geometry.paths)
  ).toFixed(2);

  detailMessage.storeNum = messageMap.inEditAreaStore.length;
  detailMessage.openStoreNum = useMarkingStore.storeList
    .filter(item => messageMap.inEditAreaStore.includes(item.id))
    .filter(item => item.Master_data_person_name_crm_really).length;
  detailMessage.closeStoreNum = useMarkingStore.storeList
    .filter(item => messageMap.inEditAreaStore.includes(item.id))
    .filter(item => !item.Master_data_person_name_crm_really).length;

  // 过滤出有负责人的门店
  detailMessage.userStoreList = useMarkingStore.storeList
    .filter(item => messageMap.inEditAreaStore.includes(item.id))
    .filter(item => item.Master_data_person_name_crm_really)
    .map(item => item.Master_data_person_name_crm_really)
    .reduce((acc, cur) => {
      acc[cur] = (acc[cur] || 0) + 1;
      return acc;
    }, {});
}

function removeThisStore(store) {
  storeLayer.updateGeometries([
    {
      id: store.id,
      position: store.position,
      rank: store.rank,
      styleId: store.styleId,
    },
  ]);
  messageMap.clickStoreList = messageMap.clickStoreList.filter(
    item => item.id !== store.id
  );
}

const changeSatellite = checked => {
  if (checked) {
    map.setBaseMap({ type: "satellite" });
  }
  if (!checked) {
    map.setBaseMap({ type: "vector" });
  }
};

const changeView = checked => {
  if (checked) {
    editLayer.setActionMode(TMap.tools.constants.EDITOR_ACTION.INTERACT);
    editLayer.delete();
    showDetail.value = false;
  }
  if (!checked) {
    infoWindow.close();
    editLayer.setActionMode(TMap.tools.constants.EDITOR_ACTION.DRAW);
  }
};

const changeClickStore = checked => {
  if (checked) {
    editLayer.setActionMode(TMap.tools.constants.EDITOR_ACTION.INTERACT);
    editLayer.delete();
    showDetail.value = false;
  }
  if (!checked) {
    infoWindow.close();
    let geometries = [];
    messageMap.clickStoreList.forEach(item => {
      geometries.push({
        id: item.id,
        position: item.position,
        rank: item.rank,
        styleId: item.styleId,
      });
    });
    storeLayer.updateGeometries(geometries);
    messageMap.clickStoreList = [];
    if (!openView.value) {
      editLayer.setActionMode(TMap.tools.constants.EDITOR_ACTION.DRAW);
    }
  }
};

const changeOpenStreet = checked => {
  const areaLayerData = areaLayer.getGeometries();

  //  如果未选择经销区，则不打开街道图层
  if (areaLayerData.length === 0) {
    openStreet.value = false;
    message.warn("未选择经销区");
    return;
  }
  if (checked) {
    streetLayer.setVisible(true);
  }
  if (!checked) {
    streetLayer.setVisible(false);
  }
};

function drawStreet(geometries) {
  streetLayer.setGeometries(geometries);
}

const activeKey = ref("1");
function changeTabs(tab) {
  infoWindow.close();
  if (tab === "1") {
    areaLayer.setVisible(true);
    storeLayer.setVisible(true);
    areaEditLayer.setVisible(true);
    editLabelLayer.setVisible(true);
    editViewLayer.setVisible(true);
    editHistoryLayer.setGeometries([]);
    areaHistoryLayer.setGeometries([]);
    storeHistoryLayer.setGeometries([]);
    editHistoryLayer.setVisible(false);
    areaHistoryLayer.setVisible(false);
    storeHistoryLayer.setVisible(false);

    // 默认选中之前画的
    const polygonSelect = areaEditLayer.getGeometries();
    if (polygonSelect.length > 0) {
      editLayer.select([polygonSelect[0].id]);
    }
    if (polygonSelect.length === 0 && !openView.value) {
      editLayer.setActionMode(TMap.tools.constants.EDITOR_ACTION.DRAW);
    }
    map.setCenter(historyCenter);
  }
  if (tab === "2") {
    editLayer.setActionMode(TMap.tools.constants.EDITOR_ACTION.INTERACT);
    areaLayer.setVisible(false);
    storeLayer.setVisible(false);
    areaEditLayer.setVisible(false);
    editViewLayer.setVisible(false);
    editLabelLayer.setVisible(false);
    editHistoryLayer.setVisible(true);
    areaHistoryLayer.setVisible(true);
    storeHistoryLayer.setVisible(true);
    editLayer.stop();
  }
}

let historyCenter; //保留中心的，切换tab回来使用
// 画出地图选择区域
function drawArea(geometries, center) {
  areaLayer.setGeometries(geometries);
  if (openView.value || storePlanOpen.value) {
    editLayer.setActionMode(TMap.tools.constants.EDITOR_ACTION.INTERACT);
  } else {
    editLayer.setActionMode(TMap.tools.constants.EDITOR_ACTION.DRAW);
  }

  map.setCenter(center);
  historyCenter = center;
  map.setZoom(12);
}

// 画出规划区域
function drawPlanArea(geometries, geometries1, geometriesStore, center) {
  editHistoryLayer.setGeometries(geometries);
  areaHistoryLayer.setGeometries(geometries1);
  storeHistoryLayer.setGeometries(geometriesStore);
  map.setCenter(center);
  map.setZoom(14);
}

function changeViewStore(geometriesStore) {
  storeHistoryLayer.setGeometries(geometriesStore);
}

// 画出当前区域的时间内历史所有规划
function drawHistoryArea(geometries, styles, centers) {
  editViewLayer.setStyles(styles);
  editViewLayer.setGeometries(geometries);
  editLabelLayer.setGeometries(centers);
}

// 获取选择区域的门店
function drawStore(geometries, type) {
  if (type === "clear") {
    storeLayer.setGeometries([]);
  }
  if (type === "add") {
    storeLayer.add(geometries);
  }
}

// 删除历史规划
function deletedPlan() {
  editHistoryLayer.setGeometries([]);
  storeHistoryLayer.setGeometries([]);
}

// 重置逻辑
function resetPlan(type) {
  showDetail.value = false;
  infoWindow.close();
  if (storePlanOpen.value) {
    let geometries = [];
    messageMap.clickStoreList.forEach(item => {
      geometries.push({
        id: item.id,
        position: item.position,
        rank: item.rank,
        styleId: item.styleId,
      });
    });
    storeLayer.updateGeometries(geometries);
    messageMap.clickStoreList = [];
  }
  if (type === "all") {
    editViewLayer.setGeometries([]);
    editLabelLayer.setGeometries([]);
    areaEditLayer.setGeometries([]);
    editLayer.delete();
    if (userStore.userInfo.Role_grade === "决策层") {
      storeLayer.setGeometries([]);
      areaLayer.setGeometries([]);
      editLayer.setActionMode(TMap.tools.constants.EDITOR_ACTION.INTERACT);
    }
    if (userStore.userInfo.Role_grade === "经销商") {
      if (!storePlanOpen.value && !openView.value) {
        editLayer.setActionMode(TMap.tools.constants.EDITOR_ACTION.DRAW);
      }
    }
  }
  if (type === "plan") {
    editLayer.delete();
    if (!storePlanOpen.value && !openView.value) {
      editLayer.setActionMode(TMap.tools.constants.EDITOR_ACTION.DRAW);
    }
  }
}

nextTick(() => {
  initMap();
  if (userStore.userInfo.Role_grade === "决策层") {
    editLayer.setActionMode(TMap.tools.constants.EDITOR_ACTION.INTERACT);
  }
  if (userStore.userInfo.Role_grade === "经销商") {
    editLayer.setActionMode(TMap.tools.constants.EDITOR_ACTION.DRAW);
  }
});
</script>

<style lang="scss" scoped>
.map-container {
  height: calc(100vh - 50px);
  width: 100vw;
  z-index: 1;
}
.edit-form {
  position: absolute;
  top: 30px;
  left: 30px;
  max-height: 88vh;
  overflow: auto;
  z-index: 2;
  padding: 15px;
  background-color: #fff;
  border-radius: 5px;

  .map-type {
    display: flex;
    gap: 10px;
    padding: 10px 0;
  }
  .area-message {
    grid-column: span 3 / span 3;
    padding: 10px;
    border-radius: 10px;
    box-shadow: 0px 1px 31px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
  }
  .click-store {
    max-height: 200px;
    display: flex;
    flex-direction: column;
    gap: 5px;
    overflow-y: auto;
  }
  .detail-message {
    display: grid;
    grid-template-columns: repeat(3, minmax(0, 1fr));
    gap: 5px;
    .list {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 10px;
      border-radius: 10px;
      box-shadow: 0px 1px 31px rgba(0, 0, 0, 0.1);
      span {
        font-weight: bold;
      }
    }
    .user-store-list {
      grid-column: span 3 / span 3;
      max-height: 300px;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      gap: 5px;
      background-color: #fff;
      .item-list {
        display: flex;
        gap: 5px;
        align-items: center;
        padding: 10px;
        border-radius: 10px;
        box-shadow: 0px 1px 31px rgba(0, 0, 0, 0.1);
        span {
          font-weight: bold;
        }
      }
    }
  }
}
.map-legend {
  position: absolute;
  z-index: 2;
  top: 200px;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  font-size: 15px;
  div {
    display: flex;
    gap: 2px;
    align-items: center;
    img {
      width: 15px;
      height: 15px;
    }
  }
}
.content {
  color: red;
}
</style>
