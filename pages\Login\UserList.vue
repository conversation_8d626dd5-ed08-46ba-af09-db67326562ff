<template>
  <view class="user-list-container">
    <!-- 搜索框 -->
    <view class="search-container">
      <u-search
        v-model="searchKeyword"
        placeholder="请输入姓名或用户编码搜索..."
        :show-action="false"
        bg-color="#f8f8f8"
        :focus="false"
        :disabled="loading"
        @change="handleSearch"
        @search="handleSearch"
      ></u-search>
    </view>

    <!-- 首次加载中状态 -->
    <view v-if="loading && users.length === 0" class="loading-container">
      <u-loading-page
        :loading="loading"
        loading-text="正在获取用户列表..."
        :z-index="99"
      ></u-loading-page>
    </view>

    <!-- 人员列表 -->
    <view class="user-list" v-else-if="users.length > 0">
      <view
        v-for="user in users"
        :key="user.id"
        class="user-item"
        @click="selectUser(user)"
      >
        <view class="user-avatar">
          <u-avatar
            :text="user.Master_data_person_name.charAt(0)"
            size="80"
            bg-color="#667eea"
            color="#fff"
            :font-size="30"
            shape="circle"
          ></u-avatar>
        </view>

        <view class="user-info">
          <view class="user-name">{{ user.Master_data_person_name }}</view>
          <view class="user-code">
            编码：{{ user.Master_data_person_code }}
          </view>
          <view class="user-dept">{{ `${[user.Role_grade]}` }}</view>
        </view>
      </view>

      <!-- 分页加载更多提示 -->
      <view v-if="loadingMore" class="loading-more">
        <u-loading-icon mode="circle" size="32"></u-loading-icon>
        <text class="loading-more-text">正在加载更多...</text>
      </view>

      <!-- 没有更多数据提示 -->
      <view v-if="noMoreData && users.length > 0" class="no-more-data">
        <text class="no-more-text">没有更多数据了</text>
      </view>
    </view>

    <!-- 空状态 -->
    <view v-else-if="!loading" class="empty-container">
      <u-empty
        mode="search"
        text="未找到相关人员"
        :text-size="32"
        :icon-size="120"
      >
        <view slot="bottom" class="empty-hint">
          <u-text
            text="请尝试其他关键词搜索"
            color="#999"
            size="26rpx"
          ></u-text>
        </view>
      </u-empty>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, reactive, onMounted } from "vue";
import { getAllUserList } from "/api/login/login";
import { useUserInfo } from "../../store/user/userInfo";
import {
  getProvinceUnderAreaIds,
  getRegionUnderAreaIds,
} from "../../api/login/login";
import { onReachBottom } from "@dcloudio/uni-app";

const userStore = useUserInfo();

// 搜索关键词
const searchKeyword = ref("");

// 加载状态
const loading = ref(false);
const loadingMore = ref(false);
const noMoreData = ref(false);

// 用户数据
const users = ref([]);

const parama = reactive({
  page: 1,
  page_size: 10,
  search: null,
});

// 搜索防抖定时器
let searchTimer = null;

// 获取用户列表
const getUserList = async (isLoadMore = false) => {
  try {
    if (isLoadMore) {
      loadingMore.value = true;
    } else {
      loading.value = true;
      noMoreData.value = false;
    }

    const { data } = await getAllUserList(parama);

    if (isLoadMore) {
      // 分页加载，追加数据
      users.value = [...users.value, ...data.result];
    } else {
      // 首次加载或搜索，替换数据
      users.value = data.result;
    }

    // 判断是否还有更多数据
    if (data.result.length < parama.page_size) {
      noMoreData.value = true;
    }
  } catch (error) {
    console.log(error);
    uni.showToast({
      title: "获取用户列表失败",
      icon: "error",
      duration: 2000,
    });
  } finally {
    loading.value = false;
    loadingMore.value = false;
  }
};

// 搜索处理（带防抖）
const handleSearch = (value) => {
  console.log("搜索关键词:", value);

  // 清除之前的定时器
  if (searchTimer) {
    clearTimeout(searchTimer);
  }

  // 设置防抖
  searchTimer = setTimeout(() => {
    parama.search = value.trim() || null;
    parama.page = 1;
    noMoreData.value = false;
    getUserList(false);
  }, 500);
};

// 初始化加载
getUserList();

// 选择用户
const selectUser = async (user) => {
  uni.showToast({
    title: `已选择：${user.Master_data_person_name}`,
    duration: 2000,
  });
  userStore.userInfo = {
    userName: user.Master_data_person_name,
    fz_area_id: user.Master_data_person_area_name,
    id: user.id,
    jxqId: [],
    Role_grade: user.Role_grade,
    userCode: user.Master_data_person_code,
    Role_name: user.Role_name,
  };

  console.log("user", user);

  if (user.Role_grade === "经销商") {
    userStore.userInfo.jxqId = [user.Master_data_person_area_name];
    console.log("经销商", userStore.userInfo);
  }

  if (user.Role_grade === "大区总") {
    //获取大区下的所有经销区
    const res3 = await getRegionUnderAreaIds(user.Master_data_person_area_name);
    const jxqIdList = res3.data.result.map((item) => item.id) || [];
    userStore.userInfo.jxqId = jxqIdList;
    console.log("大区总", userStore.userInfo);
  }

  if (user.Role_grade === "省区总") {
    //获取省区下的所有经销区
    const res3 = await getProvinceUnderAreaIds(
      user.Master_data_person_area_name
    );
    const jxqIdList = res3.data.result.map((item) => item.id) || [];
    userStore.userInfo.jxqId = jxqIdList;
    console.log("省区总", userStore.userInfo);
  }

  sessionStorage.setItem("userInfo", JSON.stringify(userStore.userInfo));

  // 这里可以添加选择用户后的逻辑，比如返回上一页并传递数据
  // 可以通过 uni.$emit 或者 getCurrentPages 来传递数据
  setTimeout(() => {
    uni.reLaunch({
      url: "/pages/Index/index",
    });
  }, 500);
};

onMounted(() => {
  // 没有用户信息，直接退回登录界面
  if (
    userStore.userInfo === null ||
    userStore.userInfo.Role_grade !== "系统管理员"
  ) {
    uni.getSystemInfo({
      success: function (res) {
        // 判断设备平台
        if (res.platform === "windows" || res.platform === "mac") {
          uni.reLaunch({
            url: "/pages/Login/WebLogin",
          });
        } else {
          uni.reLaunch({
            url: "/pages/Login/Login",
          });
        }
      },
      fail: function (err) {
        console.error("获取系统信息失败", err);
      },
    });
  }
});

// 滑动到底部加载更多
onReachBottom(() => {
  console.log("滑动到底部");

  // 如果正在加载或没有更多数据，则不执行
  if (
    loadingMore.value ||
    noMoreData.value ||
    loading.value ||
    users.value.length < 10
  ) {
    return;
  }

  parama.page++;
  getUserList(true);
});
</script>

<style scoped>
.user-list-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding-bottom: env(safe-area-inset-bottom);
}

/* 搜索框容器 */
.search-container {
  background-color: #fff;
  padding: 24rpx 32rpx 32rpx;
  border-bottom: 1px solid #e5e5e5;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.06);
}

/* 用户列表 */
.user-list {
  padding: 24rpx 32rpx;
}

.user-item {
  display: flex;
  align-items: center;
  padding: 32rpx 24rpx;
  margin-bottom: 24rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.user-item::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 8rpx;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.user-item:active {
  transform: translateY(4rpx);
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.user-item:last-child {
  margin-bottom: 0;
}

.user-avatar {
  margin-right: 32rpx;
  flex-shrink: 0;
}

.user-info {
  flex: 1;
  overflow: hidden;
}

.user-name {
  font-size: 34rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 12rpx;
}

.user-code {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.user-dept {
  font-size: 24rpx;
  color: #999;
  background: #f8f9fa;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  display: inline-block;
}

.user-action {
  flex-shrink: 0;
  margin-left: 16rpx;
}

/* 空状态容器 */
.empty-container {
  background-color: #fff;
  margin: 24rpx 32rpx;
  padding: 120rpx 32rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.empty-hint {
  margin-top: 16rpx;
}

/* 加载状态容器 */
.loading-container {
  position: relative;
  min-height: 400rpx;
}

/* 分页加载更多样式 */
.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32rpx;
  /* background-color: #fff; */
}

.loading-more-text {
  margin-left: 16rpx;
  font-size: 26rpx;
  color: #666;
}

/* 没有更多数据样式 */
.no-more-data {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32rpx;
  /* background-color: #fff; */
}

.no-more-text {
  font-size: 26rpx;
  color: #999;
}

/* 响应式设计 - PC端适配 */
@media screen and (min-width: 768px) {
  .user-list-container {
    max-width: 800px;
    margin: 0 auto;
    box-shadow: 0 0 40px rgba(0, 0, 0, 0.1);
    border-radius: 20px;
  }

  .search-container {
    padding: 30px 40px 40px;
    position: sticky;
    top: 0;
    z-index: 100;
  }

  .user-list {
    padding: 20px 40px 40px;
  }

  .user-item {
    padding: 25px;
    margin-bottom: 20px;
    border-radius: 12px;
    cursor: pointer;
  }

  .user-item::before {
    width: 5px;
  }

  .user-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  }

  .user-item:active {
    transform: translateY(0);
  }

  .user-avatar {
    margin-right: 20px;
  }

  .user-name {
    font-size: 18px;
    margin-bottom: 8px;
  }

  .user-code {
    font-size: 14px;
    margin-bottom: 6px;
  }

  .user-dept {
    font-size: 12px;
    padding: 3px 10px;
    border-radius: 8px;
  }

  .user-action {
    margin-left: 16px;
  }

  .empty-container {
    margin: 20px 40px;
    padding: 80px 40px;
    border-radius: 12px;
  }

  .loading-container {
    margin: 20px 40px;
    min-height: 300px;
  }

  .loading-more {
    padding: 20px;
    margin: 0 40px;
    border-radius: 12px;
  }

  .loading-more-text {
    margin-left: 12px;
    font-size: 14px;
  }

  .no-more-data {
    padding: 20px;
    margin: 0 40px;
    border-radius: 12px;
  }

  .no-more-text {
    font-size: 14px;
  }
}

/* 小屏幕优化 */
@media screen and (max-width: 375px) {
  .search-container {
    padding: 20rpx 24rpx 28rpx;
  }

  .user-list {
    padding: 20rpx 24rpx;
  }

  .user-item {
    padding: 28rpx 20rpx;
    margin-bottom: 20rpx;
    border-radius: 12rpx;
  }

  .user-avatar {
    margin-right: 24rpx;
  }

  .user-name {
    font-size: 30rpx;
    margin-bottom: 10rpx;
  }

  .user-code {
    font-size: 24rpx;
    margin-bottom: 6rpx;
  }

  .user-dept {
    font-size: 22rpx;
    padding: 3rpx 10rpx;
    border-radius: 10rpx;
  }

  .user-action {
    margin-left: 12rpx;
  }

  .empty-container {
    margin: 20rpx 24rpx;
    padding: 100rpx 24rpx;
    border-radius: 12rpx;
  }
}
</style>
