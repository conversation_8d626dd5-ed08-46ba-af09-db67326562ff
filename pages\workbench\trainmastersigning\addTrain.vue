<template>
  <div class="container">
    <a-card title="库长签约信息" class="main-card">
      <a-form
        :model="formData"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
        ref="formRef"
      >
        <!-- 基本信息区域 -->
        <a-divider orientation="left">基本信息</a-divider>
        <a-row :gutter="24">
          <a-col :span="12" v-for="item in basicFields" :key="item.key">
            <a-form-item :label="item.label" :name="item.key">
              <a-input
                v-model:value="formData[item.key]"
                :placeholder="getPlaceholder(item.key, item.label)"
                @click="labelclick(item.key)"
                :disabled="isFieldDisabled(item.key)"
                :maxlength="getMaxLength(item.key)"

              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 法人信息区域 -->
        <a-divider orientation="left">法人信息</a-divider>
        <a-row :gutter="24">
          <a-col :span="12" v-for="item in legalFields" :key="item.key">
            <a-form-item :label="item.label" :name="item.key">
              <a-input
                v-model:value="formData[item.key]"
                :placeholder="getPlaceholder(item.key, item.label)"
                :disabled="isFieldDisabled(item.key)"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 委托执行人信息区域 -->
        <a-divider orientation="left">委托执行人信息</a-divider>
        <a-row :gutter="24">
          <a-col :span="12" v-for="item in executorFields" :key="item.key">
            <a-form-item :label="item.label" :name="item.key">
              <a-input
                v-model:value="formData[item.key]"
                :placeholder="getPlaceholder(item.key, item.label)"
                :disabled="isFieldDisabled(item.key)"
              />
            </a-form-item>
          </a-col>
          <!-- 性别字段 -->
          <a-col :span="12">
            <a-form-item label="性别" name="sex">
              <a-radio-group v-model:value="formData.sex">
                <a-radio value="男">男</a-radio>
                <a-radio value="女">女</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 银行信息区域 -->
        <a-divider orientation="left">银行信息</a-divider>
        <a-row :gutter="24">
          <a-col :span="12" v-for="item in bankFields" :key="item.key">
            <a-form-item :label="item.label" :name="item.key">
              <a-input
                v-model:value="formData[item.key]"
                :placeholder="getPlaceholder(item.key, item.label)"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 保证金信息 -->
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="保证金金额" name="bzjje">
              <a-input
                v-model:value="formData.bzjje"
                placeholder="请输入保证金金额"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 保证金打款截图上传 -->
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="保证金打款截图" name="bzjdkzt">
              <a-upload
                :file-list="formData.bzjdkzt ? [{ uid: '1', url: formData.bzjdkzt, status: 'done' }] : []"
                list-type="picture-card"
                :before-upload="beforeUpload"
                @change="(info) => handleSingleUploadChange(info, 'bzjdkzt')"
                :max-count="1"
                accept="image/*"
              >
                <div v-if="!formData.bzjdkzt">
                  <plus-outlined />
                  <div style="margin-top: 8px">上传截图</div>
                </div>
              </a-upload>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 区域信息 -->
        <a-divider orientation="left">区域信息</a-divider>
        <a-row :gutter="24">
          <a-col :span="12" v-for="item in regionFields" :key="item.key">
            <a-form-item :label="item.label" :name="item.key">
              <a-input
                v-model:value="formData[item.key]"
                :placeholder="getPlaceholder(item.key, item.label)"
                :disabled="isFieldDisabled(item.key)"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 图片上传区域 -->
        <a-divider orientation="left">证件照片</a-divider>

        <!-- 营业执照照片上传 -->
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="营业执照照片" name="yyzztp">
              <a-upload
                :file-list="formData.yyzztp ? [{ uid: '1', url: formData.yyzztp, status: 'done' }] : []"
                list-type="picture-card"
                :before-upload="beforeUpload"
                @change="(info) => handleSingleUploadChange(info, 'yyzztp')"
                :max-count="1"
                accept="image/*"
              >
                <div v-if="!formData.yyzztp">
                  <plus-outlined />
                  <div style="margin-top: 8px">上传营业执照</div>
                </div>
              </a-upload>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 法人身份证照片上传 -->
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="法人身份证正面照片" name="sfztp_zm">
              <a-upload
                :file-list="formData.sfztp_zm ? [{ uid: '1', url: formData.sfztp_zm, status: 'done' }] : []"
                list-type="picture-card"
                :before-upload="beforeUpload"
                @change="(info) => handleSingleUploadChange(info, 'sfztp_zm')"
                :max-count="1"
                accept="image/*"
              >
                <div v-if="!formData.sfztp_zm">
                  <plus-outlined />
                  <div style="margin-top: 8px">上传正面</div>
                </div>
              </a-upload>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="法人身份证反面照片" name="sfztp_fm">
              <a-upload
                :file-list="formData.sfztp_fm ? [{ uid: '1', url: formData.sfztp_fm, status: 'done' }] : []"
                list-type="picture-card"
                :before-upload="beforeUpload"
                @change="(info) => handleSingleUploadChange(info, 'sfztp_fm')"
                :max-count="1"
                accept="image/*"
              >
                <div v-if="!formData.sfztp_fm">
                  <plus-outlined />
                  <div style="margin-top: 8px">上传反面</div>
                </div>
              </a-upload>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 银行卡照片上传 -->
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="银行卡正面照片" name="yhktp_zm">
              <a-upload
                :file-list="formData.yhktp_zm ? [{ uid: '1', url: formData.yhktp_zm, status: 'done' }] : []"
                list-type="picture-card"
                :before-upload="beforeUpload"
                @change="(info) => handleSingleUploadChange(info, 'yhktp_zm')"
                :max-count="1"
                accept="image/*"
              >
                <div v-if="!formData.yhktp_zm">
                  <plus-outlined />
                  <div style="margin-top: 8px">上传正面</div>
                </div>
              </a-upload>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="银行卡反面照片" name="yhktp_fm">
              <a-upload
                :file-list="formData.yhktp_fm ? [{ uid: '1', url: formData.yhktp_fm, status: 'done' }] : []"
                list-type="picture-card"
                :before-upload="beforeUpload"
                @change="(info) => handleSingleUploadChange(info, 'yhktp_fm')"
                :max-count="1"
                accept="image/*"
              >
                <div v-if="!formData.yhktp_fm">
                  <plus-outlined />
                  <div style="margin-top: 8px">上传反面</div>
                </div>
              </a-upload>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 提交按钮 -->
        <a-form-item :wrapper-col="{ span: 24 }" style="text-align: center; margin-top: 32px">
          <a-button
            type="primary"
            size="large"
            :loading="buttonLoading"
            @click="submit"
            style="width: 200px"
          >
            提交申请
          </a-button>
        </a-form-item>
      </a-form>
    </a-card>
  </div>

  <CustomModal
    :show="showModal"
    title="请选择"
    :width="600"
    @close="showModal = false"
  >
    <MultiSelectListOne
      :items="allItems"
      :items2="allItems2"
      @update:confirm="confirm"
      :items-per-page="pageSize"
      :current-page="currentPage"
      :single-select="true"
    />
  </CustomModal>
</template>

<script setup>
import { ref, reactive } from "vue";
import { PlusOutlined } from '@ant-design/icons-vue';
import {
  getTrainmasterDetailApi,
  get_rzkf,
  add_qy,
  get_rzkfDq
} from "/api/workbench/trainmastersigning/index.js";
import { get_rzkf_wlfc } from "/api/workbench/logisticsWarehouseBuild/index.js";
import { uploadFile } from "/api/upload.js";
import { useUserInfo } from "/store/user/userInfo";
import { onLoad } from "@dcloudio/uni-app";
import CustomModal from "@/pages/workbench/partnerSign/components/CustomModal.vue";
import MultiSelectListOne from "@/pages/workbench/partnerSign/components/MultiSelectListOne.vue";
import dayjs from "dayjs";
const userStore = useUserInfo();
const formRef = ref(null);

// 存储经销区域名称
const outletName = ref("");
const formData = reactive({
  rzkf: null, //入职库房
  rzkfId: null, //入职库房ID
  wlfcmc: null, //物流分仓
  wlfcbm: null, //物流分仓编码
  wlfcId: null, //物流分仓ID
  jxqy: null, //经销区域
  kzxtmc: null, //库长系统名称
  yfyyzzmc: null, //营业执照名称
  yfyyzzh: null, //营业执照号
  yfjyz: null, //法定代表人
  yflxdh: null, //法人电话
  yfjycs: null, //营业执照经营场所
  rzrymc: null, //委托执行人系统名称
  rzryjs: "库长", //委托执行人角色，默认值为"库长"
  wtzxrxm: null, //委托执行人姓名
  sex: null, //性别
  sfzh: null, //身份证号
  lxdh: null, //联系电话
  khyh: null, //开户银行
  zhmc: null, //账户名称
  yxkh: null, //银行账号
  bzjje: null, //保证金金额
  bzjdkzt: null, //保证金打款截图
  ssdq: null, //所属大区
  sssq: null, //所属省区
  kfwz: null, //库房位置
  yyzztp: null, //营业执照照片
  sfztp_zm: null, //法人身份证正面照片
  sfztp_fm: null, //法人身份证反面照片
  yhktp_zm: null, //银行卡正面照片
  yhktp_fm: null, //银行卡反面照片
});


const buttonLoading = ref(false);
const types = ref(false);

// 字段禁用状态控制
const isFieldDisabled = (key) => {
  const disabledFields = ['jxqy', 'rzryjs', 'ssdq', 'sssq', 'kfwz'];
  return disabledFields.includes(key);
};

// 获取placeholder文本
const getPlaceholder = (key, label) => {
  switch (key) {
    case 'rzrymc':
      return '请输入区域简称和身份证姓名';
    case 'wtzxrxm':
      return '请输入身份证姓名';
    default:
      return `请输入${label}`;
  }
};

// 获取字段最大长度
const getMaxLength = (key) => {
  // 库长系统名称不限制字数
  return null;
};

// 字段分组配置
const basicFields = [
  { label: "入职库房", key: "rzkf" },
  { label: "物流分仓", key: "wlfcmc" },
  { label: "经销区域", key: "jxqy" },
  { label: "库长系统名称", key: "kzxtmc" }
];

const legalFields = [
  { label: "营业执照名称", key: "yfyyzzmc" },
  { label: "营业执照号", key: "yfyyzzh" },
  { label: "法定代表人", key: "yfjyz" },
  { label: "法人电话", key: "yflxdh" },
  { label: "营业执照经营场所", key: "yfjycs" }
];

const executorFields = [
  { label: "委托执行人系统名称", key: "rzrymc" },
  { label: "委托执行人角色", key: "rzryjs" },
  { label: "委托执行人姓名", key: "wtzxrxm" },
  { label: "身份证号", key: "sfzh" },
  { label: "联系电话", key: "lxdh" }
];

const bankFields = [
  { label: "开户银行", key: "khyh" },
  { label: "账户名称", key: "zhmc" },
  { label: "银行账号", key: "yxkh" }
];

const regionFields = [
  { label: "所属大区", key: "ssdq" },
  { label: "所属省区", key: "sssq" },
  { label: "库房位置", key: "kfwz" }
];

// 保持原有的formObject用于验证
const formObject = [
  ...basicFields,
  ...legalFields,
  ...executorFields,
  ...bankFields,
  { label: "保证金金额", key: "bzjje" },
  ...regionFields
];

// 文件上传前验证
const beforeUpload = (file) => {
  // 检查文件类型
  const isImage = file.type.startsWith('image/');
  if (!isImage) {
    uni.showToast({
      title: '只能上传图片文件',
      icon: 'none',
      duration: 2000
    });
    return false;
  }

  // 检查文件大小（限制为5MB）
  const isLt5M = file.size / 1024 / 1024 < 5;
  if (!isLt5M) {
    uni.showToast({
      title: '图片大小不能超过5MB',
      icon: 'none',
      duration: 2000
    });
    return false;
  }

  return false; // 阻止默认上传行为，使用自定义上传
};

// 单文件上传处理函数
const handleSingleUploadChange = async (info, fieldName) => {
  const { fileList } = info;

  // 如果文件列表为空，清空字段
  if (fileList.length === 0) {
    formData[fieldName] = null;
    return;
  }

  // 获取最新的文件
  const file = fileList[fileList.length - 1];

  // 如果文件已经有 url，直接使用
  if (file.url) {
    formData[fieldName] = file.url;
    return;
  }

  // 如果是新上传的文件，调用 uploadFile 接口
  if (file.originFileObj) {
    try {
      // 显示上传提示
      uni.showLoading({
        title: '正在上传...'
      });

      // 创建 FormData 对象
      const uploadFormData = new FormData();
      uploadFormData.append('file', file.originFileObj);

      // 调用上传接口
      const response = await uploadFile(uploadFormData);

      // 隐藏加载提示
      uni.hideLoading();

      // 获取文件URL
      const fileUrl = response.data?.url ||
                     response.data?.file_url ||
                     response.data?.path ||
                     response.data?.fileUrl ||
                     response.data?.filePath ||
                     response.url;

      if (!fileUrl) {
        throw new Error('上传接口未返回文件URL');
      }

      // 直接将URL赋值给字段
      formData[fieldName] = fileUrl;

      // 显示上传成功提示
      uni.showToast({
        title: '上传成功',
        icon: 'success',
        duration: 1000
      });

    } catch (error) {
      console.error('文件上传失败:', error);

      // 隐藏加载提示
      uni.hideLoading();

      const errorMessage = error.message || error.errMsg || '文件上传失败';
      uni.showToast({
        title: errorMessage,
        icon: 'none',
        duration: 2000
      });

      // 上传失败时清空字段
      formData[fieldName] = null;
    }
  }
};

// 提交逻辑
async function submit() {
  buttonLoading.value = true;

  if (!validateForm()) {
    buttonLoading.value = false;
    return;
  }

  const dataParams = {
    ...formData,
    type: types.value,
    user: userStore.userInfo.id,
    userName: userStore.userInfo.userName,
  };

  console.log(dataParams, "dataParams");
  try {
    await add_qy(dataParams);
    uni.showToast({
      title: "提交成功",
      duration: 1000,
      icon: "success",
    });
    setTimeout(() => {
      uni.navigateBack();
    }, 1100);
  } catch (error) {
    buttonLoading.value = false;
    console.error("提交失败", error);
    uni.showToast({
      title: "提交失败",
      icon: "none",
      duration: 2000,
    });
  }
}
const validateForm = () => {
  for (let item of formObject) {
    if (formData[item.key] == null || formData[item.key] == "") {
      uni.showToast({
        title: `请输入${item.label}`,
        icon: "none",
      });
      return false;
    }
  }

  // 库长系统名称不限制字数，删除长度验证

  // 验证图片上传（检查字符串字段是否为空）
  if (!formData.bzjdkzt) {
    uni.showToast({
      title: "请上传保证金打款截图",
      icon: "none",
    });
    return false;
  }

  if (!formData.yyzztp) {
    uni.showToast({
      title: "请上传营业执照照片",
      icon: "none",
    });
    return false;
  }

  if (!formData.sfztp_zm) {
    uni.showToast({
      title: "请上传法人身份证正面照片",
      icon: "none",
    });
    return false;
  }

  if (!formData.sfztp_fm) {
    uni.showToast({
      title: "请上传法人身份证反面照片",
      icon: "none",
    });
    return false;
  }

  if (!formData.yhktp_zm) {
    uni.showToast({
      title: "请上传银行卡正面照片",
      icon: "none",
    });
    return false;
  }

  if (!formData.yhktp_fm) {
    uni.showToast({
      title: "请上传银行卡反面照片",
      icon: "none",
    });
    return false;
  }

  return true;
};
const detailId = ref("");

async function getDetailMessage() {
  const { data } = await getTrainmasterDetailApi(detailId.value);
  formData.id = detailId.value;
  const message = data.result[0];
  for (let key in message) {
    if (key === "xyqsrq") {
      formData[key] = message["xyqsrq_f"];
    }
    if (key === "htksrq") {
      formData[key] = message["htksrq_f"];
    }
    if (key === "htjsrq") {
      formData[key] = message["htjsrq_f"];
    }
    if (!["xyqsrq", "htksrq", "htjsrq"].includes(key)) {
      formData[key] = message[key];
    }
  }
}





onLoad(options => {
  types.value = options.type;
  detailId.value = options.id;
  getDetailMessage();
});

const info = ref();

async function labelclick(data) {
  console.log(data);
  info.value = data;
  if (data == 'rzkf') {
    rzkfClick();
  } else if (data == 'wlfcmc') {
    // 如果已选择入职库房，则获取对应的物流分仓
    if (formData.rzkf && formData.rzkfId) {
      wlfcClick(formData.rzkfId);
    } else {
      uni.showToast({
        title: "请先选择入职库房",
        icon: "none",
        duration: 2000,
      });
    }
  }
}

const rzkfClick = async () => {
  let res = await get_rzkfDq(userStore.userInfo.id);
  allItems.value = [];
  for (var i = 0; i < res.data.result.length; i++) {
    allItems.value.push({
      value: res.data.result[i].id,
      label: res.data.result[i].DistributionOrganizationName,
      label2: res.data.result[i].DistributionOrganizationName2,
      label3: res.data.result[i].DistributionOrganizationName3,
      province_name: res.data.result[i].DistributionOrganizationName2,
      region_name: res.data.result[i].DistributionOrganizationName3,
      outlet_name: res.data.result[i].DistributionOrganizationName,
      LW_delivery_address: res.data.result[i].LW_delivery_address,
      radio: 0
    });
  }
  allItems2.value = [];
  allItems2.value.push({
    value: 1,
    label: '库房名称',
    label2: '具体位置',
    label3: '所属大区',
    radio: 0
  });
  showModal.value = true;
};

// 物流分仓选择函数
const wlfcClick = async (rzkfId) => {
  try {
    const res = await get_rzkf_wlfc(rzkfId);
    allItems.value = [];

    if (res.data && res.data.result) {
      for (var i = 0; i < res.data.result.length; i++) {
        allItems.value.push({
          value: res.data.result[i].id,
          label: res.data.result[i].LW_name, // 分仓名称
          label2: res.data.result[i].LW_delivery_address, // 具体位置
          label3: res.data.result[i].LW_number, // 分仓编号
          lid: res.data.result[i].lid, // 分仓ID
          radio: 0
        });
      }
    }

    allItems2.value = [];
    allItems2.value.push({
      value: 1,
      label: '分仓名称',
      label2: '具体位置',
      label3: '所属区域',
      radio: 0
    });
    showModal.value = true;
  } catch (error) {
    console.error("获取物流分仓数据失败", error);
    uni.showToast({
      title: "获取数据失败",
      icon: "none",
      duration: 2000,
    });
  }
};

const showModal = ref(false);
const allItems = ref([]);
const allItems2 = ref([]);
const pageSize = ref(10);
const currentPage = ref(1);

const confirm = (item) => {
  console.log("item", item);
  formData[info.value] = item.label;
  if (info.value == "rzkf") {
    // 存储入职库房ID，以便后续使用
    formData["rzkfId"] = item.value;
    formData["rzkfbm"] = `R${dayjs().format("YYYYMMDDHHmmss")}`;
    // 自动填入区域信息
    formData["sssq"] = item.province_name; // 所属省区
    formData["ssdq"] = item.region_name;   // 所属大区
    formData["kfwz"] = item.LW_delivery_address;        // 库房位置使用具体位置
    formData["jxqy"] = item.outlet_name || ""; // 经销区域
    // 保存outlet_name
    outletName.value = item.outlet_name || "";
  } else if (info.value == "wlfcmc") {
    // 存储物流分仓相关信息
    formData["wlfcId"] = item.value;
    formData["wlfcmc"] = item.label;
    formData["wlfcbm"] = item.label3;
  }

  showModal.value = false;
};
</script>

<style lang="scss" scoped>
.container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.main-card {
  max-width: 1200px;
  margin: 0 auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 8px;

  :deep(.ant-card-head) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px 8px 0 0;

    .ant-card-head-title {
      color: white;
      font-size: 18px;
      font-weight: 600;
    }
  }

  :deep(.ant-card-body) {
    padding: 32px;
  }
}

// 分割线样式
:deep(.ant-divider) {
  margin: 24px 0 16px 0;

  .ant-divider-inner-text {
    font-size: 16px;
    font-weight: 600;
    color: #1890ff;
  }
}

// 表单项样式
:deep(.ant-form-item) {
  margin-bottom: 16px;

  .ant-form-item-label {
    > label {
      font-weight: 500;
      color: #262626;
    }
  }

  .ant-input {
    border-radius: 6px;

    &:focus, &:hover {
      border-color: #40a9ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }

    &:disabled {
      background-color: #f5f5f5;
      color: #8c8c8c;
    }
  }
}

// 上传组件样式
:deep(.ant-upload) {
  .ant-upload-select {
    border-radius: 6px;
    border: 1px dashed #d9d9d9;
    transition: all 0.3s;

    &:hover {
      border-color: #40a9ff;
    }
  }

  .ant-upload-list-picture-card {
    .ant-upload-list-item {
      border-radius: 6px;
    }
  }
}

// 开关样式
:deep(.ant-switch) {
  background-color: #f5f5f5;

  &.ant-switch-checked {
    background-color: #1890ff;
  }

  .ant-switch-inner {
    font-size: 12px;
    color: #fff;
  }
}

// 按钮样式
:deep(.ant-btn-primary) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 6px;
  font-weight: 500;

  &:hover, &:focus {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }

  .main-card {
    :deep(.ant-card-body) {
      padding: 16px;
    }
  }

  :deep(.ant-col) {
    &.ant-col-12 {
      width: 100% !important;
    }
  }

  :deep(.ant-form-item) {
    .ant-form-item-label {
      text-align: left !important;
    }
  }
}
</style>
