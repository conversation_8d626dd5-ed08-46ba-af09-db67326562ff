import { defineStore } from "pinia";
import { ref } from "vue";
import {
  randomColor,
  processStoreStyle,
  processProStoreStyle,
  processVehicleStyle,
} from "../../pages/Map/data-uitls";
import { transtionCopy } from "../../utils/map";
import {
  getNationalAreas,
  getAllStores,
  get_pro_stores_all,
  getRegionUnderling,
  get_cache,
  get_work,
  carMapGetAllJxs,
  GetAllDq,
  get_all_users,
  get_all_cars,
  getAreaInfoBycode,
  get_all_country_area,
  getAllCountryStores,
  getAllProd,
  getAllProdCategory,
  get_update_cars,
} from "../../api/Map/index";
import { message } from "ant-design-vue";

export const useMapStore = defineStore("mapstore", () => {
  const store = useMapStore();
  const checkedCar = ref(false);
  const checkedArea = ref(true);
  const checkedStore = ref(false);
  const checkedAllArea = ref(false);
  const loading = ref(true);

  const selectObj = ref({
    jxsName: "全部",
    jxsId: "",
    fxsName: null,
    fxsId: "",
    optionValue: "近30天",
    dateSection: null,
    modalShow: false,
    areaName: "数据",
    currentAreaMessage: null,
    province: null,
    productClass: null,
    product: null,
    dqName: "全部",
    dqId: null,
    jxqName: "全部",
    jxqId: "",
    start_date: null,
    end_date: null,
  });

  const storeList = ref([]); //门店
  const filterStoreList = ref([]); //门店
  const proStoreList = ref([]);
  const proFilterStoreList = ref([]);
  const areaCodeGlobal = ref([]); // 全局区域code
  let all_stores = ref(); // 全部门店

  const areaList = ref([]); //区域
  const filterAreaList = ref([]); //区域

  const carList = ref([]); //车
  const allUsers = ref([]);
  const filterAllUsers = ref([]);

  const jxsUserList = ref([]);
  const allJxsList = ref([]); // 经销商备份
  const fxsUserList = ref([]);
  const userCache = ref([]); // 经销区下的库长缓存
  const jxqCache = ref([]); // 经销区缓存

  const provinceMessage = ref([]);
  const AllAreaList = ref([]); // 所有区域
  const geometries_arr = ref([]);

  const control_show = ref();
  // 当前门店数
  const store_count = ref();
  // 当前人口数
  const people_count = ref();
  // 所有产品
  const product_list = ref();
  // 所有产品类别
  const product_list_category = ref();

  let pickOn = ref("");

  const dqList = ref([]);
  const jxqList = ref([]);
  let timerCar = null; //定义车辆刷新定时器
  let polygon1 = null;

  function getDqIdJxqId(dqList, jxqList) {
    let region = "";
    let area = "";
    dqList.forEach((item) => {
      if (selectObj.value.dqName === "全部") {
        area = "";
      } else if (item.label === selectObj.value.dqName) {
        area = item.dqId;
      }
    });
    jxqList.forEach((item) => {
      if (selectObj.value.jxqName === "全部") {
        region = "";
      } else if (item.label === selectObj.value.jxqName) {
        region = item.jxqId;
      }
    });

    return [region, area];
  }
  // 获取全部区域
  async function getAllAreas(polygon) {
    polygon1 = polygon;
    const res = await getNationalAreas();
    let styles = {};
    try {
      provinceMessage.value = res.data.result.filter(
        (item) => item.District_name !== "" && item.District_name !== null
      );
      provinceMessage.value.forEach((item, index) => {
        styles[`${item.id}`] = new TMap.PolygonStyle({
          color: randomColor(),
          showBorder: true,
          borderColor: "rgba(0,0,0,0.8)",
          borderWidth: 1,
        });
        polygon.setStyles(styles);

        let outlinePath;
        JSON.parse(item.Region_profile).forEach((val) => {
          outlinePath = transtionCopy(val).map(
            (item) => new TMap.LatLng(item.latitude, item.longitude)
          );
          let path = {
            styleId: `${item.id}`,
            paths: outlinePath,
          };
          polygon.add(path);
        });
      });
      //区域点缓存
      areaList.value = polygon.getGeometries();
      filterAreaList.value = polygon.getGeometries();
      loading.value = false;
    } catch (err) {
      console.err(err);
    }
  }
  //获取全部门店
  async function getAllStore(storeMarker, infoWindow, product_class, product) {
    storeList.value = [];
    // let PCRMessage = [];
    // let chunkSize = 80;
    // let twoDimensionalArray = [];
    // 获取省市区信息
    try {
      let id = getDqIdJxqId(dqList.value, jxqList.value);
      const res = await getAllStores(...id);
      if (res.data.result[0][0].exist_flag === "0") {
        const res1 = await get_cache("", "", ...id);
        let res2 = await get_work(res1.data.data.task_iid_iiids);
        let num = 0;
        while (res2.data.data.status !== "success") {
          res2 = await get_work(res1.data.data.task_iid_iiids);
          await delay(1000);
          num++;
          if (num > 60) {
            loading.value = false;
            return;
          }
        }
        const res = await getAllStores(...id);

        storeList.value = storeList.value.concat(
          JSON.parse(JSON.parse(res.data.result[0][0].result))
        );
        loading.value = false;
      } else {
        storeList.value = storeList.value.concat(
          JSON.parse(JSON.parse(res.data.result[0][0].result))
        );
        loading.value = false;
      }

      filterStoreList.value = storeList.value;
      // const eventMousemove = (e) => {
      //   const filterStore = filterStoreList.value.filter(
      //     (item) => item.id === e.geometry.id
      //   )[0];
      //   // console.log(filterStore, "filterStore1");
      //   const content = `${filterStore?.Store_name} | ${
      //     Number(filterStore?.Store_monthly_sales).toFixed(2)
      //       ? Number(filterStore?.Store_monthly_sales).toFixed(2)
      //       : 0
      //   }元`;
      //   infoWindow.open();
      //   infoWindow.setPosition(e.geometry.position); //设置信息窗位置
      //   infoWindow.setContent(content); //设置信息窗内容
      // };
      // const eventClick = (e) => {
      //   try {
      //     const filterStore = filterStoreList.value.filter(
      //       (item) => item.id === e.geometry.id
      //     )[0];
      //     const content = `${filterStore.Store_name} | ${
      //       filterStore.Store_monthly_sales
      //         ? Number(filterStore.Store_monthly_sales).toFixed(2)
      //         : 0
      //     }元`;
      //     infoWindow.open();
      //     infoWindow.setPosition(e.geometry.position); //设置信息窗位置
      //     infoWindow.setContent(content); //设置信息窗内容

      //     //打开门店销弹窗
      //     selectObj.value.modalShow = true;
      //     sessionStorage.setItem("storeId", filterStore.id);
      //   } catch (err) {}
      // };

      const eventMousemove = (e) =>
        mousemove(e, filterStoreList.value, infoWindow);
      const eventClick = (e) => click(e, filterStoreList.value, infoWindow);

      storeMarker.on("mouseover", eventMousemove);
      storeMarker.on("click", eventClick);
      all_stores.value = storeMarker;
    } catch (err) {
      console.log(err);
    }
  }

  const mousemove = (e, storeList, info) => {
    try {
      const filterStore = storeList.filter(
        (item) => item.id === e.geometry.id
      )[0];
      const content = `${filterStore?.Store_name} | ${
        Number(filterStore?.Store_monthly_sales).toFixed(2)
          ? Number(filterStore?.Store_monthly_sales).toFixed(2)
          : 0
      }元`;
      info.open();
      info.setPosition(e.geometry.position); //设置信息窗位置
      info.setContent(content); //设置信息窗内容
    } catch (error) {
      console.log(error, "鼠标移动发生错误");
    }
  };

  const click = (e, storeList, info) => {
    try {
      const filterStore = storeList.filter(
        (item) => item.id === e.geometry.id
      )[0];
      const content = `${filterStore.Store_name} | ${
        filterStore.Store_monthly_sales
          ? Number(filterStore.Store_monthly_sales).toFixed(2)
          : 0
      }元`;
      info.open();
      info.setPosition(e.geometry.position); //设置信息窗位置
      info.setContent(content); //设置信息窗内容

      //打开门店销弹窗
      selectObj.value.modalShow = true;
      sessionStorage.setItem("storeId", filterStore.id);
    } catch (err) {
      console.log(err, "点击门店发生错误");
    }
  };

  // 延时函数
  function delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
  //点击产品类别/产品获取的门店
  async function getProStore(
    storeMarker,
    infoWindow,
    product_class,
    product
    // status = 1
  ) {
    proStoreList.value = [];
    // let PCRMessage = [];
    // let chunkSize = 50;
    // let twoDimensionalArray = [];
    try {
      // if (status === 1) {
      //   provinceMessage.value.forEach((item) => {
      //     PCRMessage.push(
      //       item.Provincial_name + item.City_name + item.District_name
      //     );
      //   });
      // } else {
      //   areaCodeGlobal.value.forEach((item) => {
      //     PCRMessage.push(
      //       item.Provincial_name + item.City_name + item.District_name
      //     );
      //   });
      // }

      // // 将省市区信息分割
      // for (let i = 0; i < PCRMessage.length; i += chunkSize) {
      //   twoDimensionalArray.push(PCRMessage.slice(i, i + chunkSize));
      // }

      // for (let i = 0; i < twoDimensionalArray.length; i++) {
      //   const _PCRMessage = twoDimensionalArray[i].join(",");
      //   const res = await get_pro_stores_all(
      //     // _PCRMessage,
      //     product_class,
      //     product
      //   );
      //   // 将符合条件的门店添加到数组中
      //   if (res.data.result[0].exist_flag === "0") {
      //     return;
      //   } else {
      //     console.log(res, "res");
      //     proStoreList.value = proStoreList.value.concat(
      //       JSON.parse(JSON.parse(res.data.result[0].result))
      //     );
      //   }

      //   // proStoreList.value = proStoreList.value.concat(res.data.result);
      // }
      let id = getDqIdJxqId(store.dqList, store.jxqList);
      const res = await get_pro_stores_all(product_class, product, ...id);
      if (res.data.result[0][0].exist_flag === "0") {
        // const res1 = await get_cache(product_class, product, ...id);
        // let res2 = await get_work(res1.data.data.task_iid_iiids);
        // let num = 0;
        // while (res2.data.data.status !== "success") {
        //   res2 = await get_work(res1.data.data.task_iid_iiids);
        //   await delay(1000);
        //   num++;
        //   if (num > 30) {
        //     loading.value = false;
        //     return;
        //   }
        // }
        // const res = await get_pro_stores_all(product_class, product, ...id);

        const res = await circulation(
          product_class,
          product,
          id,
          30,
          async () => {
            const result = await get_pro_stores_all(
              product_class,
              product,
              ...id
            );
            return result;
          }
        );
        proStoreList.value = proStoreList.value.concat(
          JSON.parse(JSON.parse(res.data.result[0][0].result))
        );
        loading.value = false;
      } else {
        proStoreList.value = proStoreList.value.concat(
          JSON.parse(JSON.parse(res.data.result[0][0].result))
        );
        loading.value = false;
      }

      proFilterStoreList.value = proStoreList.value;
      const eventMousemove = (e) => {
        const filterStore = proStoreList.value.filter(
          (item) => item.id === e.geometry.id
        )[0];
        // console.log(proFilterStoreList.value, "proFilterStoreList.value");
        // console.log(filterStore, "filterStore2");
        const content = `${filterStore?.Store_name} | ${
          Number(filterStore?.product_sale_money).toFixed(2)
            ? Number(filterStore?.product_sale_money).toFixed(2)
            : 0
        }元`;
        infoWindow.open();
        infoWindow.setPosition(e.geometry.position); //设置信息窗位置
        infoWindow.setContent(content); //设置信息窗内容
      };
      const eventClick = (e) => {
        try {
          const filterStore = proStoreList.value.filter(
            (item) => item.id === e.geometry.id
          )[0];
          const content = `${filterStore.Store_name} | ${
            filterStore.Store_monthly_sales
              ? Number(filterStore.Store_monthly_sales).toFixed(2)
              : 0
          }元`;
          infoWindow.open();
          infoWindow.setPosition(e.geometry.position); //设置信息窗位置
          infoWindow.setContent(content); //设置信息窗内容

          //打开门店销弹窗
          selectObj.value.modalShow = true;
          sessionStorage.setItem("storeId", filterStore.id);
        } catch (err) {}
      };
      storeMarker.on("mouseover", eventMousemove);
      storeMarker.on("click", eventClick);
    } catch (err) {
      console.log(err);
    }
  }

  const circulation = async (product_class, product, id, limit, callback) => {
    try {
      const res1 = await get_cache(product_class, product, ...id);
      let res2 = await get_work(res1.data.data.task_iid_iiids);
      let num = 0;
      while (res2.data.data.status !== "success") {
        res2 = await get_work(res1.data.data.task_iid_iiids);
        await delay(1000);
        num++;
        if (num > limit) {
          loading.value = false;
          message.error("网络请求超时");
          throw new Error(
            `Timeout: Operation did not complete within ${limit} seconds.`
          );
        }
      }
      const callbackResult = await callback();
      return callbackResult;
    } catch (error) {
      console.error("An error occurred:", error);
      loading.value = false;
      throw error; // Rethrow the error if you want to handle it further up the call stack
    }
  };

  // 获取全部车辆
  async function getAllcars(carMarker) {
    try {
      loading.value = true;
      const resCar = await get_all_cars();
      const resUser = await get_all_users("all");
      allUsers.value = resUser.data.result[0];
      if (filterAllUsers.value.length === 0) {
        filterAllUsers.value = resUser.data.result[0];
      }

      carList.value = resCar.data.result;
    } catch (err) {
      console.log(err);
    }
    loading.value = false;
    carMarker.on("click", (e) => {
      const currentUser = allUsers.value.find(
        (item) => "0" + item.IMEI_code === e.geometry.id
      );
      console.log("e.geometry.id", e.geometry.id);
      console.log("allUsers.value", allUsers.value);
      const newPageUrl = `/pages/Map/components/MapTrack?imei=${e.geometry.id}&userid=${currentUser.id}`;
      uni.navigateTo({
        url: newPageUrl,
      });
    });
  }

  // 更新车辆
  async function updateCar(carMarker) {
    const imei_list = filterAllUsers.value
      .filter((item) => item.IMEI_code !== null)
      .map((item) => {
        return "0" + item.IMEI_code;
      })
      .join(",");
    const res = await get_update_cars(imei_list);
    carList.value = res.data.result;
    const { points, styles } = processVehicleStyle(
      carList.value,
      filterAllUsers.value
    );
    carMarker.setStyles(styles);
    carMarker.setGeometries(points);
  }

  //改变现实控件
  async function changeChecked(
    polygon,
    storeMarker,
    carMarker,
    infoWindow,
    allView,
    label
  ) {
    infoWindow.close();
    if (checkedCar.value) {
      timerCar = setInterval(() => {
        updateCar(carMarker);
      }, 10000);
      if (carList.value.length === 0) {
        await getAllcars(carMarker);
        const { points, styles } = processVehicleStyle(
          carList.value,
          filterAllUsers.value
        );
        carMarker.setStyles(styles);
        carMarker.setGeometries(points);
        return;
      }

      const { points, styles } = processVehicleStyle(
        carList.value,
        filterAllUsers.value
      );
      carMarker.setStyles(styles);
      carMarker.setGeometries(points);
    } else {
      carMarker.setGeometries([]);
      clearInterval(timerCar);
    }
    if (checkedArea.value) {
      polygon.setGeometries(filterAreaList.value);
    } else {
      polygon.setGeometries([]);
    }
    if (checkedStore.value) {
      pickOn.value = "";
      if (selectObj.value.currentAreaMessage) {
        if (selectObj.value.productClass || selectObj.value.product) {
          loading.value = true;
          await getProStore(
            storeMarker,
            infoWindow,
            selectObj.value.productClass,
            selectObj.value.product
          );
          let filterS = [];

          areaCodeGlobal.value.forEach((it) => {
            const a = proStoreList.value.filter(
              (item) =>
                item.Store_province === it.Provincial_name &&
                item.Store_city === it.City_name &&
                item.Store_district === it.District_name
            );
            filterS = [...filterS, ...a];
          });
          filterAllUsers.value.forEach((it) => {
            const a = proStoreList.value.filter((item) => {
              it.id === item.Cyclist_name;
            });
            filterS = [...filterS, ...a];
          });
          filterStoreList.value = filterS;
          const { points, styles } = processProStoreStyle(
            filterStoreList.value
          );
          storeMarker.setStyles(styles);
          storeMarker.setGeometries(points);
          loading.value = false;
        } else {
          loading.value = true;
          await getAllStore(storeMarker, infoWindow);
          let filterS = [];
          areaCodeGlobal.value.forEach((it) => {
            const a = storeList.value.filter(
              (item) =>
                item.Store_province === it.Provincial_name &&
                item.Store_city === it.City_name &&
                item.Store_district === it.District_name
            );
            filterS = [...filterS, ...a];
          });
          filterAllUsers.value.forEach((it) => {
            const a = storeList.value.filter((item) => {
              it.id === item.Cyclist_name;
            });
            filterS = [...filterS, ...a];
          });
          filterStoreList.value = filterS;
          const { points, styles } = processStoreStyle(filterStoreList.value);
          storeMarker.setStyles(styles);
          storeMarker.setGeometries(points);
          loading.value = false;
        }
      } else {
        if (selectObj.value.productClass || selectObj.value.product) {
          loading.value = true;
          await getProStore(
            storeMarker,
            infoWindow,
            selectObj.value.productClass,
            selectObj.value.product
          );
          const { points, styles } = processProStoreStyle(proStoreList.value);
          storeMarker.setStyles(styles);
          storeMarker.setGeometries(points);
          loading.value = false;
        } else {
          if (storeList.value.length === 0) {
            loading.value = true;
            await getAllStore(storeMarker, infoWindow);
            const { points, styles } = processStoreStyle(filterStoreList.value);
            storeMarker.setStyles(styles);
            storeMarker.setGeometries(points);
            loading.value = false;
          } else {
            const { points, styles } = processStoreStyle(filterStoreList.value);
            storeMarker.setStyles(styles);
            storeMarker.setGeometries(points);
          }
        }
      }
    } else {
      storeMarker.setGeometries([]);
    }
    if (checkedAllArea.value) {
      loading.value = true;
      if (geometries_arr.value.length === 0) {
        await allArea(allView, label);
      } else {
        loading.value = false;
        allView.add(allPath.value);
        label.setGeometries(geometries_arr.value);
      }
    } else {
      allView.setGeometries([]);
      label.setGeometries([]);
      loading.value = false;
    }
  }
  // 获取全部大区
  async function getAllRegion() {
    const res = await GetAllDq();
    try {
      const aa = res.data.result.map((item) => {
        return {
          label: item.DistributionOrganizationName,
          value: item.DistributionOrganizationName,
          dqId: item.id,
        };
      });

      dqList.value = aa.reduce((uniqueItems, currentItem) => {
        // 如果当前遍历的item的value不在uniqueItems中任何一个item的value里，则添加进uniqueItems
        if (!uniqueItems.some((item) => item.value === currentItem.value)) {
          uniqueItems.push(currentItem);
        }
        return uniqueItems;
      }, []);

      dqList.value.unshift({
        label: "全部",
        value: "全部",
        dqId: 0,
      });
    } catch (e) {
      console.log(e);
    }
  }

  // 获取全部经销区
  async function getAllOutRegion() {
    const res = await getNationalAreas();
    console.log(res, "res");
    try {
      const aa = res.data.result.map((item) => {
        return {
          label: item.DistributionOrganizationName,
          value: item.DistributionOrganizationName,
          jxqId: item.Distribution_territory,
        };
      });

      jxqList.value = aa.reduce((uniqueItems, currentItem) => {
        // 如果当前遍历的item的value不在uniqueItems中任何一个item的value里，则添加进uniqueItems
        if (!uniqueItems.some((item) => item.value === currentItem.value)) {
          uniqueItems.push(currentItem);
        }
        return uniqueItems;
      }, []);

      jxqList.value.unshift({
        label: "全部",
        value: "全部",
        dqId: 0,
      });
      jxqCache.value = jxqList.value;
      console.log(jxqList.value, "jxqList.value");
    } catch (e) {
      console.log(e);
    }
  }

  // 获取全部经销商
  async function getAllJxsUserList() {
    const res = await carMapGetAllJxs();
    try {
      const aa = res.data.result.map((item) => {
        return {
          label: item.jxsName1,
          value: item.jxsName1,
          jxqId: item.jxqId,
          jxsId: item.jxsId,
        };
      });

      jxsUserList.value = aa.reduce((uniqueItems, currentItem) => {
        // 如果当前遍历的item的value不在uniqueItems中任何一个item的value里，则添加进uniqueItems
        if (!uniqueItems.some((item) => item.value === currentItem.value)) {
          uniqueItems.push(currentItem);
        }
        return uniqueItems;
      }, []);

      jxsUserList.value.unshift({
        label: "全部",
        value: "全部",
        jxqId: 0,
        jxsId: 0,
      });
      allJxsList.value = jxsUserList.value;
    } catch (e) {
      console.log(e);
    }
  }

  const dqId = ref("");
  // 选择大区
  async function chooseDq(map, polygon, storeMarker, carMarker, infoWindow, v) {
    selectObj.value.dqId = v.dqId;
    dqId.value = v.dqId;

    carMarker.setGeometries([]);
    infoWindow.close();

    selectObj.value.jxqName = null;
    selectObj.value.jxsName = null;
    selectObj.value.fxsName = null;
    selectObj.value.province = null;
    selectObj.value.currentAreaMessage = null;
    selectObj.value.areaName = "数据";

    // 如果选择全部
    if (v.label === "全部") {
      if (checkedStore.value) {
        if (
          (selectObj.value.productClass === null ||
            selectObj.value.productClass === "") &&
          (selectObj.value.product === null || selectObj.value.product === "")
        ) {
          loading.value = true;
          await getAllStore(storeMarker, infoWindow);
          markStore(storeList.value, storeMarker);
          loading.value = false;
        } else {
          loading.value = true;
          await getProStore(
            storeMarker,
            infoWindow,
            selectObj.value.productClass,
            selectObj.value.product
          );
          markStore(proStoreList.value, storeMarker, true);
          loading.value = false;
        }
      }
      jxqList.value = jxqCache.value;
      jxsUserList.value = allJxsList.value;
      polygon.setGeometries(areaList.value);
    } else {
      // 如果选择大区
      loading.value = true;
      filterAllUsers.value = []; // 清空用户列表
      const res = await getRegionUnderling(v.dqId);
      console.log(res, "dqIdRes");
      try {
        jxqList.value = jxqCache.value.filter((item) => {
          return res.data.result.some((node) => {
            return item.label === node.DistributionOrganizationName;
          });
        });
        jxqList.value.unshift({
          label: "全部",
          value: "全部",
          jxqId: 0,
        });

        const areaCode = provinceMessage.value.filter((item) => {
          return jxqList.value.some((node) => {
            return node.jxqId === item.Distribution_territory;
          });
        });
        selectObj.value.currentAreaMessage = areaCode;
        areaCodeGlobal.value = areaCode;
        if (areaCode.length === 0) return message.info("未找到大区");

        for (const item of areaCode) {
          const res = await get_all_users(item.Distribution_territory);
          filterAllUsers.value = filterAllUsers.value.concat(
            res.data.result[0]
          );
        }
        // 去重
        filterAllUsers.value = Array.from(new Set(filterAllUsers.value));
        const tempUser = ref([]);
        tempUser.value.unshift({
          label: "全部",
          value: "全部",
          jxqId: 0,
          jxsId: 0,
        });
        for (const item of areaCode) {
          allJxsList.value.forEach((node) => {
            if (item.Distribution_territory === node.jxqId) {
              tempUser.value.push(node);
            }
          });
        }
        tempUser.value = Array.from(new Set(tempUser.value));
        jxsUserList.value = tempUser.value; // 选择大区时将该区域的库长也拿到

        filterAreaList.value = [];
        areaCode.forEach((item_s) => {
          const a = areaList.value.filter((item) => item.styleId === item_s.id);
          filterAreaList.value = filterAreaList.value.concat(a);
        });
        polygon.setGeometries(filterAreaList.value);
        loading.value = false;
      } catch (e) {
        console.log(e);
        loading.value = false;
      }

      // 打开门店开关
      if (checkedStore.value) {
        if (
          (selectObj.value.productClass === null ||
            selectObj.value.productClass === "") &&
          (selectObj.value.product === null || selectObj.value.product === "")
        ) {
          loading.value = true;
          await getAllStore(storeMarker, infoWindow);
          getAreaProStore(areaCodeGlobal.value, storeList.value, (res) => {
            filterStoreList.value = res;
          });
          markStore(filterStoreList.value, storeMarker);
          loading.value = false;
        } else {
          loading.value = true;
          await getProStore(
            storeMarker,
            infoWindow,
            selectObj.value.productClass,
            selectObj.value.product
          );
          getAreaProStore(areaCodeGlobal.value, proStoreList.value, (res) => {
            proFilterStoreList.value = res;
          });
          markStore(proFilterStoreList.value, storeMarker, true);
          loading.value = false;
        }
      }
    }
  }

  const getAreaProStore = (code, store, callback) => {
    let filterS = [];

    code.forEach((it) => {
      const a = store.filter(
        (item) =>
          item.Store_province === it.Provincial_name &&
          item.Store_city === it.City_name &&
          item.Store_district === it.District_name
      );
      filterS = [...filterS, ...a];
    });
    filterAllUsers.value.forEach((it) => {
      const a = store.filter((item) => {
        it.id === item.Cyclist_name;
      });
      filterS = [...filterS, ...a];
    });
    callback(filterS);
  };

  // 标记门店
  const markStore = (store, instance, isPro = false) => {
    if (!isPro) {
      const { points, styles } = processStoreStyle(store);
      instance.setStyles(styles);
      instance.setGeometries(points);
    } else {
      const { points, styles } = processProStoreStyle(store);
      instance.setStyles(styles);
      instance.setGeometries(points);
    }
  };
  // 选择经销区
  // async function chooseJxq(
  //   map,
  //   polygon,
  //   storeMarker,
  //   carMarker,
  //   infoWindow,
  //   v
  // ) {
  //   try {
  //     carMarker.setGeometries([]);
  //     infoWindow.close();
  //     const jxsMatchList = ref([]);

  //     jxsMatchList.value.unshift({
  //       label: "全部",
  //       value: "全部",
  //       jxqId: 0,
  //       jxsId: 0,
  //     });
  //     allJxsList.value.forEach((item) => {
  //       if (item.jxqId === v.jxqId) {
  //         jxsMatchList.value.push(item);
  //       }
  //       selectObj.value.jxsName = jxsMatchList.value[0];
  //     });
  //     userCache.value = jxsMatchList.value;

  //     jxsUserList.value = jxsMatchList.value;
  //     selectObj.value.fxsName = null;
  //     selectObj.value.province = null;
  //     // 经销区选择全部时执行的逻辑
  //     if (v.label === "全部") {
  //       if (selectObj.value.dqName !== "全部") {
  //         const obj = {
  //           dqId: dqId.value,
  //           label: selectObj.value.dqName,
  //         };

  //         await chooseDq(map, polygon, storeMarker, carMarker, infoWindow, obj);
  //         return;
  //       } else {
  //         selectObj.value.jxsName = "全部";
  //         selectObj.value.currentAreaMessage = null;
  //         filterAreaList.value = areaList.value;
  //         filterStoreList.value = storeList.value;
  //         filterAllUsers.value = allUsers.value;
  //         selectObj.value.areaName = "数据";
  //         polygon.setGeometries(areaList.value);
  //         // 如果产品类别和产品都为空
  //         if (
  //           (selectObj.value.productClass === null ||
  //             selectObj.value.productClass === "") &&
  //           (selectObj.value.product === null || selectObj.value.product === "")
  //         ) {
  //           // 如果门店开关打开
  //           if (checkedStore.value) {
  //             loading.value = true;
  //             await getAllStore(storeMarker, infoWindow);
  //             const { points, styles } = processStoreStyle(storeList.value);
  //             storeMarker.setStyles(styles);
  //             storeMarker.setGeometries(points);
  //             loading.value = false;
  //           }
  //         } else {
  //           // 如果产品类别和产品不为空并且门店开关打开
  //           if (checkedStore.value) {
  //             loading.value = true;
  //             await getProStore(
  //               storeMarker,
  //               infoWindow,
  //               selectObj.value.productClass,
  //               selectObj.value.product
  //             );
  //             const { points, styles } = processProStoreStyle(
  //               proStoreList.value
  //             );
  //             storeMarker.setStyles(styles);
  //             storeMarker.setGeometries(points);
  //             loading.value = false;
  //           }
  //         }
  //         if (checkedCar.value) {
  //           const _carPoints = processVehicleStyle(
  //             carList.value,
  //             allUsers.value
  //           );
  //           carMarker.setStyles(_carPoints.styles);
  //           carMarker.setGeometries(_carPoints.points);
  //         }

  //         return;
  //       }
  //     }

  //     const areaCode = provinceMessage.value.filter(
  //       (item) => item.Distribution_territory === v.jxqId
  //     );
  //     areaCodeGlobal.value = areaCode;
  //     console.log("v.jxqId", v.jxqId);
  //     console.log("areaCode", areaCode);
  //     if (areaCode.length === 0) return message.info("未找到经销区");
  //     // 获取分销商
  //     const res = await get_all_users(areaCode[0].Distribution_territory);
  //     filterAllUsers.value = res.data.result[0];

  //     fxsUserList.value = res.data.result[0].map((item) => {
  //       return {
  //         label: item.Master_data_person_name,
  //         value: item.Master_data_person_name,
  //         IMEI_code: item.IMEI_code,
  //         Licence_number: item.Licence_number,
  //         id: item.id,
  //       };
  //     });
  //     fxsUserList.value.unshift({
  //       label: "全部",
  //       value: "全部",
  //       IMEI_code: 0,
  //       Licence_number: 0,
  //       id: 0,
  //     });

  //     selectObj.value.currentAreaMessage = areaCode[0];
  //     console.log(
  //       "selectObj.value.currentAreaMessage",
  //       selectObj.value.currentAreaMessage
  //     );

  //     selectObj.value.areaName = areaCode[0].DistributionOrganizationName;
  //     // 过滤区域
  //     filterAreaList.value = [];
  //     areaCode.forEach((item_s) => {
  //       const a = areaList.value.filter((item) => item.styleId === item_s.id);
  //       filterAreaList.value = filterAreaList.value.concat(a);
  //       console.log("areaList.value", areaList.value);
  //     });
  //     polygon.setGeometries(filterAreaList.value);

  //     // 过滤门店
  //     if (
  //       (selectObj.value.productClass === null ||
  //         selectObj.value.productClass === "") &&
  //       (selectObj.value.product === null || selectObj.value.product === "")
  //     ) {
  //       let filterS = [];
  //       areaCode.forEach((it) => {
  //         const a = storeList.value.filter(
  //           (item) =>
  //             item.Store_province === it.Provincial_name &&
  //             item.Store_city === it.City_name &&
  //             item.Store_district === it.District_name
  //         );
  //         filterS = [...filterS, ...a];
  //       });

  //       filterAllUsers.value.forEach((it) => {
  //         const a = storeList.value.filter((item) => {
  //           it.id === item.Cyclist_name;
  //         });
  //         filterS = [...filterS, ...a];
  //       });
  //       filterStoreList.value = filterS;

  //       if (checkedStore.value) {
  //         console.log("filterStoreList.valueJXQ", filterStoreList.value);

  //         const { points, styles } = processStoreStyle(filterStoreList.value);
  //         storeMarker.setStyles(styles);
  //         storeMarker.setGeometries(points);
  //       }
  //     } else {
  //       let filterS = [];
  //       areaCode.forEach((it) => {
  //         const a = proStoreList.value.filter(
  //           (item) =>
  //             item.Store_province === it.Provincial_name &&
  //             item.Store_city === it.City_name &&
  //             item.Store_district === it.District_name
  //         );
  //         filterS = [...filterS, ...a];
  //       });
  //       filterAllUsers.value.forEach((it) => {
  //         const a = proStoreList.value.filter((item) => {
  //           it.id === item.Cyclist_name;
  //         });
  //         filterS = [...filterS, ...a];
  //       });
  //       proFilterStoreList.value = filterS;
  //       if (checkedStore.value) {
  //         const { points, styles } = processProStoreStyle(
  //           proFilterStoreList.value
  //         );
  //         storeMarker.setStyles(styles);
  //         storeMarker.setGeometries(points);
  //       }
  //     }

  //     //过滤车辆
  //     if (checkedCar.value) {
  //       const imei_list = filterAllUsers.value
  //         .filter((item) => item.IMEI_code !== null)
  //         .map((item) => {
  //           return "0" + item.IMEI_code;
  //         })
  //         .join(",");
  //       const res = await get_update_cars(imei_list);
  //       carList.value = res.data.result;

  //       const _carPoints = processVehicleStyle(
  //         carList.value,
  //         filterAllUsers.value
  //       );
  //       carMarker.setStyles(_carPoints.styles);
  //       carMarker.setGeometries(_carPoints.points);
  //     }
  //     map.setCenter(
  //       new TMap.LatLng(
  //         areaCode[0].Resident_latitude,
  //         areaCode[0].Station_longitude
  //       )
  //     );
  //     map.setZoom(10);
  //   } catch (err) {
  //     console.log(err);
  //   }
  // }
  async function chooseJxq(
    map,
    polygon,
    storeMarker,
    carMarker,
    infoWindow,
    v
  ) {
    try {
      carMarker.setGeometries([]);
      infoWindow.close();
      const jxsMatchList = ref([]);

      jxsMatchList.value.unshift({
        label: "全部",
        value: "全部",
        jxqId: 0,
        jxsId: 0,
      });
      allJxsList.value.forEach((item) => {
        if (item.jxqId === v.jxqId) {
          jxsMatchList.value.push(item);
        }
        selectObj.value.jxsName = jxsMatchList.value[0];
      });
      userCache.value = jxsMatchList.value;

      jxsUserList.value = jxsMatchList.value;
      selectObj.value.fxsName = null;
      selectObj.value.province = null;
      // 经销区选择全部时执行的逻辑
      if (v.label === "全部") {
        if (selectObj.value.dqName !== "全部") {
          const obj = {
            dqId: dqId.value,
            label: selectObj.value.dqName,
          };

          await chooseDq(map, polygon, storeMarker, carMarker, infoWindow, obj);
          return;
        } else {
          selectObj.value.jxsName = "全部";
          selectObj.value.currentAreaMessage = null;
          filterAreaList.value = areaList.value;
          filterStoreList.value = storeList.value;
          filterAllUsers.value = allUsers.value;
          selectObj.value.areaName = "数据";
          polygon.setGeometries(areaList.value);
          // 如果产品类别和产品都为空
          if (checkedStore.value) {
            if (
              (selectObj.value.productClass === null ||
                selectObj.value.productClass === "") &&
              (selectObj.value.product === null ||
                selectObj.value.product === "")
            ) {
              loading.value = true;
              await getAllStore(storeMarker, infoWindow);
              markStore(storeList.value, storeMarker);
              loading.value = false;
            } else {
              loading.value = true;
              await getProStore(
                storeMarker,
                infoWindow,
                selectObj.value.productClass,
                selectObj.value.product
              );
              markStore(proStoreList.value, storeMarker, true);
              loading.value = false;
            }
          }

          if (checkedCar.value) {
            const _carPoints = processVehicleStyle(
              carList.value,
              allUsers.value
            );
            carMarker.setStyles(_carPoints.styles);
            carMarker.setGeometries(_carPoints.points);
          }

          return;
        }
      }

      const areaCode = provinceMessage.value.filter(
        (item) => item.Distribution_territory === v.jxqId
      );
      areaCodeGlobal.value = areaCode;
      console.log("v.jxqId", v.jxqId);
      console.log("areaCode", areaCode);
      if (areaCode.length === 0) return message.info("未找到经销区");
      // 获取分销商
      const res = await get_all_users(areaCode[0].Distribution_territory);
      filterAllUsers.value = res.data.result[0];

      fxsUserList.value = res.data.result[0].map((item) => {
        return {
          label: item.Master_data_person_name,
          value: item.Master_data_person_name,
          IMEI_code: item.IMEI_code,
          Licence_number: item.Licence_number,
          id: item.id,
        };
      });
      fxsUserList.value.unshift({
        label: "全部",
        value: "全部",
        IMEI_code: 0,
        Licence_number: 0,
        id: 0,
      });

      selectObj.value.currentAreaMessage = areaCode[0];
      selectObj.value.areaName = areaCode[0].DistributionOrganizationName;
      selectObj.value.jxqName = areaCode[0].DistributionOrganizationName;
      // 过滤区域
      filterAreaList.value = [];
      areaCode.forEach((item_s) => {
        const a = areaList.value.filter((item) => item.styleId === item_s.id);
        filterAreaList.value = filterAreaList.value.concat(a);
      });
      polygon.setGeometries(filterAreaList.value);

      // 过滤门店
      if (checkedStore.value) {
        if (
          (selectObj.value.productClass === null ||
            selectObj.value.productClass === "") &&
          (selectObj.value.product === null || selectObj.value.product === "")
        ) {
          loading.value = true;
          await getAllStore(storeMarker, infoWindow);
          getAreaProStore(areaCode, storeList.value, (res) => {
            filterStoreList.value = res;
          });
          markStore(filterStoreList.value, storeMarker);
          loading.value = false;
        } else {
          loading.value = true;
          await getProStore(
            storeMarker,
            infoWindow,
            selectObj.value.productClass,
            selectObj.value.product
          );
          getAreaProStore(areaCode, proStoreList.value, (res) => {
            proFilterStoreList.value = res;
          });
          markStore(proFilterStoreList.value, storeMarker);
          loading.value = false;
        }
      }

      //过滤车辆
      if (checkedCar.value) {
        const imei_list = filterAllUsers.value
          .filter((item) => item.IMEI_code !== null)
          .map((item) => {
            return "0" + item.IMEI_code;
          })
          .join(",");
        const res = await get_update_cars(imei_list);
        carList.value = res.data.result;

        const _carPoints = processVehicleStyle(
          carList.value,
          filterAllUsers.value
        );
        carMarker.setStyles(_carPoints.styles);
        carMarker.setGeometries(_carPoints.points);
      }
      map.setCenter(
        new TMap.LatLng(
          areaCode[0].Resident_latitude,
          areaCode[0].Station_longitude
        )
      );
      map.setZoom(10);
    } catch (err) {
      console.log(err);
    }
  }

  // 选择经销商(库长)
  // async function chooseJxs(
  //   map,
  //   polygon,
  //   storeMarker,
  //   carMarker,
  //   infoWindow,
  //   v
  // ) {
  //   try {
  //     carMarker.setGeometries([]);
  //     infoWindow.close();
  //     jxqList.value.forEach((item) => {
  //       if (v.label === "全部") {
  //         return;
  //       }
  //       if (item.jxqId === v.jxqId) {
  //         selectObj.value.jxqName = item.value;
  //       }
  //     });
  //     selectObj.value.fxsName = null;
  //     selectObj.value.province = null;
  //     if (v.label === "全部") {
  //       loading.value = true;
  //       console.log(selectObj.value.jxqName, "selectObj.value.jxqName");

  //       if (selectObj.value.jxqName !== "全部") {
  //         console.log(v, "v");

  //         loading.value = false;
  //         return;
  //       } else {
  //         console.log("执行了下面");

  //         await getAllJxsUserList();
  //         loading.value = false;
  //         // selectObj.value.jxqName = "全部";
  //         selectObj.value.currentAreaMessage = null;
  //         filterAreaList.value = areaList.value;
  //         filterStoreList.value = storeList.value;
  //         filterAllUsers.value = allUsers.value;
  //         selectObj.value.areaName = "数据";
  //         polygon.setGeometries(areaList.value);
  //         if (
  //           (selectObj.value.productClass === null ||
  //             selectObj.value.productClass === "") &&
  //           (selectObj.value.product === null || selectObj.value.product === "")
  //         ) {
  //           if (checkedStore.value) {
  //             loading.value = true;
  //             await getAllStore(storeMarker, infoWindow);
  //             const { points, styles } = processStoreStyle(storeList.value);
  //             storeMarker.setStyles(styles);
  //             storeMarker.setGeometries(points);
  //             loading.value = false;
  //           }
  //         } else {
  //           if (checkedStore.value) {
  //             loading.value = true;
  //             await getProStore(
  //               storeMarker,
  //               infoWindow,
  //               selectObj.value.productClass,
  //               selectObj.value.product
  //             );
  //             const { points, styles } = processProStoreStyle(
  //               proStoreList.value
  //             );
  //             storeMarker.setStyles(styles);
  //             storeMarker.setGeometries(points);
  //             loading.value = false;
  //           }
  //         }
  //         if (checkedCar.value) {
  //           const _carPoints = processVehicleStyle(
  //             carList.value,
  //             allUsers.value
  //           );
  //           carMarker.setStyles(_carPoints.styles);
  //           carMarker.setGeometries(_carPoints.points);
  //         }
  //         return;
  //       }
  //     }

  //     const areaCode = provinceMessage.value.filter(
  //       (item) => item.Distribution_territory === v.jxqId
  //     );
  //     areaCodeGlobal.value = areaCode;
  //     if (areaCode.length === 0) return message.info("未找到经销区");
  //     // 获取分销商
  //     const res = await get_all_users(areaCode[0].Distribution_territory);
  //     filterAllUsers.value = res.data.result[0];
  //     fxsUserList.value = res.data.result[0].map((item) => {
  //       return {
  //         label: item.Master_data_person_name,
  //         value: item.Master_data_person_name,
  //         IMEI_code: item.IMEI_code,
  //         Licence_number: item.Licence_number,
  //         id: item.id,
  //       };
  //     });
  //     fxsUserList.value.unshift({
  //       label: "全部",
  //       value: "全部",
  //       IMEI_code: 0,
  //       Licence_number: 0,
  //       id: 0,
  //     });

  //     selectObj.value.currentAreaMessage = areaCode[0];
  //     selectObj.value.areaName = areaCode[0].DistributionOrganizationName;
  //     // 过滤区域
  //     filterAreaList.value = [];
  //     areaCode.forEach((item_s) => {
  //       const a = areaList.value.filter((item) => item.styleId === item_s.id);
  //       filterAreaList.value = filterAreaList.value.concat(a);
  //     });
  //     polygon.setGeometries(filterAreaList.value);
  //     // 过滤门店

  //     if (
  //       (selectObj.value.productClass === null ||
  //         selectObj.value.productClass === "") &&
  //       (selectObj.value.product === null || selectObj.value.product === "")
  //     ) {
  //       let filterS = [];
  //       // areaCode.forEach((it) => {
  //       //   const a = storeList.value.filter(
  //       //     (item) =>
  //       //       item.Store_province === it.Provincial_name &&
  //       //       item.Store_city === it.City_name &&
  //       //       item.Store_district === it.District_name
  //       //   );
  //       //   filterS = [...filterS, ...a];
  //       // });

  //       // filterAllUsers.value.forEach((it) => {
  //       //   const a = storeList.value.filter((item) => {
  //       //     it.id === item.Cyclist_name;
  //       //   });
  //       //   filterS = [...filterS, ...a];
  //       // });
  //       let dqId = "";
  //       dqList.value.forEach((item) => {
  //         if (selectObj.value.dqName === "全部") {
  //           dqId = "";
  //         } else if (item.label === selectObj.value.dqName) {
  //           dqId = item.dqId;
  //         }
  //       });
  //       let res = await getAllStores(v.jxqId, dqId);

  //       if (res.data.result[0][0].exist_flag === "0") {
  //         const res1 = await get_cache("", "");
  //         let res2 = await get_work(res1.data.data.task_iid_iiids);
  //         let num = 0;
  //         while (res2.data.data.status !== "success") {
  //           res2 = await get_work(res1.data.data.task_iid_iiids);
  //           await delay(1000);
  //           num++;
  //           if (num > 60) {
  //             loading.value = false;
  //             return;
  //           }
  //         }
  //         const res = await getAllStores(v.jxqId, dqId);

  //         storeList.value = storeList.value.concat(
  //           JSON.parse(JSON.parse(res.data.result[0][0].result))
  //         );
  //         loading.value = false;
  //       } else {
  //         storeList.value = storeList.value.concat(
  //           JSON.parse(JSON.parse(res.data.result[0][0].result))
  //         );
  //         loading.value = false;
  //       }
  //       // filterStoreList.value = filterS;

  //       // if (checkedStore.value) {
  //       //   const { points, styles } = processStoreStyle(filterStoreList.value);
  //       //   storeMarker.setStyles(styles);
  //       //   storeMarker.setGeometries(points);
  //       // }
  //     } else {
  //       let filterS = [];
  //       areaCode.forEach((it) => {
  //         const a = proStoreList.value.filter(
  //           (item) =>
  //             item.Store_province === it.Provincial_name &&
  //             item.Store_city === it.City_name &&
  //             item.Store_district === it.District_name
  //         );
  //         filterS = [...filterS, ...a];
  //       });
  //       filterAllUsers.value.forEach((it) => {
  //         const a = proStoreList.value.filter((item) => {
  //           it.id === item.Cyclist_name;
  //         });
  //         filterS = [...filterS, ...a];
  //       });
  //       proFilterStoreList.value = filterS;
  //       if (checkedStore.value) {
  //         const { points, styles } = processProStoreStyle(
  //           proFilterStoreList.value
  //         );
  //         storeMarker.setStyles(styles);
  //         storeMarker.setGeometries(points);
  //       }
  //     }

  //     //过滤车辆
  //     if (checkedCar.value) {
  //       const imei_list = filterAllUsers.value
  //         .filter((item) => item.IMEI_code !== null)
  //         .map((item) => {
  //           return "0" + item.IMEI_code;
  //         })
  //         .join(",");
  //       const res = await get_update_cars(imei_list);
  //       carList.value = res.data.result;

  //       const _carPoints = processVehicleStyle(
  //         carList.value,
  //         filterAllUsers.value
  //       );
  //       carMarker.setStyles(_carPoints.styles);
  //       carMarker.setGeometries(_carPoints.points);
  //     }
  //     map.setCenter(
  //       new TMap.LatLng(
  //         areaCode[0].Resident_latitude,
  //         areaCode[0].Station_longitude
  //       )
  //     );
  //     map.setZoom(10);
  //   } catch (err) {
  //     console.log(err);
  //   }
  // }
  async function chooseJxs(
    map,
    polygon,
    storeMarker,
    carMarker,
    infoWindow,
    v
  ) {
    try {
      carMarker.setGeometries([]);
      infoWindow.close();
      jxqList.value.forEach((item) => {
        if (v.label === "全部") {
          return;
        }
        if (item.jxqId === v.jxqId) {
          selectObj.value.jxqName = item.value;
        }
      });
      selectObj.value.fxsName = null;
      selectObj.value.province = null;
      if (v.label === "全部") {
        loading.value = true;
        if (selectObj.value.jxqName !== "全部") {
          loading.value = false;

          return;
        } else {
          await getAllJxsUserList();
          loading.value = false;
          // selectObj.value.jxqName = "全部";
          selectObj.value.currentAreaMessage = null;
          filterAreaList.value = areaList.value;
          filterStoreList.value = storeList.value;
          filterAllUsers.value = allUsers.value;
          selectObj.value.areaName = "数据";
          polygon.setGeometries(areaList.value);
          if (checkedStore.value) {
            if (
              (selectObj.value.productClass === null ||
                selectObj.value.productClass === "") &&
              (selectObj.value.product === null ||
                selectObj.value.product === "")
            ) {
              loading.value = true;
              await getAllStore(storeMarker, infoWindow);
              markStore(storeList.value, storeMarker);
              loading.value = false;
            } else {
              loading.value = true;
              await getProStore(
                storeMarker,
                infoWindow,
                selectObj.value.productClass,
                selectObj.value.product
              );
              markStore(proStoreList.value, storeMarker, true);
              loading.value = false;
            }
          }

          if (checkedCar.value) {
            const _carPoints = processVehicleStyle(
              carList.value,
              allUsers.value
            );
            carMarker.setStyles(_carPoints.styles);
            carMarker.setGeometries(_carPoints.points);
          }
          return;
        }
      }

      const areaCode = provinceMessage.value.filter(
        (item) => item.Distribution_territory === v.jxqId
      );
      areaCodeGlobal.value = areaCode;
      if (areaCode.length === 0) return message.info("未找到经销区");
      // 获取分销商
      const res = await get_all_users(areaCode[0].Distribution_territory);
      filterAllUsers.value = res.data.result[0];
      fxsUserList.value = res.data.result[0].map((item) => {
        return {
          label: item.Master_data_person_name,
          value: item.Master_data_person_name,
          IMEI_code: item.IMEI_code,
          Licence_number: item.Licence_number,
          id: item.id,
        };
      });
      fxsUserList.value.unshift({
        label: "全部",
        value: "全部",
        IMEI_code: 0,
        Licence_number: 0,
        id: 0,
      });

      selectObj.value.currentAreaMessage = areaCode[0];
      selectObj.value.areaName = areaCode[0].DistributionOrganizationName;
      // 过滤区域
      filterAreaList.value = [];
      areaCode.forEach((item_s) => {
        const a = areaList.value.filter((item) => item.styleId === item_s.id);
        filterAreaList.value = filterAreaList.value.concat(a);
      });
      polygon.setGeometries(filterAreaList.value);
      // 过滤门店

      if (checkedStore.value) {
        if (
          (selectObj.value.productClass === null ||
            selectObj.value.productClass === "") &&
          (selectObj.value.product === null || selectObj.value.product === "")
        ) {
          loading.value = true;
          await getAllStore(storeMarker, infoWindow);
          getAreaProStore(areaCode, storeList.value, (res) => {
            filterStoreList.value = res;
          });
          markStore(storeList.value, storeMarker);
          loading.value = false;
        } else {
          loading.value = true;
          await getProStore(
            storeMarker,
            infoWindow,
            selectObj.value.productClass,
            selectObj.value.product
          );
          getAreaProStore(areaCode, proStoreList.value, (res) => {
            proFilterStoreList.value = res;
          });
          markStore(proFilterStoreList.value, storeMarker, true);
          loading.value = false;
        }
      }

      //过滤车辆
      if (checkedCar.value) {
        const imei_list = filterAllUsers.value
          .filter((item) => item.IMEI_code !== null)
          .map((item) => {
            return "0" + item.IMEI_code;
          })
          .join(",");
        const res = await get_update_cars(imei_list);
        carList.value = res.data.result;

        const _carPoints = processVehicleStyle(
          carList.value,
          filterAllUsers.value
        );
        carMarker.setStyles(_carPoints.styles);
        carMarker.setGeometries(_carPoints.points);
      }
      map.setCenter(
        new TMap.LatLng(
          areaCode[0].Resident_latitude,
          areaCode[0].Station_longitude
        )
      );
      map.setZoom(10);
    } catch (err) {
      console.log(err);
    }
  }

  //选择分销商
  // async function chooseFxs(storeMarker, infoWindow, carMarker, v) {
  //   try {
  //     infoWindow.close();
  //     const res = await get_all_users(
  //       selectObj.value.currentAreaMessage.Distribution_territory
  //     );
  //     filterAllUsers.value = res.data.result[0];
  //     if (v.label === "全部") {
  //       if (
  //         (selectObj.value.productClass === null ||
  //           selectObj.value.productClass === "") &&
  //         (selectObj.value.product === null || selectObj.value.product === "")
  //       ) {
  //         let filterS = [];
  //         areaCodeGlobal.value.forEach((it) => {
  //           const a = storeList.value.filter(
  //             (item) =>
  //               item.Store_province === it.Provincial_name &&
  //               item.Store_city === it.City_name &&
  //               item.Store_district === it.District_name
  //           );
  //           filterS = [...filterS, ...a];
  //         });
  //         filterAllUsers.value.forEach((it) => {
  //           const a = storeList.value.filter((item) => {
  //             it.id === item.Cyclist_name;
  //           });
  //           filterS = [...filterS, ...a];
  //         });
  //         filterStoreList.value = filterS;
  //         if (checkedStore.value) {
  //           const { points, styles } = processStoreStyle(filterStoreList.value);
  //           storeMarker.setStyles(styles);
  //           storeMarker.setGeometries(points);
  //         }
  //       } else {
  //         let filterS = [];
  //         areaCodeGlobal.value.forEach((it) => {
  //           const a = proStoreList.value.filter(
  //             (item) =>
  //               item.Store_province === it.Provincial_name &&
  //               item.Store_city === it.City_name &&
  //               item.Store_district === it.District_name
  //           );
  //           filterS = [...filterS, ...a];
  //         });
  //         filterAllUsers.value.forEach((it) => {
  //           const a = proStoreList.value.filter((item) => {
  //             it.id === item.Cyclist_name;
  //           });
  //           filterS = [...filterS, ...a];
  //         });
  //         proFilterStoreList.value = filterS;
  //         if (checkedStore.value) {
  //           const { points, styles } = processProStoreStyle(
  //             proFilterStoreList.value
  //           );
  //           storeMarker.setStyles(styles);
  //           storeMarker.setGeometries(points);
  //         }
  //       }

  //       if (checkedCar.value) {
  //         const _carPoints = processVehicleStyle(
  //           carList.value,
  //           filterAllUsers.value
  //         );
  //         carMarker.setStyles(_carPoints.styles);
  //         carMarker.setGeometries(_carPoints.points);
  //       }
  //       return;
  //     }

  //     if (
  //       (selectObj.value.productClass === null ||
  //         selectObj.value.productClass === "") &&
  //       (selectObj.value.product === null || selectObj.value.product === "")
  //     ) {
  //       const filter = storeList.value.filter(
  //         (item) => item.Cyclist_name === v.id
  //       );
  //       filterStoreList.value = filter;
  //       if (checkedStore.value) {
  //         const { points, styles } = processStoreStyle(filterStoreList.value);
  //         storeMarker.setStyles(styles);
  //         storeMarker.setGeometries(points);
  //       }
  //     } else {
  //       const filter = proStoreList.value.filter(
  //         (item) => item.Cyclist_name === v.id
  //       );
  //       console.log(filter, "filter1");
  //       proFilterStoreList.value = filter;
  //       if (checkedStore.value) {
  //         const { points, styles } = processProStoreStyle(
  //           proFilterStoreList.value
  //         );
  //         storeMarker.setStyles(styles);
  //         storeMarker.setGeometries(points);
  //       }
  //     }

  //     filterAllUsers.value = allUsers.value.filter(
  //       (item) => item.IMEI_code === v.IMEI_code
  //     );
  //     if (checkedCar.value) {
  //       const _carPoints = processVehicleStyle(
  //         carList.value,
  //         filterAllUsers.value
  //       );
  //       carMarker.setStyles(_carPoints.styles);
  //       carMarker.setGeometries(_carPoints.points);
  //     }
  //   } catch (error) {
  //     console.log(error);
  //   }
  // }
  async function chooseFxs(storeMarker, infoWindow, carMarker, v) {
    try {
      infoWindow.close();
      const res = await get_all_users(
        selectObj.value.currentAreaMessage.Distribution_territory
      );
      filterAllUsers.value = res.data.result[0];
      if (v.label === "全部") {
        if (checkedStore.value) {
          if (
            (selectObj.value.productClass === null ||
              selectObj.value.productClass === "") &&
            (selectObj.value.product === null || selectObj.value.product === "")
          ) {
            getAreaProStore(areaCodeGlobal.value, storeList.value, (res) => {
              filterStoreList.value = res;
            });
            markStore(filterStoreList.value, storeMarker);
          } else {
            getAreaProStore(areaCodeGlobal.value, proStoreList.value, (res) => {
              proFilterStoreList.value = res;
            });
            markStore(proFilterStoreList.value, storeMarker, true);
          }
        }

        if (checkedCar.value) {
          const _carPoints = processVehicleStyle(
            carList.value,
            filterAllUsers.value
          );
          carMarker.setStyles(_carPoints.styles);
          carMarker.setGeometries(_carPoints.points);
        }
        return;
      }
      if (checkedStore.value) {
        if (
          (selectObj.value.productClass === null ||
            selectObj.value.productClass === "") &&
          (selectObj.value.product === null || selectObj.value.product === "")
        ) {
          const filter = storeList.value.filter(
            (item) => item.Cyclist_name === v.id
          );
          filterStoreList.value = filter;
          markStore(filterStoreList.value, storeMarker);
        } else {
          const filter = proStoreList.value.filter(
            (item) => item.Cyclist_name === v.id
          );
          proFilterStoreList.value = filter;
          markStore(proFilterStoreList.value, storeMarker, true);
        }
      }

      filterAllUsers.value = allUsers.value.filter(
        (item) => item.IMEI_code === v.IMEI_code
      );
      if (checkedCar.value) {
        const _carPoints = processVehicleStyle(
          carList.value,
          filterAllUsers.value
        );
        carMarker.setStyles(_carPoints.styles);
        carMarker.setGeometries(_carPoints.points);
      }
    } catch (error) {
      console.log(error);
    }
  }

  // 选择产品品类
  // async function chooseProType(storeMarker, infoWindow, v) {
  //   try {
  //     infoWindow.close();
  //     selectObj.value.fxsName = null;
  //     selectObj.value.province = null;
  //     if (v.label === "全部") {
  //       selectObj.value.areaName = "数据";
  //       if (checkedStore.value) {
  //         if (selectObj.value.currentAreaMessage) {
  //           loading.value = true;
  //           await getAllStore(storeMarker, infoWindow);
  //           loading.value = false;
  //           let filterS = [];

  //           areaCodeGlobal.value.forEach((it) => {
  //             const a = storeList.value.filter(
  //               (item) =>
  //                 item.Store_province === it.Provincial_name &&
  //                 item.Store_city === it.City_name &&
  //                 item.Store_district === it.District_name
  //             );
  //             filterS = [...filterS, ...a];
  //           });
  //           filterAllUsers.value.forEach((it) => {
  //             const a = storeList.value.filter((item) => {
  //               it.id === item.Cyclist_name;
  //             });
  //             filterS = [...filterS, ...a];
  //           });
  //           filterStoreList.value = filterS;
  //           const { points, styles } = processStoreStyle(filterStoreList.value);
  //           storeMarker.setStyles(styles);
  //           storeMarker.setGeometries(points);
  //           loading.value = false;
  //         } else {
  //           loading.value = true;
  //           await getAllStore(storeMarker, infoWindow);
  //           loading.value = false;
  //           const { points, styles } = processStoreStyle(storeList.value);
  //           storeMarker.setStyles(styles);
  //           storeMarker.setGeometries(points);
  //         }
  //       }

  //       return;
  //     }
  //     if (checkedStore.value) {
  //       if (selectObj.value.currentAreaMessage) {
  //         loading.value = true;
  //         await getProStore(storeMarker, infoWindow, v.code, "");
  //         let filterS = [];
  //         areaCodeGlobal.value.forEach((it) => {
  //           const a = proStoreList.value.filter(
  //             (item) =>
  //               item.Store_province === it.Provincial_name &&
  //               item.Store_city === it.City_name &&
  //               item.Store_district === it.District_name
  //           );
  //           filterS = [...filterS, ...a];
  //         });
  //         filterAllUsers.value.forEach((it) => {
  //           const a = proStoreList.value.filter((item) => {
  //             it.id === item.Cyclist_name;
  //           });
  //           filterS = [...filterS, ...a];
  //         });
  //         console.log(filterS, "filters");
  //         filterStoreList.value = filterS;
  //         const { points, styles } = processProStoreStyle(
  //           filterStoreList.value
  //         );
  //         storeMarker.setStyles(styles);
  //         storeMarker.setGeometries(points);
  //         loading.value = false;
  //       } else {
  //         loading.value = true;
  //         await getProStore(storeMarker, infoWindow, v.code, "");
  //         const { points, styles } = processProStoreStyle(proStoreList.value);
  //         storeMarker.setStyles(styles);
  //         storeMarker.setGeometries(points);
  //       }
  //     }
  //     console.log("v.code", v.code);
  //   } catch (err) {
  //     console.log(err);
  //   }
  // }
  async function chooseProType(storeMarker, infoWindow, v) {
    try {
      infoWindow.close();
      selectObj.value.fxsName = null;
      selectObj.value.province = null;
      if (v.label === "全部") {
        selectObj.value.areaName = "数据";
        if (checkedStore.value) {
          if (selectObj.value.currentAreaMessage) {
            loading.value = true;
            await getAllStore(storeMarker, infoWindow);
            getAreaProStore(areaCodeGlobal.value, storeList.value, (res) => {
              filterStoreList.value = res;
            });
            markStore(filterStoreList.value, storeMarker);
            loading.value = false;
          } else {
            loading.value = true;
            await getAllStore(storeMarker, infoWindow);
            loading.value = false;
            markStore(storeList.value, storeMarker);
          }
        }
        return;
      }
      if (checkedStore.value) {
        if (selectObj.value.currentAreaMessage) {
          loading.value = true;
          await getProStore(storeMarker, infoWindow, v.code, "");
          getAreaProStore(areaCodeGlobal.value, proStoreList.value, (res) => {
            proFilterStoreList.value = res;
          });
          markStore(proFilterStoreList.value, storeMarker, true);
          loading.value = false;
        } else {
          loading.value = true;
          await getProStore(storeMarker, infoWindow, v.code, "");
          markStore(proStoreList.value, storeMarker, true);
        }
      }
    } catch (err) {
      console.log(err);
    }
  }

  //选择产品
  async function choosePro(storeMarker, infoWindow, productClass, v) {
    try {
      infoWindow.close();
      selectObj.value.fxsName = null;
      selectObj.value.province = null;
      if (v.label === "全部") {
        selectObj.value.areaName = "数据";
        if (checkedStore.value) {
          if (selectObj.value.currentAreaMessage) {
            loading.value = true;
            await getProStore(
              storeMarker,
              infoWindow,
              selectObj.value.productClass,
              ""
            );
            let filterS = [];
            areaCodeGlobal.value.forEach((it) => {
              const a = proStoreList.value.filter(
                (item) =>
                  item.Store_province === it.Provincial_name &&
                  item.Store_city === it.City_name &&
                  item.Store_district === it.District_name
              );
              filterS = [...filterS, ...a];
            });
            filterAllUsers.value.forEach((it) => {
              const a = proStoreList.value.filter((item) => {
                it.id === item.Cyclist_name;
              });
              filterS = [...filterS, ...a];
            });
            filterStoreList.value = filterS;
            const { points, styles } = processProStoreStyle(
              filterStoreList.value
            );
            storeMarker.setStyles(styles);
            storeMarker.setGeometries(points);
            loading.value = false;
          } else {
            loading.value = true;
            await getProStore(
              storeMarker,
              infoWindow,
              selectObj.value.productClass
            );
            const { points, styles } = processProStoreStyle(proStoreList.value);
            storeMarker.setStyles(styles);
            storeMarker.setGeometries(points);
            loading.value = false;
          }
        }

        return;
      }
      if (checkedStore.value) {
        if (selectObj.value.currentAreaMessage) {
          loading.value = true;
          await getProStore(storeMarker, infoWindow, productClass, v.code);
          let filterS = [];
          areaCodeGlobal.value.forEach((it) => {
            const a = proStoreList.value.filter(
              (item) =>
                item.Store_province === it.Provincial_name &&
                item.Store_city === it.City_name &&
                item.Store_district === it.District_name
            );
            filterS = [...filterS, ...a];
          });
          filterAllUsers.value.forEach((it) => {
            const a = proStoreList.value.filter((item) => {
              it.id === item.Cyclist_name;
            });
            filterS = [...filterS, ...a];
          });
          filterStoreList.value = filterS;
          const { points, styles } = processProStoreStyle(
            filterStoreList.value
          );
          storeMarker.setStyles(styles);
          storeMarker.setGeometries(points);
          loading.value = false;
        } else {
          loading.value = true;
          await getProStore(storeMarker, infoWindow, productClass, v.code);
          const { points, styles } = processProStoreStyle(proStoreList.value);
          storeMarker.setStyles(styles);
          storeMarker.setGeometries(points);
          loading.value = false;
        }
      }
      console.log("v.code", v.code);
    } catch (err) {
      console.log(err);
    }
  }

  // 选择省市区
  async function chooseArea(
    map,
    polygon,
    storeMarker,
    carMarker,
    infoWindow,
    e
  ) {
    polygon.setGeometries([]);
    storeMarker.setGeometries([]);
    infoWindow.close();
    selectObj.value.areaName = "数据";
    selectObj.value.jxsName = null;
    // console.log("12131", e[0]);
    console.log("12131", e);
    if ((e && e[0] == "") || !e) {
      console.log(11);
      getAllAreas(polygon1);
      map.setCenter(new TMap.LatLng(39.984104, 116.307503));
      map.setZoom(6.5);
    } else {
      try {
        const res = await getAreaInfoBycode(e[2]);
        people_count.value = res.data.result[0].Population;
        const currentArea = res.data.result[0];
        map.setCenter(
          new TMap.LatLng(
            currentArea.Resident_latitude,
            currentArea.Station_longitude
          )
        );
        if (res) {
          control_show.value = false;
        }
        selectObj.value.currentAreaMessage = currentArea;
        // 画出当前区域
        res.data.result.forEach((item) => {
          let outlinePath;
          JSON.parse(item.Region_profile).forEach((val) => {
            outlinePath = transtionCopy(val).map(
              (item) => new TMap.LatLng(item.latitude, item.longitude)
            );
            let path = {
              id: `allView-${item.id}`,
              styleId: `${item.id}`,
              paths: outlinePath,
            };
            polygon.add(path);
          });
        });
        filterAreaList.value = polygon.getGeometries();
        let id = getDqIdJxqId(store.dqList, store.jxqList);
        // 过滤出当前门店
        const _PCRMessage =
          currentArea.Provincial_name +
          currentArea.City_name +
          currentArea.District_name;
        const resStore = await getAllStores(_PCRMessage, ...id);
        store_count.value = resStore.data.result.length;
        filterStoreList.value = resStore.data.result;
        if (checkedStore.value) {
          const { points, styles } = processStoreStyle(filterStoreList.value);
          storeMarker.setStyles(styles);
          storeMarker.setGeometries(points);
        }

        // 获取分销商
        const resUser = await get_all_users(currentArea.Distribution_territory);
        filterAllUsers.value = resUser.data.result[0];
        // 过滤出当前车

        if (checkedCar.value) {
          const _carPoints = processVehicleStyle(
            carList.value,
            filterAllUsers.value
          );
          carMarker.setStyles(_carPoints.styles);
          carMarker.setGeometries(_carPoints.points);
        }
        map.setZoom(12);
      } catch (err) {
        console.log(err);
      }
    }
  }

  const allProvinceMessage = ref([]);

  const limit = ref(0);
  const allPath = ref([]);
  // 获取所有区域
  async function allArea(allView, label) {
    let styles = {};

    const res = await get_all_country_area();
    // limit.value += 200;
    allProvinceMessage.value = res.data.result;

    res.data.result.forEach((item) => {
      // if (item.Distribution_territory) {
      //   styles[`${item.id}`] = new TMap.PolygonStyle({
      //     color: randomColor(),
      //     showBorder: true,
      //     borderColor: "rgba(0,0,0,0.8)",
      //     borderWidth: 1,
      //   });
      //   allView.setStyles(styles);
      // }
      let outlinePath;
      try {
        JSON.parse(item.Region_profile).forEach((val) => {
          outlinePath = transtionCopy(val).map(
            (item) => new TMap.LatLng(item.latitude, item.longitude)
          );
          let path = {
            id: `allView-${item.id}`,
            styleId: `${item.id}`,
            paths: outlinePath,
          };
          allPath.value.push(path);
        });
      } catch (e) {}

      const content = `${item.District_name}\n人口数：${item.Population}\n门店数：${item.Store_num}`;

      geometries_arr.value.push({
        content,
        position: new TMap.LatLng(
          item.Resident_latitude,
          item.Station_longitude
        ),
        styleId: "lable",
      });
    });
    //区域点缓存
    allView.add(allPath.value);
    label.setGeometries(geometries_arr.value);
    setTimeout(() => {
      loading.value = false;
    }, 5000);
  }

  // 获取所有产品
  async function getAllProduct(category) {
    const res = await getAllProd(category);
    product_list.value = res.data.result.map((item) => {
      return {
        value: item.Small_boss_product_name,
        label: item.Small_boss_product_name,
        code: item.Small_boss_product_code,
      };
    });
    product_list.value.unshift({
      value: "全部",
      label: "全部",
      code: "",
    });
  }

  // 获取所有产品分类
  async function getProductCategory() {
    const res = await getAllProdCategory();
    product_list_category.value = res.data.result.map((item) => {
      return {
        value: item.Small_boss_subject_name,
        label: item.Small_boss_subject_name,
        code: item.Small_boss_subject_code,
      };
    });
    product_list_category.value.unshift({
      value: "全部",
      label: "全部",
      code: "",
    });
  }

  // 清除车辆定时器
  function clearCarTimer() {
    clearInterval(timerCar);
  }

  return {
    all_stores,
    checkedCar,
    checkedArea,
    checkedStore,
    checkedAllArea,
    selectObj,
    areaList,
    storeList,
    carList,
    jxsUserList,
    fxsUserList,
    control_show,
    store_count,
    people_count,
    loading,
    product_list,
    product_list_category,
    dqList,
    jxqList,
    pickOn,
    clearCarTimer,
    getAllAreas,
    changeChecked,
    getAllStore,
    getProStore,
    getAllcars,
    getAllJxsUserList,
    getAllRegion,
    getAllOutRegion,
    chooseDq,
    chooseJxq,
    chooseJxs,
    chooseFxs,
    chooseArea,
    getAllProduct,
    getProductCategory,
    chooseProType,
    choosePro,
  };
});
