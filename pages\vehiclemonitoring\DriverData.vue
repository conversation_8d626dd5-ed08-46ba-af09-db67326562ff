<template>
  <a-tabs v-model:activeKey="activeKey">
    <a-tab-pane key="1" tab="驾驶报表">
      <a-table :columns="columns_carMsg" :data-source="dataSource" size="small">
      </a-table>
    </a-tab-pane>
    <a-tab-pane key="2" tab="点火详情">
      <a-table
        :columns="columns_ignitionDetail"
        :data-source="acc_dataSource"
        size="small"
      >
      </a-table>
    </a-tab-pane>
    <a-tab-pane key="3" tab="近30批运费">
      <a-table
        :columns="columnsFreight"
        :data-source="freightData"
        size="small"
      >
      </a-table>
    </a-tab-pane>
  </a-tabs>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { getCarFreight30, getCarTracks } from "../../api/vehiclemonitoring";
import dayjs from "dayjs";

const props = defineProps(["currentClickCar"]);

const activeKey = ref("1");

onMounted(() => {
  getStopTimeList(
    "0" + props.currentClickCar.Tms_car_imei,
    props.currentClickCar.Tms_car_number
  );
  getCarFreightData();
});

const dataSource = ref([]);
const acc_dataSource = ref([]);
const freightData = ref([]);

// 获取停车时长
async function getStopTimeList(imei, car_num) {
  const startTime = dayjs().format("YYYY-MM-DD 00:00:00");
  const endTime = dayjs().format("YYYY-MM-DD 23:59:59");

  const params = {
    imei: imei,
    start_time: startTime,
    end_time: endTime,
  };
  const res = await getCarTracks(params);
  let speedData = res.data.result;
  let resultStop = [];
  let resultidling = [];
  let segment = [];
  let ignitionIndex = 0;
  // 筛选出速度停车状态
  speedData.forEach(item => {
    if (item.speed === 0 && item.acc_state === 0) {
      segment.push(item);
    } else {
      if (segment.length > 0) {
        resultStop.push(segment);
      }
      segment = [];
    }
  });
  resultStop = resultStop.filter(item => {
    return item.length > 1;
  });
  // console.log(resultStop)

  resultStop.forEach(item => {
    // 计算时间差
    let s = new Date(item[0].create_time);
    let e = new Date(item[item.length - 1].create_time);
    if (s > e) {
      let t = e;
      e = s;
      s = t;
    }
    const diffs = e - s;

    const diffSeconds = Math.floor(diffs / 1000);
    const diffMinutes = Math.floor(diffSeconds / 60);
    const diffHours = Math.floor(diffMinutes / 60);

    const hours = diffHours % 24;
    const minutes = diffMinutes % 60;
    const seconds = diffSeconds % 60;

    // 创建表格数据
    if (e > s) {
      dataSource.value.push({
        car_num,
        stop_time: `${hours}小时${minutes}分钟${seconds}秒`,
        start_time: item[0].create_time,
        end_time: item[item.length - 1].create_time,
        type: "正常停车",
      });
    } else if (e < s) {
      dataSource.value.push({
        car_num,
        stop_time: `${hours}小时${minutes}分钟${seconds}秒`,
        start_time: item[item.length - 1].create_time,
        end_time: item[0].create_time,
        type: "正常停车",
      });
    }
  });

  speedData.forEach(item => {
    if (item.speed === 0 && item.acc_state === 1) {
      segment.push(item);
    } else {
      if (segment.length > 0) {
        resultidling.push(segment);
      }
      segment = [];
    }
  });

  resultidling = resultidling.filter(item => {
    return item.length > 1;
  });

  resultidling.forEach(item => {
    // 计算时间差
    let s = new Date(item[0].create_time);
    let e = new Date(item[item.length - 1].create_time);
    if (s > e) {
      let t = e;
      e = s;
      s = t;
    }
    const diffs = e - s;

    const diffSeconds = Math.floor(diffs / 1000);
    const diffMinutes = Math.floor(diffSeconds / 60);
    const diffHours = Math.floor(diffMinutes / 60);

    const hours = diffHours % 24;
    const minutes = diffMinutes % 60;
    const seconds = diffSeconds % 60;

    // 创建表格数据
    if (e > s) {
      dataSource.value.push({
        car_num,
        stop_time: `${hours}小时${minutes}分钟${seconds}秒`,
        start_time: item[0].create_time,
        end_time: item[item.length - 1].create_time,
        type: "怠速停车",
      });
    } else if (e < s) {
      dataSource.value.push({
        car_num,
        stop_time: `${hours}小时${minutes}分钟${seconds}秒`,
        start_time: item[item.length - 1].create_time,
        end_time: item[0].create_time,
        type: "怠速停车",
      });
    }
  });

  // ------------------------------ 点火详情 -----------------------------------------
  ignitionIndex = 0;
  let flag = false;
  let acc_startTime;
  let acc_endTime;
  let diffTime = 0;
  let first_acc = "";
  acc_dataSource.value = [];
  speedData.forEach(item => {
    if (
      item.acc_state === 1 &&
      (item.speed === 0 || item.speed > 0) &&
      !first_acc
    ) {
      first_acc = item.create_time;
    }
    if (item.acc_state === 1 && (item.speed === 0 || item.speed > 0)) {
      flag = true;
      acc_startTime = new Date(item.create_time);
    }
    if (item.acc_state === 0 && item.speed === 0 && flag == true) {
      flag = false;
      ignitionIndex++;
      acc_endTime = new Date(item.create_time);
    }
    if (acc_endTime && acc_startTime && acc_endTime > acc_startTime) {
      diffTime += acc_endTime - acc_startTime;
    } else if (acc_endTime && acc_startTime && acc_endTime < acc_startTime) {
      diffTime += acc_startTime - acc_endTime;
    }
  });
  const diffSeconds = Math.floor(diffTime / 1000);
  const diffMinutes = Math.floor(diffSeconds / 60);
  const diffHours = Math.floor(diffMinutes / 60);

  const hours = diffHours % 24;
  const minutes = diffMinutes % 60;
  const seconds = diffSeconds % 60;
  acc_dataSource.value.push({
    car_num,
    acc_times: ignitionIndex,
    acc_all_time: `${hours}小时${minutes}分钟${seconds}秒`,
    acc_starting_time: first_acc,
  });
}

const columns_carMsg = [
  {
    title: "车牌号",
    dataIndex: "car_num",
    key: "car_num",
  },
  {
    title: "停车开始时间",
    dataIndex: "start_time",
    key: "start_time",
  },
  {
    title: "停车结束时间",
    dataIndex: "end_time",
    key: "end_time",
  },
  {
    title: "停车时长",
    dataIndex: "stop_time",
    key: "stop_time",
  },
  {
    title: "类型",
    dataIndex: "type",
    key: "type",
  },
];

const columns_ignitionDetail = [
  {
    title: "车牌号",
    dataIndex: "car_num",
    key: "car_num",
  },
  {
    title: "ACC次数",
    dataIndex: "acc_times",
    key: "acc_times",
  },
  {
    title: "ACC总时长",
    dataIndex: "acc_all_time",
    key: "acc_all_time",
  },
  {
    title: "ACC启动时间",
    dataIndex: "acc_starting_time",
    key: "acc_starting_time",
  },
];

const columnsFreight = [
  {
    title: "发车日期",
    dataIndex: "fcrq",
    key: "fcrq",
    align: "center",
  },
  {
    title: "佣金",
    dataIndex: "yfyj",
    key: "yfyj",
    align: "center",
  },
  {
    title: "燃料",
    dataIndex: "CNJYJE",
    key: "CNJYJE",
    align: "center",
  },
  {
    title: "高速",
    dataIndex: "GLF",
    key: "GLF",
    align: "center",
  },
];

// 获取近30批运费情况

async function getCarFreightData() {
  const { data } = await getCarFreight30(props.currentClickCar.Tms_car_number);

  freightData.value = data.result;
}
</script>

<style lang="scss" scoped></style>
