<template>
  <!-- 图例 -->
  <div class="legend">
    <div v-for="item in legendMessageCar" :key="item.id">
      <span>-</span>
      <img :src="item.img" alt="" style="height: 24px; width: 16px" />
      <span>-</span>
      <span style="font-size: 12px">{{ item.name }}</span>
    </div>
  </div>
</template>

<script setup>
import { legendMessageCar } from "../../data-uitls/legend.js";
</script>

<style lang="scss" scoped>
.legend {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 10px;
  border-radius: 5px;
  z-index: 3;
  display: flex;
  flex-direction: column;
  gap: 5px;
}
</style>
