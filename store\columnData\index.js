import { defineStore } from "pinia";
import { ref } from "vue";

export const useColumnData = defineStore("columnData", () => {
  const columnCache = ref({
    carManColumns: {}, // 近30天的车长表缓存
    distributionColumns: {}, // 近30天的经销区表缓存
  });

  function addColumnCache(objectName, dateStr1, dateStr2 = "x") {
    // 提取日期的月份部分
    const formattedDate = dateStr1.replace(/-/g, "");
    const formattedDate2 = dateStr2.replace(/-/g, "");
    // 拼接对象名和月份
    const cacheKey = objectName + formattedDate + formattedDate2;
    if (!columnCache.value[cacheKey]) {
      columnCache.value[cacheKey] = {};
    }
    return columnCache.value[cacheKey];
  }

  async function clearColumnCache() {
    // 定义需要保留的键
    const keysToKeep = ["carManColumns", "distributionColumns"];

    // 遍历 cache 对象的所有键
    for (let key in columnCache.value) {
      // 如果当前键不在需要保留的键数组中，则删除它
      if (!keysToKeep.includes(key)) {
        delete columnCache.value[key];
      } else {
        // 如果当前键在需要保留的键数组中，则清空其内容
        columnCache.value[key] = {};
      }
    }
  }

  return {
    columnCache,
    addColumnCache,
    clearColumnCache,
  };
});
