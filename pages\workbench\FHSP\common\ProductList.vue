<template>
  <view class="product-info">
    <view v-for="(item, index) in productInfoList" :key="index" class="product-item">
      <view class="product-img" @click="test">
        <image style="width: 100%; height: 100%" :src="item.productUrl || '/static/svg/nophoto.svg'" alt="" />
      </view>
      <view class="product-detail">
        <view class="product-name">{{ item.name }}</view>
        <view class="product-spec">商品编码：{{ item.code }}</view>
        <view class="product-spec">
          商品条形码：
          <text :style="{ color: !item.barCode ? '#9a9fa9' : '' }">
            {{ item.barCode || '暂无' }}
          </text>
        </view>
        <view class="product-spec">
          <view>
            商品规格：
            <text :style="{ color: !item.specification ? '#9a9fa9' : '' }">
              {{ item.specification || '暂无' }}
            </text>
          </view>
        </view>
        <view class="product-spec">
          单价：
          <text class="product-price">
            <text style="font-size: 24rpx">￥</text>
            <text>{{ item.price.toFixed(2) }}</text>
          </text>
        </view>
		<view class="product-spec" v-if="item.Excessive_information" style="color: red;font-size: 32rpx">
		  超标信息：
		  <text class="product-price" style="color: red;">
		    <text>{{ item.Excessive_information }}</text>
		  </text>
		</view>
      </view>
	  <view class="product-num1" >
		<text>是否不允许上账: </text>
	    <switch :checked="item.isSwitchOn" @change="(e) => onSwitchChange(item, e)" color="#4cd964" />
	    <text style="font-size: 20rpx;color: black;">若不允许上账则点击</text>
	  </view>
      <view class="product-num">
        <view>返货数量</view>
        <view>
          <text class="num">{{ Number(item.number).toFixed(2) }}</text>
          <text>{{ item.unit }}</text>
        </view>
        <view>金额合计</view>
        <view class="money">
          <text style="font-size: 24rpx">￥</text>
          <text class="money-num">{{ Number(Number(item.price)* Number(item.number)).toFixed(2)}}</text>
        </view>
      </view>
      <view v-if="item.Excessive_information" class="problem-sign"> 超标 </view>
    </view>
  </view>
</template>

<script setup>
  import { fnFloorToFixed2, fnBigCalculation } from '@/api/workbench/FHSP/return-common.js'
  import { ref } from 'vue'
  const props = defineProps({
    productInfoList: {
      type: Array,
      default: () => [],
    },
  })
  const isSwitchOn = ref(false)
  

 const onSwitchChange = (item, e) => {
   const newValue = e.detail.value
   
   item.isSwitchOn = newValue
   console.log('item.isSwitchOn',item.isSwitchOn);
 }
</script>

<style lang="scss" scoped>

  .product-info {
    position: relative;
    min-height: 100%;
    box-sizing: border-box;
    // height: 500rpx;
    // overflow: auto;
    // overflow-y: scroll;
  }
  .product-item {
    position: relative;
    display: flex;
    justify-content: space-between;
    height: 240rpx;
    padding: 10rpx;
    margin-bottom: 20rpx;
    box-shadow: 0rpx 10rpx 20rpx rgba(215, 215, 215, 0.34);
    font-size: 26rpx;
    color: #606266;

    .product-img {
      width: 160rpx;
      padding: 20rpx 0;
      background-color: #fff;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .product-detail {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      margin: 0 10rpx;

      .product-name {
        font-size: 36rpx;
        font-weight: bold;
        color: #0e0a0a;
      }
      .product-spec {
        color: #606266;
        white-space: nowrap;
      }
      .product-price {
        font-size: 30rpx;
        font-weight: 500;
        color: #606266;
      }
    }
	.product-num1 {
	  display: flex;
	  flex-direction: column;
	  justify-content: space-around;
	  width: 300rpx;
	  margin-right: 200rpx;
	  font-size: 28rpx;
	  text-align: center;
	  color: #3b3b3b;
	  .num {
	    color: #000;
	    font-size: 38rpx;
	    font-weight: 500;
	    padding-right: 10rpx;
	  }
	  .money {
	    color: #606266;
	    .money-num {
	      font-size: 30rpx;
	    }
	  }
	}
    .product-num {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      width: 150rpx;
      font-size: 28rpx;
      text-align: center;
      color: #3b3b3b;
      .num {
        color: #000;
        font-size: 38rpx;
        font-weight: 500;
        padding-right: 10rpx;
      }
      .money {
        color: #606266;
        .money-num {
          font-size: 30rpx;
        }
      }
    }
    .problem-sign {
      position: absolute;
      top: 80rpx;
      right: 90rpx;
      width: 150rpx;
      height: 60rpx;
      line-height: 60rpx;
      border: 4rpx dashed red;
      border-radius: 10rpx;
      text-align: center;
      color: red;
      font-size: 32rpx;
      font-weight: bold;
      transform: rotate(45deg);
    }
  }
</style>
