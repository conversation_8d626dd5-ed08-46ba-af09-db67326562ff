<template>
  <div class="loading" v-if="isLoading">
    <a-spin /><span style="margin-left: 14px">正在加载页面，请稍后</span>
  </div>
  <div class="exchange-rules-page" v-else>
    <a-config-provider :locale="locale">
      <!-- 头部操作区 -->
      <div class="header-section">
        <!-- 搜索框 -->
        <div class="search-section">
          {{ mainData.Small_boss_price_result_product_name }}
        </div>
      </div>
      <!-- 列表内容 -->
      <div class="list-container">
        <div class="status-tabs">
          <div>
            <span class="label">可订：</span>{{ mainData.distinct_posts_order }}
          </div>
          <div>
            <span class="label">车长可订：</span
            >{{ mainData.distinct_posts_che_ding }}
          </div>
          <div>
            <span class="label">可销货：</span
            >{{ mainData.distinct_posts_sale }}
          </div>
          <div>
            <span class="label">可返货：</span
            >{{ mainData.distinct_posts_back }}
          </div>
          <div>
            <span class="label">可退货：</span
            >{{ mainData.distinct_posts_return }}
          </div>
          <div>
            <span class="label">可质返：</span
            >{{ mainData.distinct_posts_zhifan }}
          </div>
        </div>
        <!-- 规则卡片列表 -->
        <div v-if="areaList.length > 0" class="rules-list">
          <a-card
            v-for="(item, index) in areaList"
            :key="item.id"
            class="rule-card"
            :hoverable="true"
          >
            <!-- 卡片头部 -->
            <template #title>
              <div class="card-header">
                <div class="rule-title">
                  <span class="serial-number">{{ item.org_name }}</span>
                </div>
                <div>
                  <a-button
                    type="primary"
                    v-if="!item.change"
                    @click="changeSwitch(index)"
                    ><template #icon> <EditOutlined /> </template>修改</a-button
                  >
                  <a-button
                    style="margin-right: 14px"
                    type="primary"
                    ghost
                    v-if="item.change"
                    @click="cancelSwitch(index)"
                    >取消</a-button
                  >
                  <a-button
                    type="primary"
                    v-if="item.change"
                    @click="sureSwitch(index)"
                    >确定</a-button
                  >
                </div>
              </div>
            </template>

            <!-- 卡片内容 -->
            <div class="card-content">
              <!-- 基本信息行 -->
              <a-row :gutter="16" class="info-row">
                <a-col :span="8">
                  <div class="info-item">
                    <span class="label">可订：</span>
                    <span class="value">
                      <a-switch
                        v-model:checked="item.OrderGoodsif"
                        :disabled="!item.change"
                    /></span>
                  </div>
                </a-col>
                <a-col :span="8">
                  <div class="info-item">
                    <span class="label">车长可订：</span>
                    <span class="value"
                      ><a-switch
                        v-model:checked="item.che_is_ding"
                        :disabled="!item.change"
                    /></span>
                  </div>
                </a-col>
                <a-col :span="8">
                  <div class="info-item">
                    <span class="label">可销货：</span>
                    <span class="value"
                      ><a-switch
                        v-model:checked="item.SalesGoodsif"
                        :disabled="!item.change"
                    /></span>
                  </div>
                </a-col>
              </a-row>
              <a-row :gutter="16" class="info-row">
                <a-col :span="8">
                  <div class="info-item">
                    <span class="label">可返货：</span>
                    <span class="value"
                      ><a-switch
                        v-model:checked="item.BackGoodsif"
                        :disabled="!item.change"
                    /></span>
                  </div>
                </a-col>
                <a-col :span="8">
                  <div class="info-item">
                    <span class="label">可退货：</span>
                    <span class="value"
                      ><a-switch
                        v-model:checked="item.ReturnGoodsif"
                        :disabled="!item.change"
                    /></span>
                  </div>
                </a-col>
                <a-col :span="8">
                  <div class="info-item">
                    <span class="label">可质返：</span>
                    <span class="value"
                      ><a-switch
                        v-model:checked="item.is_zhifan"
                        :disabled="!item.change"
                    /></span>
                  </div>
                </a-col>
              </a-row>
            </div>
          </a-card>
        </div>

        <!-- 空状态 -->
        <div v-else class="empty-state">
          <a-empty :description="getEmptyDescription()">
            <template #image>
              <FileTextOutlined style="font-size: 48px; color: #d9d9d9" />
            </template>
          </a-empty>
        </div>
      </div>
    </a-config-provider>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { message, Modal } from "ant-design-vue";
import { onShow, onLoad, onReachBottom } from "@dcloudio/uni-app";
import {
  FileTextOutlined,
  UnorderedListOutlined,
  EditOutlined,
} from "@ant-design/icons-vue";
import zhCN from "ant-design-vue/es/locale/zh_CN";
import { useRoute } from "vue-router";
import { getAreaApi, changeSwitchApi } from "/api/workbench/switch/index";
import { useUserInfo } from "/store/user/userInfo";

// 产品名称
const foodName = ref("");
//产品编码
const foodCode = ref("");
const route = useRoute();

const user_name = ref("");
// 中文语言包
const locale = zhCN;

const isLoading = ref(false);

// 模拟数据
const areaList = ref([]);

const mainData = ref({
  Small_boss_price_result_product_name: "",
  distinct_posts_back: "", //可返货
  distinct_posts_che_ding: "", //车长可订
  distinct_posts_order: "", //可订货
  distinct_posts_return: "", //可退货
  distinct_posts_sale: "", //可销货
  distinct_posts_zhifan: "", //可质返
  max_product_code: "",
});

// 获取详情信息
const getDetail = async () => {
  isLoading.value = true;
  const res = await getAreaApi(foodName.value, foodCode.value);
  if (res.data.code == 200) {
    if (res.data.result.length > 0 && res.data.result[0]) {
      mainData.value = res.data.result[0][0];
    }
    if (res.data.result.length > 1 && res.data.result[1]) {
      areaList.value = res.data.result[1].map((item) => {
        return {
          ...item,
          BackGoodsif: item.BackGoodsif ? true : false,
          OrderGoodsif: item.OrderGoodsif ? true : false,
          ReturnGoodsif: item.ReturnGoodsif ? true : false,
          SalesGoodsif: item.SalesGoodsif ? true : false,
          che_is_ding: item.che_is_ding ? true : false,
          is_zhifan: item.is_zhifan ? true : false,
          change: false,
        };
      });
    }
    isLoading.value = false;
  } else {
    isLoading.value = false;
    message.error("获取详情信息失败");
  }
};

// 目前修改的数据
const ysData = ref({});

// 修改状态
const changeSwitch = (index) => {
  areaList.value[index].change = true;
  ysData.value = JSON.parse(JSON.stringify(areaList.value[index]));
};
const cancelSwitch = (index) => {
  areaList.value[index] = ysData.value;
  areaList.value[index].change = false;
};
const sureSwitch = (index) => {
  console.log("areaList.value[index]", areaList.value[index]);
  // 遍历所有属性
  const current = areaList.value[index];
  const original = ysData.value;
  const diff = {};

  const fieldMap = {
    ReturnGoodsif: "可退货",
    che_is_ding: "车长可订",
    BackGoodsif: "可返货",
    OrderGoodsif: "可订货",
    SalesGoodsif: "可销货",
    is_zhifan: "可质返",
  };

  // 遍历并记录差异
  Object.keys(current).forEach((key) => {
    if (current[key] !== original[key]) {
      diff[key] = {
        original: original[key],
        current: current[key],
      };
    }
  });

  // 构造 change_arr 数组
  const change_arr = Object.entries(diff).map(([key, value]) => {
    const chineseName = fieldMap[key];
    const btnVal = value.current ? 1 : 0;
    return {
      Btn_name: chineseName,
      Btn_val: btnVal,
    };
  });

  console.log("change_arr:", change_arr);

  // 打印差异
  if (Object.keys(diff).length > 0) {
    console.log("修改的属性及值：", diff);
    submitData(current, change_arr);
  } else {
    console.log("没有修改任何属性");
  }
  areaList.value[index].change = false;
};
// 是否提交修改中
const changeLoad = ref(false);
// 整理提交信息
const submitData = async (data, arr) => {
  if (!changeLoad.value) {
    const toData = {
      person_name: user_name.value,
      food_name: mainData.value.Small_boss_price_result_product_name,
      food_code: mainData.value.max_product_code,
      area_name: data.org_name,
      order_if: data.OrderGoodsif ? 1 : 0,
      back_if: data.BackGoodsif ? 1 : 0,
      che_ding: data.che_is_ding ? 1 : 0,
      return_if: data.ReturnGoodsif ? 1 : 0,
      sale_if: data.SalesGoodsif ? 1 : 0,
      zhifan_if: data.is_zhifan ? 1 : 0,
      rule_id: data.post,
      change_arr: arr,
    };
    changeLoad.value = true;
    const res = await changeSwitchApi(toData);
    if (res.data.code == 200) {
      changeLoad.value = false;
      message.success("修改成功");
      getDetail();
    } else {
      changeLoad.value = false;
    }
  }
};

// 空状态描述
const getEmptyDescription = () => {
  return "暂无数据";
};
const userStore = useUserInfo();
// 页面生命周期
onMounted(() => {
  if (!userStore.userInfo) {
    const userInfo = sessionStorage.getItem("userInfo");
    userStore.userInfo = JSON.parse(userInfo);
  }
  console.log("userStore.userInfo", userStore.userInfo);
  user_name.value = userStore.userInfo?.userName;

  foodName.value = route.query?.product;
  foodCode.value = route.query?.code;
  getDetail();
});
</script>

<style scoped>
.loading {
  height: 200px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 36rpx;
  font-weight: bold;
  color: #999;
}
.exchange-rules-page {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding: 0;
}

/* 头部操作区 */
.header-section {
  background: #fff;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.add-section {
  margin-bottom: 16px;
}

.search-section {
  font-size: 26px;
  width: 100%;
}

/* 列表容器 */
.list-container {
  padding: 16px;
}

/* 规则卡片列表 */
.rules-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.rule-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.rule-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
}

.rule-title {
  display: flex;
  /* flex-direction: column; */
  gap: 6px;
  flex: 1;
  align-items: center;
}

.serial-number {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1.3;
}

.status-tag {
  align-self: flex-start;
}

/* 卡片内容 */
.card-content {
  margin-top: 12px;
}

.info-row {
  margin-bottom: 12px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-item {
  display: flex;
  /* flex-direction: column; */
  gap: 2px;
  /* font-size: 30px; */
}

.info-item.full-width {
  width: 100%;
}

.label {
  font-size: 16px;
  color: #909399;
  line-height: 1.2;
}

.value {
  font-size: 14px;
  color: #2c3e50;
  font-weight: 500;
  line-height: 1.3;
  word-break: break-all;
}

.value.highlight {
  color: #fa8c16;
  font-weight: 600;
}

.scope-text {
  color: #606266;
  font-size: 13px;
  line-height: 1.3;
}

.time-info {
  margin-top: 6px;
  padding-top: 8px;
  border-top: 1px solid #f5f7fa;
}

.time-text {
  font-size: 12px;
  color: #c0c4cc;
  line-height: 1.2;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 空状态 */
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

/* 显示数量栏 */
.status-tabs {
  margin-bottom: 16px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
  padding: 8px 12px;
  display: flex;
  gap: 14px;
  flex-wrap: wrap;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .header-section {
    padding: 12px;
  }

  .list-container {
    padding: 8px 12px;
  }

  .rule-card {
    border-radius: 8px;
  }

  .serial-number {
    font-size: 15px;
  }

  .value {
    font-size: 13px;
  }

  .scope-text {
    font-size: 12px;
  }

  .info-row {
    margin-bottom: 10px;
  }

  .card-content {
    margin-top: 8px;
  }

  .time-info {
    margin-top: 4px;
    padding-top: 6px;
  }
}

/* PC端适配 */
@media (min-width: 769px) {
  .exchange-rules-page {
    max-width: 1200px;
    margin: 0 auto;
  }

  .header-section {
    padding: 20px 24px;
    display: flex;
    align-items: center;
    gap: 20px;
  }

  .add-section {
    margin-bottom: 0;
    flex-shrink: 0;
  }

  .search-section {
    max-width: 400px;
  }

  .list-container {
    padding: 20px 24px;
  }

  .rules-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 16px;
  }

  .serial-number {
    font-size: 17px;
  }
}

/* 中等屏幕适配 */
@media (min-width: 1024px) {
  .exchange-rules-page {
    max-width: 1400px;
  }

  .rules-list {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 18px;
  }
}

/* 大屏幕适配 */
@media (min-width: 1280px) {
  .exchange-rules-page {
    max-width: 1600px;
  }

  .rules-list {
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
    gap: 20px;
  }
}

/* 超大屏幕适配 */
@media (min-width: 1600px) {
  .exchange-rules-page {
    max-width: 1800px;
  }

  .rules-list {
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 24px;
  }
}

/* antd 组件样式重写 */
:deep(.ant-card-head) {
  border-bottom: 1px solid #f5f7fa;
  padding: 12px 16px 8px;
}

:deep(.ant-card-body) {
  padding: 0 16px 16px;
}

:deep(.ant-empty-image) {
  margin-bottom: 16px;
}

:deep(.ant-tag) {
  border-radius: 6px;
  font-size: 12px;
}

:deep(.ant-dropdown-menu) {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

:deep(.ant-dropdown-menu-item) {
  padding: 8px 12px;
}

:deep(.ant-dropdown-menu-item-icon) {
  margin-right: 8px;
}

/* 移动端卡片样式调整 */
@media (max-width: 768px) {
  :deep(.ant-card-head) {
    padding: 10px 12px 6px;
  }

  :deep(.ant-card-body) {
    padding: 0 12px 12px;
  }
}
</style>
