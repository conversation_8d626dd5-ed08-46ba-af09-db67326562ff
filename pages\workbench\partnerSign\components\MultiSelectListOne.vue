<template>
  <div class="multi-select-container">
    <!-- 搜索框 -->
    <div class="search-section">
      <a-input
        v-model:value="searchKeyword"
        placeholder="搜索..."
        allow-clear
        @change="handleSearch"
      >
        <template #prefix>
          <search-outlined />
        </template>
      </a-input>
    </div>

    <!-- 表头 -->
    <div class="table-header" v-if="items2 && items2.length > 0">
      <div class="header-row">
        <div 
          v-for="header in items2" 
          :key="header.value"
          class="header-cell"
          :class="{ 'main-column': header.label }"
        >
          {{ header.label }}
        </div>
      </div>
    </div>

    <!-- 列表内容 -->
    <div class="list-content">
      <div class="empty-state" v-if="filteredItems.length === 0">
        <a-empty description="暂无数据" />
      </div>
      
      <div v-else>
        <div 
          v-for="item in paginatedItems" 
          :key="item.value"
          class="list-item"
          :class="{ 'selected': isSelected(item) }"
          @click="handleItemClick(item)"
        >
          <div class="item-radio">
            <a-radio 
              :checked="isSelected(item)"
              @click.stop="handleItemClick(item)"
            />
          </div>
          <div class="item-content">
            <div class="main-info">{{ item.label }}</div>
            <div class="sub-info" v-if="item.label2">{{ item.label2 }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-section" v-if="totalPages > 1">
      <a-pagination
        v-model:current="currentPage"
        :total="filteredItems.length"
        :page-size="itemsPerPage"
        :show-size-changer="false"
        :show-quick-jumper="false"
        :show-total="(total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`"
        size="small"
        @change="handlePageChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, defineProps, defineEmits } from 'vue'
import { SearchOutlined } from '@ant-design/icons-vue'

const props = defineProps({
  items: {
    type: Array,
    default: () => []
  },
  items2: {
    type: Array,
    default: () => []
  },
  selectedItems: {
    type: Array,
    default: () => []
  },
  itemsPerPage: {
    type: Number,
    default: 10
  },
  currentPage: {
    type: Number,
    default: 1
  },
  totalPages: {
    type: Number,
    default: 1
  },
  singleSelect: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits([
  'update:selected-items',
  'update:close', 
  'update:confirm'
])

const searchKeyword = ref('')
const currentPage = ref(props.currentPage)
const selectedItem = ref(null)

// 过滤后的项目
const filteredItems = computed(() => {
  if (!searchKeyword.value) {
    return props.items
  }
  
  return props.items.filter(item => 
    item.label?.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
    item.label2?.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

// 分页后的项目
const paginatedItems = computed(() => {
  const start = (currentPage.value - 1) * props.itemsPerPage
  const end = start + props.itemsPerPage
  return filteredItems.value.slice(start, end)
})

// 总页数
const totalPages = computed(() => {
  return Math.ceil(filteredItems.value.length / props.itemsPerPage)
})

// 检查项目是否被选中
const isSelected = (item) => {
  if (props.singleSelect) {
    return selectedItem.value?.value === item.value
  }
  return props.selectedItems.some(selected => selected.value === item.value)
}

// 处理项目点击
const handleItemClick = (item) => {
  if (props.singleSelect) {
    selectedItem.value = item
    // 单选模式下直接确认选择
    emit('update:confirm', item)
  } else {
    // 多选模式逻辑（如果需要的话）
    const newSelectedItems = [...props.selectedItems]
    const index = newSelectedItems.findIndex(selected => selected.value === item.value)
    
    if (index > -1) {
      newSelectedItems.splice(index, 1)
    } else {
      newSelectedItems.push(item)
    }
    
    emit('update:selected-items', newSelectedItems)
  }
}

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1
}

// 处理分页变化
const handlePageChange = (page) => {
  currentPage.value = page
}

// 监听搜索关键词变化
watch(searchKeyword, () => {
  currentPage.value = 1
})
</script>

<style lang="scss" scoped>
.multi-select-container {
  width: 100%;
  max-height: 500px;
  display: flex;
  flex-direction: column;
}

.search-section {
  margin-bottom: 16px;
}

.table-header {
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 8px;
  
  .header-row {
    display: flex;
    padding: 8px 0;
    font-weight: 600;
    color: #262626;
    background-color: #fafafa;
    
    .header-cell {
      flex: 1;
      padding: 0 12px;
      text-align: center;
      
      &.main-column {
        flex: 2;
        text-align: left;
      }
    }
  }
}

.list-content {
  flex: 1;
  overflow-y: auto;
  min-height: 200px;
  max-height: 300px;
  
  .empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
  }
  
  .list-item {
    display: flex;
    align-items: center;
    padding: 12px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: all 0.2s;
    
    &:hover {
      background-color: #f5f5f5;
    }
    
    &.selected {
      background-color: #e6f7ff;
      border-color: #91d5ff;
    }
    
    &:last-child {
      border-bottom: none;
    }
    
    .item-radio {
      margin-right: 12px;
    }
    
    .item-content {
      flex: 1;
      
      .main-info {
        font-size: 14px;
        color: #262626;
        margin-bottom: 4px;
      }
      
      .sub-info {
        font-size: 12px;
        color: #8c8c8c;
      }
    }
  }
}

.pagination-section {
  margin-top: 16px;
  display: flex;
  justify-content: center;
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
}

// 响应式设计
@media (max-width: 768px) {
  .multi-select-container {
    max-height: 400px;
  }
  
  .list-content {
    max-height: 250px;
  }
  
  .table-header {
    .header-row {
      .header-cell {
        padding: 0 8px;
        font-size: 12px;
      }
    }
  }
  
  .list-item {
    padding: 8px;
    
    .item-content {
      .main-info {
        font-size: 13px;
      }
      
      .sub-info {
        font-size: 11px;
      }
    }
  }
}
</style>
