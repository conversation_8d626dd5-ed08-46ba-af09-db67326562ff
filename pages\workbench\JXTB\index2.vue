<template>
  <view class="container">
    <!-- 标题和切换按钮 -->
    <view class="header">
      <text class="title">区域销售数据报表</text>
      <view class="controls">
        <picker
            v-if="activeTab === 'monthly'"
            mode="date"
            fields="date"
            :value="selectedMonth"
            @change="onMonthChange"
            class="picker"
        >
          <view class="picker-text">选择日期: {{ selectedMonthDisplay }}</view>
        </picker>


      </view>
    </view>

    <!-- 数据表格 -->
    <scroll-view scroll-x scroll-y class="table-scroll-container" >
      <view class="table-container" >
        <view class="table-header">

          <!-- 动态产品列 -->
          <view
              class="header-cell product-cell"
              v-for="(product, index) in products"
              :key="'product-'+index"
          >
            <view class="product-name">{{ product.name }}</view>

          </view>
        </view>

        <view class="table-body">
          <view
              class="table-row"
              v-for="(item, index) in regionData"
              :key="index"
          >


            <!-- 产品销量数据 -->
            <view
                class="row-cell product-cell"
                v-for="(product, pIndex) in products"
                :key="'data-'+index+'-'+pIndex"
            >
              {{ getProductSales(item, product.id) }}
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <div v-if="loading" class="loading-indicator">
      <i class="fas fa-spinner fa-spin"></i> 数据加载中...
    </div>

  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { onLoad } from "@dcloudio/uni-app";
import { get_jxyl } from "../../../api/workbench/JXYL/index.js";
// import {get_wlfc} from "../../../api/workbench/logisticsWarehouseBuild";

import dayjs from 'dayjs';


const selectedMonth = ref(dayjs().format('YYYY-MM-DD')); // 2023-11-15

// 当前激活的标签
const activeTab = ref('monthly');
const loading = ref(false);
// 日期选择
// const selectedMonth = ref('2023-01-01');
const selectedYear = ref('2023');
const selectedMonthDisplay = computed(() => {
  const [year, month, day] = selectedMonth.value.split('-');
  return `${year}年${month}月${day}日`;
});

// 产品列表
const products = ref([
  { id: 'Region', name: '大区', unit: '' },
  { id: 'Province', name: '省区', unit: '' },
  { id: 'DistributionOrganizationName', name: '经销库房', unit: '' },
  { id: 'datetime', name: '日期', unit: '' },
  { id: 'cl_num', name: '车辆数', unit: '辆' },
  { id: 'target', name: '目标', unit: '' },
  { id: 'target_month', name: '当月目标', unit: '' },
  { id: 'target_rate', name: '管理目标达成率', unit: '' },
  { id: 'last_year_target', name: '去年同期目标', unit: '' },
  { id: 'last_year_target_month', name: '去年上月累计', unit: '辆' },
  { id: 'last_year_growth_rate', name: '较去年同期成长率', unit: '' },
  { id: 'previous_month_rate', name: '较上月环比成长率', unit: '' },
  { id: 'Deviation_standard_progress', name: '较标准进度差异', unit: '' },
  { id: 'Actual_Goal', name: '实际-目标 (数字量化)', unit: '' },
  { id: 'bicycle_sales_per', name: '单车均销', unit: '辆' },
  { id: 'ranking', name: '管理目标达成排名 (大区内)', unit: '' },
  { id: 'placed_month', name: '本月应下单批次数', unit: '' },
  { id: 'placed_now', name: '累计目前应下单批次', unit: '' },
  { id: 'placed_batches', name: '实际下单批次数', unit: '' },
  { id: 'Absenteeism_Batch', name: '缺勤批次', unit: '辆' },
  { id: 'Regular_amount', name: '常规品到货金额', unit: '' },
  { id: 'Regular_amount_rate', name: '常规品到货金占比', unit: '' },
  { id: 'Time_amount', name: '档期品到货金额', unit: '' },
  { id: 'Time_amount_rate', name: '档期品到货占比', unit: '' },
  { id: 'Return_rate_kf', name: '库房业务返货率', unit: '辆' },
  { id: 'Return_rate_cw', name: '财务返货率', unit: '' },
  { id: 'Return_money_kf', name: '本月业务返货金额', unit: '' },
  { id: 'Return_money_cw', name: '财务上账金额', unit: '' },
  { id: 'no_return_money', name: '未上账金额', unit: '辆' },
]);

// 模拟数据 - 按区域和产品
const regionData = ref({});



// 获取产品销量
const getProductSales = (item, productId) => {
  return item[productId];
};







const onMonthChange = async (e) => {
  console.log(e)
  regionData.value =[]
  loading.value = true
  selectedMonth.value = e.detail.value;
  const res = await get_jxyl(selectedMonth.value);
  regionData.value = res.data.result;
  loading.value = false
};



const formatNumber = (num) => {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
};

// 模拟API获取产品列表
// onMounted(async() => {
//   // 实际项目中这里可以调用API获取产品列表
//   // fetchProducts().then(data => products.value = data);
//   // const res = await get_jxyl();
//   //
//   // regionData.monthly.value = res.data.result
// });
const tableWidth = ref(0);
onLoad(async(option) => {
  loading.value = true
  const res = await get_jxyl(selectedMonth.value);
  regionData.value = res.data.result;
  loading.value = false
});


</script>

<style scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 90vh;

  padding: 20rpx;
  background-color: #f5f5f5;
  box-sizing: border-box;
}

.header {
  margin-bottom: 20rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  text-align: center;
}

.controls {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.switch-container {
  display: flex;
  justify-content: center;
}

.switch-btn {
  padding: 10rpx 30rpx;
  margin: 0 10rpx;
  background-color: #e0e0e0;
  color: #666;
  border-radius: 30rpx;
  font-size: 28rpx;
  border: none;
}

.switch-btn.active {
  background-color: #007aff;
  color: white;
}

.picker {
  background-color: white;
  padding: 15rpx 20rpx;
  border-radius: 8rpx;
  text-align: center;
}

.picker-text {
  color: #333;
  font-size: 28rpx;
}

/* 滚动容器 */
.table-scroll-container {
  flex: 1;
  width: 100%;
  overflow: auto;
  background-color: white;
  -webkit-overflow-scrolling: touch; /* iOS 平滑滚动 */

  /* 强制显示滚动条 */
  overflow-x: scroll !important;
  overflow-y: scroll !important;
}

/* 表格容器（关键：禁止换行） */
.table-container {
  min-width: 100%;
  display: inline-block;
  white-space: nowrap; /* 防止列换行 */

}

/* 表头和行保持横向排列 */
.table-header, .table-row {
  display: flex;

}



.header-cell, .row-cell {
  padding: 20rpx 10rpx;
  text-align: center;
  border-bottom: 1rpx solid #eee;
  font-size: 26rpx;
}

.header-cell {
  font-weight: bold;
  background-color: #f9f9f9;
  position: sticky;
  top: 0;
}



/* 产品列样式 */
/* 产品列样式 - 允许换行 */
.product-cell {
  min-width: 220rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  word-break: break-all;  /* 强制换行 */
  white-space: normal;    /* 覆盖之前的 nowrap */
  padding: 12rpx 8rpx;    /* 增加内边距 */
  box-sizing: border-box;
}

/* 表头单元格特别处理 */
.header-cell .product-name {
  white-space: normal;    /* 表头也允许换行 */
  word-break: break-word; /* 更优雅的换行方式 */
  line-height: 1.4;       /* 增加行高 */
}

.product-name {
  font-weight: bold;
  margin-bottom: 5rpx;
}






</style>