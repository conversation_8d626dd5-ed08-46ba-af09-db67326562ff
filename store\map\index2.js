import { defineStore } from "pinia";
import { ref } from "vue";
import {
  randomColor,
  processStoreStyle,
  processProStoreStyle,
  processVehicleStyle,
  processTypeStoreStyle,
} from "../../pages/Map/data-uitls";
import { transtionCopy } from "../../utils/map";
import {
  getNationalAreasNew,
  getAllStores,
  get_pro_stores_all,
  getRegionUnderling,
  get_cache,
  get_work,
  carMapGetAllJxs,
  GetAllDqNew,
  get_all_users,
  get_all_cars,
  getAreaInfoBycode,
  get_all_country_area,
  getAllCountryStores,
  getAllProd,
  getAllProdCategory,
  getProductGroup,
  get_update_cars,
  all_region_sell,
  getAllAreas_sell,
  getAllAreas_region,
  getAllAreas_regionnew,
  all_region_dqz,
  all_region_dqz_new,
  getAllUsers_dqz,
  all_region_sqz,
  getStoresListByCoreId,
  getRegionUnderlingNew,
  getRegionUnderlingNew2,
  GetAllSqNew,
  all_region_sell_new,
  all_provinces_sell_new,
  all_provinces_dqz_new,
  all_region_sqz_new,
  all_provinces_sqz_new, getProvincesUnderlingNew,
} from "../../api/Map/index";
import { message } from "ant-design-vue";
import { useUserInfo } from "../user/userInfo";
import { markerTypeStoreStyle } from "../../pages/Map/data-uitls/legend";
import { getAreaStreets } from "../../api/workingplanning";

const userStore = useUserInfo();

export const useMapStore = defineStore("mapstore", () => {
  const store = useMapStore();
  const checkedCar = ref(false); // 地图车辆控件开关
  const checkedArea = ref(true); // 地图区域控件开关
  const checkedStore = ref(false); // 地图门店控件开关
  const checkedAllArea = ref(false); // 地图区域规划控件开关
  const checkedOpenStore = ref(false); // 地图已开门店控制开关
  const checkedTypeStore = ref(false); // 地图类型门店控制开关
  const checkedOpenStreet = ref(false); // 地图街道显示控制开关
  const loading = ref(false); // 地图加载状态

  const selectObj = ref({
    jxsName: "全部", // 经销商名称
    jxsId: "", // 经销商id
    fxsName: null, // 分销商名称
    fxsId: "", // 分销商id
    optionValue: "近30天", // 时间选择
    dateSection: null, // 时间范围选择
    modalShow: false, // 模态框显示
    areaName: "数据", // 地区名称
    currentAreaMessage: null, // 当前地区信息
    province: null, // 省级
    productClass: null, // 产品分类
    product: null, // 产品
    productGroup: "", // 产品组
    dqName: "全部", // 大区名称
    dqId: null, // 大区id
    jxqName: "全部", // 经销区名称
    jxqId: "", // 经销区id
    sqName: "全部", // 经销区名称
    sqId: "", // 经销区id
    xzqyName: "全部", // 经销区名称
    xzqyId: "", // 经销区id
    start_date: null, // 开始时间
    end_date: null, // 结束时间
  });

  const storeList = ref([]); // 全部门店列表
  const filterStoreList = ref([]); // 过滤出的门店列表
  const proStoreList = ref([]); // 产品门店列表
  const proFilterStoreList = ref([]); // 产品过滤门店列表
  const areaCodeGlobal = ref([]); // 全局区域code
  let all_stores = ref(); // 全部门店

  const areaList = ref([]); // 区域列表
  const filterAreaList = ref([]); // 过滤出的区域列表

  const carList = ref([]); // 车
  const allUsers = ref([]); // 全部用户
  const filterAllUsers = ref([]); // 过滤出的用户

  const jxsUserList = ref([]); // 经销商用户
  const allJxsList = ref([]); // 经销商备份
  const fxsUserList = ref([]); // 分销商用户
  const userCache = ref([]); // 经销区下的库长缓存
  const sqCache = ref([]); // 省区缓存
  const jxqCache = ref([]); // 经销区缓存
  const xzqyCache = ref([]); // 省区缓存
  const provinceMessage = ref([]); // 省级信息
  const allProvinceMessage = ref([]); // 所有省市信息
  const allPath = ref([]);
  const geometries_arr = ref([]);

  const control_show = ref();
  // 当前门店数
  const store_count = ref();
  // 当前人口数
  const people_count = ref();
  // 所有产品
  const product_list = ref();
  // 所有产品类别
  const product_list_category = ref();

  let pickOn = ref("");

  const dqList = ref([]);
  const dqId = ref(""); // 大区ID,因为selectObj.dqId被覆盖,所以单独命名一个
  const sqList = ref([]);
  const sqId = ref("");
  const xzqyList = ref([]);
  const xzqyId = ref("");
  const jxqList = ref([]);
  const jxqId = ref("");
  let timerCar = null; //定义车辆刷新定时器
  let polygon1 = null;

  // 获取大区Id 经销区Id
  function getDqIdJxqId(dqList, jxqList) {
    let region = "";
    let area = "";
    dqList.forEach(item => {
      if (selectObj.value.dqName === "全部") {
        area = "";
      } else if (item.label === selectObj.value.dqName) {
        area = item.dqId;
      }
    });
    jxqList.forEach(item => {
      if (selectObj.value.jxqName === "全部") {
        region = "";
      } else if (item.label === selectObj.value.jxqName) {
        region = item.jxqId;
      }
    });

    return [region, area];
  }

  // 获取全部区域
  async function getAllAreas(polygon, map) {
    try {
      loading.value = true;
      polygon1 = polygon;
      let styles = {};
      let res;
      if (userStore.userInfo.Role_grade === "决策层") {
        res = await getNationalAreasNew();
      }
      if (userStore.userInfo.Role_grade === "经销商") {
        res = await getAllAreas_sell(userStore.userInfo.jxqId[0]);
        map.setCenter(
            new TMap.LatLng(
                res.data.result[0].Resident_latitude,
                res.data.result[0].Station_longitude
            )
        );
        map.setZoom(10);
      }
      if (
          userStore.userInfo.Role_grade === "大区总" ||
          userStore.userInfo.Role_grade === "省区总"
      ) {
        // const jxqIdList = userStore.userInfo.jxqId.join(",");
        // res = await getAllAreas_region(jxqIdList);
        res = await getAllAreas_regionnew(userStore.userInfo.id);
        map.setCenter(
            new TMap.LatLng(
                res.data.result[0].Resident_latitude,
                res.data.result[0].Station_longitude
            )
        );
        map.setZoom(10);
      }

      provinceMessage.value = res.data.result.filter(
          item => item.District_name !== "" && item.District_name !== null
      );
      // 切割数组
      let result = [];
      for (let i = 0; i < provinceMessage.value.length; i += 50) {
        // 使用slice方法从原始数组中截取一段，并将其添加到结果数组中
        result.push(provinceMessage.value.slice(i, i + 50));
      }
      function drawMap(result, index = 0) {
        if (index >= result.length) {
          areaList.value = polygon.getGeometries();
          filterAreaList.value = polygon.getGeometries();
          loading.value = false;
          return;
        }
        const item_area = result[index];
        setTimeout(() => {
          item_area
              .filter(
                  item => item.District_name !== "" && item.District_name !== null
              )
              .forEach((item, index) => {
                styles[`${item.id}`] = new TMap.PolygonStyle({
                  color: randomColor(),
                  showBorder: true,
                  borderColor: "rgba(0,0,0,0.8)",
                  borderWidth: 1,
                });
                polygon.setStyles(styles);

                let outlinePath;
                // 坐标点转换
                JSON.parse(item.Region_profile).forEach(val => {
                  outlinePath = transtionCopy(val).map(
                      item => new TMap.LatLng(item.latitude, item.longitude)
                  );
                  let path = {
                    styleId: `${item.id}`,
                    paths: outlinePath,
                  };
                  polygon.add(path);
                });
              });
          // 继续绘制下一个子数组
          drawMap(result, index + 1);
        }, 500);
      }

      drawMap(result);
    } catch (err) {
      console.log(err);
    }
  }

  // 获取所有区域
  async function allArea(allView, label) {
    const res = await get_all_country_area();
    allProvinceMessage.value = res.data.result;

    res.data.result.forEach(item => {
      let outlinePath;
      try {
        JSON.parse(item.Region_profile).forEach(val => {
          outlinePath = transtionCopy(val).map(
              item => new TMap.LatLng(item.latitude, item.longitude)
          );
          let path = {
            id: `allView-${item.id}`,
            styleId: `${item.id}`,
            paths: outlinePath,
          };
          allPath.value.push(path);
        });
      } catch (e) {}

      const content = `${item.District_name}\n人口数：${item.Population}\n门店数：${item.Store_num}`;

      geometries_arr.value.push({
        content,
        position: new TMap.LatLng(
            item.Resident_latitude,
            item.Station_longitude
        ),
        styleId: "lable",
      });
    });
    //区域点缓存
    allView.add(allPath.value);
    label.setGeometries(geometries_arr.value);
    setTimeout(() => {
      loading.value = false;
    }, 5000);
  }

  //获取全部门店
  async function getAllStore(storeMarker, infoWindow, product_class, product) {
    storeList.value = [];
    // 获取省市区信息
    try {
      let id = getDqIdJxqId(dqList.value, jxqList.value);
      const res = await getAllStores(...id);
      // 缓存没有数据
      if (res.data.result[0][0].exist_flag === "0") {
        const res = await circulation("", "", "", id, 60, async () => {
          const result = await getAllStores(...id);
          return result;
        });
        const coreIdList = res.data.result[0][0].result || [];
        for (let i = 0; i < coreIdList.length; i++) {
          const response = await fetchData(coreIdList[i]);

          storeList.value = storeList.value.concat(
              JSON.parse(response.data.result[0].result)
          );
        }
        loading.value = false;
      }
      // 有缓存数据
      if (res.data.result[0][0].exist_flag === "1") {
        const coreIdList = res.data.result[0][0].result || [];
        for (let i = 0; i < coreIdList.length; i++) {
          const response = await fetchData(coreIdList[i]);

          storeList.value = storeList.value.concat(
              JSON.parse(response.data.result[0].result)
          );
        }

        loading.value = false;
      }

      filterStoreList.value = storeList.value;
      const eventMousemove = e =>
          mousemove(e, filterStoreList.value, infoWindow);
      const eventClick = e => click(e, filterStoreList.value, infoWindow);

      storeMarker.on("mouseover", eventMousemove);
      storeMarker.on("click", eventClick);
      all_stores.value = storeMarker;
    } catch (err) {
      console.log(err);
    }
  }

  function fetchData(id) {
    return new Promise((resolve, reject) => {
      // 模拟接口请求的延迟
      setTimeout(() => {
        if (id) {
          // 模拟请求成功，返回一个包含 id 的对象
          getStoresListByCoreId(id).then(res => {
            resolve(res);
          });
        } else {
          // 模拟请求失败
          reject(new Error("请求失败"));
        }
      }, 100);
    });
  }
  //点击产品类别/产品获取的门店
  async function getProStore(
      storeMarker,
      infoWindow,
      product_class,
      product,
      product_group
  ) {
    proStoreList.value = [];
    try {
      let id = getDqIdJxqId(store.dqList, store.jxqList);
      const res = await get_pro_stores_all(
          product_class,
          product,
          product_group,
          ...id
      );
      if (res.data.result[0][0].exist_flag === "0") {
        const res = await circulation(
            product_class,
            product,
            product_group,
            id,
            30,
            async () => {
              const result = await get_pro_stores_all(
                  product_class,
                  product,
                  product_group,
                  ...id
              );
              return result;
            }
        );

        const coreIdList = res.data.result[0][0].result || [];
        for (let i = 0; i < coreIdList.length; i++) {
          const response = await fetchData(coreIdList[i]);
          proStoreList.value = proStoreList.value.concat(
              JSON.parse(response.data.result[0].result)
          );
        }
        loading.value = false;
      } else {
        const coreIdList = res.data.result[0][0].result;
        for (let i = 0; i < coreIdList.length; i++) {
          const response = await fetchData(coreIdList[i]);
          proStoreList.value = proStoreList.value.concat(
              JSON.parse(response.data.result[0].result)
          );
        }

        loading.value = false;
      }

      proFilterStoreList.value = proStoreList.value;
      const eventMousemove = e => mousemove(e, proStoreList.value, infoWindow);
      const eventClick = e => click(e, proStoreList.value, infoWindow);
      storeMarker.on("mouseover", eventMousemove);
      storeMarker.on("click", eventClick);
      all_stores.value = storeMarker;
    } catch (err) {
      console.log(err);
    }
  }

  // 获取全部车辆
  async function getAllcars(carMarker) {
    // console.log("filterAllUsers.value", filterAllUsers.value);

    try {
      loading.value = true;
      if (filterAllUsers.value.length === 0) {
        const resUser = await get_all_users("all");
        // allUsers.value = resUser.data.result[0];
        filterAllUsers.value = resUser.data.result[0];

        const imei_list = filterAllUsers.value
            .filter(item => item.IMEI_code !== null)
            .map(item => {
              return "0" + item.IMEI_code;
            })
            .join(",");

        // const resCar = await get_all_cars();

        const resCar = await get_update_cars(imei_list);

        carList.value = resCar.data.result;
        const _userList = filterAllUsers.value.filter(
            item => item.IMEI_code !== null
        );
        _userList.forEach(item => {
          carList.value.forEach(_item => {
            if ("0" + item.IMEI_code === _item.imei) {
              _item.userName = item.Master_data_person_name;
              _item.carNumber = item.Licence_number;
            }
          });
        });
      } else {
        const imei_list = filterAllUsers.value
            .filter(item => item.IMEI_code !== null)
            .map(item => {
              return "0" + item.IMEI_code;
            })
            .join(",");

        const resCar = await get_update_cars(imei_list);

        carList.value = resCar.data.result;
        const _userList = filterAllUsers.value.filter(
            item => item.IMEI_code !== null
        );
        _userList.forEach(item => {
          carList.value.forEach(_item => {
            if ("0" + item.IMEI_code === _item.imei) {
              _item.userName = item.Master_data_person_name;
              _item.carNumber = item.Licence_number;
            }
          });
        });
      }
    } catch (err) {
      console.log(err);
    }
    loading.value = false;
    carMarker.on("click", e => {
      console.log("filterAllUsers.value", allUsers.value);

      const currentUser = filterAllUsers.value.find(
          item => "0" + item.IMEI_code === e.geometry.id
      );
      console.log("currentUser", currentUser);

      const newPageUrl = `/pages/Map/components/MapTrack?imei=${e.geometry.id}&userid=${currentUser.id}`;
      uni.navigateTo({
        url: newPageUrl,
      });
    });
  }

  // 更新车辆
  async function updateCar(carMarker) {
    const imei_list = filterAllUsers.value
        .filter(item => item.IMEI_code !== null)
        .map(item => {
          return "0" + item.IMEI_code;
        })
        .join(",");
    const res = await get_update_cars(imei_list);
    carList.value = res.data.result;
    const _userList = filterAllUsers.value.filter(
        item => item.IMEI_code !== null
    );
    _userList.forEach(item => {
      carList.value.forEach(_item => {
        if ("0" + item.IMEI_code === _item.imei) {
          _item.userName = item.Master_data_person_name;
          _item.carNumber = item.Licence_number;
        }
      });
    });
    const { points, styles } = processVehicleStyle(
        carList.value,
        filterAllUsers.value
    );
    carMarker.setStyles(styles);
    carMarker.setGeometries(points);
  }

  //改变现实控件
  async function changeChecked(
      polygon,
      storeMarker,
      carMarker,
      infoWindow,
      allView,
      label,
      streetLayer
  ) {
    infoWindow.close();

    // 如果街道开关打开,则更新街道
    if (checkedOpenStreet.value) {
      if (selectObj.value.jxqName === "全部") {
        checkedOpenStreet.value = false;
        message.warn("未选择经销区");
      } else {
        streetLayer.setVisible(true);
      }
    } else {
      streetLayer.setVisible(false);
    }

    // 如果车辆开关打开,则更新车辆位置
    if (checkedCar.value) {
      timerCar = setInterval(() => {
        updateCar(carMarker);
      }, 30000);
      if (carList.value.length === 0) {
        await getAllcars(carMarker);
        const { points, styles } = processVehicleStyle(
            carList.value,
            filterAllUsers.value
        );
        carMarker.setStyles(styles);
        carMarker.setGeometries(points);
        return;
      }

      const { points, styles } = processVehicleStyle(
          carList.value,
          filterAllUsers.value
      );
      carMarker.setStyles(styles);
      carMarker.setGeometries(points);
    } else {
      carMarker.setGeometries([]);
      clearInterval(timerCar);
    }

    // 如果区域开关打开,则更新区域
    if (checkedArea.value) {
      polygon.setGeometries(filterAreaList.value);
    } else {
      polygon.setGeometries([]);
    }

    // 如果门店开关打开,则更新门店 || 如果是类型门店打开，则显示类型门店图例
    if (checkedStore.value || checkedTypeStore.value) {
      pickOn.value = ""; // 图例值为空

      // 判断是否显示当前区域信息
      const hasCurrentAreaMessage = !!selectObj.value.currentAreaMessage;

      // 判断是否存在产品或产品类别
      const hasProductClassOrProduct =
          !!selectObj.value.productClass || !!selectObj.value.product;

      // 如果当前区域值存在
      if (hasCurrentAreaMessage) {
        if (hasProductClassOrProduct) {
          loading.value = true;
          await getProStore(
              storeMarker,
              infoWindow,
              selectObj.value.productClass,
              selectObj.value.product,
              selectObj.value.productGroup
          );
          getAreaProStore(areaCodeGlobal.value, proStoreList.value, res => {
            filterStoreList.value = res;
          });
          markStore(filterStoreList.value, storeMarker, true);
          loading.value = false;
        } else {
          loading.value = true;
          await getAllStore(storeMarker, infoWindow);
          getAreaProStore(areaCodeGlobal.value, storeList.value, res => {
            filterStoreList.value = res;
          });
          markStore(filterStoreList.value, storeMarker);
          loading.value = false;
        }
      } else {
        if (hasProductClassOrProduct) {
          loading.value = true;
          await getProStore(
              storeMarker,
              infoWindow,
              selectObj.value.productClass,
              selectObj.value.product,
              selectObj.value.productGroup
          );
          markStore(proStoreList.value, storeMarker, true);
          loading.value = false;
        } else {
          loading.value = true;
          await getAllStore(storeMarker, infoWindow);
          markStore(filterStoreList.value, storeMarker);
          loading.value = false;
        }
      }
    } else {
      storeMarker.setGeometries([]);
      // polygon.setGeometries(areaList.value);
    }

    // 如果区域规划开关打开,则更新区域
    if (checkedAllArea.value) {
      loading.value = true;
      if (geometries_arr.value.length === 0) {
        await allArea(allView, label);
      } else {
        loading.value = false;
        allView.add(allPath.value);
        label.setGeometries(geometries_arr.value);
      }
    } else {
      allView.setGeometries([]);
      label.setGeometries([]);
      loading.value = false;
    }

    // 如果是已开门店开关打开，则过滤已开门店
    if (checkedOpenStore.value) {
      // 判断是否显示当前区域信息
      const hasCurrentAreaMessage = !!selectObj.value.currentAreaMessage;

      // 判断是否存在产品或产品类别
      const hasProductClassOrProduct =
          !!selectObj.value.productClass || !!selectObj.value.product;

      // 如果当前区域值存在
      if (hasCurrentAreaMessage) {
        if (hasProductClassOrProduct) {
          loading.value = true;
          await getProStore(
              storeMarker,
              infoWindow,
              selectObj.value.productClass,
              selectObj.value.product,
              selectObj.value.productGroup
          );
          getAreaProStore(areaCodeGlobal.value, proStoreList.value, res => {
            filterStoreList.value = res.filter(
                item =>
                    item.Store_contribution !== null &&
                    item.Store_contribution > 0 &&
                    item.Cyclist_name !== null
            ); //过滤出已开门店
          });
          markStore(filterStoreList.value, storeMarker, true);
          loading.value = false;
        } else {
          loading.value = true;
          await getAllStore(storeMarker, infoWindow);
          getAreaProStore(areaCodeGlobal.value, storeList.value, res => {
            filterStoreList.value = res.filter(
                item =>
                    item.Store_contribution !== null &&
                    item.Store_contribution > 0 &&
                    item.Cyclist_name !== null
            ); //过滤出已开门店
          });
          markStore(filterStoreList.value, storeMarker);
          loading.value = false;
        }
      } else {
        if (hasProductClassOrProduct) {
          loading.value = true;
          await getProStore(
              storeMarker,
              infoWindow,
              selectObj.value.productClass,
              selectObj.value.product,
              selectObj.value.productGroup
          );
          markStore(proStoreList.value, storeMarker, true);
          loading.value = false;
        } else {
          loading.value = true;
          await getAllStore(storeMarker, infoWindow);
          const filterData = filterStoreList.value.filter(
              item =>
                  item.Store_contribution !== null &&
                  item.Store_contribution > 0 &&
                  item.Cyclist_name !== null
          ); //过滤出已开门店
          markStore(filterData, storeMarker);
          loading.value = false;
        }
      }
    }
  }

  // 获取条件下的 门店
  const getAreaProStore = (code, store, callback) => {
    let filterS = [];
    code.forEach(it => {
      const a = store.filter(
          item =>
              item.Store_province === it.Provincial_name &&
              item.Store_city === it.City_name &&
              item.Store_district === it.District_name
      );
      filterS = [...filterS, ...a];
    });
    filterAllUsers.value.forEach(it => {
      const a = store.filter(item => {
        it.id === item.Cyclist_name;
      });
      filterS = [...filterS, ...a];
    });
    callback(filterS);
  };

  // 标记门店
  const markStore = (store, instance, isPro = false) => {
    if (!isPro) {
      if (checkedStore.value || checkedOpenStore.value) {
        const { points, styles } = processStoreStyle(store);
        instance.setStyles(styles);
        instance.setGeometries(points);
      }
      if (checkedTypeStore.value) {
        const { points } = processTypeStoreStyle(store);
        const styles = markerTypeStoreStyle();
        instance.setStyles(styles);
        instance.setGeometries(points);
      }
    } else {
      const { points, styles } = processProStoreStyle(store);
      instance.setStyles(styles);
      instance.setGeometries(points);
    }
  };

  // 获取全部大区
  async function getAllRegion() {
    try {
      let res;
      if (
          userStore.userInfo.Role_grade === "决策层" ||
          userStore.userInfo.Role_grade === "运营总监" ||
          userStore.userInfo.Role_grade === "主管"
      ) {
        res = await GetAllDqNew();
      }
      if (userStore.userInfo.Role_grade === "经销商" || userStore.userInfo.Role_grade === "城市经理") {
        res = await all_region_sell_new(userStore.userInfo.id);
        // console.log("获取全部大区", res);

        selectObj.value.dqName =
            res.data.result[0]?.DistributionOrganizationName || "";
      }
      if (userStore.userInfo.Role_grade === "大区总") {
        res = await all_region_dqz_new(userStore.userInfo.id);
        console.log('res',res)
        if(res.data.result.length > 1){
          selectObj.value.dqName = '全部'
          selectObj.value.dqId = 0;
          dqId.value = 0;
        }else{
          selectObj.value.dqName =
              res.data.result[0].DistributionOrganizationName;
          selectObj.value.dqId = res.data.result[0].id;
          dqId.value = res.data.result[0].id;
        }

      }
      if (userStore.userInfo.Role_grade === "省区总") {
        res = await all_region_sqz_new(userStore.userInfo.id);
        selectObj.value.dqName =
            res.data.result[0].DistributionOrganizationName;
        selectObj.value.dqId = res.data.result[0].id;
        dqId.value = res.data.result[0].id;
      }

      const aa = res.data.result.map(item => {
        return {
          label: item.DistributionOrganizationName,
          value: item.DistributionOrganizationName,
          dqId: item.id,
        };
      });
      dqList.value = aa.reduce((uniqueItems, currentItem) => {
        // 如果当前遍历的item的value不在uniqueItems中任何一个item的value里，则添加进uniqueItems
        if (!uniqueItems.some(item => item.value === currentItem.value)) {
          uniqueItems.push(currentItem);
        }
        return uniqueItems;
      }, []);
      dqList.value.unshift({
        label: "全部",
        value: "全部",
        dqId: 0,
      });
    } catch (e) {
      console.log(e);
    }
  }

  // 获取全部省区
  async function getAllProvinces() {
    try {
      let res;
      if (
          userStore.userInfo.Role_grade === "决策层" ||
          userStore.userInfo.Role_grade === "运营总监" ||
          userStore.userInfo.Role_grade === "主管"
      ) {
        res = await GetAllSqNew();
      }
      if (userStore.userInfo.Role_grade === "经销商" || userStore.userInfo.Role_grade === "城市经理") {
        res = await all_provinces_sell_new(userStore.userInfo.id);
        // console.log("获取全部大区", res);

        selectObj.value.sqName =
            res.data.result[0]?.DistributionOrganizationName || "";
      }
      if (userStore.userInfo.Role_grade === "大区总") {
        res = await all_provinces_dqz_new(userStore.userInfo.id);
        console.log('res',res)


        if(res.data.result.length > 1){
          selectObj.value.sqName = "全部";
          selectObj.value.sqId = 0;
          sqId.value = 0;
        }else{
          selectObj.value.sqName =
              res.data.result[0].DistributionOrganizationName;
          selectObj.value.sqId = res.data.result[0].id;
          sqId.value = res.data.result[0].id;
        }

      }
      if (userStore.userInfo.Role_grade === "省区总") {
        res = await all_provinces_sqz_new(userStore.userInfo.id);
        if(res.data.result.length > 1){
          selectObj.value.sqName = "全部";
          selectObj.value.sqId = 0;
          sqId.value = 0;
        }else{
          selectObj.value.sqName =
              res.data.result[0].DistributionOrganizationName;
          selectObj.value.sqId = res.data.result[0].id;
          sqId.value = res.data.result[0].id;
        }
        // selectObj.value.sqName =
        //     res.data.result[0].DistributionOrganizationName;
        // selectObj.value.sqId = res.data.result[0].id;
        // sqId.value = res.data.result[0].id;
      }

      const aa = res.data.result.map(item => {
        return {
          label: item.DistributionOrganizationName,
          value: item.DistributionOrganizationName,
          sqId: item.id,
        };
      });
      sqList.value = aa.reduce((uniqueItems, currentItem) => {
        // 如果当前遍历的item的value不在uniqueItems中任何一个item的value里，则添加进uniqueItems
        if (!uniqueItems.some(item => item.value === currentItem.value)) {
          uniqueItems.push(currentItem);
        }
        return uniqueItems;
      }, []);
      sqList.value.unshift({
        label: "全部",
        value: "全部",
        dqId: 0,
      });
      sqCache.value = sqList.value;
    } catch (e) {
      console.log(e);
    }
  }

  // 获取全部经销区
  async function getAllOutRegion(streetLayer) {
    try {
      let res;
      if (userStore.userInfo.Role_grade === "决策层") {
        res = await getNationalAreasNew();
      }
      if (userStore.userInfo.Role_grade === "经销商" || userStore.userInfo.Role_grade === "城市经理") {
        res = await getAllAreas_regionnew(userStore.userInfo.id);
        selectObj.value.jxqName =
            res.data.result[0].DistributionOrganizationName;

        // 获取该经销区的街道数据
        const streetAreaCode = res.data.result[0].id;
        const { data } = await getAreaStreets(streetAreaCode);
        let geometries_street = [];
        data.result.forEach(item => {
          geometries_street.push({
            id: "label", // 点图形数据的标志信息
            styleId: "label", // 样式id
            position: new TMap.LatLng(item.latitude, item.longitude), // 标注点位置
            content: `${item.place_name}|${item.population}`, // 标注文本
          });
        });
        streetLayer.setGeometries(geometries_street);
      }
      if (
          userStore.userInfo.Role_grade === "大区总" ||
          userStore.userInfo.Role_grade === "省区总"
      ) {
        // const jxqIdList = userStore.userInfo.jxqId.join(",");
        // res = await getAllAreas_region(jxqIdList);
        res = await getAllAreas_regionnew(userStore.userInfo.id);
      }
      console.log('resresres',res)
      const aa = res.data.result.map(item => {
        return {
          label: item.DistributionOrganizationName,
          value: item.DistributionOrganizationName,
          jxqId: item.Distribution_territory,
        };
      });
      console.log('aa',aa)
      jxqList.value = aa.reduce((uniqueItems, currentItem) => {

        // 如果当前遍历的item的value不在uniqueItems中任何一个item的value里，则添加进uniqueItems
        if (!uniqueItems.some(item => item.value === currentItem.value)) {
          uniqueItems.push(currentItem);
        }
        return uniqueItems;
      }, []);

      jxqList.value.unshift({
        label: "全部",
        value: "全部",
        dqId: 0,
      });
      console.log('jxqList.value',jxqList.value)
      jxqCache.value = jxqList.value;
    } catch (e) {
      console.log(e);
    }
  }
  async function getAllXZQY() {
    try {
      let res;
      if (userStore.userInfo.Role_grade === "决策层") {
        res = await getNationalAreasNew();
      }
      if (userStore.userInfo.Role_grade === "经销商" || userStore.userInfo.Role_grade === "城市经理") {
        res = await getAllAreas_regionnew(userStore.userInfo.id);
        selectObj.value.xzqyName =
            res.data.result[0].Provincial_name+res.data.result[0].City_name+res.data.result[0].District_name;

        // 获取该经销区的街道数据
        const streetAreaCode = res.data.result[0].id;
        const { data } = await getAreaStreets(streetAreaCode);
        let geometries_street = [];
        data.result.forEach(item => {
          geometries_street.push({
            id: "label", // 点图形数据的标志信息
            styleId: "label", // 样式id
            position: new TMap.LatLng(item.latitude, item.longitude), // 标注点位置
            content: `${item.place_name}|${item.population}`, // 标注文本
          });
        });
        streetLayer.setGeometries(geometries_street);
      }
      if (
          userStore.userInfo.Role_grade === "大区总" ||
          userStore.userInfo.Role_grade === "省区总"
      ) {
        // const jxqIdList = userStore.userInfo.jxqId.join(",");
        // res = await getAllAreas_region(jxqIdList);
        res = await getAllAreas_regionnew(userStore.userInfo.id);
      }
      console.log('resresres',res)
      const aa = res.data.result.map(item => {
        return {
          label: item.Provincial_name+item.City_name+item.District_name,
          value: item.Provincial_name+item.City_name+item.District_name,
          jxqId: item.Distribution_territory,
          xzqyId: item.id,
        };
      });
      console.log('aa',aa)
      xzqyList.value = aa.reduce((uniqueItems, currentItem) => {
        console.log('uniqueItems',uniqueItems)
        console.log('currentItem',currentItem)
        // 如果当前遍历的item的value不在uniqueItems中任何一个item的value里，则添加进uniqueItems
        if (!uniqueItems.some(item => item.value === currentItem.value)) {
          uniqueItems.push(currentItem);
        }
        return uniqueItems;
      }, []);

      xzqyList.value.unshift({
        label: "全部",
        value: "全部",
        dqId: 0,
      });
      console.log('xzqyList.value',xzqyList.value)
      xzqyCache.value = xzqyList.value;
    } catch (e) {
      console.log(e);
    }
  }
  // 获取全部经销商
  async function getAllJxsUserList() {
    try {
      let res;
      if (userStore.userInfo.Role_grade === "决策层") {
        res = await carMapGetAllJxs();
      }
      if (userStore.userInfo.Role_grade === "经销商") {
        selectObj.value.jxsName = userStore.userInfo.userName;
        // 获取分销商
        res = await get_all_users(userStore.userInfo.jxqId[0]);
        filterAllUsers.value = res.data.result[0];
        fxsUserList.value = res.data.result[0].map(item => {
          return {
            label: item.Master_data_person_name,
            value: item.Master_data_person_name,
            IMEI_code: item.IMEI_code,
            Licence_number: item.Licence_number,
            id: item.id,
          };
        });
        fxsUserList.value.unshift({
          label: "全部",
          value: "全部",
          IMEI_code: 0,
          Licence_number: 0,
          id: 0,
        });
        selectObj.value.currentAreaMessage = provinceMessage.value[0];
        areaCodeGlobal.value = provinceMessage.value;
        selectObj.value.fxsName = "全部";
        selectObj.value.jxsId = userStore.userInfo.id;
      }
      if (
          userStore.userInfo.Role_grade === "大区总" ||
          userStore.userInfo.Role_grade === "省区总"
      ) {
        const jxqIdList = userStore.userInfo.jxqId.join(",");
        res = await getAllUsers_dqz(jxqIdList);

        // 获取大区总的全部分销商
        const jsqIDlist = userStore.userInfo.jxqId.join(",");
        const res2 = await get_all_users(jsqIDlist);
        filterAllUsers.value = res2.data.result[0];
      }
      const aa = res.data.result.map(item => {
        return {
          label: item.jxsName1,
          value: item.jxsName1,
          jxqId: item.jxqId,
          jxsId: item.jxsId,
        };
      });

      jxsUserList.value = aa.reduce((uniqueItems, currentItem) => {
        // 如果当前遍历的item的value不在uniqueItems中任何一个item的value里，则添加进uniqueItems
        if (!uniqueItems.some(item => item.value === currentItem.value)) {
          uniqueItems.push(currentItem);
        }
        return uniqueItems;
      }, []);

      jxsUserList.value.unshift({
        label: "全部",
        value: "全部",
        jxqId: 0,
        jxsId: 0,
      });
      allJxsList.value = jxsUserList.value;
    } catch (e) {
      console.log(e);
    }
  }

  // 获取所有产品
  async function getAllProduct(category) {
    const res = await getAllProd(category);
    product_list.value = res.data.result.map(item => {
      return {
        value: item.Small_boss_product_name,
        label: item.Small_boss_product_name,
        code: item.Small_boss_product_code,
      };
    });
    product_list.value.unshift({
      value: "全部",
      label: "全部",
      code: "",
    });
  }

  // 获取所有产品分类
  async function getProductCategory() {
    const [res, res1] = await Promise.all([
      getAllProdCategory(),
      getProductGroup(),
    ]);
    let temp = res1.data.result.map(item => ({
      value: item.product_group_name,
      label: item.product_group_name,
      groupId: item.id,
    }));
    const productCategories = res.data.result.map(item => ({
      value: item.Small_boss_subject_name,
      label: item.Small_boss_subject_name,
      code: item.Small_boss_subject_code,
    }));
    product_list_category.value = [
      { value: "全部", label: "全部", code: "" },
      ...productCategories,
      ...temp,
    ];
  }

  // 选择大区
  async function chooseDq(map, polygon, storeMarker, carMarker, infoWindow, v) {
    // console.log('map',map)
    // console.log('polygon',polygon)
    // console.log('storeMarker',storeMarker)
    // console.log('carMarker',carMarker)
    // console.log('infoWindow',infoWindow)
    console.log('v',v)


    selectObj.value.dqId = v.dqId;
    dqId.value = v.dqId;

    carMarker.setGeometries([]);
    infoWindow.close();
    selectObj.value.sqName = "全部";
    selectObj.value.jxqName = "全部";
    selectObj.value.xzqyName = "全部";
    selectObj.value.jxsName = null;
    selectObj.value.fxsName = null;
    selectObj.value.province = null;
    selectObj.value.currentAreaMessage = null;
    selectObj.value.areaName = "数据";

    // 如果选择全部
    if (v.label === "全部") {
      if (checkedStore.value) {
        if (
            (selectObj.value.productClass === null ||
                selectObj.value.productClass === "") &&
            (selectObj.value.product === null || selectObj.value.product === "")
        ) {
          loading.value = true;
          await getAllStore(storeMarker, infoWindow);
          markStore(storeList.value, storeMarker);
          loading.value = false;
        } else {
          loading.value = true;
          await getProStore(
              storeMarker,
              infoWindow,
              selectObj.value.productClass,
              selectObj.value.product,
              selectObj.value.productGroup
          );
          markStore(proStoreList.value, storeMarker, true);
          loading.value = false;
        }
      }
      sqList.value = sqCache.value;
      jxqList.value = jxqCache.value;
      jxsUserList.value = allJxsList.value;
      polygon.setGeometries(areaList.value);
    } else {
      // 如果选择大区
      loading.value = true;
      filterAllUsers.value = []; // 清空用户列表
      const res2 = await getRegionUnderlingNew2(v.dqId);
      console.log('res2',res2)
      console.log('sqCache.value',sqCache.value)
      try {
        sqList.value = sqCache.value.filter(item => {
          return res2.data.result.some(node => {
            return item.label === node.DistributionOrganizationName;
          });
        });
        sqList.value.unshift({
          label: "全部",
          value: "全部",
          sqId: 0,
        });
      }catch (e) {
        console.log(e);
        loading.value = false;
      }
      console.log('sqList.value',sqList.value)
      const res = await getRegionUnderlingNew(v.dqId);
      // console.log(res, "dqIdRes");
      // console.log( "jxqCache.value",jxqCache.value);
      try {
        jxqList.value = jxqCache.value.filter(item => {
          return res.data.result.some(node => {
            return item.label === node.DistributionOrganizationName;
          });
        });
        jxqList.value.unshift({
          label: "全部",
          value: "全部",
          jxqId: 0,
        });
        // console.log( "jxqList.value",jxqList.value);
        // console.log( "provinceMessage.value",provinceMessage.value);
        const areaCode = provinceMessage.value.filter(item => {
          return jxqList.value.some(node => {
            return node.jxqId === item.Distribution_territory;
          });
        });
        selectObj.value.currentAreaMessage = areaCode;
        areaCodeGlobal.value = areaCode;
        console.log(areaCode, "areaCode");

        if (areaCode.length === 0) {
          message.info("未找到大区");
          loading.value = false;
        }

        const jxqIdList = areaCode.map(item => item.Distribution_territory);



        const __res = await get_all_users(jxqIdList.join());
        filterAllUsers.value = filterAllUsers.value.concat(
            __res.data.result[0]
        );

        filterAllUsers.value = Array.from(new Set(filterAllUsers.value));
        const tempUser = ref([]);
        tempUser.value.unshift({
          label: "全部",
          value: "全部",
          jxqId: 0,
          jxsId: 0,
        });
        for (const item of areaCode) {
          allJxsList.value.forEach(node => {
            if (item.Distribution_territory === node.jxqId) {
              tempUser.value.push(node);
            }
          });
        }
        tempUser.value = Array.from(new Set(tempUser.value));
        jxsUserList.value = tempUser.value; // 选择大区时将该区域的库长也拿到

        filterAreaList.value = [];
        areaCode.forEach(item_s => {
          const a = areaList.value.filter(item => item.styleId === item_s.id);
          filterAreaList.value = filterAreaList.value.concat(a);
        });
        polygon.setGeometries(filterAreaList.value);
        loading.value = false;
      } catch (e) {
        console.log(e);
        loading.value = false;
      }

      // 打开门店开关
      if (checkedStore.value) {
        if (
            (selectObj.value.productClass === null ||
                selectObj.value.productClass === "") &&
            (selectObj.value.product === null || selectObj.value.product === "")
        ) {
          loading.value = true;
          await getAllStore(storeMarker, infoWindow);
          getAreaProStore(areaCodeGlobal.value, storeList.value, res => {
            filterStoreList.value = res;
          });
          markStore(filterStoreList.value, storeMarker);
          loading.value = false;
        } else {
          loading.value = true;
          await getProStore(
              storeMarker,
              infoWindow,
              selectObj.value.productClass,
              selectObj.value.product,
              selectObj.value.productGroup
          );
          getAreaProStore(areaCodeGlobal.value, proStoreList.value, res => {
            proFilterStoreList.value = res;
          });
          markStore(proFilterStoreList.value, storeMarker, true);
          loading.value = false;
        }
      }
      map.setCenter(
          new TMap.LatLng(
              areaCodeGlobal.value[0].Resident_latitude,
              areaCodeGlobal.value[0].Station_longitude
          )
      );
      map.setZoom(10);
    }
  }

  // 选择省区
  async function chooseSq(map, polygon, storeMarker, carMarker, infoWindow, v) {
    // console.log('map',map)
    // console.log('polygon',polygon)
    // console.log('storeMarker',storeMarker)
    // console.log('carMarker',carMarker)
    // console.log('infoWindow',infoWindow)
    console.log('SQv',v)
    selectObj.value.dqId = v.dqId;
    dqId.value = v.dqId;

    carMarker.setGeometries([]);
    infoWindow.close();

    selectObj.value.jxqName = "全部";
    selectObj.value.jxqId = 0;
    selectObj.value.xzqyName = "全部";
    selectObj.value.xzqyId = 0;
    selectObj.value.jxsName = null;
    selectObj.value.fxsName = null;
    selectObj.value.province = null;
    selectObj.value.currentAreaMessage = null;
    selectObj.value.areaName = "数据";

    sqId.value = v.sqId
    // 如果选择全部
    if (v.label === "全部") {

      if (checkedStore.value) {
        if (
            (selectObj.value.productClass === null ||
                selectObj.value.productClass === "") &&
            (selectObj.value.product === null || selectObj.value.product === "")
        ) {
          loading.value = true;
          await getAllStore(storeMarker, infoWindow);
          markStore(storeList.value, storeMarker);
          loading.value = false;
        } else {
          loading.value = true;
          await getProStore(
              storeMarker,
              infoWindow,
              selectObj.value.productClass,
              selectObj.value.product,
              selectObj.value.productGroup
          );
          markStore(proStoreList.value, storeMarker, true);
          loading.value = false;
        }
      }
      jxqList.value = jxqCache.value;
      jxsUserList.value = allJxsList.value;
      polygon.setGeometries(areaList.value);
    } else {
      // 如果选择省区
      loading.value = true;
      filterAllUsers.value = []; // 清空用户列表
      const res = await getProvincesUnderlingNew(v.sqId);
      console.log(res, "dqIdRes");
      console.log( "dqIdRes",jxqCache.value);
      try {
        jxqList.value = jxqCache.value.filter(item => {
          return res.data.result.some(node => {
            return item.label === node.DistributionOrganizationName;
          });
        });
        jxqList.value.unshift({
          label: "全部",
          value: "全部",
          jxqId: 0,
        });
        console.log('provinceMessage.value',provinceMessage.value)

        const areaCode = provinceMessage.value.filter(item => {
          return jxqList.value.some(node => {
            return node.jxqId === item.Distribution_territory;
          });
        });
        selectObj.value.currentAreaMessage = areaCode;
        areaCodeGlobal.value = areaCode;
        console.log(areaCode, "areaCode");

        if (areaCode.length === 0) {
          message.info("未找到经销区");
          loading.value = false;
        }

        const jxqIdList = areaCode.map(item => item.Distribution_territory);
        const __res = await get_all_users(jxqIdList.join());
        filterAllUsers.value = filterAllUsers.value.concat(
            __res.data.result[0]
        );
        // for (const item of areaCode) {
        // const res = await get_all_users(item.Distribution_territory);
        // filterAllUsers.value = filterAllUsers.value.concat(res.data.result[0]);
        // }
        // 去重
        filterAllUsers.value = Array.from(new Set(filterAllUsers.value));
        const tempUser = ref([]);
        tempUser.value.unshift({
          label: "全部",
          value: "全部",
          jxqId: 0,
          jxsId: 0,
        });
        for (const item of areaCode) {
          allJxsList.value.forEach(node => {
            if (item.Distribution_territory === node.jxqId) {
              tempUser.value.push(node);
            }
          });
        }
        tempUser.value = Array.from(new Set(tempUser.value));
        jxsUserList.value = tempUser.value; // 选择大区时将该区域的库长也拿到

        filterAreaList.value = [];
        areaCode.forEach(item_s => {
          const a = areaList.value.filter(item => item.styleId === item_s.id);
          filterAreaList.value = filterAreaList.value.concat(a);
        });
        polygon.setGeometries(filterAreaList.value);
        loading.value = false;
      } catch (e) {
        console.log(e);
        loading.value = false;
      }

      // 打开门店开关
      if (checkedStore.value) {
        if (
            (selectObj.value.productClass === null ||
                selectObj.value.productClass === "") &&
            (selectObj.value.product === null || selectObj.value.product === "")
        ) {
          loading.value = true;
          await getAllStore(storeMarker, infoWindow);
          getAreaProStore(areaCodeGlobal.value, storeList.value, res => {
            filterStoreList.value = res;
          });
          markStore(filterStoreList.value, storeMarker);
          loading.value = false;
        } else {
          loading.value = true;
          await getProStore(
              storeMarker,
              infoWindow,
              selectObj.value.productClass,
              selectObj.value.product,
              selectObj.value.productGroup
          );
          getAreaProStore(areaCodeGlobal.value, proStoreList.value, res => {
            proFilterStoreList.value = res;
          });
          markStore(proFilterStoreList.value, storeMarker, true);
          loading.value = false;
        }
      }
      map.setCenter(
          new TMap.LatLng(
              areaCodeGlobal.value[0].Resident_latitude,
              areaCodeGlobal.value[0].Station_longitude
          )
      );
      map.setZoom(10);
    }
  }

  // 选择经销区
  async function chooseJxq(
      map,
      polygon,
      storeMarker,
      carMarker,
      infoWindow,
      v,
      streetLayer
  ) {
    // console.log('map',map)
    // console.log('polygon',polygon)
    // console.log('storeMarker',storeMarker)
    // console.log('carMarker',carMarker)
    // console.log('infoWindow',infoWindow)
    console.log('v',v)
    // console.log('streetLayer',streetLayer)
    jxqId.value = v.jxqId
    selectObj.value.xzqyName = "全部";
    selectObj.value.xzqyId = 0;
    try {
      carMarker.setGeometries([]);
      infoWindow.close();
      const jxsMatchList = ref([]);

      jxsMatchList.value.unshift({
        label: "全部",
        value: "全部",
        jxqId: 0,
        jxsId: 0,
      });
      allJxsList.value.forEach(item => {
        if (item.jxqId === v.jxqId) {
          jxsMatchList.value.push(item);
        }
        selectObj.value.jxsName = jxsMatchList.value[0];
      });
      userCache.value = jxsMatchList.value;

      jxsUserList.value = jxsMatchList.value;
      selectObj.value.fxsName = null;
      selectObj.value.province = null;
      // 经销区选择全部时执行的逻辑
      if (v.label === "全部") {
        streetLayer.setGeometries([]);
        streetLayer.setVisible(false);
        checkedOpenStreet.value = false;
        selectObj.value.jxqName = "全部";
        selectObj.value.jxqId = 0;
        jxqId.value = 0;
        xzqyList.value = xzqyCache.value;


        if (selectObj.value.sqName !== "全部") {
          const list = sqCache.value.filter(
              item => item.sqId === sqId.value
          );
          console.log('list-----------------------------',list)
          const obj = {
            sqId: list[0].sqId,
            label: list[0].label,
            value: list[0].value,
          };
          await chooseSq(map, polygon, storeMarker, carMarker, infoWindow, obj);
          return;
        } else {
          selectObj.value.jxsName = "全部";
          selectObj.value.currentAreaMessage = null;
          filterAreaList.value = areaList.value;
          filterStoreList.value = storeList.value;
          filterAllUsers.value = allUsers.value;
          selectObj.value.areaName = "数据";
          polygon.setGeometries(areaList.value);
          // 如果产品类别和产品都为空
          if (checkedStore.value) {
            if (
                (selectObj.value.productClass === null ||
                    selectObj.value.productClass === "") &&
                (selectObj.value.product === null ||
                    selectObj.value.product === "")
            ) {
              loading.value = true;
              await getAllStore(storeMarker, infoWindow);
              markStore(storeList.value, storeMarker);
              loading.value = false;
            } else {
              loading.value = true;
              await getProStore(
                  storeMarker,
                  infoWindow,
                  selectObj.value.productClass,
                  selectObj.value.product,
                  selectObj.value.productGroup
              );
              markStore(proStoreList.value, storeMarker, true);
              loading.value = false;
            }
          }

          if (checkedCar.value) {
            const _carPoints = processVehicleStyle(
                carList.value,
                allUsers.value
            );
            carMarker.setStyles(_carPoints.styles);
            carMarker.setGeometries(_carPoints.points);
          }

          return;
        }
      }

      console.log('jxqCache.value',jxqCache.value)
      console.log('xzqyCache.value',xzqyCache.value)
      xzqyList.value = xzqyCache.value.filter(item => {

          return item.jxqId === v.jxqId;

      });
      xzqyList.value.unshift({
        label: "全部",
        value: "全部",
        dqId: 0,
      });
      console.log('xzqyList.value',xzqyList.value)

      const areaCode = provinceMessage.value.filter(
          item => item.Distribution_territory === v.jxqId
      );

      if (areaCode.length > 0) {
        const streetAreaCode = areaCode[0].id;
        // 获取该经销区的街道数据
        const { data } = await getAreaStreets(streetAreaCode);
        let geometries_street = [];
        data.result.forEach(item => {
          geometries_street.push({
            id: "label", // 点图形数据的标志信息
            styleId: "label", // 样式id
            position: new TMap.LatLng(item.latitude, item.longitude), // 标注点位置
            content: `${item.place_name}|${item.population}`, // 标注文本
          });
        });
        streetLayer.setGeometries(geometries_street);
      }

      areaCodeGlobal.value = areaCode;
      console.log("v.jxqIdJXQ", v.jxqId);
      console.log("areaCode", areaCode);
      if (areaCode.length === 0) return message.info("未找到经销区");
      // 获取分销商
      const res = await get_all_users(areaCode[0].Distribution_territory);
      filterAllUsers.value = res.data.result[0];

      fxsUserList.value = res.data.result[0].map(item => {
        return {
          label: item.Master_data_person_name,
          value: item.Master_data_person_name,
          IMEI_code: item.IMEI_code,
          Licence_number: item.Licence_number,
          id: item.id,
        };
      });
      fxsUserList.value.unshift({
        label: "全部",
        value: "全部",
        IMEI_code: 0,
        Licence_number: 0,
        id: 0,
      });
      console.log('areaCode-------------',areaCode)
      selectObj.value.currentAreaMessage = areaCode[0];
      selectObj.value.areaName = areaCode[0].DistributionOrganizationName;
      selectObj.value.jxqName = areaCode[0].DistributionOrganizationName;
      // 过滤区域
      filterAreaList.value = [];
      areaCode.forEach(item_s => {
        const a = areaList.value.filter(item => item.styleId === item_s.id);
        filterAreaList.value = filterAreaList.value.concat(a);
      });
      polygon.setGeometries(filterAreaList.value);

      // 过滤门店
      if (
          checkedStore.value ||
          checkedOpenStore.value ||
          checkedTypeStore.value
      ) {
        if (
            (selectObj.value.productClass === null ||
                selectObj.value.productClass === "") &&
            (selectObj.value.product === null || selectObj.value.product === "")
        ) {
          loading.value = true;
          await getAllStore(storeMarker, infoWindow);
          if (checkedOpenStore.value) {
            getAreaProStore(areaCode, storeList.value, res => {
              filterStoreList.value = res.filter(
                  item =>
                      item.Store_contribution !== null &&
                      item.Store_contribution > 0 &&
                      item.Cyclist_name !== null
              ); //过滤出已开门店
            });
          }
          if (checkedStore.value || checkedTypeStore.value) {
            getAreaProStore(areaCode, storeList.value, res => {
              filterStoreList.value = res;
            });
          }
          markStore(filterStoreList.value, storeMarker);
          loading.value = false;
        } else {
          loading.value = true;
          await getProStore(
              storeMarker,
              infoWindow,
              selectObj.value.productClass,
              selectObj.value.product,
              selectObj.value.productGroup
          );
          getAreaProStore(areaCode, proStoreList.value, res => {
            proFilterStoreList.value = res;
          });
          markStore(proFilterStoreList.value, storeMarker);
          loading.value = false;
        }
      }

      //过滤车辆
      if (checkedCar.value) {
        const imei_list = filterAllUsers.value
            .filter(item => item.IMEI_code !== null)
            .map(item => {
              return "0" + item.IMEI_code;
            })
            .join(",");
        const res = await get_update_cars(imei_list);
        carList.value = res.data.result;

        const _carPoints = processVehicleStyle(
            carList.value,
            filterAllUsers.value
        );
        carMarker.setStyles(_carPoints.styles);
        carMarker.setGeometries(_carPoints.points);
      }
      map.setCenter(
          new TMap.LatLng(
              areaCode[0].Resident_latitude,
              areaCode[0].Station_longitude
          )
      );
      map.setZoom(10);
    } catch (err) {
      console.log(err);
    }
  }
  async function chooseXzqx(
      map,
      polygon,
      storeMarker,
      carMarker,
      infoWindow,
      v,
      streetLayer
  ) {
    console.log('map',map)
    console.log('polygon',polygon)
    console.log('storeMarker',storeMarker)
    console.log('carMarker',carMarker)
    console.log('infoWindow',infoWindow)
    console.log('v',v)
    console.log('streetLayer',streetLayer)
    try {
      carMarker.setGeometries([]);
      infoWindow.close();
      const jxsMatchList = ref([]);

      jxsMatchList.value.unshift({
        label: "全部",
        value: "全部",
        jxqId: 0,
        jxsId: 0,
      });
      allJxsList.value.forEach(item => {
        if (item.jxqId === v.jxqId) {
          jxsMatchList.value.push(item);
        }
        selectObj.value.jxsName = jxsMatchList.value[0];
      });
      userCache.value = jxsMatchList.value;

      jxsUserList.value = jxsMatchList.value;
      selectObj.value.fxsName = null;
      selectObj.value.province = null;
      // 经销区选择全部时执行的逻辑
      if (v.label === "全部") {
        streetLayer.setGeometries([]);
        streetLayer.setVisible(false);
        checkedOpenStreet.value = false;
        selectObj.value.xzqyName = "全部";
        selectObj.value.xzqyId = 0;
        xzqyId.value = 0;
        if (selectObj.value.jxqName !== "全部") {


          const list = jxqCache.value.filter(
              item => item.jxqId === jxqId.value
          );

          const obj = {
            jxqId: list[0].jxqId,
            label: list[0].label,
            value: list[0].value,
          };

          await chooseJxq(map, polygon, storeMarker, carMarker, infoWindow, obj,streetLayer);
          return;
        } else {
          selectObj.value.xzqyName = "全部";
          selectObj.value.currentAreaMessage = null;
          filterAreaList.value = areaList.value;
          filterStoreList.value = storeList.value;
          filterAllUsers.value = allUsers.value;
          selectObj.value.areaName = "数据";
          polygon.setGeometries(areaList.value);
          // 如果产品类别和产品都为空
          if (checkedStore.value) {
            if (
                (selectObj.value.productClass === null ||
                    selectObj.value.productClass === "") &&
                (selectObj.value.product === null ||
                    selectObj.value.product === "")
            ) {
              loading.value = true;
              await getAllStore(storeMarker, infoWindow);
              markStore(storeList.value, storeMarker);
              loading.value = false;
            } else {
              loading.value = true;
              await getProStore(
                  storeMarker,
                  infoWindow,
                  selectObj.value.productClass,
                  selectObj.value.product,
                  selectObj.value.productGroup
              );
              markStore(proStoreList.value, storeMarker, true);
              loading.value = false;
            }
          }

          if (checkedCar.value) {
            const _carPoints = processVehicleStyle(
                carList.value,
                allUsers.value
            );
            carMarker.setStyles(_carPoints.styles);
            carMarker.setGeometries(_carPoints.points);
          }

          return;
        }
      }
      console.log('provinceMessage.value',provinceMessage.value)
      console.log('v.xzqyId',v.xzqyId)
      const areaCode = provinceMessage.value.filter(
          item => item.id === v.xzqyId
      );
      console.log('areaCode',areaCode)
      if (areaCode.length > 0) {
        const streetAreaCode = areaCode[0].id;
        // 获取该经销区的街道数据
        const { data } = await getAreaStreets(streetAreaCode);
        let geometries_street = [];
        data.result.forEach(item => {
          geometries_street.push({
            id: "label", // 点图形数据的标志信息
            styleId: "label", // 样式id
            position: new TMap.LatLng(item.latitude, item.longitude), // 标注点位置
            content: `${item.place_name}|${item.population}`, // 标注文本
          });
        });
        streetLayer.setGeometries(geometries_street);
      }

      areaCodeGlobal.value = areaCode;
      console.log("v.jxqIdXZQY", v.jxqId);
      console.log("areaCode", areaCode);
      if (areaCode.length === 0) return message.info("未找到经销区");
      // 获取分销商
      const res = await get_all_users(areaCode[0].Distribution_territory);
      filterAllUsers.value = res.data.result[0];

      fxsUserList.value = res.data.result[0].map(item => {
        return {
          label: item.Master_data_person_name,
          value: item.Master_data_person_name,
          IMEI_code: item.IMEI_code,
          Licence_number: item.Licence_number,
          id: item.id,
        };
      });
      fxsUserList.value.unshift({
        label: "全部",
        value: "全部",
        IMEI_code: 0,
        Licence_number: 0,
        id: 0,
      });

      selectObj.value.currentAreaMessage = areaCode[0];
      selectObj.value.areaName = areaCode[0].DistributionOrganizationName;
      selectObj.value.jxqName = areaCode[0].DistributionOrganizationName;
      // 过滤区域
      filterAreaList.value = [];
      areaCode.forEach(item_s => {
        const a = areaList.value.filter(item => item.styleId === item_s.id);
        filterAreaList.value = filterAreaList.value.concat(a);
      });
      polygon.setGeometries(filterAreaList.value);

      // 过滤门店
      if (
          checkedStore.value ||
          checkedOpenStore.value ||
          checkedTypeStore.value
      ) {
        if (
            (selectObj.value.productClass === null ||
                selectObj.value.productClass === "") &&
            (selectObj.value.product === null || selectObj.value.product === "")
        ) {
          loading.value = true;
          await getAllStore(storeMarker, infoWindow);
          if (checkedOpenStore.value) {
            getAreaProStore(areaCode, storeList.value, res => {
              filterStoreList.value = res.filter(
                  item =>
                      item.Store_contribution !== null &&
                      item.Store_contribution > 0 &&
                      item.Cyclist_name !== null
              ); //过滤出已开门店
            });
          }
          if (checkedStore.value || checkedTypeStore.value) {
            getAreaProStore(areaCode, storeList.value, res => {
              filterStoreList.value = res;
            });
          }
          markStore(filterStoreList.value, storeMarker);
          loading.value = false;
        } else {
          loading.value = true;
          await getProStore(
              storeMarker,
              infoWindow,
              selectObj.value.productClass,
              selectObj.value.product,
              selectObj.value.productGroup
          );
          getAreaProStore(areaCode, proStoreList.value, res => {
            proFilterStoreList.value = res;
          });
          markStore(proFilterStoreList.value, storeMarker);
          loading.value = false;
        }
      }

      //过滤车辆
      if (checkedCar.value) {
        const imei_list = filterAllUsers.value
            .filter(item => item.IMEI_code !== null)
            .map(item => {
              return "0" + item.IMEI_code;
            })
            .join(",");
        const res = await get_update_cars(imei_list);
        carList.value = res.data.result;

        const _carPoints = processVehicleStyle(
            carList.value,
            filterAllUsers.value
        );
        carMarker.setStyles(_carPoints.styles);
        carMarker.setGeometries(_carPoints.points);
      }
      map.setCenter(
          new TMap.LatLng(
              areaCode[0].Resident_latitude,
              areaCode[0].Station_longitude
          )
      );
      map.setZoom(10);
    } catch (err) {
      console.log(err);
    }
  }
  // 选择经销商(库长)
  async function chooseJxs(
      map,
      polygon,
      storeMarker,
      carMarker,
      infoWindow,
      v
  ) {
    try {
      carMarker.setGeometries([]);
      infoWindow.close();
      jxqList.value.forEach(item => {
        if (v.label === "全部") {
          return;
        }
        if (item.jxqId === v.jxqId) {
          selectObj.value.jxqName = item.value;
        }
      });
      selectObj.value.fxsName = null;
      selectObj.value.province = null;
      if (v.label === "全部") {
        loading.value = true;
        if (selectObj.value.jxqName !== "全部") {
          loading.value = false;
          return;
        } else {
          await getAllJxsUserList();
          loading.value = false;
          // selectObj.value.jxqName = "全部";
          selectObj.value.currentAreaMessage = null;
          filterAreaList.value = areaList.value;
          filterStoreList.value = storeList.value;
          filterAllUsers.value = allUsers.value;
          selectObj.value.areaName = "数据";
          polygon.setGeometries(areaList.value);
          if (checkedStore.value) {
            if (
                (selectObj.value.productClass === null ||
                    selectObj.value.productClass === "") &&
                (selectObj.value.product === null ||
                    selectObj.value.product === "")
            ) {
              loading.value = true;
              await getAllStore(storeMarker, infoWindow);
              markStore(storeList.value, storeMarker);
              loading.value = false;
            } else {
              loading.value = true;
              await getProStore(
                  storeMarker,
                  infoWindow,
                  selectObj.value.productClass,
                  selectObj.value.product,
                  selectObj.value.productGroup
              );
              markStore(proStoreList.value, storeMarker, true);
              loading.value = false;
            }
          }

          if (checkedCar.value) {
            const _carPoints = processVehicleStyle(
                carList.value,
                allUsers.value
            );
            carMarker.setStyles(_carPoints.styles);
            carMarker.setGeometries(_carPoints.points);
          }
          return;
        }
      }

      const areaCode = provinceMessage.value.filter(
          item => item.Distribution_territory === v.jxqId
      );
      areaCodeGlobal.value = areaCode;
      if (areaCode.length === 0) return message.info("未找到经销区");
      // 获取分销商
      const res = await get_all_users(areaCode[0].Distribution_territory);
      filterAllUsers.value = res.data.result[0];
      fxsUserList.value = res.data.result[0].map(item => {
        return {
          label: item.Master_data_person_name,
          value: item.Master_data_person_name,
          IMEI_code: item.IMEI_code,
          Licence_number: item.Licence_number,
          id: item.id,
        };
      });
      fxsUserList.value.unshift({
        label: "全部",
        value: "全部",
        IMEI_code: 0,
        Licence_number: 0,
        id: 0,
      });

      selectObj.value.currentAreaMessage = areaCode[0];
      selectObj.value.areaName = areaCode[0].DistributionOrganizationName;
      // 过滤区域
      filterAreaList.value = [];
      areaCode.forEach(item_s => {
        const a = areaList.value.filter(item => item.styleId === item_s.id);
        filterAreaList.value = filterAreaList.value.concat(a);
      });
      polygon.setGeometries(filterAreaList.value);
      // 过滤门店

      if (checkedStore.value) {
        if (
            (selectObj.value.productClass === null ||
                selectObj.value.productClass === "") &&
            (selectObj.value.product === null || selectObj.value.product === "")
        ) {
          loading.value = true;
          await getAllStore(storeMarker, infoWindow);
          getAreaProStore(areaCode, storeList.value, res => {
            filterStoreList.value = res;
          });
          markStore(storeList.value, storeMarker);
          loading.value = false;
        } else {
          loading.value = true;
          await getProStore(
              storeMarker,
              infoWindow,
              selectObj.value.productClass,
              selectObj.value.product,
              selectObj.value.productGroup
          );
          getAreaProStore(areaCode, proStoreList.value, res => {
            proFilterStoreList.value = res;
          });
          markStore(proFilterStoreList.value, storeMarker, true);
          loading.value = false;
        }
      }

      //过滤车辆
      if (checkedCar.value) {
        const imei_list = filterAllUsers.value
            .filter(item => item.IMEI_code !== null)
            .map(item => {
              return "0" + item.IMEI_code;
            })
            .join(",");
        const res = await get_update_cars(imei_list);
        carList.value = res.data.result;

        const _carPoints = processVehicleStyle(
            carList.value,
            filterAllUsers.value
        );
        carMarker.setStyles(_carPoints.styles);
        carMarker.setGeometries(_carPoints.points);
      }
      map.setCenter(
          new TMap.LatLng(
              areaCode[0].Resident_latitude,
              areaCode[0].Station_longitude
          )
      );
      map.setZoom(10);
    } catch (err) {
      console.log(err);
    }
  }

  //选择分销商
  async function chooseFxs(storeMarker, infoWindow, carMarker, v) {
    try {
      infoWindow.close();
      const res = await get_all_users(
          selectObj.value.currentAreaMessage.Distribution_territory
      );
      filterAllUsers.value = res.data.result[0];
      if (v.label === "全部") {
        if (checkedStore.value) {
          if (
              (selectObj.value.productClass === null ||
                  selectObj.value.productClass === "") &&
              (selectObj.value.product === null || selectObj.value.product === "")
          ) {
            getAreaProStore(areaCodeGlobal.value, storeList.value, res => {
              filterStoreList.value = res;
            });
            markStore(filterStoreList.value, storeMarker);
          } else {
            getAreaProStore(areaCodeGlobal.value, proStoreList.value, res => {
              proFilterStoreList.value = res;
            });
            markStore(proFilterStoreList.value, storeMarker, true);
          }
        }

        if (checkedCar.value) {
          const _carPoints = processVehicleStyle(
              carList.value,
              filterAllUsers.value
          );
          carMarker.setStyles(_carPoints.styles);
          carMarker.setGeometries(_carPoints.points);
        }
        return;
      }
      if (checkedStore.value) {
        if (
            (selectObj.value.productClass === null ||
                selectObj.value.productClass === "") &&
            (selectObj.value.product === null || selectObj.value.product === "")
        ) {
          const filter = storeList.value.filter(
              item => item.Cyclist_name === v.id
          );
          filterStoreList.value = filter;
          markStore(filterStoreList.value, storeMarker);
        } else {
          const filter = proStoreList.value.filter(
              item => item.Cyclist_name === v.id
          );
          proFilterStoreList.value = filter;
          markStore(proFilterStoreList.value, storeMarker, true);
        }
      }

      filterAllUsers.value = allUsers.value.filter(
          item => item.IMEI_code === v.IMEI_code
      );
      if (checkedCar.value) {
        const _carPoints = processVehicleStyle(
            carList.value,
            filterAllUsers.value
        );
        carMarker.setStyles(_carPoints.styles);
        carMarker.setGeometries(_carPoints.points);
      }
    } catch (error) {
      console.log(error);
    }
  }

  // 选择产品品类
  async function chooseProType(storeMarker, infoWindow, v) {
    try {
      infoWindow.close();
      selectObj.value.fxsName = null;
      selectObj.value.province = null;
      if (v.label === "全部") {
        selectObj.value.areaName = "数据";
        if (checkedStore.value) {
          if (selectObj.value.currentAreaMessage) {
            loading.value = true;
            await getAllStore(storeMarker, infoWindow);
            getAreaProStore(areaCodeGlobal.value, storeList.value, res => {
              filterStoreList.value = res;
            });
            markStore(filterStoreList.value, storeMarker);
            loading.value = false;
          } else {
            loading.value = true;
            await getAllStore(storeMarker, infoWindow);
            loading.value = false;
            markStore(storeList.value, storeMarker);
          }
        }
        return;
      }
      if (checkedStore.value) {
        if (selectObj.value.currentAreaMessage) {
          loading.value = true;
          await getProStore(storeMarker, infoWindow, v.code, "", v.groupId);
          getAreaProStore(areaCodeGlobal.value, proStoreList.value, res => {
            proFilterStoreList.value = res;
          });
          markStore(proFilterStoreList.value, storeMarker, true);
          loading.value = false;
        } else {
          loading.value = true;
          await getProStore(storeMarker, infoWindow, v.code, "", v.groupId);
          markStore(proStoreList.value, storeMarker, true);
        }
      }
    } catch (err) {
      console.log(err);
    }
  }

  // 选择产品
  async function choosePro(storeMarker, infoWindow, productClass, v) {
    try {
      infoWindow.close();
      selectObj.value.fxsName = null;
      selectObj.value.province = null;
      if (v.label === "全部") {
        selectObj.value.areaName = "数据";
        if (checkedStore.value) {
          if (selectObj.value.currentAreaMessage) {
            loading.value = true;
            await getProStore(
                storeMarker,
                infoWindow,
                selectObj.value.productClass,
                "",
                selectObj.value.productGroup
            );
            getAreaProStore(areaCodeGlobal.value, proStoreList.value, res => {
              proFilterStoreList.value = res;
            });
            markStore(proFilterStoreList.value, storeMarker, true);
            loading.value = false;
          } else {
            loading.value = true;
            await getProStore(
                storeMarker,
                infoWindow,
                selectObj.value.productClass,
                selectObj.value.productGroup
            );
            markStore(proStoreList.value, storeMarker, true);
            loading.value = false;
          }
        }

        return;
      }
      if (checkedStore.value) {
        if (selectObj.value.currentAreaMessage) {
          loading.value = true;
          await getProStore(
              storeMarker,
              infoWindow,
              productClass,
              v.code,
              v.id
          );
          getAreaProStore(areaCodeGlobal.value, proStoreList.value, res => {
            proFilterStoreList.value = res;
          });
          markStore(proFilterStoreList.value, storeMarker, true);
          loading.value = false;
        } else {
          loading.value = true;
          await getProStore(
              storeMarker,
              infoWindow,
              productClass,
              v.code,
              v.id
          );
          markStore(proStoreList.value, storeMarker, true);
          loading.value = false;
        }
      }
      console.log("v.code", v.code);
    } catch (err) {
      console.log(err);
    }
  }

  async function chooseArea(
      map,
      polygon,
      storeMarker,
      carMarker,
      infoWindow,
      e
  ) {
    polygon.setGeometries([]);
    storeMarker.setGeometries([]);
    infoWindow.close();
    selectObj.value.areaName = "数据";
    selectObj.value.jxsName = null;
    // console.log("12131", e[0]);
    console.log("12131", e);
    if ((e && e[0] == "") || !e) {
      console.log(11);
      getAllAreas(polygon1);
      map.setCenter(new TMap.LatLng(39.984104, 116.307503));
      map.setZoom(6.5);
    } else {
      try {
        const res = await getAreaInfoBycode(e[2]);
        people_count.value = res.data.result[0].Population;
        const currentArea = res.data.result[0];
        map.setCenter(
            new TMap.LatLng(
                currentArea.Resident_latitude,
                currentArea.Station_longitude
            )
        );
        if (res) {
          control_show.value = false;
        }
        selectObj.value.currentAreaMessage = currentArea;
        // 画出当前区域
        res.data.result.forEach(item => {
          let outlinePath;
          JSON.parse(item.Region_profile).forEach(val => {
            outlinePath = transtionCopy(val).map(
                item => new TMap.LatLng(item.latitude, item.longitude)
            );
            let path = {
              id: `allView-${item.id}`,
              styleId: `${item.id}`,
              paths: outlinePath,
            };
            polygon.add(path);
          });
        });
        filterAreaList.value = polygon.getGeometries();
        let id = getDqIdJxqId(store.dqList, store.jxqList);
        // 过滤出当前门店
        const _PCRMessage =
            currentArea.Provincial_name +
            currentArea.City_name +
            currentArea.District_name;
        const resStore = await getAllStores(_PCRMessage, ...id);
        store_count.value = resStore.data.result.length;
        filterStoreList.value = resStore.data.result;
        if (checkedStore.value) {
          markStore(filterStoreList.value, storeMarker);
        }

        // 获取分销商
        const resUser = await get_all_users(currentArea.Distribution_territory);
        filterAllUsers.value = resUser.data.result[0];
        // 过滤出当前车

        if (checkedCar.value) {
          const _carPoints = processVehicleStyle(
              carList.value,
              filterAllUsers.value
          );
          carMarker.setStyles(_carPoints.styles);
          carMarker.setGeometries(_carPoints.points);
        }
        map.setZoom(12);
      } catch (err) {
        console.log(err);
      }
    }
  }

  // 清除车辆定时器
  function clearCarTimer() {
    clearInterval(timerCar);
  }
  // 延时函数
  function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // 提取通用的错误处理逻辑到一个单独的函数
  const handleAsyncError = (error, loading) => {
    console.error("An error occurred:", error);
    loading.value = false;
    throw error;
  };

  const circulation = async (
      product_class,
      product,
      product_group,
      id,
      limit,
      callback
  ) => {
    loading.value = true;
    try {
      // 获取缓存数据
      const res1 = await get_cache(
          product_class,
          product,
          product_group,
          ...id
      );

      // 获取工作数据并等待特定状态
      const workPromise = get_work(res1.data.data.task_iid_iiids);
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => {
          reject(
              new Error(
                  `Timeout: Operation did not complete within ${limit} seconds.`
              )
          );
        }, limit * 1000);
      });

      // 使用Promise.race来同时等待工作完成或超时
      const res2 = await Promise.race([workPromise, timeoutPromise]);

      // 检查是否因为超时而失败
      if (res2 instanceof Error) {
        loading.value = false;
        message.error("网络请求超时");
        throw res2;
      }

      // 如果成功获取到满足状态的数据，执行回调函数并返回结果
      const callbackResult = await callback();
      return callbackResult;
    } catch (error) {
      // 调用通用错误处理函数
      handleAsyncError(error, loading);
    }
  };

  const mousemove = (e, storeList, info) => {
    try {
      const filterStore = storeList.filter(
          item => item.id === e.geometry.id
      )[0];
      let content = "";
      if (filterStore.Store_monthly_sales) {
        content = `${filterStore?.Store_name} | ${
            Number(filterStore?.Store_monthly_sales).toFixed(2)
                ? Number(filterStore?.Store_monthly_sales).toFixed(2)
                : 0
        }元`;
      } else {
        content = `${filterStore?.Store_name} | ${
            Number(filterStore?.product_return_money).toFixed(2)
                ? Number(filterStore?.product_return_money).toFixed(2)
                : 0
        }元`;
      }

      info.open();
      info.setPosition(e.geometry.position); //设置信息窗位置
      info.setContent(content); //设置信息窗内容
    } catch (error) {
      console.log(error, "鼠标移动发生错误");
    }
  };

  const click = (e, storeList, info) => {
    try {
      const filterStore = storeList.filter(
          item => item.id === e.geometry.id
      )[0];
      const content = `${filterStore.Store_name} | ${
          filterStore.Store_monthly_sales
              ? Number(filterStore.Store_monthly_sales).toFixed(2)
              : 0
      }元`;
      info.open();
      info.setPosition(e.geometry.position); //设置信息窗位置
      info.setContent(content); //设置信息窗内容

      //打开门店销弹窗
      selectObj.value.modalShow = true;
      sessionStorage.setItem("storeId", filterStore.id);
    } catch (err) {
      console.log(err, "点击门店发生错误");
    }
  };

  return {
    all_stores,
    checkedCar,
    checkedArea,
    checkedStore,
    checkedAllArea,
    checkedOpenStore,
    checkedTypeStore,
    checkedOpenStreet,
    filterStoreList,
    selectObj,
    areaList,
    storeList,
    carList,
    jxsUserList,
    fxsUserList,
    control_show,
    store_count,
    people_count,
    loading,
    product_list,
    product_list_category,
    dqList,
    sqList,
    jxqList,
    xzqyList,
    pickOn,
    clearCarTimer,
    getAllAreas,
    changeChecked,
    getAllStore,
    getProStore,
    getAllcars,
    getAllJxsUserList,
    getAllRegion,
    getAllProvinces,
    getAllXZQY,
    getAllOutRegion,
    chooseDq,
    chooseSq,
    chooseXzqx,
    chooseJxq,
    chooseJxs,
    chooseFxs,
    chooseArea,
    getAllProduct,
    getProductCategory,
    chooseProType,
    choosePro,
  };
});
