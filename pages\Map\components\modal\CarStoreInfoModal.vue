<template>
  <div style="max-height: 600px; overflow-y: scroll" class="modal-content">
    <div>
      <div class="top-info">
        <div class="message-list">
          <div class="message-item">
            <div class="key-name">门店名称</div>
            <div class="key-value">
              {{ storeInfo?.Store_name }}
            </div>
          </div>
          <div class="col-item">
            <div class="message-item" style="width: 49%; min-width: 250px">
              <div class="key-name">当前负责人</div>
              <div class="key-value">
                {{ storeInfo?.Cyclist_name }}
              </div>
            </div>
            <div class="message-item" style="width: 49%; min-width: 250px">
              <div class="key-name">车长</div>
              <div class="key-value">
                {{ storeInfo?.jxs_name }}
              </div>
            </div>
          </div>
          <div class="col-item">
            <div class="message-item" style="width: 49%; min-width: 250px">
              <div class="key-name">柜体投放数量</div>
              <div class="key-value">
                {{ storeInfo?.Material_num ? storeInfo.Material_num : "暂无" }}
              </div>
            </div>
            <div class="message-item" style="width: 49%; min-width: 250px">
              <div class="key-name">筐库存数</div>
              <div class="key-value">暂无</div>
            </div>
          </div>
          <div class="col-item">
            <div class="message-item" style="width: 49%; min-width: 250px">
              <div class="key-name">总净销售额</div>
              <div class="key-value">
                {{
                  storeInfo?.Store_all_sales ? storeInfo?.Store_all_sales : 0
                }}
              </div>
            </div>
            <div class="message-item" style="width: 49%; min-width: 250px">
              <div class="key-name">当前净销售额</div>
              <div class="key-value">
                {{
                  storeInfo?.Store_monthly_sales
                    ? storeInfo?.Store_monthly_sales
                    : 0
                }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div style="margin: 25px 0 10px 0">
        <a-select
          v-model:value="optionValue"
          style="width: 120px"
          :options="options"
          @select="select"
        ></a-select>
      </div>
      <a-tabs v-model:activeKey="activeKey" type="card">
        <a-tab-pane key="1" tab="今日销售明细">
          <a-table
            :dataSource="storeGoodsInfoDetail"
            :columns="storeGoodsInfoColumnsDetails"
            :pagination="{ pageSize: 5 }"
            size="small"
          />
        </a-tab-pane>
        <a-tab-pane key="2" tab="今日返货明细">
          <a-table
            :dataSource="storeGoodsInfoReturnDetail"
            :columns="storeGoodsInfoColumnsReturnDetails"
            :pagination="{ pageSize: 5 }"
            size="small"
          />
        </a-tab-pane>
        <a-tab-pane key="3" tab="商品销售详情">
          <a-table
            :dataSource="storeGoodsInfo"
            :columns="storeGoodsInfoColumns"
            :pagination="{ pageSize: 5 }"
            size="small"
          />
        </a-tab-pane>
        <a-tab-pane key="4" tab="商品返货详情">
          <a-table
            :dataSource="storeGoodsReturnInfo"
            :columns="storeGoodsReturnInfoColumns"
            :pagination="{ pageSize: 5 }"
            size="small"
          />
        </a-tab-pane>
        <a-tab-pane key="5" tab="销售额">
          <a-table
            :dataSource="storeSalesroom"
            :columns="storeSalesroomColumns"
            :pagination="{ pageSize: 5 }"
            size="small"
          />
        </a-tab-pane>
        <a-tab-pane key="6" tab="物料库存">
          <a-table
            :dataSource="storeMMI"
            :columns="storeMMIColumns"
            :pagination="{ pageSize: 5 }"
            size="small"
          />
        </a-tab-pane>
        <a-tab-pane key="7" tab="盘筐明细">
          <a-table
            :dataSource="storeBasketDetail"
            :columns="storeBasketDetailColumns"
            :pagination="{ pageSize: 5 }"
            size="small"
          />
        </a-tab-pane>
        <a-tab-pane key="8" tab="物料盘点明细">
          <a-table
            :dataSource="storeMMIDetail"
            :columns="storeMMIDetailColumns"
            :pagination="{ pageSize: 5 }"
            size="small"
          />
        </a-tab-pane>
        <a-tab-pane key="9" tab="操作历史">
          <a-table
            :dataSource="storeEditHistory"
            :columns="storeEditHistoryColumns"
            :pagination="{ pageSize: 5 }"
            size="small"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'Update_beforce'">
                <a-image
                  v-if="
                    (record.Update_attribute === '门店门头照' ||
                      record.Update_attribute === '门店内景照') &&
                    record.Update_beforce !== null &&
                    record.Update_beforce !== ''
                  "
                  :src="record.Update_beforce"
                  style="height: 50px; width: 50px"
                ></a-image>
                <span v-else>{{ record.Update_beforce }}</span>
              </template>
              <template v-if="column.dataIndex === 'Update_after'">
                <a-image
                  v-if="
                    (record.Update_attribute === '门店门头照' ||
                      record.Update_attribute === '门店内景照') &&
                    record.Update_after !== null &&
                    record.Update_after !== ''
                  "
                  :src="record.Update_after"
                  style="height: 50px; width: 50px"
                ></a-image>
                <span span v-else>{{ record.Update_after }}</span>
              </template>
            </template>
          </a-table>
        </a-tab-pane>
      </a-tabs>
    </div>
    <div
      style="
        display: flex;
        flex-wrap: wrap;
        justify-content: space-around;
        gap: 10px;
      "
    >
      <div class="img-box">
        <span style="font-weight: 700">门头照片</span>
        <a-image
          style="height: 150px; width: 150px; border-radius: 7px"
          v-if="
            storeInfoPic?.Store_photo !== null &&
            storeInfoPic?.Store_photo !== '' &&
            storeInfoPic?.Store_photo !== 'None'
          "
          :src="storeInfoPic?.Store_photo"
        />
        <div
          class="no-png"
          v-if="
            storeInfoPic?.Store_photo === null ||
            storeInfoPic?.Store_photo === '' ||
            storeInfoPic?.Store_photo === 'None'
          "
        >
          <a-empty description="暂无门头照" />
        </div>
      </div>
      <div class="img-box">
        <span style="font-weight: 700">盘点前陈列照片</span>
        <a-image
          style="height: 150px; width: 150px; border-radius: 7px"
          v-if="
            storeInfoPic?.Sales_last_pic !== null &&
            storeInfoPic?.Sales_last_pic !== '' &&
            storeInfoPic?.Sales_last_pic !== 'None'
          "
          :src="storeInfoPic?.Sales_last_pic"
        />
        <div
          class="no-png"
          v-if="
            storeInfoPic?.Sales_last_pic === null ||
            storeInfoPic?.Sales_last_pic === '' ||
            storeInfoPic?.Sales_last_pic === 'None'
          "
        >
          <a-empty description="暂无盘点前陈列照片" />
        </div>
      </div>
      <div class="img-box">
        <span style="font-weight: 700">盘点后陈列照片</span>
        <a-image
          style="height: 150px; width: 150px; border-radius: 7px"
          v-if="
            storeInfoPic?.Sales_next_pic !== null &&
            storeInfoPic?.Sales_next_pic !== '' &&
            storeInfoPic?.Sales_next_pic !== 'None'
          "
          :src="storeInfoPic?.Sales_next_pic"
        />
        <div
          class="no-png"
          v-if="
            storeInfoPic?.Sales_next_pic === null ||
            storeInfoPic?.Sales_next_pic === '' ||
            storeInfoPic?.Sales_next_pic === 'None'
          "
        >
          <a-empty description="暂无盘点后陈列照片" />
        </div>
      </div>
      <div class="img-box">
        <span style="font-weight: 700">盘点后陈列照片2</span>
        <a-image
          style="height: 150px; width: 150px; border-radius: 7px"
          v-if="
            storeInfoPic?.Sales_next_pic2 !== null &&
            storeInfoPic?.Sales_next_pic2 !== '' &&
            storeInfoPic?.Sales_next_pic2 !== 'None'
          "
          :src="storeInfoPic?.Sales_next_pic2"
        />
        <div
          class="no-png"
          v-if="
            storeInfoPic?.Sales_next_pic2 === null ||
            storeInfoPic?.Sales_next_pic2 === '' ||
            storeInfoPic?.Sales_next_pic2 === 'None'
          "
        >
          <a-empty description="暂无盘点后陈列照片2" />
        </div>
      </div>
      <div class="img-box">
        <span style="font-weight: 700">暂无盘点后陈列照片3</span>
        <a-image
          style="height: 150px; width: 150px; border-radius: 7px"
          v-if="
            storeInfoPic?.Sales_next_pic3 !== null &&
            storeInfoPic?.Sales_next_pic3 !== '' &&
            storeInfoPic?.Sales_next_pic3 !== 'None'
          "
          :src="storeInfoPic?.Sales_next_pic3"
        />
        <div
          class="no-png"
          v-if="
            storeInfoPic?.Sales_next_pic3 === null ||
            storeInfoPic?.Sales_next_pic3 === '' ||
            storeInfoPic?.Sales_next_pic3 === 'None'
          "
        >
          <a-empty description="暂无盘点后陈列照片3" />
        </div>
      </div>
      <div class="img-box">
        <span style="font-weight: 700">盘点后陈列照片4</span>
        <a-image
          style="height: 150px; width: 150px; border-radius: 7px"
          v-if="
            storeInfoPic?.Sales_next_pic4 !== null &&
            storeInfoPic?.Sales_next_pic4 !== '' &&
            storeInfoPic?.Sales_next_pic4 !== 'None'
          "
          :src="storeInfoPic?.Sales_next_pic4"
        />
        <div
          class="no-png"
          v-if="
            storeInfoPic?.Sales_next_pic4 === null ||
            storeInfoPic?.Sales_next_pic4 === '' ||
            storeInfoPic?.Sales_next_pic4 === 'None'
          "
        >
          <a-empty description="暂无盘点后陈列照片4" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, defineProps } from "vue";

import {
  getRecent24Months,
  storeGoodsInfoColumns,
  storeGoodsInfoColumnsDetails,
  storeGoodsInfoColumnsReturnDetails,
  storeGoodsReturnInfoColumns,
  storeSalesroomColumns,
  storeEditHistoryColumns,
  storeMMIColumns,
  storeMMIDetailColumns,
  storeBasketDetailColumns,
} from "../../data-uitls/index";
import {
  carMapGetStoreInfo,
  carMapGetStoreGoodsInfo,
  carMapGetStoreGoodsInfo01,
  carMapGetStoreInfoPic,
  carMapGetStoreGoodsInfoDetail,
  carMapGetStoreGoodsInfoReturnDetail,
  carMapGetStoreSsaleroom,
  carMapGetStoreEditHistory,
  getStoreMaterialDetail,
  getStoreMMI,
  getBasketDetail,
} from "../../../../api/Map/index";

const props = defineProps({
  startTime: {
    type: String,
    default: "",
  },
  endTime: {
    type: String,
    default: "",
  },
});

const currentStoreId = ref();
const storeInfo = ref();
const storeInfoPic = ref();
async function getStoreInfo() {
  const res = await carMapGetStoreInfo(currentStoreId.value);
  storeInfo.value = res.data?.result[0];
  const res1 = await carMapGetStoreInfoPic(
    currentStoreId.value,
    props.startTime,
    props.endTime
  );
  storeInfoPic.value = res1.data?.result[0];
}

const activeKey = ref("1");

const optionValue = ref("近30天");
const options = getRecent24Months();

// 今日销,返 start --------------
const storeGoodsInfoDetail = ref([]);
const storeGoodsInfoReturnDetail = ref([]);
async function getStoreGoodsInfoDetail(start_date, end_date) {
  const res = await carMapGetStoreGoodsInfoDetail(
    currentStoreId.value,
    start_date,
    end_date
  );
  const res1 = await carMapGetStoreGoodsInfoReturnDetail(
    currentStoreId.value,
    start_date,
    end_date
  );
  console.log(res, res1, "结果");

  storeGoodsInfoDetail.value = await formatData(
    res.data?.result,
    "Sales_son_good_name",
    "Sales_son_total_prices"
  );
  storeGoodsInfoReturnDetail.value = await formatData(
    res1.data?.result,
    "XF_goods_name",
    "XF_total_prices"
  );
}

// ---------销，返start----------
const storeGoodsInfo = ref([]);
const storeGoodsReturnInfo = ref([]);
async function getStoreGoodsInfo(month, year) {
  const res = await carMapGetStoreGoodsInfo(currentStoreId.value, month, year);
  const res1 = await carMapGetStoreGoodsInfo01(
    currentStoreId.value,
    month,
    year
  );
  storeGoodsInfo.value = await disposeGoodsInfo(res.data?.result[0]);
  storeGoodsReturnInfo.value = await disposeGoodsReturnInfo(
    res1.data?.result[0]
  );
}

async function formatData(arr, name, price) {
  return arr
    .map((item) => {
      return {
        name: item[name],
        sales: Number(item[price]).toFixed(2),
      };
    })
    .sort((a, b) => b.sales - a.sales);
}

async function disposeGoodsInfo(arrpParams) {
  let arr = arrpParams.map((item) => {
    return {
      name: item.Sales_son_good_name,
      sales: item.Sales_son_total_prices,
    };
  });

  // 使用 reduce 方法将具有相同 name 的对象的 value 值进行合并
  let result = arr.reduce((acc, curr) => {
    let existingItem = acc.find((item) => item.name === curr.name);
    if (existingItem) {
      existingItem.sales += curr.sales;
    } else {
      acc.push({ name: curr.name, sales: curr.sales });
    }
    return acc;
  }, []);

  result = result.map((item) => {
    return {
      name: item.name,
      sales: Number(item.sales).toFixed(2),
    };
  });
  return result.sort((a, b) => b.sales - a.sales);
}

async function disposeGoodsReturnInfo(arrpParams) {
  let arr = arrpParams.map((item) => {
    return {
      name: item.XF_goods_name,
      sales: item.XF_total_prices,
    };
  });

  // 使用 reduce 方法将具有相同 name 的对象的 value 值进行合并
  let result = arr.reduce((acc, curr) => {
    let existingItem = acc.find((item) => item.name === curr.name);
    if (existingItem) {
      existingItem.sales += curr.sales;
    } else {
      acc.push({ name: curr.name, sales: curr.sales });
    }
    return acc;
  }, []);

  result = result.map((item) => {
    return {
      name: item.name,
      sales: Number(item.sales).toFixed(2),
    };
  });
  return result.sort((a, b) => b.sales - a.sales);
}
// ---------销，返end----------

// ---------销售额start----------

const storeSalesroom = ref([]);
async function getStoreSsaleroom(month, year) {
  const res = await carMapGetStoreSsaleroom(currentStoreId.value, month, year);
  storeSalesroom.value = await disposeSalesInfo(res.data?.result[0]);

  // console.log("storeSalesroom.value", storeSalesroom.value);
}

//处理销售额
async function disposeSalesInfo(arrpParams) {
  let arr2 = arrpParams.map((item) => {
    return {
      dateTime: item.finish_date,
      sales: Number(item.Sales_sales_money),
      salesReturn: Number(item.Sales_stock_money),
      salesMain: Number(item.Sales_receivable_money),
    };
  });

  let result2 = Object.values(
    arr2.reduce((acc, { dateTime, sales, salesReturn, salesMain }) => {
      if (!acc[dateTime]) {
        acc[dateTime] = { dateTime, sales: 0, salesReturn: 0, salesMain: 0 };
      }
      acc[dateTime].sales += sales;
      acc[dateTime].salesReturn += salesReturn;
      acc[dateTime].salesMain += salesMain;
      return acc;
    }, {})
  );

  result2 = result2.map((item) => {
    return {
      dateTime: item.dateTime,
      sales: Number(item.sales).toFixed(2),
      salesReturn: Number(item.salesReturn).toFixed(2),
      salesMain: Number(item.salesMain).toFixed(2),
    };
  });
  return result2.sort((a, b) => b.dateTime - a.dateTime);
}
// ---------销售额end----------

// ---------物料明细start----------
const storeMMI = ref([]);
async function getAllStoreMMI() {
  const res = await getStoreMMI(currentStoreId.value);
  storeMMI.value = res.data?.result || [];
}
// ---------物料明细end----------

// ---------物料盘点明细start----------
const storeMMIDetail = ref([]);
async function getMaterialDetail(month, year) {
  const res = await getStoreMaterialDetail(currentStoreId.value, month, year);
  // const res = await getStoreMaterialDetail("2023112117274067781", month, year); // 测试
  storeMMIDetail.value = res.data?.result[0] || [];
}
// ---------物料盘点明细end----------

// ---------盘筐明细start----------
const storeBasketDetail = ref([]);
async function getStoreBasketDetail(month, year) {
  const res = await getStoreMaterialDetail(currentStoreId.value, month, year);
  // const res = await getBasketDetail("2023112117271135413", month, year); // 测试
  storeBasketDetail.value = res.data?.result[0] || [];
}
// ---------盘筐明细end----------
// ---------操作历史start----------
const storeEditHistory = ref([]);
async function getStoreEditHistory() {
  const res = await carMapGetStoreEditHistory(currentStoreId.value);
  storeEditHistory.value = res.data?.result;
}
// ---------操作历史end----------

async function select(e) {
  console.log("e", e);
  const data = e === "近30天" ? [0, 0] : e.split("-");
  getStoreGoodsInfoDetail(Number(data[1]), Number(data[0]));
  getStoreGoodsInfo(Number(data[1]), Number(data[0]));
  getStoreSsaleroom(Number(data[1]), Number(data[0]));
  getStoreEditHistory(Number(data[1]), Number(data[0]));
  getMaterialDetail(Number(data[1]), Number(data[0]));
  getStoreBasketDetail(Number(data[1]), Number(data[0]));
}

onMounted(() => {
  currentStoreId.value = sessionStorage.getItem("storeId");
  getStoreInfo();
  getStoreGoodsInfoDetail(props.startTime, props.endTime);
  getStoreGoodsInfo(0, 0);
  getStoreSsaleroom(0, 0);
  getAllStoreMMI();
  getStoreEditHistory();
  getMaterialDetail(0, 0);
  getStoreBasketDetail(0, 0);
  // console.log(props.startTime, props.endTime, "选择时间");
});
</script>

<style lang="scss" scoped>
.modal-content {
  display: flex;
  .image-content {
    flex: 1;
  }
}

.img-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  .no-png {
    width: 150px;
    height: 150px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.box {
  border-radius: 20rpx;
  padding: 0 10rpx;
  width: 70%;
  box-shadow: 1px 11px 80px rgba(0, 0, 0, 0.15);
  .store-info-list {
    padding: 10rpx 0;
    padding-right: 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    justify-content: space-between;
  }
}
.top-info {
  display: flex;
  gap: 5px;
  flex-wrap: wrap;
  .message-list {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 5px;
    .message-item {
      display: flex;
      height: 30px;
      border: 1px solid #dde3e9;
    }
  }
  .key-name {
    font-weight: 700;
    width: 120px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #dde3e9;
  }
  .key-value {
    flex: 1;
    display: flex;
    padding: 0 10px;
    align-items: center;
  }
}
.col-item {
  display: flex;
  justify-content: space-between;
}

@media (width<1077px) {
  .col-item {
    display: contents;
  }

  .message-item {
    width: 49%;
    min-width: 250px;
  }

  .top-info {
    display: contents;
  }
}
</style>
