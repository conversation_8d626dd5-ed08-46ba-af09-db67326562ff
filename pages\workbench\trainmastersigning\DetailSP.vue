<template>
  <view class="container">
    <u-form
      labelPosition="left"
      labelWidth="auto"
      :model="formData"
      ref="formRole"
    >
      <template v-for="item in formObject" :key="item.key">
        <!-- 输入类型 -->
        <u-form-item :label="item.label" :prop="item.key" borderBottom>
          <u-input
            v-model="formData[item.key]"
            placeholder="请输入"
            disabled
          ></u-input>
        </u-form-item>
      </template>
    </u-form>
     <a-textarea
      v-model:value="opinionValue"
      placeholder="请输入审批意见"
      :rows="3"
      :disabled="formData['Regional_total_status'] != '0'"
    />
    <view class="confirm-button">
      <u-button
        text="拒绝"
        shape="circle"
        color="#fb400b"
        :loading="buttonLoading"
        v-if="formData['Regional_total_status'] == '0'"
        @click="submit('拒绝')"
      ></u-button>
      <u-button
        text="通过"
        shape="circle"
        color="#4ea28d"
        :loading="buttonLoading"
        v-if="formData['Regional_total_status'] == '0'"
        @click="submit('通过')"
      ></u-button>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive } from "vue";
import {
  summitTrainmasterApi,
  summitTrainmasterApikzjy,
  getTrainmasterDetailApi,
} from "/api/workbench/trainmastersigning/index.js";

import { onLoad } from "@dcloudio/uni-app";

const formRole = ref(null);
const formData = reactive({
  rzkf: null, //入职库房
  kzmc: null, //库长姓名
  yfyyzzmc: null, //营业执照名称
  yfyyzzh: null, //营业执照号
  yfjyz: null, //法定代表人
  yflxdh: null, //法人电话
  yfjycs: null, //营业执照经营场所
  rzrymc: null, //入职人员名称
  rzryjs: null, //入职人员角色
  sfzh: null, //身份证号
  lxdh: null, //联系电话
  txdz: null, //通讯地址
  khyh: null, //开户银行
  zhmc: null, //账户名称
  yxkh: null, //银行账号
  jyqy: null, //经营区域
  bzjje: null, //保证金金额
  sfxjxs: null, //是否新经销商
  ssdq: null, //所属大区
  sssq: null, //所属省区
  kfwz: null, //库房位置
  tzcls: null, //投资车辆数
  kfnfgctx: null, //库房能否挂车同行
  cph: null, //车牌号
  fdjh: null, //发动机号
});
const opinionValue = ref(null);

const buttonLoading = ref(false);

// 提交逻辑
async function submit(type) {
  buttonLoading.value = true;
  if (!opinionValue.value && type === "拒绝") {
    uni.showToast({
      title: "请输入审批意见",
      icon: "none",
      duration: 2000,
    });
    buttonLoading.value = false;
    return;
  }

  const dataParams = {
    id: detailId.value,
    opinion: opinionValue.value,
    status: type,
  };

  await summitTrainmasterApikzjy(dataParams);
  uni.showToast({
    title: "提交成功",
    duration: 1000,
    icon: "success",
  });
  setTimeout(() => {
    uni.navigateBack();
  }, 1100);
}

const detailId = ref("");

async function getDetailMessage() {
  const { data } = await getTrainmasterDetailApi(detailId.value);
  opinionValue.value = data.result[0].Regional_total_opinion
  formData.id = detailId.value;
  const message = data.result[0];
  for (let key in message) {
    if (key === "xyqsrq") {
      formData[key] = message["xyqsrq_f"];
    }
    if (key === "htksrq") {
      formData[key] = message["htksrq_f"];
    }
    if (key === "htjsrq") {
      formData[key] = message["htjsrq_f"];
    }
    if (!["xyqsrq", "htksrq", "htjsrq"].includes(key)) {
      formData[key] = message[key];
    }
  }
}

const formObject = [
  {
    label: "入职库房:",
    key: "rzkf",
  },
  {
    label: "库长姓名:",
    key: "kzmc",
  },
  {
    label: "营业执照名称:",
    key: "yfyyzzmc",
  },
  {
    label: "营业执照号:",
    key: "yfyyzzh",
  },
  {
    label: "法定代表人:",
    key: "yfjyz",
  },
  {
    label: "法人电话:",
    key: "yflxdh",
  },
  {
    label: "营业执照经营场所:",
    key: "yfjycs",
  },
  {
    label: "入职人员名称:",
    key: "rzrymc",
  },
  {
    label: "入职人员角色:",
    key: "rzryjs",
  },
  {
    label: "身份证号:",
    key: "sfzh",
  },
    {
    label: "联系电话:",
    key: "lxdh",
  },
  {
    label: "通讯地址:",
    key: "txdz",
  },
  
  {
    label: "开户银行:",
    key: "khyh",
  },
  {
    label: "账户名称:",
    key: "zhmc",
  },
  {
    label: "银行账号:",
    key: "yxkh",
  },
  {
    label: "经营区域:",
    key: "jyqy",
  },
  {
    label: "保证金金额:",
    key: "bzjje",
  },
  {
    label: "是否新经销商:",
    key: "sfxjxs",
  },
  {
    label: "所属大区:",
    key: "ssdq",
  },
  {
    label: "所属省区:",
    key: "sssq",
  },
  {
    label: "库房位置:",
    key: "kfwz",
  },
  {
    label: "投资车辆数:",
    key: "tzcls",
  },{
    label: "库房能否挂车同行:",
    key: "kfnfgctx",
  },
  {
    label: "车牌号:",
    key: "cph",
  },
  {
    label: "发动机号:",
    key: "fdjh",
  },
  
];

onLoad(options => {
  detailId.value = options.id;
  getDetailMessage();
});
</script>

<style lang="scss" scoped>
.container {
  padding: 20rpx;
  .confirm-button {
    display: flex;
    gap: 50rpx;
    padding: 30rpx 0;
  }
}
</style>
