<template>
  <a-modal
    v-model:open="showModal"
    :title="props.modalMessage.title"
    :footer="null"
    width="80%"
    @cancel="cancel"
    style="top: 10px"
  >
    <!-- 功能按钮 -->
    <div class="button-group">
      <a-select
        v-model:value="store.selectObj.optionValue"
        :options="timeOptions"
        @select="timeSelect"
      ></a-select>
      <a-range-picker
        v-model:value="store.selectObj.dateSection"
        @change="changeDate"
        v-if="props.modalMessage.title !== '人群覆盖率' && customTime"
      />
      <a-button type="primary" @click="ExportData" :loading="exportLoading"
        >导出Excel</a-button
      >
      <a-button
        type="primary"
        v-if="props.modalMessage.title === '车长拜访情况'"
        @click="refresh"
      >
        <template #icon>
          <ReloadOutlined />
        </template>
        刷新
      </a-button>
    </div>
    <!-- 分销商多表格 -->
    <a-tabs
      v-if="props.modalMessage.title == '车长拜访情况'"
      v-model:activeKey="activeKey"
      type="card"
      @change="changeTabs"
    >
      <a-tab-pane key="1" tab="车长表">
        <a-table
          :dataSource="dataSource"
          :columns="columns"
          size="small"
          bordered
          :pagination="false"
          :loading="oLoading"
        />
        <a-pagination
          style="margin-top: 20px; text-align: right"
          v-model:current="current"
          :total="total"
          :showSizeChanger="false"
          show-less-items
          @change="changePage"
        />
      </a-tab-pane>
      <a-tab-pane key="2" tab="汇总表">
        <a-table
          :dataSource="dataSource"
          :columns="columns"
          size="small"
          bordered
          :pagination="false"
          :loading="oLoading"
        />
        <a-pagination
          style="margin-top: 20px; text-align: right"
          v-model:current="current"
          :total="total"
          show-less-items
          @change="changePage"
        />
      </a-tab-pane>
      <a-tab-pane key="3" tab="计划订单汇总">
        <a-table
          :dataSource="orderData"
          :columns="columns"
          size="small"
          bordered
          :pagination="false"
          :loading="oLoading"
        />
        <a-pagination
          style="margin-top: 20px; text-align: right"
          v-model:current="current"
          :total="total"
          show-less-items
          @change="changePage"
        />
      </a-tab-pane>
      <a-tab-pane key="4" tab="计划订单明细">
        <a-table
          :dataSource="orderData"
          :columns="columns"
          size="small"
          bordered
          :pagination="false"
          :loading="oLoading"
        />
        <a-pagination
          style="margin-top: 20px; text-align: right"
          v-model:current="current"
          :total="total"
          show-less-items
          @change="changePage"
        />
      </a-tab-pane>
      <!-- 经销区标签页 -->
      <a-tab-pane key="5" tab="经销区">
        <a-table
          :dataSource="distributionData"
          :columns="columns"
          size="small"
          bordered
          :pagination="false"
          :loading="oLoading"
        >
          <template #headerCell="{ column }">
            <span>
              {{ column.title }}
              <a-tooltip>
                <template #title> {{ column.helpText }} </template>
                <question-circle-outlined v-if="column.helpText" />
              </a-tooltip>
            </span>
          </template>
        </a-table>
        <a-pagination
          style="margin-top: 20px; text-align: right"
          v-model:current="current"
          :total="total"
          :showSizeChanger="false"
          show-less-items
          @change="changePage"
        />
      </a-tab-pane>
      <a-tab-pane key="6" tab="车长">
        <a-table
          :dataSource="carManData"
          :columns="columns"
          size="small"
          bordered
          :pagination="false"
          :loading="oLoading"
        >
          <template #headerCell="{ column }">
            <span>
              {{ column.title }}
              <a-tooltip>
                <template #title> {{ column.helpText }} </template>
                <question-circle-outlined v-if="column.helpText" />
              </a-tooltip>
            </span>
          </template>
        </a-table>
        <a-pagination
          style="margin-top: 20px; text-align: right"
          v-model:current="current"
          :total="total"
          :showSizeChanger="false"
          show-less-items
          @change="changePage"
        />
      </a-tab-pane>
    </a-tabs>

    <a-tabs
      v-if="props.modalMessage.title == '门店'"
      v-model:activeKey="activeKey"
      type="card"
      @change="changeTabs"
    >
      <a-tab-pane key="1" tab="汇总表">
        <a-table
          :dataSource="dataSource"
          :columns="columns"
          size="small"
          bordered
          :pagination="false"
          :loading="oLoading"
        />
        <a-pagination
          style="margin-top: 20px; text-align: right"
          v-model:current="current"
          :total="total"
          :showSizeChanger="false"
          show-less-items
          @change="changePage"
        />
      </a-tab-pane>
      <a-tab-pane key="2" tab="明细表">
        <a-table
          :dataSource="dataSource"
          :columns="columns"
          size="small"
          bordered
          :pagination="false"
          :loading="oLoading"
        />
        <a-pagination
          style="margin-top: 20px; text-align: right"
          v-model:current="current"
          :total="total"
          show-less-items
          @change="changePage"
        />
      </a-tab-pane>
    </a-tabs>

    <a-tabs
      v-if="props.modalMessage.title == '销售'"
      v-model:activeKey="activeKey"
      type="card"
      @change="changeTabs"
    >
      <a-tab-pane key="1" tab="经销区销货情况">
        <a-table
          :dataSource="jxqData"
          :columns="columns"
          size="small"
          bordered
          :pagination="false"
          :loading="oLoading"
        >
          <template #headerCell="{ column }">
            <span>
              {{ column.title }}
              <a-tooltip>
                <template #title> {{ column.helpText }} </template>
                <question-circle-outlined v-if="column.helpText" />
              </a-tooltip>
            </span>
          </template>
        </a-table>
        <a-pagination
          style="margin-top: 20px; text-align: right"
          v-model:current="current"
          :total="total"
          :showSizeChanger="false"
          show-less-items
          @change="changePage"
        />
      </a-tab-pane>
      <a-tab-pane key="2" tab="车长销货情况">
        <a-table
          :dataSource="czData"
          :columns="columns"
          size="small"
          bordered
          :pagination="false"
          :loading="oLoading"
        >
          <template #headerCell="{ column }">
            <span>
              {{ column.title }}
              <a-tooltip>
                <template #title> {{ column.helpText }} </template>
                <question-circle-outlined v-if="column.helpText" />
              </a-tooltip>
            </span>
          </template>
        </a-table>
        <a-pagination
          style="margin-top: 20px; text-align: right"
          v-model:current="current"
          :total="total"
          :showSizeChanger="false"
          show-less-items
          @change="changePage"
        />
      </a-tab-pane>
      <a-tab-pane key="3" tab="门店销售情况">
        <a-table
          :dataSource="dataSource"
          :columns="columns"
          size="small"
          bordered
          :pagination="false"
          :loading="oLoading"
        />
        <a-pagination
          style="margin-top: 20px; text-align: right"
          v-model:current="current"
          :total="total"
          :showSizeChanger="false"
          show-less-items
          @change="changePage"
        />
      </a-tab-pane>
    </a-tabs>

    <a-tabs
      v-if="props.modalMessage.title == '物料数量'"
      v-model:activeKey="activeKey"
      type="card"
      @change="changeTabs"
    >
      <a-tab-pane key="1" tab="盘筐详情">
        <a-table
          :dataSource="pkData"
          :columns="columns"
          size="small"
          bordered
          :pagination="false"
          :loading="oLoading"
        />
        <a-pagination
          style="margin-top: 20px; text-align: right"
          v-model:current="current"
          :total="total"
          :showSizeChanger="false"
          show-less-items
          @change="changePage"
        />
      </a-tab-pane>
      <a-tab-pane key="2" tab="盘物料详情">
        <a-table
          :dataSource="pwlData"
          :columns="columns"
          size="small"
          bordered
          :pagination="false"
          :loading="oLoading"
        />
        <a-pagination
          style="margin-top: 20px; text-align: right"
          v-model:current="current"
          :total="total"
          :showSizeChanger="false"
          show-less-items
          @change="changePage"
        />
      </a-tab-pane>
      <a-tab-pane key="3" tab="盘物料详情明细">
        <a-table
          :dataSource="pwlMxData"
          :columns="columns"
          size="small"
          bordered
          :pagination="false"
          :loading="oLoading"
        />
        <a-pagination
          style="margin-top: 20px; text-align: right"
          v-model:current="current"
          :total="total"
          :showSizeChanger="false"
          show-less-items
          @change="changePage"
        />
      </a-tab-pane>
      <a-tab-pane key="4" tab="物料盘点次数">
        <a-table
          :dataSource="pwlCsData"
          :columns="columns"
          size="small"
          bordered
          :pagination="false"
          :loading="oLoading"
        />
        <a-pagination
          style="margin-top: 20px; text-align: right"
          v-model:current="current"
          :total="total"
          :showSizeChanger="false"
          show-less-items
          @change="changePage"
        />
      </a-tab-pane>
    </a-tabs>
    <!-- 单表格 -->
    <div
      v-if="
        props.modalMessage.title !== '车长拜访情况' &&
        props.modalMessage.title !== '门店' &&
        props.modalMessage.title !== '销售' &&
        props.modalMessage.title !== '物料数量'
      "
    >
      <a-table
        :dataSource="dataSource"
        :columns="columns"
        size="small"
        bordered
        :pagination="false"
        :loading="oLoading"
      />
      <a-pagination
        style="margin-top: 20px; text-align: right"
        v-model:current="current"
        :total="total"
        :showSizeChanger="false"
        show-less-items
        @change="changePage"
      />
    </div>
  </a-modal>
</template>

<script setup>
import { ref, computed, onMounted, h } from "vue";
import {
  columns1,
  columns2,
  columns3,
  columns4,
  columns5,
  columns6,
  columns7,
  columns8,
  columns9,
  columns10,
  columns11,
  columns12,
  columns13,
  columns14,
  columns15,
  columns16,
  columns17,
  columns18,
  columns19,
  columns20,
  columns21,
  columns22,
  columns23,
  columns24,
  columns25,
  columns26,
  columns27,
  columns28,
  columns29,
  columns30,
  columns31,
  columns32,
  exportToExcel,
} from "../../data-uitls/dataModal";
import {
  getProductDetail,
  getSalesData,
  getSalesDataDetail,
  getCzData,
  getJxqData,
  getDisData,
  getDisDataCustom,
  getCmData,
  getCmDataCustom,
  getHzData,
  getMxData,
  getPkData,
  getPwlData,
  getPwlMxData,
  getPwlCsData,
  getRetailer,
  getRetailerMsg,
  refreshList,
  getPesonCover,
  getOrderCollect,
  getOrderDetail,
} from "../../../../api/Map/data";
import { useMapStore } from "../../../../store/map";
import { useColumnData } from "../../../../store/columnData";
import dayjs from "dayjs";
import { message } from "ant-design-vue";
import { QuestionCircleOutlined } from "@ant-design/icons-vue";
import { getRecent24Months } from "../../data-uitls";

const store = useMapStore();
const columnStore = useColumnData();

const props = defineProps([
  "showModal",
  "modalMessage",
  "choose_product_category_code", // 产品类别编码
  "choose_product_code", // 产品编码
  "product_group", // 产品组
]);
const showModal = ref(props.showModal);
const emit = defineEmits(["update:showModal"]);
const activeKey = ref("1");

const cancel = () => {
  if (store.selectObj.optionValue !== "自定义时间") {
    store.selectObj.dateSection = null;
  }
  emit("update:showModal", false);
};

const timeOptions = getRecent24Months();
const customTime = computed(() => {
  return store.selectObj.optionValue === "自定义时间";
});

const dataSource = ref([]);
const dataSourceDetail = ref([]);
const czData = ref([]); // 车长数据
const jxqData = ref([]); // 经销区销货数据
const distributionData = ref([]); // 经销区总体数据
const carManData = ref([]); // 车长数量里车长表数据
const pkData = ref([]); // 盘筐数据
const pwlData = ref([]); // 盘物料数据
const pwlMxData = ref([]); // 盘物料明细数据
const pwlCsData = ref([]); // 盘物料次数数据
const orderData = ref([]); // 订单数据
const selectConditions = ref(0); // 选择条件区分
const oLoading = ref(false); // 数据加载
const exportLoading = ref(false); // 导出数据加载

const columns = computed(() => {
  // if (props.modalMessage.title === "销售") {
  //   return columns1;
  // }
  // if (props.modalMessage.title === "盘物料总数") {
  //   return columns2;
  // }
  // if (props.modalMessage.title === "盘筐总数") {
  //   return columns3;
  // }
  if (props.modalMessage.title === "产品种类总数") {
    return columns4;
  }
  if (props.modalMessage.title === "人群覆盖率") {
    return columns28;
  }

  const columnMapping = {
    车长拜访情况: {
      1: columns5,
      2: props.choose_product_category_code
        ? props.choose_product_code
          ? columns6
          : columns14
        : columns7,
      3: columns29,
      4: columns30,
      5: columns31,
      6: columns32,
    },
    门店: {
      1: props.choose_product_category_code
        ? props.choose_product_code
          ? columns8
          : columns12
        : columns9,
      2: props.choose_product_category_code
        ? props.choose_product_code
          ? columns10
          : columns13
        : columns11,
    },
    销售: {
      1: props.choose_product_category_code
        ? props.choose_product_code
          ? columns16
          : columns17
        : columns15,
      2: props.choose_product_category_code
        ? props.choose_product_code
          ? columns19
          : columns20
        : columns18,
      3: props.choose_product_category_code
        ? props.choose_product_code
          ? columns22
          : columns23
        : columns21,
    },
    物料数量: {
      1: columns24,
      2: columns25,
      3: columns26,
      4: columns27,
    },
  };

  const getColumns = () => {
    const { title } = props.modalMessage;
    const activeKeyCheck =
      columnMapping[title] && columnMapping[title][activeKey.value];

    return activeKeyCheck || columnMapping[title] || null;
  };

  // 使用getColumns函数来取得相应的columns
  return getColumns();
});

const current = ref(1);
const total = ref(0);
const reLoad = ref(true); // 重新加载销售明细表开关

async function changePage() {
  reLoad.value = false;
  await getData();
}

async function changeTabs() {
  current.value = 1;
  dataSource.value = [];
  jxqData.value = [];
  distributionData.value = [];
  carManData.value = [];
  czData.value = [];
  pkData.value = [];
  pwlData.value = [];
  pwlMxData.value = [];
  pwlCsData.value = [];
  orderData.value = [];
  await getData();
}

async function getData() {
  if (props.modalMessage.title === "销售" && activeKey.value == 1) {
    await getSalesJxqData();
  }
  if (props.modalMessage.title === "销售" && activeKey.value == 2) {
    await getSalesCzData();
  }
  if (props.modalMessage.title === "销售" && activeKey.value == 3) {
    await getSalesShopData();
  }
  if (props.modalMessage.title === "物料数量" && activeKey.value == 1) {
    await getMaterialPkData();
  }
  if (props.modalMessage.title === "物料数量" && activeKey.value == 2) {
    await getMaterialPwlData();
  }
  if (props.modalMessage.title === "物料数量" && activeKey.value == 3) {
    await getMaterialPwlMxData();
  }
  if (props.modalMessage.title === "物料数量" && activeKey.value == 4) {
    await getMaterialPwlCsData();
  }
  if (props.modalMessage.title === "产品种类总数") {
    await getProductDetailList();
  }
  if (props.modalMessage.title === "车长拜访情况" && activeKey.value == 1) {
    await getRetailerMsgList();
  }
  if (props.modalMessage.title == "车长拜访情况" && activeKey.value == 2) {
    await getRetailerList();
  }
  if (props.modalMessage.title == "车长拜访情况" && activeKey.value == 3) {
    await getOrderCollectData();
  }
  if (props.modalMessage.title == "车长拜访情况" && activeKey.value == 4) {
    await getOrderDetailData();
  }
  if (props.modalMessage.title == "车长拜访情况" && activeKey.value == 5) {
    await getDistributionData();
  }
  if (props.modalMessage.title == "车长拜访情况" && activeKey.value == 6) {
    await getCarManData();
  }
  if (props.modalMessage.title === "门店" && activeKey.value == 1) {
    await getShopHzData();
  }
  if (props.modalMessage.title == "门店" && activeKey.value == 2) {
    await getShopMxData();
  }
  if (props.modalMessage.title === "人群覆盖率") {
    await getCrowdCoverage();
  }
  console.log(dataSource.value);
  if (dataSource.value.length > 0) {
    total.value = dataSource.value[0].total ? dataSource.value[0].total : 0;
  } else if (czData.value.length > 0) {
    total.value = czData.value[0].total ? czData.value[0].total : 0;
  } else if (jxqData.value.length > 0) {
    total.value = jxqData.value[0].total ? jxqData.value[0].total : 0;
  } else if (distributionData.value.length > 0) {
    total.value = distributionData.value[0].total
      ? distributionData.value[0].total
      : 0;
  } else if (carManData.value.length > 0) {
    total.value = carManData.value[0].total ? carManData.value[0].total : 0;
  } else if (pkData.value.length > 0) {
    total.value = pkData.value[0].total;
  } else if (pwlData.value.length > 0) {
    total.value = pwlData.value[0].total;
  } else if (pwlMxData.value.length > 0) {
    total.value = pwlMxData.value[0].total;
  } else if (pwlCsData.value.length > 0) {
    total.value = pwlCsData.value[0].total;
  } else if (orderData.value.length > 0) {
    total.value = orderData.value[0].total;
  } else {
    total.value = 0;
  }
  console.log(total.value, "total");
}

function getDqIdJxqId(dqList, jxqList) {
  let region = "";
  let area = "";
  dqList.forEach((item) => {
    if (store.selectObj.dqName === "全部") {
      region = "";
    } else if (item.label === store.selectObj.dqName) {
      region = item.dqId;
    }
  });
  jxqList.forEach((item) => {
    if (store.selectObj.jxqName === "全部") {
      area = "";
    } else if (item.label === store.selectObj.jxqName) {
      area = item.jxqId;
    }
  });

  return {
    region,
    area,
  };
}

// 获取销售门店数据
async function getSalesShopData() {
  let start_date;
  let end_date;
  if (store.selectObj.start_date && store.selectObj.end_date) {
    start_date = store.selectObj.start_date;
    end_date = store.selectObj.end_date;
  } else if (store.selectObj.optionValue === "近30天") {
    start_date = dayjs().subtract(30, "day").format("YYYY-MM-DD");
    end_date = dayjs().format("YYYY-MM-DD");
    store.selectObj.dateSection = [dayjs(start_date), dayjs(end_date)];
  } else if (store.selectObj.optionValue === "自定义时间") {
    if (store.selectObj.dateSection === null) {
      start_date = dayjs().subtract(30, "day").format("YYYY-MM-DD");
      end_date = dayjs().format("YYYY-MM-DD");
    } else {
      start_date = dayjs(store.selectObj.dateSection[0]).format("YYYY-MM-DD");
      end_date = dayjs(store.selectObj.dateSection[1]).format("YYYY-MM-DD");
    }
  } else {
    start_date = store.selectObj.optionValue + "-01";
    end_date = dayjs(start_date).add(30, "day").format("YYYY-MM-DD");
    store.selectObj.dateSection = [dayjs(start_date), dayjs(end_date)];
  }
  oLoading.value = true;
  let id = getDqIdJxqId(store.dqList, store.jxqList);

  const params = {
    dealer: store.selectObj.jxsId,
    retailer: store.selectObj.fxsId,
    start_date: start_date,
    end_date: end_date,
    page: current.value,
    page_size: 10,
    product_class: props.choose_product_category_code,
    product: props.choose_product_code,
    product_group: props.product_group,
    ...id,
  };

  try {
    const res = await getSalesData(params);
    dataSource.value = res.data.result;

    dataSource.value.forEach((item) => {
      item.return_rate = `${Number(item.return_rate).toFixed(2)}%`;
    });
    oLoading.value = false;
  } catch (err) {
    oLoading.value = false;
    message.error("网络错误");
  }

  const params1 = {
    dealer: store.selectObj.jxsId,
    retailer: store.selectObj.fxsId,
    start_date: start_date,
    end_date: end_date,
    page: 1,
    page_size: 9999999,
    product_group: props.product_group,
    ...id,
  };

  if (reLoad.value) {
    const res1 = await getSalesDataDetail(params1);
    dataSourceDetail.value = res1.data.result;
  }
}

// 获取门店汇总表数据
async function getShopHzData() {
  let start_date;
  let end_date;
  if (store.selectObj.start_date && store.selectObj.end_date) {
    start_date = store.selectObj.start_date;
    end_date = store.selectObj.end_date;
  } else if (store.selectObj.optionValue === "近30天") {
    start_date = dayjs().subtract(30, "day").format("YYYY-MM-DD");
    end_date = dayjs().format("YYYY-MM-DD");
    store.selectObj.dateSection = [dayjs(start_date), dayjs(end_date)];
  } else if (store.selectObj.optionValue === "自定义时间") {
    if (store.selectObj.dateSection === null) {
      start_date = dayjs().subtract(30, "day").format("YYYY-MM-DD");
      end_date = dayjs().format("YYYY-MM-DD");
    } else {
      start_date = dayjs(store.selectObj.dateSection[0]).format("YYYY-MM-DD");
      end_date = dayjs(store.selectObj.dateSection[1]).format("YYYY-MM-DD");
    }
  } else {
    start_date = store.selectObj.optionValue + "-01";
    end_date = dayjs(start_date).add(30, "day").format("YYYY-MM-DD");
    store.selectObj.dateSection = [dayjs(start_date), dayjs(end_date)];
  }

  oLoading.value = true;
  let id = getDqIdJxqId(store.dqList, store.jxqList);

  const params = {
    dealer: store.selectObj.jxsId,
    retailer: store.selectObj.fxsId,
    start_date: start_date,
    end_date: end_date,
    page: current.value,
    page_size: 10,
    product_class: props.choose_product_category_code,
    product: props.choose_product_code,
    product_group: props.product_group,
    ...id,
  };

  try {
    const res = await getHzData(params);
    dataSource.value = res.data.result;
    oLoading.value = false;
  } catch (err) {
    oLoading.value = false;
    message.error("网络错误");
  }
}

// 获取门店明细表数据
async function getShopMxData() {
  let start_date;
  let end_date;
  if (store.selectObj.start_date && store.selectObj.end_date) {
    start_date = store.selectObj.start_date;
    end_date = store.selectObj.end_date;
  } else if (store.selectObj.optionValue === "近30天") {
    start_date = dayjs().subtract(30, "day").format("YYYY-MM-DD");
    end_date = dayjs().format("YYYY-MM-DD");
    store.selectObj.dateSection = [dayjs(start_date), dayjs(end_date)];
  } else if (store.selectObj.optionValue === "自定义时间") {
    if (store.selectObj.dateSection === null) {
      start_date = dayjs().subtract(30, "day").format("YYYY-MM-DD");
      end_date = dayjs().format("YYYY-MM-DD");
    } else {
      start_date = dayjs(store.selectObj.dateSection[0]).format("YYYY-MM-DD");
      end_date = dayjs(store.selectObj.dateSection[1]).format("YYYY-MM-DD");
    }
  } else {
    start_date = store.selectObj.optionValue + "-01";
    end_date = dayjs(start_date).add(30, "day").format("YYYY-MM-DD");
    store.selectObj.dateSection = [dayjs(start_date), dayjs(end_date)];
  }

  oLoading.value = true;
  let id = getDqIdJxqId(store.dqList, store.jxqList);

  const params = {
    dealer: store.selectObj.jxsId,
    retailer: store.selectObj.fxsId,
    start_date: start_date,
    end_date: end_date,
    page: current.value,
    page_size: 10,
    product_class: props.choose_product_category_code,
    product: props.choose_product_code,
    product_group: props.product_group,
    ...id,
  };

  try {
    const res = await getMxData(params);
    dataSource.value = res.data.result;
    oLoading.value = false;
  } catch (err) {
    oLoading.value = false;
    message.error("网络错误");
  }
}

// 获取人群覆盖率数据
async function getCrowdCoverage() {
  oLoading.value = true;
  let id = getDqIdJxqId(store.dqList, store.jxqList);
  const params = {
    dealer: store.selectObj.jxsId,
    page: current.value,
    page_size: 10,
    ...id,
  };

  try {
    const res = await getPesonCover(params);
    dataSource.value = res.data.result[0] || [];
    oLoading.value = false;
  } catch (err) {
    oLoading.value = false;
    message.error("网络错误");
  }
}

// 获取车长销货数据
async function getSalesCzData() {
  let start_date;
  let end_date;
  if (store.selectObj.start_date && store.selectObj.end_date) {
    start_date = store.selectObj.start_date;
    end_date = store.selectObj.end_date;
  } else if (store.selectObj.optionValue === "近30天") {
    start_date = dayjs().subtract(30, "day").format("YYYY-MM-DD");
    end_date = dayjs().format("YYYY-MM-DD");
    store.selectObj.dateSection = [dayjs(start_date), dayjs(end_date)];
  } else if (store.selectObj.optionValue === "自定义时间") {
    if (store.selectObj.dateSection === null) {
      start_date = dayjs().subtract(30, "day").format("YYYY-MM-DD");
      end_date = dayjs().format("YYYY-MM-DD");
    } else {
      start_date = dayjs(store.selectObj.dateSection[0]).format("YYYY-MM-DD");
      end_date = dayjs(store.selectObj.dateSection[1]).format("YYYY-MM-DD");
    }
  } else {
    start_date = store.selectObj.optionValue + "-01";
    end_date = dayjs(start_date).add(30, "day").format("YYYY-MM-DD");
    store.selectObj.dateSection = [dayjs(start_date), dayjs(end_date)];
  }
  oLoading.value = true;
  let id = getDqIdJxqId(store.dqList, store.jxqList);

  const params = {
    dealer: store.selectObj.jxsId,
    retailer: store.selectObj.fxsId,
    start_date: start_date,
    end_date: end_date,
    page: current.value,
    page_size: 10,
    product_class: props.choose_product_category_code,
    product: props.choose_product_code,
    product_group: props.product_group,
    ...id,
  };

  try {
    const res = await getCzData(params);
    czData.value = res.data.result;

    czData.value.forEach((item) => {
      item.factory_return_rate = `${item.factory_return_rate}%`;
      item.return_rate = `${item.return_rate}%`;
    });
    oLoading.value = false;
  } catch (err) {
    oLoading.value = false;
    message.error("网络错误");
  }
}

// 获取经销区销货数据
async function getSalesJxqData() {
  let start_date;
  let end_date;
  if (store.selectObj.start_date && store.selectObj.end_date) {
    start_date = store.selectObj.start_date;
    end_date = store.selectObj.end_date;
  } else if (store.selectObj.optionValue === "近30天") {
    start_date = dayjs().subtract(30, "day").format("YYYY-MM-DD");
    end_date = dayjs().format("YYYY-MM-DD");
    store.selectObj.dateSection = [dayjs(start_date), dayjs(end_date)];
  } else if (store.selectObj.optionValue === "自定义时间") {
    if (store.selectObj.dateSection === null) {
      start_date = dayjs().subtract(30, "day").format("YYYY-MM-DD");
      end_date = dayjs().format("YYYY-MM-DD");
    } else {
      start_date = dayjs(store.selectObj.dateSection[0]).format("YYYY-MM-DD");
      end_date = dayjs(store.selectObj.dateSection[1]).format("YYYY-MM-DD");
    }
  } else {
    start_date = store.selectObj.optionValue + "-01";
    end_date = dayjs(start_date).add(30, "day").format("YYYY-MM-DD");
    store.selectObj.dateSection = [dayjs(start_date), dayjs(end_date)];
  }

  oLoading.value = true;
  let id = getDqIdJxqId(store.dqList, store.jxqList);

  const params = {
    dealer: store.selectObj.jxsId,
    retailer: store.selectObj.fxsId,
    start_date: start_date,
    end_date: end_date,
    page: current.value,
    page_size: 10,
    product_class: props.choose_product_category_code,
    product: props.choose_product_code,
    product_group: props.product_group,
    ...id,
  };

  try {
    const res = await getJxqData(params);
    jxqData.value = res.data.result;

    jxqData.value.forEach((item) => {
      item.factory_return_rate = `${item.factory_return_rate}%`;
      item.return_rate = `${item.return_rate}%`;
    });
    oLoading.value = false;
  } catch (err) {
    oLoading.value = false;
    message.error("网络错误");
  }
}

// 获取经销区总体数据
async function getDistributionData() {
  let start_date;
  let end_date;
  if (store.selectObj.start_date && store.selectObj.end_date) {
    selectConditions.value = 3;
    start_date = store.selectObj.start_date;
    end_date = store.selectObj.end_date;
  } else if (store.selectObj.optionValue === "近30天") {
    selectConditions.value = 0;
    start_date = dayjs().subtract(30, "day").format("YYYY-MM-DD");
    end_date = dayjs().format("YYYY-MM-DD");
    store.selectObj.dateSection = [dayjs(start_date), dayjs(end_date)];
  } else if (store.selectObj.optionValue === "自定义时间") {
    selectConditions.value = 1;
    if (store.selectObj.dateSection === null) {
      start_date = dayjs().subtract(30, "day").format("YYYY-MM-DD");
      end_date = dayjs().format("YYYY-MM-DD");
    } else {
      start_date = dayjs(store.selectObj.dateSection[0]).format("YYYY-MM-DD");
      end_date = dayjs(store.selectObj.dateSection[1]).format("YYYY-MM-DD");
    }
  } else {
    selectConditions.value = 2;
    start_date = store.selectObj.optionValue + "-01";
    end_date = dayjs(start_date).add(30, "day").format("YYYY-MM-DD");
    store.selectObj.dateSection = [dayjs(start_date), dayjs(end_date)];
  }

  const params = {
    start_date: start_date,
    end_date: end_date,
    page: current.value,
    page_size: 10,
  };

  oLoading.value = true;
  if (selectConditions.value === 0) {
    try {
      if (columnStore.columnCache.distributionColumns[current.value]) {
        distributionData.value =
          columnStore.columnCache.distributionColumns[current.value];
        oLoading.value = false;
      } else {
        const res = await getDisData(params);
        if (res.data.result.length > 0) {
          oLoading.value = false;
          distributionData.value = res.data.result;
        } else {
          const res = await getDisDataCustom(params);
          oLoading.value = false;
          distributionData.value = res.data.result;
        }
        columnStore.columnCache.distributionColumns[current.value] =
          distributionData.value;
      }
    } catch (err) {
      message.error("数据加载失败,请稍后再试");
      oLoading.value = false;
    }
  } else if (selectConditions.value === 1) {
    try {
      let cache = columnStore.addColumnCache(
        "distributionColumns",
        start_date,
        end_date
      );
      if (cache[current.value]) {
        distributionData.value = cache[current.value];
        oLoading.value = false;
      } else {
        const res = await getDisDataCustom(params);
        oLoading.value = false;
        distributionData.value = res.data.result;
        cache[current.value] = distributionData.value;
      }
    } catch (err) {
      message.error("数据加载失败,请稍后再试");
      oLoading.value = false;
    }
  } else if (selectConditions.value === 2) {
    try {
      let cache = columnStore.addColumnCache("distributionColumns", start_date);
      if (cache[current.value]) {
        distributionData.value = cache[current.value];
        oLoading.value = false;
      } else {
        const res = await getDisData(params);
        if (res.data.result.length > 0) {
          oLoading.value = false;
          distributionData.value = res.data.result;
        } else {
          const res = await getDisDataCustom(params);
          oLoading.value = false;
          distributionData.value = res.data.result;
        }
        cache[current.value] = distributionData.value;
      }
    } catch (err) {
      message.error("数据加载失败,请稍后再试");
      oLoading.value = false;
    }
  } else if (selectConditions.value === 3) {
    try {
      let cache = columnStore.addColumnCache(
        "distributionColumns",
        start_date,
        end_date
      );
      if (cache[current.value]) {
        distributionData.value = cache[current.value];
        oLoading.value = false;
      } else {
        const res = await getDisData(params);
        if (res.data.result.length > 0) {
          oLoading.value = false;
          distributionData.value = res.data.result;
        } else {
          const res = await getDisDataCustom(params);
          oLoading.value = false;
          distributionData.value = res.data.result;
        }
        cache[current.value] = distributionData.value;
      }
    } catch (err) {
      message.error("数据加载失败,请稍后再试");
      oLoading.value = false;
    }
  }

  distributionData.value = distributionData.value.map((item) => {
    const truckLoadingMoney = Number(item.Truck_loading_money); // 假设这是金额
    const returnGoodsRate = Number(item.Return_goods_percentage); // 假设这是退货率（小数形式）

    return {
      ...item,
      Truck_loading_money: truckLoadingMoney.toFixed(2), // 格式化金额
      Return_goods_percentage: `${(returnGoodsRate * 100).toFixed(2)}%`, // 计算百分比并格式化
    };
  });
}

// 获取车长数据
async function getCarManData() {
  let start_date;
  let end_date;
  if (store.selectObj.start_date && store.selectObj.end_date) {
    selectConditions.value = 3;
    start_date = store.selectObj.start_date;
    end_date = store.selectObj.end_date;
  } else if (store.selectObj.optionValue === "近30天") {
    selectConditions.value = 0;
    start_date = dayjs().subtract(30, "day").format("YYYY-MM-DD");
    end_date = dayjs().format("YYYY-MM-DD");
    store.selectObj.dateSection = [dayjs(start_date), dayjs(end_date)];
  } else if (store.selectObj.optionValue === "自定义时间") {
    selectConditions.value = 1;
    if (store.selectObj.dateSection === null) {
      start_date = dayjs().subtract(30, "day").format("YYYY-MM-DD");
      end_date = dayjs().format("YYYY-MM-DD");
    } else {
      start_date = dayjs(store.selectObj.dateSection[0]).format("YYYY-MM-DD");
      end_date = dayjs(store.selectObj.dateSection[1]).format("YYYY-MM-DD");
    }
  } else {
    selectConditions.value = 2;
    start_date = store.selectObj.optionValue + "-01";
    end_date = dayjs(start_date).add(30, "day").format("YYYY-MM-DD");
    store.selectObj.dateSection = [dayjs(start_date), dayjs(end_date)];
  }

  const params = {
    start_date: start_date,
    end_date: end_date,
    page: current.value,
    page_size: 10,
  };

  oLoading.value = true;
  console.log(columnStore.columnCache, "缓存数据");

  if (selectConditions.value === 0) {
    try {
      if (columnStore.columnCache.carManColumns[current.value]) {
        carManData.value = columnStore.columnCache.carManColumns[current.value];
        oLoading.value = false;
      } else {
        const res = await getCmData(params);
        if (res.data.result.length > 0) {
          oLoading.value = false;
          carManData.value = res.data.result;
        } else {
          const res = await getCmDataCustom(params);
          oLoading.value = false;
          carManData.value = res.data.result;
        }
        columnStore.columnCache.carManColumns[current.value] = carManData.value;
      }
    } catch (err) {
      message.error("数据加载失败,请稍后再试");
      oLoading.value = false;
    }
  } else if (selectConditions.value === 1) {
    try {
      let cache = columnStore.addColumnCache(
        "carManColumns",
        start_date,
        end_date
      );
      if (cache[current.value]) {
        carManData.value = cache[current.value];
        oLoading.value = false;
      } else {
        const res = await getCmDataCustom(params);
        oLoading.value = false;
        carManData.value = res.data.result;
        cache[current.value] = carManData.value;
      }
    } catch (err) {
      message.error("数据加载失败,请稍后再试");
      oLoading.value = false;
    }
  } else if (selectConditions.value === 2) {
    try {
      let cache = columnStore.addColumnCache("carManColumns", start_date);
      if (cache[current.value]) {
        carManData.value = cache[current.value];
        oLoading.value = false;
      } else {
        const res = await getCmData(params);
        if (res.data.result.length > 0) {
          oLoading.value = false;
          carManData.value = res.data.result;
        } else {
          const res = await getCmDataCustom(params);
          oLoading.value = false;
          carManData.value = res.data.result;
        }
        cache[current.value] = carManData.value;
      }
    } catch (err) {
      message.error("数据加载失败,请稍后再试");
      oLoading.value = false;
    }
  } else if (selectConditions.value === 3) {
    try {
      let cache = columnStore.addColumnCache(
        "carManColumns",
        start_date,
        end_date
      );
      if (cache[current.value]) {
        carManData.value = cache[current.value];
        oLoading.value = false;
      } else {
        const res = await getCmData(params);
        if (res.data.result.length > 0) {
          oLoading.value = false;
          carManData.value = res.data.result;
        } else {
          const res = await getCmDataCustom(params);
          oLoading.value = false;
          carManData.value = res.data.result;
        }
        cache[current.value] = carManData.value;
      }
    } catch (err) {
      message.error("数据加载失败,请稍后再试");
      oLoading.value = false;
    }
  }

  carManData.value = carManData.value.map((item) => {
    const truckLoadingMoney = Number(item.Truck_loading_money); // 假设这是金额
    const entryTime = Number(item.Entry_time);
    const attendanceRate = Number(item.Attendance_rate); // 假设这是出勤率（小数形式）
    const returnFactoryRate = Number(item.Return_factory_rate); // 假设这是回厂率（小数形式）
    const shopReturnRate = Number(item.Shop_return_rate); // 假设这是退货率（小数形式）
    const shopSaleMoney =
      Number(item.Shop_sale_money) === null ? 0 : Number(item.Shop_sale_money);

    return {
      ...item,
      Entry_time: entryTime.toFixed(1), // 保留一位小数
      Attendance_rate: `${(attendanceRate * 100).toFixed(2)}%`, // 计算百分比并格式化
      Return_factory_rate: `${(returnFactoryRate * 100).toFixed(2)}%`, // 计算百分比并格式化
      Truck_loading_money: truckLoadingMoney.toFixed(2), // 格式化金额
      Shop_return_rate: `${(shopReturnRate * 100).toFixed(2)}%`, // 计算百分比并格式化
      Shop_sale_money: shopSaleMoney.toFixed(2),
    };
  });
}
// 获取产品种类数据
async function getProductDetailList() {
  let start_date;
  let end_date;
  if (store.selectObj.optionValue === "近30天") {
    start_date = dayjs().subtract(30, "day").format("YYYY-MM-DD");
    end_date = dayjs().format("YYYY-MM-DD");
    store.selectObj.dateSection = [dayjs(start_date), dayjs(end_date)];
  } else if (store.selectObj.optionValue === "自定义时间") {
    if (store.selectObj.dateSection === null) {
      start_date = dayjs().subtract(30, "day").format("YYYY-MM-DD");
      end_date = dayjs().format("YYYY-MM-DD");
    } else {
      start_date = dayjs(store.selectObj.dateSection[0]).format("YYYY-MM-DD");
      end_date = dayjs(store.selectObj.dateSection[1]).format("YYYY-MM-DD");
    }
  } else {
    start_date = store.selectObj.optionValue + "-01";
    end_date = dayjs(start_date).add(30, "day").format("YYYY-MM-DD");
    store.selectObj.dateSection = [dayjs(start_date), dayjs(end_date)];
  }

  oLoading.value = true;
  let id = getDqIdJxqId(store.dqList, store.jxqList);

  const params = {
    dealer: store.selectObj.jxsId,
    retailer: store.selectObj.fxsId,
    start_date: start_date,
    end_date: end_date,
    page: current.value,
    page_size: 10,
    product_class: props.choose_product_category_code,
    product: props.choose_product_code,
    product_group: props.product_group,
    ...id,
  };

  try {
    const res = await getProductDetail(params);
    dataSource.value = res.data.result;
    dataSource.value.sort((a, b) => {
      return b.sale_money - a.sale_money;
    });
    oLoading.value = false;
  } catch (err) {
    message.error("数据加载失败,请稍后再试");
    oLoading.value = false;
  }
}

// 获取物料盘筐表数据
async function getMaterialPkData() {
  let start_date;
  let end_date;
  if (store.selectObj.start_date && store.selectObj.end_date) {
    start_date = store.selectObj.start_date;
    end_date = store.selectObj.end_date;
  } else if (store.selectObj.optionValue === "近30天") {
    start_date = dayjs().subtract(30, "day").format("YYYY-MM-DD");
    end_date = dayjs().format("YYYY-MM-DD");
    store.selectObj.dateSection = [dayjs(start_date), dayjs(end_date)];
  } else if (store.selectObj.optionValue === "自定义时间") {
    if (store.selectObj.dateSection === null) {
      start_date = dayjs().subtract(30, "day").format("YYYY-MM-DD");
      end_date = dayjs().format("YYYY-MM-DD");
    } else {
      start_date = dayjs(store.selectObj.dateSection[0]).format("YYYY-MM-DD");
      end_date = dayjs(store.selectObj.dateSection[1]).format("YYYY-MM-DD");
    }
  } else {
    start_date = store.selectObj.optionValue + "-01";
    end_date = dayjs(start_date).add(30, "day").format("YYYY-MM-DD");
    store.selectObj.dateSection = [dayjs(start_date), dayjs(end_date)];
  }
  oLoading.value = true;
  let id = getDqIdJxqId(store.dqList, store.jxqList);

  const params = {
    dealer: store.selectObj.jxsId,
    retailer: store.selectObj.fxsId,
    start_date: start_date,
    end_date: end_date,
    page: current.value,
    page_size: 10,
    product_class: props.choose_product_category_code,
    product: props.choose_product_code,
    ...id,
  };

  try {
    const res = await getPkData(params);
    pkData.value = res.data.result;
    oLoading.value = false;
  } catch (err) {
    message.error("数据加载失败,请稍后再试");
    oLoading.value = false;
  }
}

// 获取物料盘物料表数据
async function getMaterialPwlData() {
  let start_date;
  let end_date;
  if (store.selectObj.start_date && store.selectObj.end_date) {
    start_date = store.selectObj.start_date;
    end_date = store.selectObj.end_date;
  } else if (store.selectObj.optionValue === "近30天") {
    start_date = dayjs().subtract(30, "day").format("YYYY-MM-DD");
    end_date = dayjs().format("YYYY-MM-DD");
    store.selectObj.dateSection = [dayjs(start_date), dayjs(end_date)];
  } else if (store.selectObj.optionValue === "自定义时间") {
    if (store.selectObj.dateSection === null) {
      start_date = dayjs().subtract(30, "day").format("YYYY-MM-DD");
      end_date = dayjs().format("YYYY-MM-DD");
    } else {
      start_date = dayjs(store.selectObj.dateSection[0]).format("YYYY-MM-DD");
      end_date = dayjs(store.selectObj.dateSection[1]).format("YYYY-MM-DD");
    }
  } else {
    start_date = store.selectObj.optionValue + "-01";
    end_date = dayjs(start_date).add(30, "day").format("YYYY-MM-DD");
    store.selectObj.dateSection = [dayjs(start_date), dayjs(end_date)];
  }

  oLoading.value = true;
  let id = getDqIdJxqId(store.dqList, store.jxqList);

  const params = {
    dealer: store.selectObj.jxsId,
    retailer: store.selectObj.fxsId,
    start_date: start_date,
    end_date: end_date,
    page: current.value,
    page_size: 10,
    product_class: props.choose_product_category_code,
    product: props.choose_product_code,
    ...id,
  };

  try {
    const res = await getPwlData(params);
    pwlData.value = res.data.result;
    oLoading.value = false;
  } catch (err) {
    message.error("数据加载失败,请稍后再试");
    oLoading.value = false;
  }
}

// 获取物料盘物料明细表数据
async function getMaterialPwlMxData() {
  let start_date;
  let end_date;
  if (store.selectObj.start_date && store.selectObj.end_date) {
    start_date = store.selectObj.start_date;
    end_date = store.selectObj.end_date;
  } else if (store.selectObj.optionValue === "近30天") {
    start_date = dayjs().subtract(30, "day").format("YYYY-MM-DD");
    end_date = dayjs().format("YYYY-MM-DD");
    store.selectObj.dateSection = [dayjs(start_date), dayjs(end_date)];
  } else if (store.selectObj.optionValue === "自定义时间") {
    if (store.selectObj.dateSection === null) {
      start_date = dayjs().subtract(30, "day").format("YYYY-MM-DD");
      end_date = dayjs().format("YYYY-MM-DD");
    } else {
      start_date = dayjs(store.selectObj.dateSection[0]).format("YYYY-MM-DD");
      end_date = dayjs(store.selectObj.dateSection[1]).format("YYYY-MM-DD");
    }
  } else {
    start_date = store.selectObj.optionValue + "-01";
    end_date = dayjs(start_date).add(30, "day").format("YYYY-MM-DD");
    store.selectObj.dateSection = [dayjs(start_date), dayjs(end_date)];
  }

  oLoading.value = true;
  let id = getDqIdJxqId(store.dqList, store.jxqList);

  const params = {
    dealer: store.selectObj.jxsId,
    retailer: store.selectObj.fxsId,
    start_date: start_date,
    end_date: end_date,
    page: current.value,
    page_size: 10,
    product_class: props.choose_product_category_code,
    product: props.choose_product_code,
    ...id,
  };

  try {
    const res = await getPwlMxData(params);
    pwlMxData.value = res.data.result;
    oLoading.value = false;
  } catch (err) {
    message.error("数据加载失败,请稍后再试");
    oLoading.value = false;
  }
}

// 获取物料盘物料次数表数据
async function getMaterialPwlCsData() {
  let start_date;
  let end_date;
  if (store.selectObj.start_date && store.selectObj.end_date) {
    start_date = store.selectObj.start_date;
    end_date = store.selectObj.end_date;
  } else if (store.selectObj.optionValue === "近30天") {
    start_date = dayjs().subtract(30, "day").format("YYYY-MM-DD");
    end_date = dayjs().format("YYYY-MM-DD");
    store.selectObj.dateSection = [dayjs(start_date), dayjs(end_date)];
  } else if (store.selectObj.optionValue === "自定义时间") {
    if (store.selectObj.dateSection === null) {
      start_date = dayjs().subtract(30, "day").format("YYYY-MM-DD");
      end_date = dayjs().format("YYYY-MM-DD");
    } else {
      start_date = dayjs(store.selectObj.dateSection[0]).format("YYYY-MM-DD");
      end_date = dayjs(store.selectObj.dateSection[1]).format("YYYY-MM-DD");
    }
  } else {
    start_date = store.selectObj.optionValue + "-01";
    end_date = dayjs(start_date).add(30, "day").format("YYYY-MM-DD");
    store.selectObj.dateSection = [dayjs(start_date), dayjs(end_date)];
  }

  oLoading.value = true;
  let id = getDqIdJxqId(store.dqList, store.jxqList);

  const params = {
    dealer: store.selectObj.jxsId,
    retailer: store.selectObj.fxsId,
    start_date: start_date,
    end_date: end_date,
    page: current.value,
    page_size: 10,
    product_class: props.choose_product_category_code,
    product: props.choose_product_code,
    ...id,
  };

  try {
    const res = await getPwlCsData(params);
    pwlCsData.value = res.data.result;
    oLoading.value = false;
  } catch (err) {
    message.error("数据加载失败,请稍后再试");
    oLoading.value = false;
  }
}

// 获取计划订单汇总表数据
async function getOrderCollectData() {
  const params = {
    page: current.value,
    page_size: 10,
  };
  try {
    const res = await getOrderCollect(params);
    orderData.value = res.data.result;
    orderData.value.forEach((item) => {
      item.Ratio_Status = `${Number(item.Ratio_Status).toFixed(2)}%`;
    });
    oLoading.value = false;
  } catch (error) {
    oLoading.value = false;
    message.error("网络错误");
  }
}

// 获取计划订单明细表数据
async function getOrderDetailData() {
  let start_date;
  let end_date;
  if (store.selectObj.start_date && store.selectObj.end_date) {
    start_date = store.selectObj.start_date;
    end_date = store.selectObj.end_date;
  } else if (store.selectObj.optionValue === "近30天") {
    start_date = dayjs().subtract(30, "day").format("YYYY-MM-DD");
    end_date = dayjs().format("YYYY-MM-DD");
    store.selectObj.dateSection = [dayjs(start_date), dayjs(end_date)];
  } else if (store.selectObj.optionValue === "自定义时间") {
    if (store.selectObj.dateSection === null) {
      start_date = dayjs().subtract(30, "day").format("YYYY-MM-DD");
      end_date = dayjs().format("YYYY-MM-DD");
    } else {
      start_date = dayjs(store.selectObj.dateSection[0]).format("YYYY-MM-DD");
      end_date = dayjs(store.selectObj.dateSection[1]).format("YYYY-MM-DD");
    }
  } else {
    start_date = store.selectObj.optionValue + "-01";
    end_date = dayjs(start_date).add(30, "day").format("YYYY-MM-DD");
    store.selectObj.dateSection = [dayjs(start_date), dayjs(end_date)];
  }

  oLoading.value = true;
  const params = {
    start_date: start_date,
    end_date: end_date,
    page: current.value,
    page_size: 10,
  };

  try {
    const res = await getOrderDetail(params);
    orderData.value = res.data.result.map((item) => {
      if (item.Plan_order_status === "0") {
        item.Plan_order_status = "待执行";
      } else if (item.Plan_order_status === "1") {
        item.Plan_order_status = "已过期";
      } else if (item.Plan_order_status === "2") {
        item.Plan_order_status = "已完成";
      }
      return item;
    });
    oLoading.value = false;
  } catch (error) {
    oLoading.value = false;
    message.error("网络错误");
  }

  console.log(orderData.value, "orderData");
}
// 改变日期
async function changeDate(e) {
  store.selectObj.start_date = dayjs(e[0]).format("YYYY-MM-DD");
  store.selectObj.end_date = dayjs(e[1]).format("YYYY-MM-DD");
  if (props.modalMessage.title === "销售" && activeKey.value == 1) {
    const params = {
      dealer: store.selectObj.jxsId,
      retailer: store.selectObj.fxsId,
      start_date: dayjs(e[0]).format("YYYY-MM-DD"),
      end_date: dayjs(e[1]).format("YYYY-MM-DD"),
      page: current.value,
      page_size: 10,
      product_group: props.product_group,
    };
    const res = await getJxqData(params);
    jxqData.value = res.data.result;
    jxqData.value.forEach((item) => {
      item.factory_return_rate = `${item.factory_return_rate}%`;
      item.return_rate = `${item.return_rate}%`;
    });
    total.value = jxqData.value[0].total;
  }
  if (props.modalMessage.title === "销售" && activeKey.value == 2) {
    const params = {
      dealer: store.selectObj.jxsId,
      retailer: store.selectObj.fxsId,
      start_date: dayjs(e[0]).format("YYYY-MM-DD"),
      end_date: dayjs(e[1]).format("YYYY-MM-DD"),
      page: current.value,
      page_size: 10,
      product_group: props.product_group,
    };
    const res = await getCzData(params);
    czData.value = res.data.result;
    czData.value.forEach((item) => {
      item.factory_return_rate = `${item.factory_return_rate}%`;
      item.return_rate = `${item.return_rate}%`;
    });
    total.value = czData.value[0].total;
  }
  if (props.modalMessage.title === "销售" && activeKey.value == 3) {
    const params = {
      dealer: store.selectObj.jxsId,
      retailer: store.selectObj.fxsId,
      start_date: dayjs(e[0]).format("YYYY-MM-DD"),
      end_date: dayjs(e[1]).format("YYYY-MM-DD"),
      page: current.value,
      page_size: 10,
      product_group: props.product_group,
    };
    const res = await getSalesData(params);
    dataSource.value = res.data.result;
    total.value = dataSource.value[0].total;

    const params1 = {
      dealer: store.selectObj.jxsId,
      retailer: store.selectObj.fxsId,
      start_date: dayjs(e[0]).format("YYYY-MM-DD"),
      end_date: dayjs(e[1]).format("YYYY-MM-DD"),
      page: 1,
      page_size: 9999999,
      product_group: props.product_group,
    };
    const res1 = await getSalesDataDetail(params1);
    dataSourceDetail.value = res1.data.result;
  }
  if (props.modalMessage.title === "物料数量" && activeKey.value == 1) {
    const params = {
      dealer: store.selectObj.jxsId,
      retailer: store.selectObj.fxsId,
      start_date: dayjs(e[0]).format("YYYY-MM-DD"),
      end_date: dayjs(e[1]).format("YYYY-MM-DD"),
      page: current.value,
      page_size: 10,
    };
    const res = await getPkData(params);
    pkData.value = res.data.result;
    total.value = pkData.value[0].total;
  }
  if (props.modalMessage.title === "物料数量" && activeKey.value == 2) {
    const params = {
      dealer: store.selectObj.jxsId,
      retailer: store.selectObj.fxsId,
      start_date: dayjs(e[0]).format("YYYY-MM-DD"),
      end_date: dayjs(e[1]).format("YYYY-MM-DD"),
      page: current.value,
      page_size: 10,
    };

    const res = await getPwlData(params);
    pwlData.value = res.data.result;
    total.value = pwlData.value[0].total;
  }
  if (props.modalMessage.title === "物料数量" && activeKey.value == 3) {
    const params = {
      dealer: store.selectObj.jxsId,
      retailer: store.selectObj.fxsId,
      start_date: dayjs(e[0]).format("YYYY-MM-DD"),
      end_date: dayjs(e[1]).format("YYYY-MM-DD"),
      page: current.value,
      page_size: 10,
    };

    const res = await getPwlMxData(params);
    pwlMxData.value = res.data.result;
    total.value = pwlMxData.value[0].total;
  }
  if (props.modalMessage.title === "物料数量" && activeKey.value == 4) {
    const params = {
      dealer: store.selectObj.jxsId,
      retailer: store.selectObj.fxsId,
      start_date: dayjs(e[0]).format("YYYY-MM-DD"),
      end_date: dayjs(e[1]).format("YYYY-MM-DD"),
      page: current.value,
      page_size: 10,
    };

    const res = await getPwlCsData(params);
    pwlCsData.value = res.data.result;
    total.value = pwlCsData.value[0].total;
  }
  if (props.modalMessage.title === "产品种类总数") {
    const params = {
      dealer: store.selectObj.jxsId,
      retailer: store.selectObj.fxsId,
      start_date: dayjs(e[0]).format("YYYY-MM-DD"),
      end_date: dayjs(e[1]).format("YYYY-MM-DD"),
      page: current.value,
      page_size: 10,
    };
    const res = await getProductDetail(params);
    dataSource.value = res.data.result;
    total.value = dataSource.value[0].total;
  }
  if (props.modalMessage.title === "车长拜访情况") {
    let id = getDqIdJxqId(store.dqList, store.jxqList);
    if (e) {
      const params = {
        dealer: store.selectObj.jxsId,
        retailer: store.selectObj.fxsId,
        start_date: dayjs(e[0]).format("YYYY-MM-DD"),
        end_date: dayjs(e[1]).format("YYYY-MM-DD"),
        page: current.value,
        page_size: 10,
        ...id,
      };
      const res = await getRetailerMsg(params);
      dataSource.value = res.data.result;
      total.value = dataSource.value[0].total;
    } else {
      await getRetailerMsgList();
      total.value = dataSource.value[0].total;
    }
  }
  if (props.modalMessage.title === "车长拜访情况" && activeKey.value == 4) {
    if (e) {
      const params = {
        start_date: dayjs(e[0]).format("YYYY-MM-DD"),
        end_date: dayjs(e[1]).format("YYYY-MM-DD"),
        page: 1,
        page_size: 10,
      };
      const res = await getOrderDetail(params);
      orderData.value = res.data.result;
      total.value = orderData.value[0].total;
    }
  }
  if (props.modalMessage.title === "车长拜访情况" && activeKey.value == 5) {
    if (e) {
      const params = {
        start_date: dayjs(e[0]).format("YYYY-MM-DD"),
        end_date: dayjs(e[1]).format("YYYY-MM-DD"),
        page: 1,
        page_size: 10,
      };
      oLoading.value = true;
      try {
        const res = await getDisDataCustom(params);
        oLoading.value = false;
        distributionData.value = res.data.result;
        total.value = distributionData.value[0].total;

        distributionData.value = distributionData.value.map((item) => {
          const truckLoadingMoney = Number(item.Truck_loading_money); // 假设这是金额
          const returnGoodsRate = Number(item.Return_goods_percentage); // 假设这是退货率（小数形式）

          return {
            ...item,
            Truck_loading_money: truckLoadingMoney.toFixed(2), // 格式化金额
            Return_goods_percentage: `${(returnGoodsRate * 100).toFixed(2)}%`, // 计算百分比并格式化
          };
        });

        console.log(distributionData.value, "distributionData.value");
      } catch (err) {
        console.log(err, "自定义时间err");

        message.error("数据加载失败,请稍后再试");
        oLoading.value = false;
      }
    }
  }
  if (props.modalMessage.title === "车长拜访情况" && activeKey.value == 6) {
    if (e) {
      const params = {
        start_date: dayjs(e[0]).format("YYYY-MM-DD"),
        end_date: dayjs(e[1]).format("YYYY-MM-DD"),
        page: 1,
        page_size: 10,
      };
      oLoading.value = true;
      try {
        const res = await getCmDataCustom(params);
        oLoading.value = false;
        carManData.value = res.data.result;
        total.value = carManData.value[0].total;

        carManData.value = carManData.value.map((item) => {
          const truckLoadingMoney = Number(item.Truck_loading_money); // 假设这是金额
          const entryTime = Number(item.Entry_time);
          const attendanceRate = Number(item.Attendance_rate); // 假设这是出勤率（小数形式）
          const returnFactoryRate = Number(item.Return_factory_rate); // 假设这是回厂率（小数形式）
          const shopReturnRate = Number(item.Shop_return_rate); // 假设这是退货率（小数形式）
          const shopSaleMoney =
            Number(item.Shop_sale_money) === null
              ? 0
              : Number(item.Shop_sale_money);

          return {
            ...item,
            Entry_time: entryTime.toFixed(1), // 保留一位小数
            Attendance_rate: `${(attendanceRate * 100).toFixed(2)}%`, // 计算百分比并格式化
            Return_factory_rate: `${(returnFactoryRate * 100).toFixed(2)}%`, // 计算百分比并格式化
            Truck_loading_money: truckLoadingMoney.toFixed(2), // 格式化金额
            Shop_return_rate: `${(shopReturnRate * 100).toFixed(2)}%`, // 计算百分比并格式化
            Shop_sale_money: shopSaleMoney.toFixed(2),
          };
        });

        console.log(carManData.value, "carManData.value");
      } catch (err) {
        console.log(err, "自定义时间err");

        message.error("数据加载失败,请稍后再试");
        oLoading.value = false;
      }
    }
  }
}

// 导出数据
async function ExportData() {
  if (props.modalMessage.title === "销售") {
    exportLoading.value = true;
    const params = {
      dealer: store.selectObj.jxsId,
      retailer: store.selectObj.fxsId,
      start_date: dayjs(store.selectObj.dateSection[0]).format("YYYY-MM-DD"),
      end_date: dayjs(store.selectObj.dateSection[1]).format("YYYY-MM-DD"),
      page: 1,
      page_size: 9999999,
      product_group: props.product_group,
    };
    const data = ref([]);
    const cz = ref([]);
    const jxq = ref([]);
    const res = await getSalesData(params);
    const res1 = await getCzData(params);
    const res2 = await getJxqData(params);
    const res3 = await getSalesDataDetail(params);
    exportLoading.value = false;
    data.value = res.data.result;
    cz.value = res1.data.result;
    jxq.value = res2.data.result;
    dataSourceDetail.value = res3.data.result;
    exportToExcel(
      ["经销区销货情况", "车长销货情况", "门店销售情况", "门店销售明细表"],
      [
        [
          "经销区",
          "订货单数",
          "从工厂订货额",
          "返货到工厂额",
          "工厂净订货额",
          "工厂返货率",
          "销货单数",
          "门店销售额",
          "门店返货额",
          "门店净销售额",
          "门店返货率",
        ],
        [
          "经销区",
          "车长",
          "订货单数",
          "从工厂订货额",
          "返货到工厂额",
          "工厂净订货额",
          "工厂返货率",
          "销货单数",
          "门店销售额",
          "门店返货额",
          "门店净销售额",
          "门店返货率",
        ],
        [
          "经销区",
          "车长",
          "门店名称",
          "销货单数",
          "门店销售额",
          "门店返货额",
          "门店净销售额",
          "门店返货率",
        ],
        [
          "门店",
          "经销区",
          "门店负责人",
          "日期",
          "总销货金额",
          "销货产品",
          "产品数量",
          "产品单价",
          "产品总价",
          "返货金额",
          "返货产品",
          "返货数量",
          "返货单价",
          "返货总价",
          "返货率",
        ],
      ],
      [
        jxq.value.map((item) => {
          return {
            area_name: item.area_name,
            order_num: item.sale_num,
            arrivel_money: item.arrivel_money,
            return_factory_money: item.return_factory_money,
            factory_net_sale: item.factory_net_sale,
            factory_return_rate: item.factory_return_rate,
            sale_num: item.sale_num,
            sale_money: item.sale_money,
            return_money: item.return_money,
            net_sale: item.net_sale,
            return_rate: item.return_rate,
          };
        }),
        cz.value.map((item) => {
          return {
            area_name: item.area_name,
            retailer_name: item.retailer_name,
            order_num: item.sale_num,
            arrivel_money: item.arrivel_money,
            return_factory_money: item.return_factory_money,
            factory_net_sale: item.factory_net_sale,
            factory_return_rate: item.factory_return_rate,
            sale_num: item.sale_num,
            sale_money: item.sale_money,
            return_money: item.return_money,
            net_sale: item.net_sale,
            return_rate: item.return_rate,
          };
        }),
        data.value.map((item) => {
          return {
            area_name: item.area_name,
            m_name: item.m_name,
            s_name: item.s_name,
            sale_num: item.sale_num,
            sale_money: item.sale_money,
            stock_money: item.stock_money,
            net_sale: item.net_sale,
            return_rate: item.return_rate,
          };
        }),
        dataSourceDetail.value.map((item) => {
          return {
            s_name: item.s_name,
            area_name: item.area_name,
            m_name: item.m_name,
            f_date: item.f_date,
            sale_total_money: item.sale_total_money,
            sale_product: item.sale_product,
            product_num: item.product_num,
            product_unit_price: item.product_unit_price,
            product_total_price: item.product_total_price,
            return_total_money: item.return_total_money,
            return_product: item.return_product,
            return_num: item.return_num,
            return_unit_price: item.return_unit_price,
            return_product_price: item.return_product_price,
            return_rate: item.return_rate,
          };
        }),
      ]
    );
  }
  if (props.modalMessage.title === "物料数量") {
    exportLoading.value = true;
    const params = {
      dealer: store.selectObj.jxsId,
      retailer: store.selectObj.fxsId,
      start_date: dayjs(store.selectObj.dateSection[0]).format("YYYY-MM-DD"),
      end_date: dayjs(store.selectObj.dateSection[1]).format("YYYY-MM-DD"),
      page: 1,
      page_size: 9999999,
    };
    const pk = ref([]);
    const pwl = ref([]);
    const pwlMx = ref([]);
    const pwlCs = ref([]);
    const res = await getPkData(params);
    const res1 = await getPwlData(params);
    const res2 = await getPwlMxData(params);
    const res3 = await getPwlCsData(params);
    exportLoading.value = false;
    pk.value = res.data.result;
    pwl.value = res1.data.result;
    pwlMx.value = res2.data.result;
    pwlCs.value = res3.data.result;
    exportToExcel(
      ["盘筐详情表", "盘物料详情表", "盘物料详情明细表", "盘物料次数表"],
      [
        ["仓库", "仓库负责人", "日期", "数量"],
        ["仓库", "仓库负责人", "日期", "数量"],
        ["仓库", "仓库负责人", "日期", "数量"],
        ["单车", "日期", "盘点次数"],
      ],
      [
        pk.value.map((item) => {
          return {
            warehouse_name: item.warehouse_name,
            header_name: item.header_name,
            date_range: item.date_range,
            num: item.num,
          };
        }),
        pwl.value.map((item) => {
          return {
            warehouse_name: item.warehouse_name,
            header_name: item.header_name,
            date_range: item.date_range,
            num: item.num,
          };
        }),
        pwlMx.value.map((item) => {
          return {
            warehouse_name: item.warehouse_name,
            header_name: item.header_name,
            date_range: item.date_range,
            num: item.num,
          };
        }),
        pwlCs.value.map((item) => {
          return {
            retailer_name: item.retailer_name,
            date_range: item.date_range,
            num: item.num,
          };
        }),
      ]
    );
  }
  if (props.modalMessage.title === "产品种类总数") {
    exportToExcel(
      ["产品种类汇总表"],
      [["名称", "销售额", "返货额", "返货率"]],
      [
        dataSource.value.map((item) => {
          return {
            p_name: item.p_name,
            sale_money: item.sale_money,
            stock_money: item.stock_money,
            return_rate: item.return_rate,
          };
        }),
      ]
    );
  }
  if (props.modalMessage.title === "车长拜访情况") {
    exportLoading.value = true;
    const params = {
      dealer: store.selectObj.jxsId,
      retailer: store.selectObj.fxsId,
      start_date: dayjs(store.selectObj.dateSection[0]).format("YYYY-MM-DD"),
      end_date: dayjs(store.selectObj.dateSection[1]).format("YYYY-MM-DD"),
      page: 1,
      page_size: 9999999,
    };
    const params1 = {
      start_date: dayjs(store.selectObj.dateSection[0]).format("YYYY-MM-DD"),
      end_date: dayjs(store.selectObj.dateSection[1]).format("YYYY-MM-DD"),
      page: 1,
      page_size: 9999999,
    };
    let distributionList = [];
    let carManList = [];
    const res = await getRetailerMsg(params);
    const res1 = await getOrderCollect(params1);
    const res2 = await getOrderDetail(params1);
    try {
      const res3 = await getDisData(params1);
      if (res3.data.result.length > 0) {
        distributionList = res3.data.result;
      } else {
        const res3 = await getDisDataCustom(params1);
        distributionList = res3.data.result;
      }
    } catch (err) {
      message.error("暂不支持导出自定义时间段经销区数据");
    }
    try {
      const res = await getCmData(params1);
      if (res.data.result.length > 0) {
        exportLoading.value = false;
        carManList = res.data.result;
      } else {
        const res = await getCmDataCustom(params1);
        carManList = res.data.result;
      }
    } catch (err) {
      exportLoading.value = false;
      message.error("暂不支持导出自定义时间段车长数据");
    }
    let dataSourceList = res.data.result;
    let orderCollectList = res1.data.result;
    let orderDetailList = res2.data.result;
    exportToExcel(
      ["车长表", "计划订单汇总表", "计划订单明细表", "经销区", "车长"],
      [
        [
          "经销区",
          "直接上级",
          "单车",
          "日期",
          "门店拜访次数",
          "门店签到次数",
          "门店销货次数",
          "盘前拍照次数",
          "销前盘点次数",
          "门店返货次数",
          "销后陈列照次数",
          "盘筐次数",
          "盘物料次数",
          "最后更新时间",
        ],
        [
          "年月",
          "库长",
          "总金额合计",
          "成功金额累计",
          "过期金额累计",
          "完成率",
        ],
        ["计划日期", "库长", "车长", "产品", "数量", "状态"],
        [
          "经销区名称",
          "库长数量",
          "库管数量",
          "车长总数量",
          "入职一年内车长数量",
          "老车长数量",
          "装车金额",
          "返货率",
        ],
        [
          "经销区名称",
          "车长名称",
          "入职时间",
          "出勤率",
          "装车金额",
          "返工厂率",
          "门店销货金额",
          "门店退货率",
          "重点门店数",
        ],
      ],
      [
        dataSourceList.map((item) => {
          return {
            area_name: item.area_name,
            leader_name: item.leader_name,
            retailer_name: item.retailer_name,
            record_date: item.record_date,
            store_visit_num: item.store_visit_num,
            store_register_num: item.store_register_num,
            store_sale_num: item.store_sale_num,
            pre_photo_num: item.pre_photo_num,
            check_num: item.check_num,
            return_num: item.return_num,
            post_photo_num: item.post_photo_num,
            check_basket_num: item.check_basket_num,
            check_staff_num: item.check_staff_num,
            last_update_datetime: item.last_update_datetime,
          };
        }),
        orderCollectList.map((item) => {
          return {
            order_year_month: item.order_year_month,
            place_order_man: item.place_order_man,
            Total_Plan_price: item.Total_Plan_price,
            Total_price_success: item.Total_price_success,
            Total_price_past: item.Total_price_past,
            Ratio_Status: item.Ratio_Status,
          };
        }),
        orderDetailList.map((item) => {
          if (item.Plan_order_status === "0") {
            item.Plan_order_status = "待执行";
          } else if (item.Plan_order_status === "1") {
            item.Plan_order_status = "已过期";
          } else if (item.Plan_order_status === "2") {
            item.Plan_order_status = "已完成";
          }
          return {
            plan_order_date: item.plan_order_date,
            place_order_man: item.place_order_man,
            car_man: item.car_man,
            product_name: item.product_name,
            Plan_basket_num: item.Plan_basket_num,
            Plan_order_status: item.Plan_order_status,
          };
        }),
        distributionList.map((item) => {
          const truckLoadingMoney = Number(item.Truck_loading_money); // 假设这是金额
          const returnGoodsRate = Number(item.Return_goods_percentage); // 假设这是退货率（小数形式）
          return {
            Distribution_name: item.Distribution_name,
            Store_manager_num: item.Store_manager_num,
            Store_keeper_num: item.Store_keeper_num,
            Car_man_count: item.Car_man_count,
            Car_man_count_year: item.Car_man_count_year,
            Car_man_count_old: item.Car_man_count_old,
            Truck_loading_money: truckLoadingMoney.toFixed(2),
            Return_goods_percentage: `${(returnGoodsRate * 100).toFixed(2)}%`,
          };
        }),
        carManList.map((item) => {
          const truckLoadingMoney = Number(item.Truck_loading_money); // 假设这是金额
          const entryTime = Number(item.Entry_time);
          const attendanceRate = Number(item.Attendance_rate); // 假设这是出勤率（小数形式）
          const returnFactoryRate = Number(item.Return_factory_rate); // 假设这是回厂率（小数形式）
          const shopReturnRate = Number(item.Shop_return_rate); // 假设这是退货率（小数形式）
          const shopSaleMoney =
            Number(item.Shop_sale_money) === null
              ? 0
              : Number(item.Shop_sale_money);

          return {
            Distribution_name: item.Distribution_name,
            Car_man_name: item.Car_man_name,
            Entry_time: entryTime.toFixed(1), // 保留一位小数
            Attendance_rate: `${(attendanceRate * 100).toFixed(2)}%`, // 计算百分比并格式化
            Return_factory_rate: `${(returnFactoryRate * 100).toFixed(2)}%`, // 计算百分比并格式化
            Truck_loading_money: truckLoadingMoney.toFixed(2), // 格式化金额
            Shop_return_rate: `${(shopReturnRate * 100).toFixed(2)}%`, // 计算百分比并格式化
            Shop_sale_money: shopSaleMoney.toFixed(2),
            Shop_return_rate: item.Shop_return_rate,
            Shop_keys_num: item.Shop_keys_num,
          };
        }),
      ]
    );
  }
  if (props.modalMessage.title === "门店") {
    exportLoading.value = true;
    const params = {
      dealer: store.selectObj.jxsId,
      retailer: store.selectObj.fxsId,
      start_date: dayjs(store.selectObj.dateSection[0]).format("YYYY-MM-DD"),
      end_date: dayjs(store.selectObj.dateSection[1]).format("YYYY-MM-DD"),
      product_class: store.selectObj.productClass,
      product: store.selectObj.product,
      page: 1,
      page_size: 9999999,
    };
    console.log(params, "params");
    const hz = ref([]);
    const mx = ref([]);

    const res = await getHzData(params);
    const res1 = await getMxData(params);
    exportLoading.value = false;
    // console.log("res", res);
    // console.log("res1", res1);
    hz.value = res.data.result;
    mx.value = res1.data.result;
    exportToExcel(
      ["汇总表", "明细表"],
      [
        [
          "单车",
          "门店",
          "销货次数",
          "产品/产品类别",
          "合计销货",
          "合计返货",
          "第一次销前盘点",
          "最后一次销后库存",
          "估算动销",
        ],
        [
          "单车",
          "日期时间",
          "门店",
          "产品/产品类别",
          "销货数量",
          "返货数量",
          "销后库存数量",
        ],
      ],
      [
        hz.value.map((item) => {
          return {
            retailer_name: item.retailer_name,
            store_name: item.store_name,
            sale_num: item.sale_num,
            product: item.product,
            sale_money: item.sale_money,
            return_money: item.return_money,
            first_pre_check: item.first_pre_check,
            last_post_check: item.last_post_check,
            estimate_sale: item.estimate_sale,
          };
        }),
        mx.value.map((item) => {
          return {
            retailer_name: item.retailer_name,
            finish_date: item.finish_date,
            store_name: item.store_name,
            product: item.product,
            sale_money: item.sale_money,
            return_money: item.return_money,
            post_check: item.post_check,
          };
        }),
      ]
    );
  }
  if (props.modalMessage.title === "人群覆盖率") {
    exportLoading.value = true;
    const params = {
      dealer: store.selectObj.jxsId,
      page: 1,
      page_size: 99999999,
    };
    const res = await getPesonCover(params);
    exportLoading.value = false;
    const data = res.data.result[0] || [];
    exportToExcel(
      ["人群覆盖率"],
      [["经销区", "人群覆盖率", "重点门店数量"]],
      [
        data.map((item) => {
          return {
            area_name: item.area_name,
            people_cover_rate: item.people_cover_rate,
            key_store_num: item.key_store_num,
          };
        }),
      ]
    );
  }
}

async function getRetailerMsgList() {
  let start_date;
  let end_date;
  dataSource.value = "";
  if (store.selectObj.start_date && store.selectObj.end_date) {
    start_date = store.selectObj.start_date;
    end_date = store.selectObj.end_date;
  } else if (store.selectObj.optionValue === "近30天") {
    start_date = dayjs().subtract(30, "day").format("YYYY-MM-DD");
    end_date = dayjs().format("YYYY-MM-DD");
    store.selectObj.dateSection = [dayjs(start_date), dayjs(end_date)];
  } else if (store.selectObj.optionValue === "自定义时间") {
    if (store.selectObj.dateSection === null) {
      start_date = dayjs().subtract(30, "day").format("YYYY-MM-DD");
      end_date = dayjs().format("YYYY-MM-DD");
    } else {
      start_date = dayjs(store.selectObj.dateSection[0]).format("YYYY-MM-DD");
      end_date = dayjs(store.selectObj.dateSection[1]).format("YYYY-MM-DD");
    }
  } else {
    start_date = store.selectObj.optionValue + "-01";
    end_date = dayjs(start_date).add(30, "day").format("YYYY-MM-DD");
    store.selectObj.dateSection = [dayjs(start_date), dayjs(end_date)];
  }

  oLoading.value = true;
  let id = getDqIdJxqId(store.dqList, store.jxqList);
  const params = {
    dealer: store.selectObj.jxsId,
    retailer: store.selectObj.fxsId,
    start_date: start_date,
    end_date: end_date,
    page: current.value,
    page_size: 10,
    ...id,
  };

  try {
    const res = await getRetailerMsg(params);
    dataSource.value = res.data.result;
    oLoading.value = false;
  } catch (error) {
    oLoading.value = false;
    message.error("网络错误");
  }
}

async function getRetailerList() {
  let start_date;
  let end_date;
  dataSource.value = "";
  if (store.selectObj.start_date && store.selectObj.end_date) {
    start_date = store.selectObj.start_date;
    end_date = store.selectObj.end_date;
  } else if (store.selectObj.optionValue === "近30天") {
    start_date = dayjs().subtract(30, "day").format("YYYY-MM-DD");
    end_date = dayjs().format("YYYY-MM-DD");
    store.selectObj.dateSection = [dayjs(start_date), dayjs(end_date)];
  } else if (store.selectObj.optionValue === "自定义时间") {
    if (store.selectObj.dateSection === null) {
      start_date = dayjs().subtract(30, "day").format("YYYY-MM-DD");
      end_date = dayjs().format("YYYY-MM-DD");
    } else {
      start_date = dayjs(store.selectObj.dateSection[0]).format("YYYY-MM-DD");
      end_date = dayjs(store.selectObj.dateSection[1]).format("YYYY-MM-DD");
    }
  } else {
    start_date = store.selectObj.optionValue + "-01";
    end_date = dayjs(start_date).add(30, "day").format("YYYY-MM-DD");
    store.selectObj.dateSection = [dayjs(start_date), dayjs(end_date)];
  }

  oLoading.value = true;
  let id = getDqIdJxqId(store.dqList, store.jxqList);
  const params = {
    dealer: store.selectObj.jxsId,
    retailer: store.selectObj.fxsId,
    start_date: start_date,
    end_date: end_date,
    page: current.value,
    page_size: 10,
    product_class: props.choose_product_category_code,
    product: props.choose_product_code,
    product_group: props.product_group,
    ...id,
  };
  try {
    const res = await getRetailer(params);
    dataSource.value = res.data.result;
    oLoading.value = false;
  } catch (error) {
    oLoading.value = false;
    message.error("网络错误");
  }
}

const refresh = async () => {
  await columnStore.clearColumnCache();
  await getData();
  if (dataSource.value.length > 0) {
    total.value = dataSource.value[0].total ? dataSource.value[0].total : 0;
  } else if (orderData.value.length > 0) {
    total.value = orderData.value[0].total;
  } else if (distributionData.value.length > 0) {
    total.value = distributionData.value[0].total
      ? distributionData.value[0].total
      : 0;
  } else if (carManData.value.length > 0) {
    total.value = carManData.value[0].total ? carManData.value[0].total : 0;
  } else {
    total.value = 0;
  }
  // console.log(res);
  message.success("刷新成功");
};

async function timeSelect(e) {
  emit("timeSelect", e);
  getData();
}
onMounted(() => {
  getData();
  // console.log(props);
});
</script>

<style lang="scss" scoped>
.button-group {
  margin-bottom: 10px;
  display: flex;
  gap: 15px;
}
</style>
