<template>
  <a-config-provider :locale="zhCN">
    <IndexAll v-if="show" />
  </a-config-provider>
</template>

<script setup>
import IndexAll from "./IndexAll.vue";
import zhC<PERSON> from "ant-design-vue/es/locale/zh_CN";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
dayjs.locale("zh-cn");
import { onMounted, ref } from "vue";
import { useUserInfo } from "../../store/user/userInfo";

const userStore = useUserInfo();
const show = ref(false);
onMounted(async () => {
  const userInfo = sessionStorage.getItem("userInfo");
  if (userInfo) {
    userStore.userInfo = JSON.parse(userInfo);
    show.value = true;
    return;
  }
});
</script>

<style lang="scss" scoped></style>
