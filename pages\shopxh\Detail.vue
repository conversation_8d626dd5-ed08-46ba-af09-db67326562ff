<template>
  <h2 class="title">销货明细</h2>
  <div class="top">
    <span>门店名称</span>
    <span> {{ allDetail.Sales_shop_name }} </span>
    <span>经销区</span>
    <span> {{ allDetail.Sales_aifiliation_area_name }} </span>
    <span>销货时间</span>
    <span>{{ allDetail.CreatedDate }}</span>
    <span>销货人</span>
    <span>{{ allDetail.Sales_initiatorname }}</span>
    <span>销货金额</span>
    <span>{{ allDetail.Sales_sales_money }}</span>
    <span>返货金额</span>
    <span>{{ allDetail.Sales_stock_money }}</span>
    <span>净销售额</span>
    <span>{{ allDetail.Sales_receivable_money }}</span>
  </div>
  <div class="bottom">
    <template v-for="item in imgList">
      <div class="img">
        <div class="img-alt">{{ item.alt }}</div>
        <a-image
          style="height: 10vh"
          :src="item.src"
          v-if="item.src !== null && item.src !== 'None' && item.src !== ''"
        />
        <div
          style="
            height: 10vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: rgba(0, 0, 0, 0.5);
          "
          v-else
        >
          暂无图片
        </div>
      </div>
    </template>
  </div>
  <div class="detail-container">
    <div class="product-list" v-for="item in dataList1">
      <div class="pro-img">
        <a-image
          style="height: 12vh"
          :src="item.Sales_son_good_img"
          v-if="item.Sales_son_good_img !== null"
        />
        <span v-else>暂无图片</span>
      </div>
      <div class="pro-message">
        <span>产品</span>
        <span style="grid-column: span 3 / span 3">{{
          item.Sales_son_good_name
        }}</span>
        <span>规格</span>
        <span style="grid-column: span 3 / span 3">{{
          item.Sales_son_specification
        }}</span>
        <span>数量</span>
        <span style="grid-column: span 3 / span 3">{{
          item.Sales_son_goods_number +
          item.Sales_son_select_unit +
          item.S_number_two +
          item.Sales_unit_two
        }}</span>
        <span>单价</span>
        <span>{{ item.S_unit_two_prce }}</span>
        <span>金额小计</span>
        <span>{{ item.Sales_son_total_prices }}</span>
      </div>
    </div>
  </div>
  <h2 class="title">返货明细</h2>
  <div class="detail-container">
    <div class="product-list" v-for="item in dataList2">
      <div class="pro-img">
        <a-image
          style="height: 12vh"
          :src="item.XF_goods_class_img"
          v-if="item.XF_goods_class_img !== null"
        />
        <span v-else>暂无图片</span>
      </div>
      <div class="pro-message">
        <span>产品</span>
        <span style="grid-column: span 3 / span 3">{{
          item.XF_goods_name
        }}</span>
        <span>规格</span>
        <span style="grid-column: span 3 / span 3">{{
          item.XF_goods_specification
        }}</span>
        <span>数量</span>
        <span style="grid-column: span 3 / span 3">
          {{
            item.XF_goods_number +
            item.XF_select_unit +
            item.XF_sales_number_two +
            item.XF_sales_unit_two
          }}
        </span>
        <span>单价</span>
        <span>{{ item.XF_price_two }}</span>
        <span>金额小计</span>
        <span>{{ item.XF_total_prices }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onLoad } from "@dcloudio/uni-app";
import { ref } from "vue";
import { getSalesDetail } from "../../api/shopxh";

let allDetail = ref(null);
const imgList = [];
onLoad((option) => {
  allDetail.value = JSON.parse(option.all);
  allDetail.value.imgList.forEach((item) => {
    imgList.push({
      alt: item.alt,
      src: item.src,
    });
  });
  getDetailData(option.id);
});
const dataList1 = ref([]);
const dataList2 = ref([]);

// 获取销返货明细
async function getDetailData(id) {
  const res = await getSalesDetail(id);
  try {
    dataList1.value = res.data.result[0];
    dataList2.value = res.data.result[1];
  } catch (err) {
    console.log(err);
  }
}
</script>

<style lang="scss" scoped>
.top {
  display: grid;
  border: 1px solid rgba(0, 0, 0, 0.1);
  grid-template-columns: repeat(4, minmax(0, 1fr));
  span {
    padding: 5px;
    text-align: center;
    border: 1px solid rgba(0, 0, 0, 0.1);
    &:nth-child(odd) {
      background-color: #f5f5f5;
    }
  }
}
.bottom {
  margin-top: 5px;
  display: grid;
  grid-template-columns: repeat(3, minmax(0, 1fr));
  gap: 10px;
  .img {
    border: 1px solid rgba(0, 0, 0, 0.1);
    text-align: center;
    .img-alt {
      background-color: #f5f5f5;
      padding: 3px 0;
    }
  }
}
.title {
  font-weight: bold;
  text-align: center;
  background: #1677ff;
  color: #fff;
}
.detail-container {
  font-size: 12px;
  display: grid;
  gap: 10px;
  padding: 10px;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  .product-list {
    margin-bottom: 5px;
    padding: 10px;
    border-radius: 10px;
    box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.1);
    display: grid;
    grid-template-columns: repeat(7, minmax(0, 1fr));
    .pro-img {
      grid-column: span 2 / span 2;
      height: 12vh;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 3px;
      overflow: hidden;
      span {
        font-weight: bold;
        color: rgba(0, 0, 0, 0.5);
      }
    }
    .pro-message {
      grid-column: span 5 / span 5;
      display: grid;
      grid-template-columns: repeat(4, minmax(0, 1fr));
      span {
        padding: 5px;
        text-align: center;
        border: 1px solid rgba(0, 0, 0, 0.1);
        &:nth-child(odd) {
          background-color: #f5f5f5;
        }
      }
    }
  }
}

/* 针对屏幕宽度小于等于 600px 的设备 */
@media (max-width: 600px) {
  .detail-container {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
}
/* 针对屏幕宽度大于 600px 但小于等于 1200px 的设备 */
@media (min-width: 601px) and (max-width: 1200px) {
  .detail-container {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

/* 针对屏幕宽度大于 1200px 的设备 */
@media (min-width: 1201px) {
  .detail-container {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}
</style>
