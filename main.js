import App from "./App";
import Antd from "ant-design-vue";
import "ant-design-vue/dist/reset.css";
import { createP<PERSON> } from "pinia";
import uviewPlus from "@/uni_modules/uview-plus";

import cptChartColumnLine from "./components/echarts/cpt-chart-column-line.vue";
import cptChartpie from "./components/echarts/cpt-chart-pie.vue";
import cptChartColumn from "./components/echarts/cpt-chart-column.vue";
import cptChartTdColumn from "./components/echarts/cpt-chart-td-column.vue";
import cptChartLine from "./components/echarts/cpt-chart-line.vue";
import cptDataVScrollTable from "./components/dataV/cpt-dataV-scrollTable.vue";
import cptDataVScrollList from "./components/dataV/cpt-dataV-scrollList.vue";

const optList = [
  {
    key: "cpt-chart-column-line",
    value: cptChartColumnLine,
  },
  {
    key: "cpt-chart-pie",
    value: cptC<PERSON>pie,
  },
  {
    key: "cpt-chart-column",
    value: cptChartColumn,
  },
  {
    key: "cpt-chart-td-column",
    value: cptChartTdColumn,
  },
  {
    key: "cpt-chart-line",
    value: cptChartLine,
  },
  {
    key: "cpt-dataV-scrollTable",
    value: cptDataVScrollTable,
  },
  {
    key: "cpt-dataV-scrollList",
    value: cptDataVScrollList,
  },
];

// #ifndef VUE3
import Vue from "vue";
import "./uni.promisify.adaptor";
Vue.config.productionTip = false;
App.mpType = "app";
const app = new Vue({
  ...App,
});
app.$mount();
// #endif

// #ifdef VUE3
import { createSSRApp } from "vue";

import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'



export function createApp() {
  const app = createSSRApp(App);
  optList.forEach(item => {
    app.component(item.key, item.value);
  });
  const pinia = createPinia();
  app.use(uviewPlus);
  app.use(Antd);
  app.use(pinia);
  app.use(ElementPlus)
 
  return {
    app,
  };
}
// #endif
