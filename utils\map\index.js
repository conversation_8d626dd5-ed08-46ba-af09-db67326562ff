//坐标点转换
export function transtionCopy(arr) {
  const combinedArr = arr.reduce((result, current, index) => {
    if (index % 2 === 0) {
      // 当索引为偶数时，将当前元素与下一个元素组合成一个新数组，并添加到 result 中
      result.push([current, arr[index + 1]]);
    }
    return result;
  }, []);

  const pions = combinedArr.map((item) => {
    return {
      longitude: item[0],
      latitude: item[1],
    };
  });

  return pions;
}
