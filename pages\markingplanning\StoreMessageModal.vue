<template>
  <div class="img-list">
    <div class="text-img">
      <span>门头照片</span>
      <a-image
        :width="200"
        :src="curStore.door_photo"
        v-if="curStore.door_photo"
      />
      <div class="no-img" v-else>未上传</div>
    </div>
    <div class="text-img">
      <span>现场照片1</span>
      <a-image
        :width="200"
        :src="curStore.scene_photo1"
        v-if="curStore.scene_photo1"
      />
      <div class="no-img" v-else>未上传</div>
    </div>
    <div class="text-img">
      <span>现场照片2</span>
      <a-image
        :width="200"
        :src="curStore.scene_photo2"
        v-if="curStore.scene_photo2"
      />
      <div class="no-img" v-else>未上传</div>
    </div>
  </div>
  <div class="status-list">
    <div class="status-item">
      <span>是否预谈：</span>
      <span style="color: green" v-if="curStore.is_negotiation">是</span>
      <span style="color: red" v-else>否</span>
    </div>
    <div class="status-item">
      <span>是否有意向：</span>
      <span style="color: green" v-if="curStore.is_stork">是</span>
      <span style="color: red" v-else>否</span>
    </div>
    <div class="status-item">
      <span>是否投柜：</span>
      <span style="color: green" v-if="curStore.is_drop_cabinet">是</span>
      <span style="color: red" v-else>否</span>
    </div>
    <div class="status-item">
      <span>门店状态：</span>
      <span style="color: green">{{ curStore.store_status }}</span>
    </div>
  </div>
  <div class="status-list" style="margin-top: 15px">
    <div class="status-item">
      <span>门店名称：</span>
      <span>{{ curStore.Store_name }}</span>
    </div>
    <div class="status-item">
      <span>预估销售额：</span>
      <span style="font-size: large; font-weight: bold">{{
        curStore.estimated_sales
      }}</span>
    </div>
  </div>
  <div class="text-list" style="margin-top: 15px; display: block">
    <div class="text-item">
      <span>进货情况说明：</span>
      <a-textarea
        v-model:value="curStore.condition"
        :rows="3"
        style="width: 80%"
        disabled
      />
    </div>
    <div class="text-item">
      <span>投柜情况说明：</span>
      <a-textarea
        v-model:value="curStore.drop_condition"
        :rows="3"
        style="width: 80%"
        disabled
      />
    </div>
  </div>
</template>

<script setup>
import { toRefs } from "vue";

const props = defineProps(["curStore"]);
const { curStore } = toRefs(props);
console.log("curStore", curStore);
</script>

<style lang="scss" scoped>
.img-list {
  display: flex;
  justify-content: space-between;
  .text-img {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    font-weight: bold;
    .no-img {
      width: 200px;
      height: 200px;
      border: 1px solid rgba(0, 0, 0, 0.2);
      font-size: 24px;
      color: rgba(0, 0, 0, 0.2);
      border-radius: 8px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
.status-list {
  display: flex;
  gap: 20px;
  padding: 10px;
  margin-top: 30px;
  border-radius: 5px;
  box-shadow: 0px 1px 31px rgba(0, 0, 0, 0.2);
  .status-item {
    display: flex;
    align-items: center;
  }
}
.text-list {
  padding: 10px;
  margin-top: 30px;
  border-radius: 5px;
  box-shadow: 0px 1px 31px rgba(0, 0, 0, 0.2);
  .text-item {
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
  }
}
</style>
