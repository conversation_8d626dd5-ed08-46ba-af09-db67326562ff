import request from "/api/index";

/**
 * 获取地图总览数据
 * @param {object} data - 请求参数dealer(经销商),retailer(分销商),start_date,end_date,
 * @returns
 */
export function getDataView(data) {
  return request({
    method: "post",
    url: "/bi/home",
    data,
  });
}

/**
 * 获取销售数据汇总
 * @param {object} data - 请求参数dealer(经销商),retailer(分销商),start_date,end_date,page,page_size,product_class(产品类别编号),product(产品编号)
 * @returns
 */
export function getSalesData(data) {
  return request({
    method: "post",
    url: "/bi/sale_sum2",
    data,
  });
}

/**
 * 获取车长销货数据汇总
 * @param {object} data - 请求参数dealer(经销商),retailer(分销商),start_date,end_date,page,page_size,product_class(产品类别编号),product(产品编号)
 * @returns
 */
export function getCzData(data) {
  return request({
    method: "post",
    url: "/bi/sale_retailer_sum",
    data,
  });
}

/**
 * 获取经销区销货数据汇总
 * @param {object} data - 请求参数dealer(经销商),retailer(分销商),start_date,end_date,page,page_size,product_class(产品类别编号),product(产品编号)
 * @returns
 */
export function getJxqData(data) {
  return request({
    method: "post",
    url: "/bi/sale_area_sum",
    data,
  });
}

/**
 * 获取经销区总体数据汇总(预缓存)
 * @param {object} data - start_date,end_date,page,page_size
 * @returns
 */
export function getDisData(data) {
  return request({
    method: "post",
    url: "/bi/distribution_time_range",
    data,
  });
}

/**
 * 获取经销区总体数据汇总(自定义时间)
 * @param {object} data - start_date,end_date,page,page_size
 * @returns
 */
export function getDisDataCustom(data) {
  return request({
    method: "post",
    url: "/crm/distribution_data_custom",
    data,
  });
}

/**
 * 获取车长表数据汇总(预缓存)
 * @param {object} data - start_date,end_date,page,page_size
 * @returns
 */
export function getCmData(data) {
  return request({
    method: "post",
    url: "/bi/car_man_time_range",
    data,
  });
}

/**
 * 获取车长表数据汇总(自定义时间)
 * @param {object} data - start_date,end_date,page,page_size
 * @returns
 */
export function getCmDataCustom(data) {
  return request({
    method: "post",
    url: "/crm/car_man_data_custom",
    data,
  });
}
/**
 * 获取分销商数据汇总
 * @param {object} data - 请求参数dealer(经销商),retailer(分销商),start_date,end_date,page,page_size
 * @returns
 */
export function getRetailerMsg(data) {
  return request({
    method: "post",
    url: "/bi/retailer_activity",
    data,
  });
}

/**
 * 获取销售数据明细
 * @param {object} data - 请求参数dealer(经销商),retailer(分销商),start_date,end_date,page,page_size,product_class(产品类别编号),product(产品编号)
 * @returns
 */
export function getSalesDataDetail(data) {
  return request({
    method: "post",
    url: "/bi/sale_sum_detail",
    data,
  });
}

/**
 * 获取产品种类明细
 * @param {object} data - 请求参数dealer(经销商),retailer(分销商),start_date,end_date,page,page_size
 * @returns
 */
export function getProductDetail(data) {
  return request({
    method: "post",
    url: "/bi/product_type",
    data,
  });
}

export function refreshList() {
  return request({
    method: "get",
    url: "/form/retailer_activity",
  });
}

/**
 * 获取分销商汇总表
 * @param {object} data - 请求参数dealer(经销商),retailer(分销商),start_date,end_date,page,page_size
 * @returns
 */
export function getRetailer(data) {
  return request({
    method: "post",
    url: "/bi/retailer_summary",
    data,
  });
}

/**
 * 获取门店汇总数据
 * @param {object} data - 请求参数dealer(经销商),retailer(分销商),start_date,end_date,page,page_size,product_class(产品类别编号),product(产品编号)
 * @returns
 */
export function getHzData(data) {
  return request({
    method: "post",
    url: "/bi/store_summary",
    data,
  });
}

/**
 * 获取门店明细数据
 * @param {object} data - 请求参数dealer(经销商),retailer(分销商),start_date,end_date,page,page_size,product_class(产品类别编号),product(产品编号)
 * @returns
 */
export function getMxData(data) {
  return request({
    method: "post",
    url: "/bi/store_detail",
    data,
  });
}

/**
 * 获取盘筐数据
 * @param {object} data - 请求参数dealer(经销商),retailer(分销商),start_date,end_date,page,page_size,product_class(产品类别编号),product(产品编号)
 * @returns
 */
export function getPkData(data) {
  return request({
    method: "post",
    url: "/bi/basket_check",
    data,
  });
}

/**
 * 获取盘物料数据
 * @param {object} data - 请求参数dealer(经销商),retailer(分销商),start_date,end_date,page,page_size,product_class(产品类别编号),product(产品编号)
 * @returns
 */
export function getPwlData(data) {
  return request({
    method: "post",
    url: "/bi/material_check",
    data,
  });
}

/**
 * 获取盘物料明细数据
 * @param {object} data - 请求参数dealer(经销商),retailer(分销商),start_date,end_date,page,page_size,product_class(产品类别编号),product(产品编号)
 * @returns
 */
export function getPwlMxData(data) {
  return request({
    method: "post",
    url: "/bi/material_check_detail",
    data,
  });
}

/**
 * 获取盘物料次数数据
 * @param {object} data - 请求参数dealer(经销商),retailer(分销商),start_date,end_date,page,page_size,product_class(产品类别编号),product(产品编号)
 * @returns
 */
export function getPwlCsData(data) {
  return request({
    method: "post",
    url: "/bi/material_check_num",
    data,
  });
}

// 获取人群覆盖率数据
export function getPesonCover(data) {
  return request({
    method: "post",
    url: "/bi/people_rate_detail",
    data,
  });
}

// 获取分销商计划订单汇总信息
export function getOrderCollect(data) {
  return request({
    method: "post",
    url: "/bi/order_collect",
    data,
  });
}

// 获取分销商计划订单明细信息
export function getOrderDetail(data) {
  return request({
    method: "post",
    url: "/bi/order_detail",
    data,
  });
}

export function totalAmountLoaded(data) {
  return request({
    method: "post",
    url: "/get/total_amount_loaded",
    data,
  });
}
