<template>
  <div class="multi-select-list">
    <div class="search-box" v-if="searchable">
      <input
        type="text"
        v-model="searchQuery"
        placeholder="搜索..."
        class="search-input"
        @input="resetPagination"
      />
    </div>
    <div >
      <div
       class="list-item"
      >
      
    	<span class="item-label3"></span>
        <span class="item-label2">{{items2[0].label}}</span>
    	<span class="item-label2">{{items2[0].label2}}</span>
    	<span class="item-label2">{{items2[0].label3}}</span>
    	
      </div>

    </div>
    <div class="list-container">
      <div
        v-for="item in paginatedItems"
        :key="item[valueKey]"
        class="list-item"
        :class="{ selected: isSelected(item) }"
        @click="toggleSelect(item)"
      >
        <span class="item-label3"><radio :value="item.value" :checked="item.radio == 1"  @click="clickRadio(item)"/></span>
		
        <span class="item-label">{{ item.label }}</span>
		<span class="item-label">{{ item.label2 }}</span>
		<span class="item-label">{{ item.label3 }}</span>
		
      </div>
      
      <div v-if="filteredItems.length === 0" class="empty-tip">
        没有找到匹配的选项
      </div>
    </div>

    <div class="pagination-controls" v-if="showPagination">
      <button 
        class="pagination-button"
        :disabled="currentPage === 1"
        @click="prevPage"
      >
        上一页
      </button>
      <span class="page-info">
        第 {{ currentPage }} 页 / 共 {{ totalPages }} 页
      </span>
      <button
        class="pagination-button"
        :disabled="currentPage === totalPages"
        @click="nextPage"
      >
        下一页
      </button>
    </div>

    <div class="modal-footer">
      <button class="modal-button cancel" @click="close()">取消</button>
      <button class="modal-button confirm" @click="confirm()">确认</button>
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'

const props = defineProps({
  items: {
    type: Array,
    required: true
  },
  items2: {
    type: Array,
    required: true
  },
  selectedItems: {
    type: Array,
    default: () => []
  },
  labelKey: {
    type: String,
    default: 'label'
  },
  valueKey: {
    type: String,
    default: 'value'
  },
  searchable: {
    type: Boolean,
    default: true
  },
  itemsPerPage: {
    type: Number,
    default: 10
  },
  showPagination: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['update:selectedItems', 'close', 'confirm'])

const searchQuery = ref('')
const currentPage = ref(1)
const info = ref('')

const filteredItems = computed(() => {
  if (!searchQuery.value) return props.items
  return props.items.filter(item => 
    item[props.labelKey].toLowerCase().includes(searchQuery.value.toLowerCase())
	)
})

const totalPages = computed(() => {
  return Math.ceil(filteredItems.value.length / props.itemsPerPage)
})

const paginatedItems = computed(() => {
  const start = (currentPage.value - 1) * props.itemsPerPage
  const end = start + props.itemsPerPage
  return filteredItems.value.slice(start, end)
})

const isSelected = (item) => {
  return props.selectedItems.some(
    selected => selected[props.valueKey] === item[props.valueKey]
  )
}

const toggleSelect = (item) => {
  const newSelected = [...props.selectedItems]
  const index = newSelected.findIndex(
    selected => selected[props.valueKey] === item[props.valueKey]
  )
  
  if (index > -1) {
    newSelected.splice(index, 1)
  } else {
    newSelected.push(item)
  }
  
  // emit('update:selectedItems', newSelected)
}

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
  }
}

const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
  }
}

const resetPagination = () => {
  currentPage.value = 1
  for (var i = 0; i < paginatedItems.value.length; i++) {
  	paginatedItems.value[i].radio = 0
  }
  // paginatedItems.filter(item =>
  //   item.label.toLowerCase().includes(searchQuery.value.toLowerCase())
  // 	)
}

const clickRadio = (item) => {
	
	if(item.radio == 0){
		for (var i = 0; i < paginatedItems.value.length; i++) {
			paginatedItems.value[i].radio = 0
		}
		
		info.value = item
		console.log(info.value);
		item.radio = 1
		
	}else{
		info.value =  ''
		item.radio = 0
	}
	
  console.log(item);
}
const close = () => {
  emit('update:close' )
}

const confirm = () => {
	console.log('info.value',info.value);
	if(info.value == ''){
		uni.showToast({
			title: "请选择数据后确认",
			icon: "none",
			duration: 2000,
		});
	}else{
		emit('update:confirm',info.value )
	}
  
}

</script>

<style scoped>
.multi-select-list {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.search-box {
  padding: 10px;
  border-bottom: 1px solid #eee;
}

.search-input {
  width: 100%;
  height: 100rpx;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.list-container {
  flex-grow: 1;
  overflow-y: auto;
  min-height: 200px;
}
.list-container2 {
  flex-grow: 1;
  overflow-y: auto;

}

.list-item {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  cursor: pointer;
  transition: background-color 0.2s;
}
.list-item2 {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.list-item:hover {
  background-color: #f5f5f5;
}

.list-item.selected {
  background-color: #f0f7ff;
}

.checkbox {
  margin-right: 10px;
  cursor: pointer;
}

input[type="checkbox"] {
  -webkit-appearance: checkbox;
  -moz-appearance: checkbox;
  appearance: checkbox;
}
.item-label {
  flex-grow: 1;
  width: 200px;

}

.item-label2 {
  flex-grow: 1;
  color: #0091da;
  width: 200px;

}
.item-label3 {
  flex-grow: 1;
  width: 50px;

}
.empty-tip {
  padding: 20px;
  text-align: center;
  color: #999;
}

.pagination-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 15px;
  gap: 15px;

}

.pagination-button {
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #f5f5f5;
  cursor: pointer;
}

.pagination-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  font-size: 14px;
  color: #666;
}

.modal-footer {
  padding: 15px 20px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.modal-button {
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.modal-button.cancel {
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
}

.modal-button.confirm {
  background-color: #409eff;
  color: white;
  border: 1px solid #409eff;
}

.radio-button {
  width: 18px;
  height: 18px;
  border: 2px solid #ccc;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: border-color 0.2s;
}

.radio-option.selected .radio-button {
  border-color: #409eff;
}
</style>