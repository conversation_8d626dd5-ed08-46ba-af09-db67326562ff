<template>
  <div style="max-height: 400px; width: 100%">
    <div class="scrollTable">
      <div class="scroll-header">
        <div class="items" v-for="item in headerList">{{ item.title }}</div>
      </div>
      <div class="scroll-item">
        <div class="items" v-for="item in itemValueList">
          <div v-for="it in item">{{ it }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, nextTick } from "vue";

const props = defineProps(["data"]);

const headerList = ref(JSON.parse(props.data.attribute.columns));

const itemValueList = ref(JSON.parse(props.data.cptDataForm.dataText));

//监听滚动到底部
function watchScrollBottom() {
  const scrollItem = document.querySelector(".scroll-item");
  console.log("scrollItem", scrollItem);
  // 每秒向下滚动40px
  const scrollInterval = window.setInterval(() => {
    scrollItem.scrollTop += 40;
  }, 2000);
  // 监听滚动事件
  scrollItem.addEventListener("scroll", () => {
    const scrollTop = scrollItem.scrollTop;
    const scrollHeight = scrollItem.scrollHeight;
    const clientHeight = scrollItem.clientHeight;

    if (scrollTop + clientHeight >= scrollHeight) {
      scrollItem.scrollTop = 0;
    }
  });
}

nextTick(() => {
  watchScrollBottom();
});
</script>

<style scoped lang="scss">
.scrollTable {
  height: 100%;
  background-color: #fff;
  //   border: 1px solid blue;
  background-color: rgba(0, 0, 0, 0);
  .scroll-header {
    height: 40px;
    color: #fff;
    font-weight: bold;
    background-color: rgba(160, 189, 237);
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(50px, 1fr));
    .items {
      display: flex;
      justify-content: center;
      align-items: center;

      //   border-right: 1px solid blue;
      //   &:last-child {
      //     border-right: none;
      //   }
    }
  }
  .scroll-item {
    max-height: 360px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    scroll-behavior: smooth;
    .items {
      height: 40px;
      line-height: 40px;
      text-align: center;
      display: grid;
      color: #fff;
      grid-template-columns: repeat(auto-fit, minmax(50px, 1fr));
      &:nth-child(even) {
        background-color: rgba(0, 59, 81); // 偶数元素的背景颜色
      }
      &:nth-child(odd) {
        background-color: rgba(10, 39, 50); // 奇数元素的背景颜色
      }
    }
  }
}
</style>
