<template>
  <view class="price-value-container">
    <!-- 新增按钮 -->
    <view class="add-button-container">
      <u-button type="primary" :custom-style="buttonStyle" @click="toAdd">
        <u-icon name="plus" size="20" style="margin-right: 8px"></u-icon>
        新增产品活动申请
      </u-button>
    </view>

    <!-- 搜索区域 -->
    <view class="search-container">
      <u-search
        placeholder="请输入产品名称、渠道类型或选择标签..."
        v-model="keyword"
        @search="keywordSearch"
        @clear="keywordClear"
        @custom="keywordSearch"
        :custom-style="searchStyle"
      ></u-search>
    </view>

    <!-- 列表区域 -->
    <view class="list-container">
      <view v-if="filteredList.length === 0 && !isLoading" class="empty-state">
        <u-empty text="暂无数据" mode="data"></u-empty>
      </view>

      <view v-else class="card-list">
        <view v-for="item in filteredList" :key="item.id" class="list-item">
          <!-- 卡片头部 -->
          <view class="item-header">
            <view
              class="status-badge"
              :class="getStatusClass(item.approval_status)"
            >
              {{ item.approval_status }}
            </view>
            <view class="item-time">{{ item.start_date }}</view>
          </view>

          <!-- 产品信息 -->
          <view class="item-content">
            <view class="content-row">
              <view class="label">产品名称：</view>
              <view class="value">{{ item.product_name || "未知产品" }}</view>
            </view>

            <view class="content-row">
              <view class="label">渠道类型：</view>
              <view class="value">{{ getChannelTypeText(item.type) }}</view>
            </view>

            <view class="content-row">
              <view class="label">产品价格：</view>
              <view class="value price-value">{{ item.product_price || "未设置" }}</view>
            </view>

            <!-- 渠道选择标签 -->
            <view v-if="item.channel_type !== 'company'" class="channel-tags">
              <view class="tags-container">
                <view v-for="tag in item.tag" :key="tag" class="channel-tag">
                  {{ tag }}
                </view>
              </view>
            </view>

            <view class="content-row" style="margin-top: 10px">
              <view class="label">活动开始日期：</view>
              <view class="value effective-time">{{ item.start_date }}</view>
            </view>

            <view class="content-row">
              <view class="label">活动结束日期：</view>
              <view class="value end-time">{{ item.end_date || "未设置" }}</view>
            </view>
          </view>

          <!-- 价值链详情 -->
          <view class="value-chain-info">
            <view class="chain-row">
              <view class="chain-item">
                <span class="chain-label">公司价值链</span>
                <span class="chain-value highlight">{{
                  item.product_value_chain || 0
                }}</span>
              </view>
              <view class="chain-item">
                <span class="chain-label">车长提点</span>
                <span class="chain-value">{{ item.driver_rate || 0 }}</span>
              </view>
            </view>
            <view class="chain-row">
              <view class="chain-item">
                <span class="chain-label">库长提点</span>
                <span class="chain-value">{{
                  item.warehouse_manage_rate || 0
                }}</span>
              </view>
              <view class="chain-item">
                <span class="chain-label">区长提点</span>
                <span class="chain-value">{{
                  item.product_value_chain -
                    item.driver_rate -
                    item.warehouse_manage_rate || 0
                }}</span>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载提示 -->
    <view v-if="isLoading" class="loading-tip">
      <u-loading-icon></u-loading-icon>
      <text>正在加载...</text>
    </view>

    <view v-if="limitValue === '加载完成'" class="no-more-tip">
      <text>没有更多数据了</text>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { onShow, onLoad, onReachBottom } from "@dcloudio/uni-app";
import {
  getProductActivityListApi,
  getProductActivitySearchApi,
} from "/api/workbench/productEvent/index.js";
import dayjs from "dayjs";

// 响应式数据
const filteredList = ref([]);
const isLoading = ref(false);
const keyword = ref(null);
const limit = ref(0);
const limitValue = ref("加载更多");

// 计算属性 - 响应式样式
const buttonStyle = computed(() => {
  return {
    borderRadius: "8px",
    fontSize: "16px",
    fontWeight: "600",
    padding: "12px 24px",
    background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
    border: "none",
    boxShadow: "0 4px 12px rgba(102, 126, 234, 0.3)",
    transition: "all 0.3s ease",
  };
});

const searchStyle = computed(() => {
  return {
    borderRadius: "8px",
    backgroundColor: "#f8f9fa",
  };
});

// 方法定义
const keywordSearch = async e => {
  console.log("搜索", e);
  if (e) {
    keyword.value = e;
  } else {
    keyword.value = null;
  }
  page.value = 1;
  filteredList.value = [];
  limitValue.value = "加载更多";
  isLoading.value = true;
  await getList();
};

const keywordClear = async () => {
  console.log("清空");
  keyword.value = null;
  isLoading.value = true;
  page.value = 1;
  filteredList.value = [];
  limitValue.value = "加载更多";
  await getList();
};

const page = ref(1);
const pageSize = ref(10);

// 获取列表数据
const getList = async () => {
  const userInfo = JSON.parse(sessionStorage.getItem("userInfo"));
  try {
    isLoading.value = true;
    if (keyword.value) {
      const { data } = await getProductActivitySearchApi({
        page: page.value,
        page_size: pageSize.value,
        filter: keyword.value,
        user_id: userInfo.id,
      });
      filteredList.value = [...filteredList.value, ...data.result];
    } else {
      const { data } = await getProductActivityListApi({
        page: page.value,
        page_size: pageSize.value,
        user_id: userInfo.id,
      });
      filteredList.value = [...filteredList.value, ...data.result];
    }
    if (filteredList.value.length < pageSize.value * page.value) {
      limitValue.value = "加载完成";
    }
  } catch (error) {
    console.error("获取列表数据失败", error);
    filteredList.value = [];
  } finally {
    isLoading.value = false;
  }
};

// 跳转到新增页面
const toAdd = () => {
  uni.navigateTo({
    url: "/pages/workbench/productEvent/add",
  });
};

// 获取状态样式类
const getStatusClass = status => {
  const classMap = {
    待审批: "status-pending",
    同意: "status-approved",
    驳回: "status-rejected",
  };
  return classMap[status] || "status-default";
};

// 获取渠道类型文本
const getChannelTypeText = type => {
  const typeMap = {
    company: "全公司",
    region: "多选大区",
    province: "多选省区",
    dealer: "多选经销区",
    driver: "多选车长",
  };
  return typeMap[type] || "未知类型";
};

// 格式化时间
const formatTime = timeStr => {
  if (!timeStr) return "";
  return dayjs(timeStr).format("YYYY-MM-DD HH:mm");
};

// 生命周期
onShow(async () => {
  try {
    console.log("页面显示，加载数据");
    isLoading.value = true;
    page.value = 1;
    filteredList.value = [];
    limitValue.value = "加载更多";
    await getList();
  } catch (error) {
    isLoading.value = false;
    uni.showToast({
      title: "页面初始化失败",
      icon: "error",
    });
    console.error("页面初始化失败:", error);
  }
});

// 下拉刷新
onReachBottom(async () => {
  if (limitValue.value === "加载完成" || isLoading.value) return;

  try {
    page.value += 1;
    await getList();
  } catch (error) {
    console.error("加载更多数据失败:", error);
    uni.showToast({
      title: "加载更多失败",
      icon: "error",
    });
    // 回退limit值
    limit.value -= 1;
  }
});
</script>

<style scoped>
.price-value-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 16px;
}

.add-button-container {
  margin-bottom: 16px;
  padding: 0 4px;
}

.search-container {
  margin-bottom: 16px;
}

.list-container {
  flex: 1;
}

.empty-state {
  padding: 60px 0;
  text-align: center;
}

.card-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.list-item {
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.list-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

.status-pending {
  background: #fff7e6;
  color: #fa8c16;
}

.status-approved {
  background: #f6ffed;
  color: #52c41a;
}

.status-rejected {
  background: #fff2f0;
  color: #ff4d4f;
}

.item-time {
  color: #999;
  font-size: 12px;
}

.item-content {
  margin-bottom: 12px;
}

.content-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.label {
  font-size: 14px;
  color: #666;
  min-width: 80px;
}

.value {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.highlight {
  color: #1890ff;
  font-weight: 600;
}

.effective-time {
  color: #52c41a;
  font-weight: 600;
}

.price-value {
  color: #fa8c16;
  font-weight: 600;
}

.end-time {
  color: #ff4d4f;
  font-weight: 600;
}

.channel-tags {
  margin-top: 8px;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.channel-tag {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  white-space: nowrap;
  box-shadow: 0 2px 4px rgba(102, 126, 234, 0.2);
}

.value-chain-info {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 12px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.chain-row {
  display: flex;
  justify-content: space-between;
  gap: 12px;
}

.chain-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex: 1;
  background: white;
  border-radius: 6px;
  padding: 8px 12px;
  border: 1px solid #e6e6e6;
}

.chain-label {
  font-size: 12px;
  color: #666;
  margin-right: 8px;
}

.chain-value {
  font-size: 14px;
  color: #333;
  font-weight: 600;
}

.chain-value.highlight {
  color: #1890ff;
  font-weight: 700;
}

.loading-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #666;
}

.loading-tip text {
  margin-left: 8px;
}

.no-more-tip {
  text-align: center;
  padding: 20px;
  color: #999;
  font-size: 14px;
}

/* 响应式布局 */
@media (min-width: 768px) {
  .price-value-container {
    max-width: 1200px;
    margin: 0 auto;
  }

  .card-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 16px;
  }

  .list-item {
    height: fit-content;
  }
}

@media (min-width: 1024px) {
  .card-list {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  }
}

/* 小屏幕优化 */
@media (max-width: 480px) {
  .price-value-container {
    padding: 12px;
  }

  .value-chain-info {
    flex-direction: column;
    gap: 8px;
  }

  .chain-row {
    display: flex;
    justify-content: space-between;
    gap: 8px;
  }

  .chain-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex: 1;
    background: white;
    border-radius: 6px;
    padding: 8px 10px;
    border: 1px solid #e6e6e6;
  }

  .chain-label {
    font-size: 11px;
    color: #666;
    margin-right: 6px;
  }

  .chain-value {
    font-size: 13px;
    color: #333;
    font-weight: 600;
  }

  .chain-value.highlight {
    color: #1890ff;
    font-weight: 700;
  }

  .content-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
  }

  .label {
    font-size: 14px;
    color: #666;
    min-width: auto;
    flex: 0 0 auto;
    margin-right: 8px;
  }

  .value {
    font-size: 14px;
    color: #333;
    font-weight: 500;
    flex: 1;
    text-align: right;
  }

  .channel-tags {
    margin-top: 8px;
  }

  .tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
  }

  .channel-tag {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 3px 6px;
    border-radius: 10px;
    font-size: 10px;
    font-weight: 500;
    white-space: nowrap;
    box-shadow: 0 2px 4px rgba(102, 126, 234, 0.2);
  }
}
</style>
