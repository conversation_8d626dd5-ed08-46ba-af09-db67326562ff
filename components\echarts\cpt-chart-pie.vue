<template>
  <div id="pie" style="height: 400px; width: 100%"></div>
</template>

<script setup>
import * as echarts from "echarts";
import { nextTick } from "vue";

const props = defineProps(["data"]);

const attribute = props.data.attribute;
const xyData = JSON.parse(props.data.cptDataForm.dataText);

nextTick(() => {
  // 基于准备好的dom，初始化echarts实例
  var myChart = echarts.init(document.getElementById("pie"));
  // 绘制图表
  myChart.setOption({
    color: attribute.pieColor,
    title: {
      text: attribute.chartTitle,
      subtext: attribute.subtext,
      left: attribute.titleX,
      top: attribute.titleY,
      textStyle: {
        fontSize: attribute.titleFontSize,
        color: "#fff",
      },
      // 副标题文本样式设置
      subtextStyle: { fontSize: 12, color: "#fff" },
    },
    tooltip: {
      trigger: "item",
    },
    legend: {
      show: attribute.legendShow,
      orient: attribute.orient,
      x: attribute.legendX,
      y: attribute.legendY,
      textStyle: {
        color: "#fff",
        fontSize: attribute.legendFontSize,
      },
    },
    series: [
      {
        name: attribute.chartTitle,
        type: "pie",
        roseType: attribute.roseType === "false" ? false : attribute.roseType,
        radius: [attribute.radiusInside + "%", attribute.radiusOutside + "%"],
        label: {
          position: attribute.labelPosition,
          fontSize: attribute.labelFontSize,
          color: "#fff",
        },
        itemStyle: {
          borderRadius: attribute.borderRadius,
        },
        data: xyData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(255, 0, 0, 0.5)",
          },
        },
      },
    ],
  });
});
</script>

<style></style>
