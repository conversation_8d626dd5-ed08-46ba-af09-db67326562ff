import { saveAs } from "file-saver";
import * as XLSX from "xlsx";

export const columns1 = [
  {
    title: "门店",
    dataIndex: "s_name",
    key: "s_name",
    align: "center",
  },
  {
    title: "经销区",
    dataIndex: "area_name",
    key: "area_name",
    align: "center",
  },
  {
    title: "门店负责人",
    dataIndex: "m_name",
    key: "m_name",
    align: "center",
  },
  {
    title: "日期(期间)",
    dataIndex: "date_range",
    key: "date_range",
    align: "center",
  },
  {
    title: "销货单数量",
    dataIndex: "sale_num",
    key: "sale_num",
    align: "center",
  },
  {
    title: "总销货金额",
    dataIndex: "sale_money",
    key: "sale_money",
    align: "center",
  },
  {
    title: "总返货金额",
    dataIndex: "stock_money",
    key: "stock_money",
    align: "center",
  },
  {
    title: "总返货率",
    dataIndex: "return_rate",
    key: "return_rate",
    align: "center",
  },
];

export const columns2 = [
  {
    title: "门店",
    dataIndex: "store_name",
    key: "store_name",
    align: "center",
  },
  {
    title: "门店负责人",
    dataIndex: "store_userName",
    key: "store_userName",
    align: "center",
  },
  {
    title: "日期",
    dataIndex: "store_date",
    key: "store_date",
    align: "center",
  },
  {
    title: "数量",
    dataIndex: "count",
    key: "count",
    align: "center",
  },
];
export const columns3 = [
  {
    title: "门店",
    dataIndex: "store_name",
    key: "store_name",
    align: "center",
  },
  {
    title: "门店负责人",
    dataIndex: "store_userName",
    key: "store_userName",
    align: "center",
  },
  {
    title: "日期",
    dataIndex: "store_date",
    key: "store_date",
    align: "center",
  },
  {
    title: "数量",
    dataIndex: "count",
    key: "count",
    align: "center",
  },
];

export const columns4 = [
  {
    title: "名称",
    dataIndex: "p_name",
    key: "p_name",
    align: "center",
  },
  {
    title: "销售额",
    dataIndex: "sale_money",
    key: "sale_money",
    align: "center",
  },
  {
    title: "返货额",
    dataIndex: "stock_money",
    key: "stock_money",
    align: "center",
  },
  {
    title: "返货率",
    dataIndex: "return_rate",
    key: "return_rate",
    align: "center",
  },
];

export const columns5 = [
  {
    title: "经销区",
    dataIndex: "area_name",
    key: "area_name",
    align: "center",
  },
  {
    title: "直接上级",
    dataIndex: "leader_name",
    key: "leader_name",
    align: "center",
  },
  {
    title: "单车",
    dataIndex: "retailer_name",
    key: "retailer_name",
    align: "center",
  },
  {
    title: "日期",
    dataIndex: "record_date",
    key: "record_date",
    align: "center",
  },
  {
    title: "门店拜访次数",
    dataIndex: "store_visit_num",
    key: "store_visit_num",
    align: "center",
  },
  {
    title: "门店签到次数",
    dataIndex: "store_register_num",
    key: "store_register_num",
    align: "center",
  },
  {
    title: "门店销货次数",
    dataIndex: "store_sale_num",
    key: "store_sale_num",
    align: "center",
  },
  {
    title: "盘前拍照次数",
    dataIndex: "pre_photo_num",
    key: "pre_photo_num",
    align: "center",
  },
  {
    title: "销前盘点次数",
    dataIndex: "check_num",
    key: "check_num",
    align: "center",
  },
  {
    title: "门店返货次数",
    dataIndex: "return_num",
    key: "return_num",
    align: "center",
  },
  {
    title: "销后陈列照次数",
    dataIndex: "post_photo_num",
    key: "post_photo_num",
    align: "center",
  },
  {
    title: "盘筐次数",
    dataIndex: "check_basket_num",
    key: "check_basket_num",
    align: "center",
  },
  {
    title: "盘物料次数",
    dataIndex: "check_staff_num",
    key: "check_staff_num",
    align: "center",
  },
  {
    title: "最后更新时间",
    dataIndex: "last_update_datetime",
    key: "last_update_datetime",
    align: "center",
  },
];

// 经销商(产品)
export const columns6 = [
  {
    title: "单车",
    dataIndex: "retailer_name",
    key: "retailer_name",
    align: "center",
  },
  {
    title: "销货次数",
    dataIndex: "sale_num",
    key: "sale_num",
    align: "center",
  },
  {
    title: "产品",
    dataIndex: "product",
    key: "product",
    align: "center",
  },
  {
    title: "合计到货",
    dataIndex: "arrivel_money",
    key: "arrivel_money",
    align: "center",
  },
  {
    title: "合计销货",
    dataIndex: "sale_money",
    key: "sale_money",
    align: "center",
  },
  {
    title: "合计返货",
    dataIndex: "return_money",
    key: "return_money",
    align: "center",
  },
  {
    title: "合计返工厂",
    dataIndex: "return_factory_money",
    key: "return_factory_money",
    align: "center",
  },
  {
    title: "估算动销",
    dataIndex: "estimate_sale",
    key: "estimate_sale",
    align: "center",
  },
];

// 经销商(产品类别)
export const columns14 = [
  {
    title: "单车",
    dataIndex: "retailer_name",
    key: "retailer_name",
    align: "center",
  },
  {
    title: "销货次数",
    dataIndex: "sale_num",
    key: "sale_num",
    align: "center",
  },
  {
    title: "产品类别",
    dataIndex: "product",
    key: "product",
    align: "center",
  },
  {
    title: "合计到货",
    dataIndex: "arrivel_money",
    key: "arrivel_money",
    align: "center",
  },
  {
    title: "合计销货",
    dataIndex: "sale_money",
    key: "sale_money",
    align: "center",
  },
  {
    title: "合计返货",
    dataIndex: "return_money",
    key: "return_money",
    align: "center",
  },
  {
    title: "合计返工厂",
    dataIndex: "return_factory_money",
    key: "return_factory_money",
    align: "center",
  },
  {
    title: "估算动销",
    dataIndex: "estimate_sale",
    key: "estimate_sale",
    align: "center",
  },
];

// 经销商
export const columns7 = [
  {
    title: "单车",
    dataIndex: "retailer_name",
    key: "retailer_name",
    align: "center",
  },
  {
    title: "销货次数",
    dataIndex: "sale_num",
    key: "sale_num",
    align: "center",
  },
  {
    title: "合计到货",
    dataIndex: "arrivel_money",
    key: "arrivel_money",
    align: "center",
  },
  {
    title: "合计销货",
    dataIndex: "sale_money",
    key: "sale_money",
    align: "center",
  },
  {
    title: "合计返货",
    dataIndex: "return_money",
    key: "return_money",
    align: "center",
  },
  {
    title: "合计返工厂",
    dataIndex: "return_factory_money",
    key: "return_factory_money",
    align: "center",
  },
  {
    title: "估算动销",
    dataIndex: "estimate_sale",
    key: "estimate_sale",
    align: "center",
  },
];

// 门店1（产品）
export const columns8 = [
  {
    title: "单车",
    dataIndex: "retailer_name",
    key: "retailer_name",
    align: "center",
  },
  {
    title: "门店",
    dataIndex: "store_name",
    key: "store_name",
    align: "center",
  },
  {
    title: "销货次数",
    dataIndex: "sale_num",
    key: "sale_num",
    align: "center",
  },
  {
    title: "产品",
    dataIndex: "product",
    key: "product",
    align: "center",
  },
  {
    title: "合计销货",
    dataIndex: "sale_money",
    key: "sale_money",
    align: "center",
  },
  {
    title: "合计返货",
    dataIndex: "return_money",
    key: "return_money",
    align: "center",
  },
  {
    title: "第一次销前盘点",
    dataIndex: "first_pre_check",
    key: "first_pre_check",
    align: "center",
  },
  {
    title: "最后一次销后库存",
    dataIndex: "last_post_check",
    key: "last_post_check",
    align: "center",
  },
  {
    title: "估算动销",
    dataIndex: "estimate_sale",
    key: "estimate_sale",
    align: "center",
  },
];

// 门店1（产品类别）
export const columns12 = [
  {
    title: "单车",
    dataIndex: "retailer_name",
    key: "retailer_name",
    align: "center",
  },
  {
    title: "门店",
    dataIndex: "store_name",
    key: "store_name",
    align: "center",
  },
  {
    title: "销货次数",
    dataIndex: "sale_num",
    key: "sale_num",
    align: "center",
  },
  {
    title: "产品类别",
    dataIndex: "product",
    key: "product",
    align: "center",
  },
  {
    title: "合计销货",
    dataIndex: "sale_money",
    key: "sale_money",
    align: "center",
  },
  {
    title: "合计返货",
    dataIndex: "return_money",
    key: "return_money",
    align: "center",
  },
  {
    title: "第一次销前盘点",
    dataIndex: "first_pre_check",
    key: "first_pre_check",
    align: "center",
  },
  {
    title: "最后一次销后库存",
    dataIndex: "last_post_check",
    key: "last_post_check",
    align: "center",
  },
  {
    title: "估算动销",
    dataIndex: "estimate_sale",
    key: "estimate_sale",
    align: "center",
  },
];

// 门店1
export const columns9 = [
  {
    title: "单车",
    dataIndex: "retailer_name",
    key: "retailer_name",
    align: "center",
  },
  {
    title: "门店",
    dataIndex: "store_name",
    key: "store_name",
    align: "center",
  },
  {
    title: "销货次数",
    dataIndex: "sale_num",
    key: "sale_num",
    align: "center",
  },
  {
    title: "合计销货",
    dataIndex: "sale_money",
    key: "sale_money",
    align: "center",
  },
  {
    title: "合计返货",
    dataIndex: "return_money",
    key: "return_money",
    align: "center",
  },
  {
    title: "第一次销前盘点",
    dataIndex: "first_pre_check",
    key: "first_pre_check",
    align: "center",
  },
  {
    title: "最后一次销后库存",
    dataIndex: "last_post_check",
    key: "last_post_check",
    align: "center",
  },
  {
    title: "估算动销",
    dataIndex: "estimate_sale",
    key: "estimate_sale",
    align: "center",
  },
];

// 门店2（产品）
export const columns10 = [
  {
    title: "单车",
    dataIndex: "retailer_name",
    key: "retailer_name",
    align: "center",
  },
  {
    title: "日期时间",
    dataIndex: "finish_date",
    key: "finish_date",
    align: "center",
  },
  {
    title: "门店",
    dataIndex: "store_name",
    key: "store_name",
    align: "center",
  },
  {
    title: "产品",
    dataIndex: "product",
    key: "product",
    align: "center",
  },
  {
    title: "销售数量",
    dataIndex: "sale_num",
    key: "sale_num",
    align: "center",
  },
  {
    title: "销售金额",
    dataIndex: "sale_money",
    key: "sale_money",
    align: "center",
  },
  {
    title: "返货数量",
    dataIndex: "return_num",
    key: "return_num",
    align: "center",
  },
  {
    title: "返货金额",
    dataIndex: "return_money",
    key: "return_money",
    align: "center",
  },
  {
    title: "销前盘点数量",
    dataIndex: "pre_check_num",
    key: "pre_check_num",
    align: "center",
  },
  {
    title: "销后库存数量",
    dataIndex: "post_check_num",
    key: "post_check_num",
    align: "center",
  },
];

// 门店2（产品类别）
export const columns13 = [
  {
    title: "单车",
    dataIndex: "retailer_name",
    key: "retailer_name",
    align: "center",
  },
  {
    title: "日期时间",
    dataIndex: "finish_date",
    key: "finish_date",
    align: "center",
  },
  {
    title: "门店",
    dataIndex: "store_name",
    key: "store_name",
    align: "center",
  },
  {
    title: "产品类别",
    dataIndex: "product",
    key: "product",
    align: "center",
  },
  {
    title: "销售数量",
    dataIndex: "sale_num",
    key: "sale_num",
    align: "center",
  },
  {
    title: "销售金额",
    dataIndex: "sale_money",
    key: "sale_money",
    align: "center",
  },
  {
    title: "返货数量",
    dataIndex: "return_num",
    key: "return_num",
    align: "center",
  },
  {
    title: "返货金额",
    dataIndex: "return_money",
    key: "return_money",
    align: "center",
  },
  {
    title: "销前盘点数量",
    dataIndex: "pre_check_num",
    key: "pre_check_num",
    align: "center",
  },
  {
    title: "销后库存数量",
    dataIndex: "post_check_num",
    key: "post_check_num",
    align: "center",
  },
];

// 门店2
export const columns11 = [
  {
    title: "单车",
    dataIndex: "retailer_name",
    key: "retailer_name",
    align: "center",
  },
  {
    title: "日期时间",
    dataIndex: "finish_date",
    key: "finish_date",
    align: "center",
  },
  {
    title: "门店",
    dataIndex: "store_name",
    key: "store_name",
    align: "center",
  },
  {
    title: "销售数量",
    dataIndex: "sale_num",
    key: "sale_num",
    align: "center",
  },
  {
    title: "销售金额",
    dataIndex: "sale_money",
    key: "sale_money",
    align: "center",
  },
  {
    title: "返货数量",
    dataIndex: "return_num",
    key: "return_num",
    align: "center",
  },
  {
    title: "返货金额",
    dataIndex: "return_money",
    key: "return_money",
    align: "center",
  },
  {
    title: "销前盘点数量",
    dataIndex: "pre_check_num",
    key: "pre_check_num",
    align: "center",
  },
  {
    title: "销后库存数量",
    dataIndex: "post_check_num",
    key: "post_check_num",
    align: "center",
  },
];

// 经销区销货情况
export const columns15 = [
  {
    title: "经销区",
    dataIndex: "area_name",
    key: "area_name",
    align: "center",
  },
  {
    title: "订货单数",
    dataIndex: "order_num",
    key: "order_num",
    align: "center",
  },
  {
    title: "从工厂订货额",
    dataIndex: "arrivel_money",
    key: "arrivel_money",
    align: "center",
  },
  {
    title: "返货到工厂额",
    dataIndex: "return_factory_money",
    key: "return_factory_money",
    align: "center",
  },
  {
    title: "工厂净订货额",
    dataIndex: "factory_net_sale",
    key: "factory_net_sale",
    align: "center",
  },
  {
    title: "工厂返货率",
    dataIndex: "factory_return_rate",
    key: "factory_return_rate",
    helpText: "返货到工厂额/从工厂订货额",
    align: "center",
  },
  {
    title: "销货单数",
    dataIndex: "sale_num",
    key: "sale_num",
    align: "center",
  },
  {
    title: "门店销售额",
    dataIndex: "sale_money",
    key: "sale_money",
    align: "center",
  },
  {
    title: "门店返货额",
    dataIndex: "return_money",
    key: "return_money",
    align: "center",
  },
  {
    title: "门店净销售额",
    dataIndex: "net_sale",
    key: "net_sale",
    align: "center",
  },
  {
    title: "门店返货率",
    dataIndex: "return_rate",
    key: "return_rate",
    helpText: "门店返货额/门店销售额",
    align: "center",
  },
];

// 经销区销货情况(产品)
export const columns16 = [
  {
    title: "经销区",
    dataIndex: "area_name",
    key: "area_name",
    align: "center",
  },
  {
    title: "订货单数",
    dataIndex: "order_num",
    key: "order_num",
    align: "center",
  },
  {
    title: "从工厂订货额",
    dataIndex: "arrivel_money",
    key: "arrivel_money",
    align: "center",
  },
  {
    title: "返货到工厂额",
    dataIndex: "return_factory_money",
    key: "return_factory_money",
    align: "center",
  },
  {
    title: "工厂净订货额",
    dataIndex: "factory_net_sale",
    key: "factory_net_sale",
    align: "center",
  },
  {
    title: "工厂返货率",
    dataIndex: "factory_return_rate",
    key: "factory_return_rate",
    helpText: "返货到工厂额/从工厂订货额",
    align: "center",
  },
  {
    title: "销货单数",
    dataIndex: "sale_num",
    key: "sale_num",
    align: "center",
  },
  {
    title: "门店销售额",
    dataIndex: "sale_money",
    key: "sale_money",
    align: "center",
  },
  {
    title: "门店返货额",
    dataIndex: "return_money",
    key: "return_money",
    align: "center",
  },
  {
    title: "门店净销售额",
    dataIndex: "net_sale",
    key: "net_sale",
    align: "center",
  },
  {
    title: "门店返货率",
    dataIndex: "return_rate",
    key: "return_rate",
    helpText: "门店返货额/门店销售额",
    align: "center",
  },
];

// 经销区销货情况(产品类别)
export const columns17 = [
  {
    title: "经销区",
    dataIndex: "area_name",
    key: "area_name",
    align: "center",
  },
  {
    title: "订货单数",
    dataIndex: "order_num",
    key: "order_num",
    align: "center",
  },
  {
    title: "从工厂订货额",
    dataIndex: "arrivel_money",
    key: "arrivel_money",
    align: "center",
  },
  {
    title: "返货到工厂额",
    dataIndex: "return_factory_money",
    key: "return_factory_money",
    align: "center",
  },
  {
    title: "工厂净订货额",
    dataIndex: "factory_net_sale",
    key: "factory_net_sale",
    align: "center",
  },
  {
    title: "工厂返货率",
    dataIndex: "factory_return_rate",
    key: "factory_return_rate",
    helpText: "返货到工厂额/从工厂订货额",
    align: "center",
  },
  {
    title: "销货单数",
    dataIndex: "sale_num",
    key: "sale_num",
    align: "center",
  },
  {
    title: "门店销售额",
    dataIndex: "sale_money",
    key: "sale_money",
    align: "center",
  },
  {
    title: "门店返货额",
    dataIndex: "return_money",
    key: "return_money",
    align: "center",
  },
  {
    title: "门店净销售额",
    dataIndex: "net_sale",
    key: "net_sale",
    align: "center",
  },
  {
    title: "门店返货率",
    dataIndex: "return_rate",
    key: "return_rate",
    helpText: "门店返货额/门店销售额",
    align: "center",
  },
];

// 车长销货情况
export const columns18 = [
  {
    title: "经销区",
    dataIndex: "area_name",
    key: "area_name",
    align: "center",
  },
  {
    title: "车长",
    dataIndex: "retailer_name",
    key: "retailer_name",
    align: "center",
  },
  {
    title: "订货单数",
    dataIndex: "order_num",
    key: "order_num",
    align: "center",
  },
  {
    title: "从工厂订货额",
    dataIndex: "arrivel_money",
    key: "arrivel_money",
    align: "center",
  },
  {
    title: "返货到工厂额",
    dataIndex: "return_factory_money",
    key: "return_factory_money",
    align: "center",
  },
  {
    title: "工厂净订货额",
    dataIndex: "factory_net_sale",
    key: "factory_net_sale",
    align: "center",
  },
  {
    title: "工厂返货率",
    dataIndex: "factory_return_rate",
    key: "factory_return_rate",
    helpText: "返货到工厂额/从工厂订货额",
    align: "center",
  },
  {
    title: "销货单数",
    dataIndex: "sale_num",
    key: "sale_num",
    align: "center",
  },
  {
    title: "门店销售额",
    dataIndex: "sale_money",
    key: "sale_money",
    align: "center",
  },
  {
    title: "门店返货额",
    dataIndex: "return_money",
    key: "return_money",
    align: "center",
  },
  {
    title: "门店净销售额",
    dataIndex: "net_sale",
    key: "net_sale",
    align: "center",
  },
  {
    title: "门店返货率",
    dataIndex: "return_rate",
    key: "return_rate",
    helpText: "门店返货额/门店销售额",
    align: "center",
  },
];

// 车长销货情况(产品)
export const columns19 = [
  {
    title: "经销区",
    dataIndex: "area_name",
    key: "area_name",
    align: "center",
  },
  {
    title: "车长",
    dataIndex: "retailer_name",
    key: "retailer_name",
    align: "center",
  },
  {
    title: "订货单数",
    dataIndex: "order_num",
    key: "order_num",
    align: "center",
  },
  {
    title: "从工厂订货额",
    dataIndex: "arrivel_money",
    key: "arrivel_money",
    align: "center",
  },
  {
    title: "返货到工厂额",
    dataIndex: "return_factory_money",
    key: "return_factory_money",
    align: "center",
  },
  {
    title: "工厂净订货额",
    dataIndex: "factory_net_sale",
    key: "factory_net_sale",
    align: "center",
  },
  {
    title: "工厂返货率",
    dataIndex: "factory_return_rate",
    key: "factory_return_rate",
    helpText: "返货到工厂额/从工厂订货额",
    align: "center",
  },
  {
    title: "销货单数",
    dataIndex: "sale_num",
    key: "sale_num",
    align: "center",
  },
  {
    title: "门店销售额",
    dataIndex: "sale_money",
    key: "sale_money",
    align: "center",
  },
  {
    title: "门店返货额",
    dataIndex: "return_money",
    key: "return_money",
    align: "center",
  },
  {
    title: "门店净销售额",
    dataIndex: "net_sale",
    key: "net_sale",
    align: "center",
  },
  {
    title: "门店返货率",
    dataIndex: "return_rate",
    key: "return_rate",
    helpText: "门店返货额/门店销售额",
    align: "center",
  },
];

// 车长销货情况(产品类别)
export const columns20 = [
  {
    title: "经销区",
    dataIndex: "area_name",
    key: "area_name",
    align: "center",
  },
  {
    title: "车长",
    dataIndex: "retailer_name",
    key: "retailer_name",
    align: "center",
  },
  {
    title: "订货单数",
    dataIndex: "order_num",
    key: "order_num",
    align: "center",
  },
  {
    title: "从工厂订货额",
    dataIndex: "arrivel_money",
    key: "arrivel_money",
    align: "center",
  },
  {
    title: "返货到工厂额",
    dataIndex: "return_factory_money",
    key: "return_factory_money",
    align: "center",
  },
  {
    title: "工厂净订货额",
    dataIndex: "factory_net_sale",
    key: "factory_net_sale",
    align: "center",
  },
  {
    title: "工厂返货率",
    dataIndex: "factory_return_rate",
    key: "factory_return_rate",
    helpText: "返货到工厂额/从工厂订货额",
    align: "center",
  },
  {
    title: "销货单数",
    dataIndex: "sale_num",
    key: "sale_num",
    align: "center",
  },
  {
    title: "门店销售额",
    dataIndex: "sale_money",
    key: "sale_money",
    align: "center",
  },
  {
    title: "门店返货额",
    dataIndex: "return_money",
    key: "return_money",
    align: "center",
  },
  {
    title: "门店净销售额",
    dataIndex: "net_sale",
    key: "net_sale",
    align: "center",
  },
  {
    title: "门店返货率",
    dataIndex: "return_rate",
    key: "return_rate",
    helpText: "门店返货额/门店销售额",
    align: "center",
  },
];

// 门店销售情况
export const columns21 = [
  {
    title: "经销区",
    dataIndex: "area_name",
    key: "area_name",
    align: "center",
  },
  {
    title: "车长",
    dataIndex: "m_name",
    key: "m_name",
    align: "center",
  },
  {
    title: "门店名称",
    dataIndex: "s_name",
    key: "s_name",
    align: "center",
  },
  {
    title: "销货单数",
    dataIndex: "sale_num",
    key: "sale_num",
    align: "center",
  },
  {
    title: "门店销售额",
    dataIndex: "sale_money",
    key: "sale_money",
    align: "center",
  },
  {
    title: "门店返货额",
    dataIndex: "stock_money",
    key: "stock_money",
    align: "center",
  },
  {
    title: "门店净销售额",
    dataIndex: "net_sale",
    key: "net_sale",
    align: "center",
  },
  {
    title: "门店返货率",
    dataIndex: "return_rate",
    key: "return_rate",
    align: "center",
  },
];

// 门店销售情况(产品)
export const columns22 = [
  {
    title: "经销区",
    dataIndex: "area_name",
    key: "area_name",
    align: "center",
  },
  {
    title: "车长",
    dataIndex: "m_name",
    key: "m_name",
    align: "center",
  },
  {
    title: "门店名称",
    dataIndex: "s_name",
    key: "s_name",
    align: "center",
  },
  {
    title: "销货单数",
    dataIndex: "sale_num",
    key: "sale_num",
    align: "center",
  },
  {
    title: "门店销售额",
    dataIndex: "sale_money",
    key: "sale_money",
    align: "center",
  },
  {
    title: "门店返货额",
    dataIndex: "stock_money",
    key: "stock_money",
    align: "center",
  },
  {
    title: "门店净销售额",
    dataIndex: "net_sale",
    key: "net_sale",
    align: "center",
  },
  {
    title: "门店返货率",
    dataIndex: "return_rate",
    key: "return_rate",
    align: "center",
  },
];

// 门店销售情况(产品类别)
export const columns23 = [
  {
    title: "经销区",
    dataIndex: "area_name",
    key: "area_name",
    align: "center",
  },
  {
    title: "车长",
    dataIndex: "m_name",
    key: "m_name",
    align: "center",
  },
  {
    title: "门店名称",
    dataIndex: "s_name",
    key: "s_name",
    align: "center",
  },
  {
    title: "销货单数",
    dataIndex: "sale_num",
    key: "sale_num",
    align: "center",
  },
  {
    title: "门店销售额",
    dataIndex: "sale_money",
    key: "sale_money",
    align: "center",
  },
  {
    title: "门店返货额",
    dataIndex: "stock_money",
    key: "stock_money",
    align: "center",
  },
  {
    title: "门店净销售额",
    dataIndex: "net_sale",
    key: "net_sale",
    align: "center",
  },
  {
    title: "门店返货率",
    dataIndex: "return_rate",
    key: "return_rate",
    align: "center",
  },
];

// 盘筐数据
export const columns24 = [
  {
    title: "仓库",
    dataIndex: "warehouse_name",
    key: "warehouse_name",
    align: "center",
  },
  {
    title: "仓库负责人",
    dataIndex: "header_name",
    key: "header_name",
    align: "center",
  },
  {
    title: "日期",
    dataIndex: "date_range",
    key: "date_range",
    align: "center",
  },
  {
    title: "数量",
    dataIndex: "num",
    key: "num",
    align: "center",
  },
];

// 盘物料数据
export const columns25 = [
  {
    title: "仓库",
    dataIndex: "warehouse_name",
    key: "warehouse_name",
    align: "center",
  },
  {
    title: "仓库负责人",
    dataIndex: "header_name",
    key: "header_name",
    align: "center",
  },
  {
    title: "日期",
    dataIndex: "date_range",
    key: "date_range",
    align: "center",
  },
  {
    title: "数量",
    dataIndex: "num",
    key: "num",
    align: "center",
  },
];

// 盘物料明细数据
export const columns26 = [
  {
    title: "仓库",
    dataIndex: "warehouse_name",
    key: "warehouse_name",
    align: "center",
  },
  {
    title: "仓库负责人",
    dataIndex: "header_name",
    key: "header_name",
    align: "center",
  },
  {
    title: "物料名称",
    dataIndex: "material_name",
    key: "material_name",
    align: "center",
  },
  {
    title: "日期",
    dataIndex: "date_range",
    key: "date_range",
    align: "center",
  },
  {
    title: "数量",
    dataIndex: "num",
    key: "num",
    align: "center",
  },
];

// 物料盘点次数
export const columns27 = [
  {
    title: "单车",
    dataIndex: "retailer_name",
    key: "retailer_name",
    align: "center",
  },
  {
    title: "日期",
    dataIndex: "date_range",
    key: "date_range",
    align: "center",
  },
  {
    title: "盘点次数",
    dataIndex: "num",
    key: "num",
    align: "center",
  },
];

// 人群覆盖率
export const columns28 = [
  {
    title: "经销区",
    dataIndex: "area_name",
    key: "area_name",
    align: "center",
  },
  {
    title: "人群覆盖率",
    dataIndex: "people_cover_rate",
    key: "people_cover_rate",
    align: "center",
  },
  {
    title: "重点门店数量",
    dataIndex: "key_store_num",
    key: "key_store_num",
    align: "center",
  },
];

// 计划订单汇总
export const columns29 = [
  {
    title: "年月",
    dataIndex: "order_year_month",
    key: "order_year_month",
    align: "center",
  },
  {
    title: "库长",
    dataIndex: "place_order_man",
    key: "place_order_man",
    align: "center",
  },
  {
    title: "总金额合计",
    dataIndex: "Total_Plan_price",
    key: "Total_Plan_price",
    align: "center",
  },
  {
    title: "成功金额累计",
    dataIndex: "Total_price_success",
    key: "Total_price_success",
    align: "center",
  },
  {
    title: "过期金额累计",
    dataIndex: "Total_price_past",
    key: "Total_price_past",
    align: "center",
  },
  {
    title: "完成率",
    dataIndex: "Ratio_Status",
    key: "Ratio_Status",
    align: "center",
  },
];

// 计划订单明细
export const columns30 = [
  {
    title: "计划日期",
    dataIndex: "plan_order_date",
    key: "plan_order_date",
    align: "center",
  },
  {
    title: "库长",
    dataIndex: "place_order_man",
    key: "place_order_man",
    align: "center",
  },
  {
    title: "车长",
    dataIndex: "car_man",
    key: "car_man",
    align: "center",
  },
  {
    title: "产品",
    dataIndex: "product_name",
    key: "product_name",
    align: "center",
  },
  {
    title: "数量",
    dataIndex: "Plan_basket_num",
    key: "Plan_basket_num",
    align: "center",
  },
  {
    title: "状态",
    dataIndex: "Plan_order_status",
    key: "Plan_order_status",
    align: "center",
  },
];

// 经销区
export const columns31 = [
  {
    title: "经销区名称",
    dataIndex: "Distribution_name",
    key: "Distribution_name",
    align: "center",
  },
  {
    title: "库长数量",
    dataIndex: "Store_manager_num",
    key: "Store_manager_num",
    align: "center",
  },
  {
    title: "库管数量",
    dataIndex: "Store_keeper_num",
    key: "Store_keeper_num",
    align: "center",
  },
  {
    title: "车长总数量",
    dataIndex: "Car_man_count",
    key: "Car_man_count",
    align: "center",
  },
  {
    title: "入职一年内车长数量",
    dataIndex: "Car_man_count_year",
    key: "Car_man_count_year",
    align: "center",
  },
  {
    title: "老车长数量",
    dataIndex: "Car_man_count_old",
    key: "Car_man_count_old",
    align: "center",
  },
  {
    title: "装车金额",
    dataIndex: "Truck_loading_money",
    key: "Truck_loading_money",
    helpText:
      "指定时间段内（近30天，或各月份），《发货单主表》单据类型为 正常发货 的金额合计",
    align: "center",
  },
  {
    title: "返货率",
    dataIndex: "Return_goods_percentage",
    key: "Return_goods_percentage",
    helpText:
      "指定时间段内（近30天，或各月份），《发货单主表》单据类型为 正常返货 金额合计/《发货单主表》单据类型为 正常发货 的金额合计",
    align: "center",
  },
];

// 车长
export const columns32 = [
  {
    title: "经销区名称",
    dataIndex: "Distribution_name",
    key: "Distribution_name",
    align: "center",
  },
  {
    title: "车长名称",
    dataIndex: "Car_man_name",
    key: "Car_man_name",
    align: "center",
  },
  {
    title: "入职时间",
    dataIndex: "Entry_time",
    key: "Entry_time",
    helpText: "当前日期-入职日期差的天数/365",
    align: "center",
  },
  {
    title: "出勤率",
    dataIndex: "Attendance_rate",
    key: "Attendance_rate",
    helpText:
      "指定时间段内近30天，或各月份），（门店拜访次数>5的天数）/时间范围天数",
    align: "center",
  },
  {
    title: "装车金额",
    dataIndex: "Truck_loading_money",
    key: "Truck_loading_money",
    helpText:
      "指定时间段内（近30天，或各月份），《发货单主表》单据类型为 正常发货 的金额合计",
    align: "center",
  },
  {
    title: "返工厂率",
    dataIndex: "Return_factory_rate",
    key: "Return_factory_rate",
    helpText:
      "指定时间段内（近30天，或各月份），《发货单主表》单据类型为 正常返货 金额合计/《发货单主表》单据类型为 正常发货 的金额合计",
    align: "center",
  },
  {
    title: "门店销货金额",
    dataIndex: "Shop_sale_money",
    key: "Shop_sale_money",
    helpText: "指定时间段内（近30天，或各月份），《销货单主表》 的销货金额合计",
    align: "center",
  },
  {
    title: "门店退货率",
    dataIndex: "Shop_return_rate",
    key: "Shop_return_rate",
    helpText:
      "指定时间段内（近30天，或各月份），《销货单主表》 的合计金额合计/《销货单主表》 的销货金额合计",
    align: "center",
  },
  {
    title: "重点门店数",
    dataIndex: "Shop_keys_num",
    key: "Shop_keys_num",
    helpText:
      "指定时间段内（近30天，或各月份），《销货单主表》 的销货金额合计>5000的门店数量",
    align: "center",
  },
];
/**
 * 导出Excel
 * @param {string[]} fileName -导出文件名称
 * @param {string[][]} headers - 表头
 * @param {object[][]} data - 数据
 */
export const exportToExcel = (fileName, headers, data) => {
  const wb = XLSX.utils.book_new();
  fileName.forEach((item, index) => {
    const ws = XLSX.utils.aoa_to_sheet([
      headers[index],
      ...data[index].map((item) => Object.values(item)),
    ]);
    XLSX.utils.book_append_sheet(wb, ws, item);
  });

  const wbout = XLSX.write(wb, { bookType: "xlsx", type: "binary" });

  function s2ab(s) {
    const buf = new ArrayBuffer(s.length);
    const view = new Uint8Array(buf);
    for (let i = 0; i !== s.length; ++i) view[i] = s.charCodeAt(i) & 0xff;
    return buf;
  }

  saveAs(
    new Blob([s2ab(wbout)], { type: "application/octet-stream" }),
    `${fileName[0]}.xlsx`
  );
};
