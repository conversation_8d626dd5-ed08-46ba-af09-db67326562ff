<template>
  <div id="cpt-chart-line" style="height: 400px; width: 100%"></div>
</template>

<script setup>
import * as echarts from "echarts";
import { nextTick } from "vue";

const props = defineProps(["data"]);

const attribute = props.data.attribute;
const xyData = JSON.parse(props.data.cptDataForm.dataText);
nextTick(() => {
  // 基于准备好的dom，初始化echarts实例
  var myChart = echarts.init(document.getElementById("cpt-chart-line"));
  // 绘制图表
  myChart.setOption({
    color: attribute.lineColor,
    title: {
      text: attribute.title,
      subtext: attribute.subtext,
      left: attribute.titleLeft,
      top: attribute.titleTop,
      textStyle: {
        color: attribute.titleTextColor,
      },
      subtextStyle: { fontSize: 12, color: attribute.subtextColor },
    },
    grid: {
      x: 10,
      y: 30,
      x2: 10,
      y2: 10,
      containLabel: true,
    },
    xAxis: {
      type: "category",
      data: xyData.xData.split(","),
      axisLabel: {
        show: attribute.xLabelShow,
        color: attribute.xLabelColor,
      },
      axisLine: {
        show: attribute.xLineShow,
        lineStyle: {
          color: attribute.xLineColor,
        },
      },
      axisTick: {
        //x轴刻度线
        show: attribute.xTickShow,
      },
    },
    yAxis: {
      type: "value",
      axisLabel: {
        show: attribute.yLabelShow,
        color: attribute.yLabelColor,
      },
      axisLine: {
        show: attribute.yLineShow,
        lineStyle: {
          color: attribute.yLineColor,
        },
      },
      axisTick: {
        //y轴刻度线
        show: attribute.yTickShow,
      },
      splitLine: {
        //网格线
        show: attribute.yGridLineShow,
      },
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross",
      },
    },
    series: [
      {
        data: xyData.yData.split(","),
        type: "line",
        smooth: attribute.smooth,
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: attribute.areaColor1,
            },
            {
              offset: 0.34,
              color: attribute.areaColor2,
            },
            {
              offset: 1,
              color: attribute.areaColor3,
            },
          ]),
        },
      },
    ],
  });
});
</script>

<style></style>
