<template>
  <div class="main-container">
    <div v-for="item in active_list" :key="item.id">
      <div class="cut-off">
        {{ item.name }}
      </div>
      <div class="top">
        <div
          class="top-list"
          v-for="(list, index) in item.children"
          @click="goToDetail(list)"
        >
          <img :src="choose_icon(index)" alt="" />
          {{ list.name }}
          <p class="list_detail">
            {{ list.explane }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { getTargetClassify, getTargetList } from "/api/FormOther/index";
import { ref, nextTick } from "vue";
const goToDetail = item => {
  console.log(item)
  if(item.address){
    uni.navigateTo({
      url: item.address,
    });
  }else{
    uni.navigateTo({
      url: `../FormOther/detail2?title=${item.name}`,
    });
  }

};

const getClassify = async () => {
  let res = await getTargetClassify();
  let res2 = await getTargetList();
  active_list.value = res.data.result.map((item, index) => ({
    name: item.target_name,
    id: index
  }));
  // res.data.result.forEach((item, index) => {
  //   active_list.value[index].name = item.target_name;
  //   active_list.value[index].id = index;
  // });

  // 将列表和列表解释添加到数组中
  let a = {};
  // res2.data.result.forEach(item => {
  //   active_list.value.forEach(val => {
  //     a = {};
  //     if (item.target_name == val.name && item.reports_enabled === 1) {
  //       a.name = item.target_list_name;
  //       a.explane = item.target_list_explain;
  //       val.children.push(a);
  //     }
  //   });
  // });
  res2.data.result.forEach(item => {
    active_list.value.forEach(val => {
      if (!val.children) {  // 如果没有 children，初始化它
        val.children = [];
      }
      if (item.target_name == val.name && item.reports_enabled === 1) {
        val.children.push({
          name: item.target_list_name,
          explane: item.target_list_explain,
          address: item.address
        });
      }
    });
  });
};

function choose_icon(item) {
  return `../static/icon/list/${item + 1}.png`;
}

const active_list = ref([
  {
    id: 1,
    name: "",
    children: [],
  },
  {
    id: 2,
    name: "",
    children: [],
  },
  {
    id: 3,
    name: "",
    children: [],
  },
]);

nextTick(() => {
  getClassify();
});
</script>

<style lang="scss" scoped>
.left_tab {
  height: 600px;
}

.cut-off {
  width: 100%;
  height: 20px;
  font-size: 13px;
  line-height: 20px;
  color: #c6c6c6;
  margin: 15px 0 0 20px;
}

.top {
  margin-left: 20px;
  margin-top: 10px;
  width: 90%;
  display: flex;
  flex-direction: column;
  border-bottom: 1px #efefef solid;
}

.top-list {
  height: 4.375rem;
  line-height: 3.15rem;
  margin-bottom: 2%;
  margin-left: 5px;
  border-bottom: 1px solid #efefef;
  position: relative;
  text-align: start;
  &:last-child {
    border: none;
  }

  img {
    padding-right: 15px;
    padding-top: 15px;
    width: 47px;
  }

  .list_detail {
    height: 30px;
    width: 150px;
    font-size: 12px;
    position: absolute;
    top: 25px;
    left: 53px;
    color: #d2d2d2;
  }
}
</style>
