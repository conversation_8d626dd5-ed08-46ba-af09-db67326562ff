<template>
  <div class="table-container">
    <!-- 筛选条件区域 -->
    <div class="filter-container">

      <el-date-picker
          v-model="filterDate"
          type="month"
          placeholder="选择年月"
          @change="handleDateFilter">
      </el-date-picker>
<!--      <el-input-->
<!--          v-model="dq"-->
<!--          placeholder="请输入大区"-->
<!--          @click="dqclick('dq')"-->
<!--          style="flex: 2;width: 200px;margin-left: 20px;">-->
<!--      </el-input>-->

<!--      <el-input-->
<!--          v-model="sq"-->
<!--          placeholder="请输入省区"-->
<!--          @click="dqclick('sq')"-->
<!--          style="flex: 2;width: 200px;margin-left: 20px;">-->
<!--      </el-input>-->

<!--      <el-input-->
<!--          v-model="jxq"-->
<!--          placeholder="请输入经销区"-->
<!--          @click="dqclick('jxq')"-->
<!--          style="flex: 2;width: 200px;margin-left: 20px;">-->
<!--      </el-input>-->
      <el-input
          v-model="jxkf"
          placeholder="请输入经销库房"
          @click="dqclick('jxkf')"
          style="flex: 2;width: 200px;margin-left: 20px;">
      </el-input>
      <el-select
          v-model="zb"
          placeholder="指标"
          clearable
          style="width: 150px; margin-left: 10px;"
          class="filter-item"
          @change="zbChange('zb')"
      >
        <el-option label="成交金额" value="deal_money"></el-option>
        <el-option label="成交家数" value="deal_store_num"></el-option>
        <el-option label="新增家数" value="new_store_num"></el-option>
        <el-option label="线外拜访" value="unplanned_visit_num"></el-option>
        <el-option label="实访家数" value="actual_visit_num"></el-option>
        <el-option label="应访超期门店" value="overdue_plan_num"></el-option>
        <el-option label="应访家数" value="plan_visit_num"></el-option>
        <el-option label="有效工时" value="vaild_work_time"></el-option>
        <el-option label="出勤时长" value="work_time"></el-option>
        <el-option label="首店时间" value="first_store_datetime"></el-option>
        <el-option label="末店时间" value="last_store_datetime"></el-option>
        <el-option label="出车时间" value="depart_datetime"></el-option>
        <el-option label="收车时间" value="back_datetime"></el-option>
        <el-option label="里程" value="km"></el-option>

      </el-select>
      <el-button
          type="info"
          @click="resetFilters">
        重置筛选
      </el-button>
      <el-button
          type="primary"
          @click="exportToExcel"
          >
        全部导出
      </el-button>
    </div>

    <!-- 大区筛选弹出框 -->
    <el-dialog
        title="选择大区"
        :visible.sync="showRegionDialog"
        width="30%"
        append-to-body>
      <el-checkbox-group v-model="selectedRegions">
        <el-checkbox
            v-for="region in regions"
            :key="region.value"
            :label="region.value">
          {{ region.label }}
        </el-checkbox>
      </el-checkbox-group>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showRegionDialog = false">取消</el-button>
        <el-button type="primary" @click="handleRegionFilter">确定</el-button>
      </span>
    </el-dialog>

    <!-- 表格区域 -->
    <el-table
        :data="filteredTableData"
        style="width: 100%"
        border
        stripe
        @row-click="handleRowClick">
      <el-table-column
          prop="month_total"
          label="合计"
          width="220"
          fixed="left">
      </el-table-column>

      <el-table-column
          prop="retailer_r_name"
          label="人员"
          width="220"
          fixed="left">
      </el-table-column>
      <el-table-column
          prop="Master_data_person_name"
          label="上级"
          width="220"
          fixed="left">
      </el-table-column>
      <!-- 动态生成日期列 -->
      <el-table-column
          v-for="(date, index) in currentMonthDates"
          :key="index"
          :label="date"
          :prop="'day_' + (index + 1)"
          width="100">
      </el-table-column>


    </el-table>
<!--    <el-pagination-->
<!--        @size-change="handleSizeChange"-->
<!--        @current-change="handleCurrentChange"-->
<!--        :current-page="currentPage"-->
<!--        :page-sizes="[10, 20, 50, 100]"-->
<!--        :page-size="pageSize"-->
<!--        layout="total, sizes, prev, pager, next, jumper"-->
<!--        :total="filteredTableData.length">-->
<!--    </el-pagination>-->
  </div>
  <Modal
      v-if="showModal"
      :show="showModal"
      title="请选择"
      @close="showModal = false"
      @confirm="handleConfirm"
  >
    <MultiSelectListOne
        :items="allItems"
        :items2="allItems2"
        @update:selected-items="update($event)"
        @update:close="close($event)"
        @update:confirm="confirm($event)"
        v-model:selected-items="tempSelectedItems"
        :items-per-page="pageSize"
        :current-page="currentPage"
        :total-pages="totalPages"
        :single-select="true"
    />
  </Modal>

  <Modal
      v-if="showModal2"
      :show="showModal2"
      title="请选择"
      @close="showModal2 = false"
      @confirm="handleConfirm"
  >
    <MultiSelectListView
        :items="allItems3"
        :items2="allItems4"
        @update:selected-items="update($event)"
        @update:close="close($event)"
        @update:confirm="confirm($event)"
        v-model:selected-items="tempSelectedItems"
        :items-per-page="pageSize"
        :current-page="currentPage"
        :total-pages="totalPages"
        :single-select="true"
    />
  </Modal>
  <div v-if="loading" class="fullscreen-loader">
    <div class="loader"></div>
    <div class="loading-text">加载中...</div>
  </div>
</template>

<script>
import {GetAllDqNew, GetAllJxqNew, GetAllSqNew} from "/api/Map/index.js";
import { get_rzkf, } from "/api/workbench/trainmastersigning/index.js";
import {get_zzjg, get_zzjg_cz_new,get_all_work} from "/api/workbench/JXYL/index.js";
import Modal from "@/pages/workbench/mod/Modal.vue";
import MultiSelectListOne from "@/pages/workbench/mod/MultiSelectListOne.vue";
import MultiSelectListView from "@/pages/workbench/mod/MultiSelectListView.vue";
import * as XLSX from 'xlsx';
import {ref} from "vue";
import dayjs from "dayjs";
export default {
  components: {
    Modal,
    MultiSelectListOne,
    MultiSelectListView
  },
  data() {
    const today = new Date();
    const formattedDate = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;


    return {
      currentPage: 1,  // 当前页码
      pageSize: 10,     // 每页显示数量
      rawData: [],
      rawData2: [],
      tableData: [], // 初始为空数组，将在created钩子中填充数据
      filterDate: formattedDate, // 单日期筛选值
      times: '',
      dq: null, // 大区
      sq: null, // 省区
      jxq: null, // 经销区
      dqid: '', // 大区
      sqid: '', // 省区
      jxqid: '', // 经销区
      jxkf: null, // 经销库房
      jxkfid: '', // 经销库房

      selectedRegions: [], // 选中大区
      allItems: [],
      allItems2: [],
      allItems3: [],
      allItems4: [],
      pageType: 1,
      // currentPage: 1,
      // pageSize: 999,
      totalPages: 1,
      showModal: false,
      showModal2: false,
      showRegionDialog: false, // 是否显示大区筛选弹窗
      currentMonthDates: [],
      regions: [ // 大区选项
        { label: '华东', value: '华东' },
        { label: '华北', value: '华北' },
        { label: '华南', value: '华南' },
        { label: '西南', value: '西南' },
        { label: '西北', value: '西北' },
        { label: '东北', value: '东北' }
      ],
      zb: '成交金额',
      zbid: 'deal_money',
      activeFilters: { // 当前生效的筛选条件
        date: null,
        regions: []
      },
      tempSelectedItems: [],
      loading: false // 新增加载状态
    }
  },

  // 新增的生命周期钩子
  created() {
    this.fetchJxylData();
    this.generateCurrentMonthDates();
  },

  computed: {
    // 筛选后的表格数据
    filteredTableData() {
      return this.tableData;
    },
    pagedTableData() {
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return this.filteredTableData.slice(start, end)
    }
  },

  methods: {
    // 新增的获取数据方法
    async fetchJxylData() {
      try {
        const now = new Date();
        const year = now.getFullYear();
        const month = now.getMonth();
        const daysInMonth = new Date(year, month + 1, 0).getDate();
        const time = `${year}-${String(month + 1).padStart(2, '0')}`;
        this.times = time
        this.currentMonthDates = [];
        for (let day = 1; day <= daysInMonth; day++) {
          this.currentMonthDates.push(`${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`);
        }
        const userInfo = JSON.parse(sessionStorage.getItem("userInfo"));
        console.log('userInfo',userInfo)
        this.loading = true;
        // const response = await get_all_work(time,this.dqid,this.sqid,this.jxqid,this.jxkfid,userInfo.id,userInfo.Role_name);
        // // 假设API返回的数据结构是{ data: [...] }
        // this.tableData = response.data.result
        // this.rawData =  response.data.result
        // this.rawData2 =  response.data.result
        // this.processData()
        this.loading = false;
      } catch (error) {
        console.error('获取经销一览数据失败:', error);
        // 可以根据需要添加错误处理，如显示提示消息
        this.$message.error('获取数据失败，请稍后重试');
      } finally {
        this.loading = false;
      }
    },
    generateCurrentMonthDates() {
      // const now = new Date();
      // const year = now.getFullYear();
      // const month = now.getMonth();
      // const daysInMonth = new Date(year, month + 1, 0).getDate();
      //
      // this.currentMonthDates = [];
      // for (let day = 1; day <= daysInMonth; day++) {
      //   this.currentMonthDates.push(`${month + 1}月${day}日`);
      // }
    },
    processData() {
      // 按人员分组
      // 按人员分组
      const peopleMap = new Map();

      console.log('123', this.tableData)
      console.log('rawData2', this.rawData2)
      console.log('zb', this.zb)
      this.rawData2.forEach(item => {
        console.log(item)
        if (!peopleMap.has(item.retailer_r_name)) {
          // 初始化每个人的数据对象
          const personData = { retailer_r_name: item.retailer_r_name };
          // 初始化每天金额为0
          this.currentMonthDates.forEach((_, index) => {
            personData[`day_${index + 1}`] = 0;
          });
          // 初始化合计列
          personData['month_total'] = 0;
          peopleMap.set(item.retailer_r_name, personData);
        }

        const dateParts = item.time.split('-');
        console.log(dateParts)
        const monthDay = `${dateParts[1]}-${dateParts[2]}`;
        const dayIndex = this.currentMonthDates.indexOf(monthDay);


        peopleMap.get(item.retailer_r_name)[`Master_data_person_name`] = item.Master_data_person_name;

        if (dayIndex !== -1) {
          const personData = peopleMap.get(item.retailer_r_name);
          const dayKey = `day_${dayIndex + 1}`;

          if (this.zb == 'deal_money' || this.zb == '成交金额') {
            console.log(11111)
            personData[dayKey] += item.deal_money;
            personData['month_total'] += item.deal_money;
          } else if (this.zb == 'deal_store_num') {
            console.log(2222)
            personData[dayKey] += item.deal_store_num;
            personData['month_total'] += item.deal_store_num;
          } else if (this.zb == 'new_store_num') {
            console.log(3333)
            personData[dayKey] += item.new_store_num;
            personData['month_total'] += item.new_store_num;
          } else if (this.zb == 'unplanned_visit_num') {
            console.log(4444)
            personData[dayKey] += item.unplanned_visit_num;
            personData['month_total'] += item.unplanned_visit_num;
          } else if (this.zb == 'actual_visit_num') {
            console.log(5555)
            personData[dayKey] += Number(item.actual_visit_num);
            personData['month_total'] += Number(item.actual_visit_num);
          } else if (this.zb == 'overdue_plan_num') {
            console.log(6666)
            personData[dayKey] += item.overdue_plan_num;
            personData['month_total'] += item.overdue_plan_num;
          } else if (this.zb == 'plan_visit_num') {
            console.log(7777)
            personData[dayKey] += item.plan_visit_num;
            personData['month_total'] += item.plan_visit_num;
          } else if (this.zb == 'vaild_work_time') {
            console.log(8888)
            personData[dayKey] += item.vaild_work_time;
            personData['month_total'] += item.vaild_work_time;
          } else if (this.zb == 'work_time') {
            console.log(9999)
            personData[dayKey] += item.work_time;
            personData['month_total'] += item.work_time;
          } else if (this.zb == 'km') {
            console.log(9999)
            personData[dayKey] += item.km;
            personData['month_total'] += item.km;
          } else if (this.zb == 'first_store_datetime') {
            personData[dayKey] = item.first_store_datetime;
            if (item.first_store_datetime != 0) {
              // 如果 month_total 是 0 或不存在，直接赋值
              if (personData['month_total'] == 0 || !personData['month_total']) {
                personData['month_total'] = item.first_store_datetime;
              }
              else {
                // 提取两个时间的时间部分进行比较
                const lastStoreTime = new Date(item.first_store_datetime).toTimeString().substr(0, 8);
                const monthTotalTime = personData['month_total'].includes(' ')
                    ? new Date(personData['month_total']).toTimeString().substr(0, 8)
                    : personData['month_total'];

                if (this.timeS(lastStoreTime) < this.timeS(monthTotalTime)) {
                  personData['month_total'] = item.first_store_datetime;
                }
              }
            }
            // 对于时间类型，不计算合计
          } else if (this.zb == 'last_store_datetime') {

            personData[dayKey] = item.last_store_datetime;
            if (item.last_store_datetime != 0) {
              // 如果 month_total 是 0 或不存在，直接赋值
              if (personData['month_total'] == 0 || !personData['month_total']) {
                personData['month_total'] = item.last_store_datetime;
              }
              else {
                // 提取两个时间的时间部分进行比较
                const lastStoreTime = new Date(item.last_store_datetime).toTimeString().substr(0, 8);
                const monthTotalTime = personData['month_total'].includes(' ')
                    ? new Date(personData['month_total']).toTimeString().substr(0, 8)
                    : personData['month_total'];

                if (this.timeS(lastStoreTime) > this.timeS(monthTotalTime)) {
                  personData['month_total'] = item.last_store_datetime;
                }
              }
            }

            // 对于时间类型，不计算合计
          } else if (this.zb == 'depart_datetime') {
            personData[dayKey] = item.depart_datetime;
            if (item.depart_datetime != 0) {
              // 如果 month_total 是 0 或不存在，直接赋值
              if (personData['month_total'] == 0 || !personData['month_total']) {
                personData['month_total'] = item.depart_datetime;
              }
              else {
                // 提取两个时间的时间部分进行比较
                const lastStoreTime = new Date(item.depart_datetime).toTimeString().substr(0, 8);
                const monthTotalTime = personData['month_total'].includes(' ')
                    ? new Date(personData['month_total']).toTimeString().substr(0, 8)
                    : personData['month_total'];

                if (this.timeS(lastStoreTime) < this.timeS(monthTotalTime)) {
                  personData['month_total'] = item.depart_datetime;
                }
              }
            }
          } else if (this.zb == 'back_datetime') {
            personData[dayKey] = item.back_datetime;
            if (item.back_datetime != 0) {
              // 如果 month_total 是 0 或不存在，直接赋值
              if (personData['month_total'] == 0 || !personData['month_total']) {
                personData['month_total'] = item.back_datetime;
              }
              else {
                // 提取两个时间的时间部分进行比较
                const lastStoreTime = new Date(item.back_datetime).toTimeString().substr(0, 8);
                const monthTotalTime = personData['month_total'].includes(' ')
                    ? new Date(personData['month_total']).toTimeString().substr(0, 8)
                    : personData['month_total'];

                if (this.timeS(lastStoreTime) > this.timeS(monthTotalTime)) {
                  personData['month_total'] = item.back_datetime;
                }
              }
            }
          }
        }
      });

      console.log('Array.from(peopleMap.values())', Array.from(peopleMap.values()))
      // 转换为数组
      this.tableData = Array.from(peopleMap.values());
      if(this.zb == 'first_store_datetime' || this.zb == 'depart_datetime' ){

        this.tableData = Array.from(peopleMap.values()).sort((a, b) => {
          // Handle cases where month_total might be missing or 0
          if (!a.month_total || a.month_total == 0) return 1;
          if (!b.month_total || b.month_total == 0) return -1;

          // Extract times for comparison
          const timeA = a.month_total.includes(' ')
              ? new Date(a.month_total).toTimeString().substr(0, 8)
              : a.month_total;
          const timeB = b.month_total.includes(' ')
              ? new Date(b.month_total).toTimeString().substr(0, 8)
              : b.month_total;

          // Compare using your timeS method
          return this.timeS(timeB) - this.timeS(timeA);
        });
      }else if(this.zb == 'last_store_datetime' || this.zb == 'back_datetime'){
        this.tableData = Array.from(peopleMap.values()).sort((a, b) => {
          // Handle cases where month_total might be missing or 0
          if (!a.month_total || a.month_total == 0) return 1;
          if (!b.month_total || b.month_total == 0) return -1;

          // Extract times for comparison
          const timeA = a.month_total.includes(' ')
              ? new Date(a.month_total).toTimeString().substr(0, 8)
              : a.month_total;
          const timeB = b.month_total.includes(' ')
              ? new Date(b.month_total).toTimeString().substr(0, 8)
              : b.month_total;

          // Compare using your timeS method
          return this.timeS(timeA) -this.timeS(timeB) ;
        });
      }else {
        this.tableData = Array.from(peopleMap.values()).sort((a, b) => {
          const valA = a.month_total || 0; // 默认值为 0
          const valB = b.month_total || 0; // 默认值为 0
          return valB - valA;
        });
      }
    },
    timeS(timeStr) {
      const [hours, minutes, seconds] = timeStr.split(':').map(Number);
      return hours * 3600 + minutes * 60 + seconds;
    },
    async zbChange(value) {
      if(!this.jxkfid){
        uni.showToast({
          title: "请选择经销库房",
          icon: "none",
          duration: 2000,
        });
        return
      }
      this.loading = true;
      // const userInfo = JSON.parse(sessionStorage.getItem("userInfo"));
      // const response = await get_all_work(this.times,this.dqid,this.sqid,this.jxqid,this.jxkfid,userInfo.id,userInfo.Role_name);
      // // 假设API返回的数据结构是{ data: [...] }
      // this.tableData = response.data.result
      // this.rawData =  response.data.result
      // this.rawData2 =  response.data.result
      this.processData()
      this.loading = false;
    },
    // 日期筛选
    async handleDateFilter(val) {
      const year = val.getFullYear();
      const month = val.getMonth();
      const daysInMonth = new Date(year, month + 1, 0).getDate();
      const time = `${year}-${String(month + 1).padStart(2, '0')}`;
      this.times = time
      this.currentMonthDates = [];
      for (let day = 1; day <= daysInMonth; day++) {
        this.currentMonthDates.push(`${month + 1}月${day}日`);
      }
      const userInfo = JSON.parse(sessionStorage.getItem("userInfo"));
      console.log('userInfo',userInfo)
      this.loading = true;
      console.log('daysInMonth',daysInMonth)
      const response = await get_all_work(time,this.dqid,this.sqid,this.jxqid,this.jxkfid,userInfo.id,userInfo.Role_name);
      // 假设API返回的数据结构是{ data: [...] }
      this.tableData = response.data.result
      this.rawData =  response.data.result
      this.rawData2 =  response.data.result
      this.processData()
      this.loading = false;
    },

    // 大区筛选
    handleRegionFilter() {
      this.activeFilters.regions = [...this.selectedRegions];
      this.showModal = false;
    },
    async dqclick(value){
      console.log('this.showRegionDialog',this.showRegionDialog)
      this.pageType = value
      if(value == 'dq'){
        let res = await GetAllDqNew();
        this.allItems = [];
        for (var i = 0; i < res.data.result.length; i++) {
          this.allItems.push({
            value: res.data.result[i].id,
            label: res.data.result[i].DistributionOrganizationName,
            label2: res.data.result[i].DistributionOrganizationName2,
            label3: res.data.result[i].DistributionOrganizationName3,
            radio: 0,
          });
        }
        this.allItems2 = [];
        this.allItems2.push({
          value: 1,
          label: "大区名称",


          radio: 0,
        });
        console.log("ssss");

      }else if(value == 'sq'){
        let res = await GetAllSqNew();
        this.allItems = [];
        for (var i = 0; i < res.data.result.length; i++) {
          this.allItems.push({
            value: res.data.result[i].id,
            label: res.data.result[i].DistributionOrganizationName,
            label2: res.data.result[i].DistributionOrganizationName2,
            label3: res.data.result[i].DistributionOrganizationName3,
            radio: 0,
          });
        }
        this.allItems2 = [];
        this.allItems2.push({
          value: 1,
          label: "省区名称",
          radio: 0,
        });
        console.log("ssss");
      }else if(value == 'jxq'){
        let res = await GetAllJxqNew();
        this.allItems = [];
        for (var i = 0; i < res.data.result.length; i++) {
          this.allItems.push({
            value: res.data.result[i].id,
            label: res.data.result[i].DistributionOrganizationName,
            label2: res.data.result[i].DistributionOrganizationName2,
            label3: res.data.result[i].DistributionOrganizationName3,
            radio: 0,
          });
        }
        this.allItems2 = [];
        this.allItems2.push({
          value: 1,
          label: "经销区名称",
          label2: "所属省区",
          label3: "所属大区",
          radio: 0,
        });
        console.log("ssss");
      }else if(value == 'jxq'){
        let res = await get_rzkf();
        this.allItems = [];
        for (var i = 0; i < res.data.result.length; i++) {
          this.allItems.push({
            value: res.data.result[i].id,
            label: res.data.result[i].DistributionOrganizationName,
            label2: res.data.result[i].DistributionOrganizationName2,
            label3: res.data.result[i].DistributionOrganizationName3,
            radio: 0,
          });
        }
        this.allItems2 = [];
        this.allItems2.push({
          value: 1,
          label: "名称",
          label2: "所属省区",
          label3: "所属大区",
          radio: 0,
        });
        console.log("ssss");
      }else if(value == 'jxkf'){
        let res = await get_rzkf();
        this.allItems = [];
        for (var i = 0; i < res.data.result.length; i++) {
          this.allItems.push({
            value: res.data.result[i].id,
            label: res.data.result[i].DistributionOrganizationName,
            label2: res.data.result[i].DistributionOrganizationName2,
            label3: res.data.result[i].DistributionOrganizationName3,
            radio: 0,
          });
        }
        this.allItems2 = [];
        this.allItems2.push({
          value: 1,
          label: "名称",
          label2: "所属省区",
          label3: "所属大区",
          radio: 0,
        });
        console.log("ssss");
      }


      this.showModal = false;
      this.$nextTick(() => {
        this.showModal = true;
      });
    },
    // 重置筛选
    async resetFilters() {
      // this.filterDate = formattedDate;
      this.dq = null;
      this.sq = null;
      this.jxq = null;
      this.jxkf = null;

      this.dqid = '';
      this.sqid = '';
      this.jxqid = '';
      this.jxkfid = '';

      this.selectedRegions = [];
      this.activeFilters = {
        date: null,
        regions: []
      };
      // const response = await get_all_work(time,this.dqid,this.sqid,this.jxqid,this.jxkfid,userInfo.id,userInfo.Role_name);
      // // 假设API返回的数据结构是{ data: [...] }
      // this.tableData = response.data.result[0]
      this.tableData = []
      this.rawData = []
      this.rawData2 = []

      // this.processData()
    },
    close(index, row) {
      this.showModal = false;
      this.showModal2 = false;
    },
    handleEdit(index, row) {
      console.log(index, row);
      // 这里可以添加编辑逻辑
    },

    handleDelete(index, row) {
      console.log(index, row);
      // 这里可以添加删除逻辑
    },
    async toggleExpand(row, index) {
      // // 如果点击的是已展开的行，则收起
      // if (row.expanded) {
      //   row.expanded = false;
      //   this.currentExpandRow = null;
      //   return;
      // }
      //
      // // 收起其他已展开的行
      // if (this.currentExpandRow !== null && this.currentExpandRow !== index) {
      //   this.filteredTableData[this.currentExpandRow].expanded = false;
      // }
      //
      // // 展开当前行
      // row.expanded = true;
      // this.currentExpandRow = index;
      //
      // // 如果还没有加载过数据，则加载
      // if (!row.expandData) {
      //   this.loadExpandData(row);
      // }
      console.log(row)
      this.showModal2 = true;
      this.allItems3 = [];
      let res = await get_zzjg_cz_new(row.DistributionOrganizationNumber);

      for (var i = 0; i < res.data.result.length; i++) {
        this.allItems3.push({
          value: res.data.result[i].id,
          label: res.data.result[i].Master_data_person_name,
          label2: res.data.result[i].Role_name,
          label3: res.data.result[i].Master_data_person_tel,
          label4: res.data.result[i].Licence_number,
          radio: 0,
        });
      }
      this.allItems4 = [];
      this.allItems4.push({
        value: 1,
        label: "名称",
        label2: "车长",
        label3: "手机号",
        label4: "车牌号",
        radio: 0,
      });
      console.log("ssss");

    },

    async loadExpandData(row) {
      // 模拟异步加载数据
      try {
        // 这里替换为你的实际API调用
        // const res = await getDetailData(row.id);
        row.expandData = {
          detail1: '详情内容1',
          detail2: '详情内容2'
          // ...从API获取的实际数据
        };
      } catch (error) {
        console.error('加载详情数据失败:', error);
      }
    },
    exportToExcel() {
      // 1. 准备数据（处理表头）
      console.log(123)
      const headers = [
        { header: "人员", key: "retailer_r_name" },
        ...this.currentMonthDates.map((date, index) => ({
          header: date,
          key: `day_${index + 1}`
        }))
      ];
      console.log(this.currentMonthDates)
      // 2. 转换数据格式（适配xlsx）
      const exportData = this.tableData.map(item => {
        const row = {};
        headers.forEach(col => {
          row[col.header] = item[col.key] || 0; // 处理空数据
        });
        return row;
      });
      console.log(exportData)
      // 3. 创建工作簿
      const worksheet = XLSX.utils.json_to_sheet(exportData);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");

      // 4. 导出文件
      const fileName = `人员数据_${new Date().toLocaleDateString()}.xlsx`;
      XLSX.writeFile(workbook, fileName);
    },
    handleRowClick(row, column, event) {
      console.log('行点击事件', row);
    },
    handleConfirm(row) {
      console.log('行点击事件', row);
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1 // 重置到第一页
    },
    // 当前页改变
    handleCurrentChange(val) {
      this.currentPage = val
    },

    async confirm(item) {
      console.log('行点击事件', item);

      if(this.pageType == 'dq'){
        this.dq = item.label
        this.dqid = item.value
      }else if(this.pageType == 'sq'){
        this.sq = item.label
        this.sqid = item.value
      }else if(this.pageType == 'jxq'){
        this.jxq = item.label
        this.jxqid = item.value
      }else if(this.pageType == 'jxkf'){
        this.jxkf = item.label
        this.jxkfid = item.value
        console.log(this.rawData)
        const userInfo = JSON.parse(sessionStorage.getItem("userInfo"));
        console.log('userInfo',userInfo)
        const response = await get_all_work(this.times,this.dqid,this.sqid,this.jxqid,this.jxkfid,userInfo.id,userInfo.Role_name);
        // 假设API返回的数据结构是{ data: [...] }
        this.tableData = response.data.result
        this.rawData =  response.data.result
        this.rawData2 =  response.data.result
        this.processData()
      }

      this.showModal = false;
      // const response =  await get_all_work(time,this.dqid,this.sqid,this.jxqid,this.jxkfid,userInfo.id,userInfo.Role_name);
      // 假设API返回的数据结构是{ data: [...] }
      // this.tableData = response.data.result[0]

    },
    //
    // const confirm = (item) => {
    //   console.log("item", item);
    //   formData[info.value] = item.label;
    //   if (info.value == "rzkf") {
    //     rzkfid.value = item.value;
    //     formData["sssq"] = item.label2;
    //     formData["ssdq"] = item.label3;
    //   } else if (info.value == "wlfcmc") {
    //     // 存储物流分仓ID，以便后续使用
    //     formData["wlfcId"] = item.value;
    //     formData["wlfcbm"] = `WLFC${dayjs().format("YYYYMMDDHHmmss")}`;
    //   }
    //
    //   showModal.value = false;
    // };

  },

}
</script>

<style scoped>
.table-container {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.filter-container > * {
  margin-right: 10px;
}
/* 全屏遮罩样式 */
.fullscreen-loader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

/* 加载动画 */
.loader {
  border: 5px solid #f3f3f3;
  border-top: 5px solid #3498db;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
}

.loading-text {
  color: white;
  margin-top: 20px;
  font-size: 1.2rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>