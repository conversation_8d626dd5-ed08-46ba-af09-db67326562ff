<template>
  <div class="exchange-rules-page">
    <a-config-provider :locale="locale">
      <!-- 头部操作区 -->
      <div class="header-section">
        <!-- 搜索框 -->
        <div class="search-section">
          <a-input-search
            v-model:value="searchKeyword"
            placeholder="请输入产品名称"
            size="large"
            allow-clear
            @search="keywordSearch"
            @clear="keywordClear"
            @custom="keywordSearch"
          />
        </div>
      </div>

      <!-- 列表内容 -->
      <div class="list-container">
        <!-- 规则卡片列表 -->
        <div v-if="foodList.length > 0" class="rules-list">
          <a-card
            v-for="(item, index) in foodList"
            :key="item.id"
            class="rule-card"
            :hoverable="true"
            @click="toDetail(item)"
          >
            <!-- 卡片头部 -->
            <template #title>
              <div class="card-header">
                <div class="rule-title">
                  <span class="serial-number">{{ item.product }}</span>
                </div>
              </div>
            </template>

            <!-- 卡片内容 -->
            <div class="card-content">
              <!-- 基本信息行 -->
              <a-row :gutter="16" class="info-row">
                <a-col :span="8">
                  <div class="info-item">
                    <span class="label">可订：</span>
                    <span class="value">{{ item.order_num }}</span>
                  </div>
                </a-col>
                <a-col :span="8">
                  <div class="info-item">
                    <span class="label">车长可订：</span>
                    <span class="value">{{ item.car_order }}</span>
                  </div>
                </a-col>
                <a-col :span="8">
                  <div class="info-item">
                    <span class="label">可销货：</span>
                    <span class="value">{{ item.sale_num }}</span>
                  </div>
                </a-col>
              </a-row>
              <a-row :gutter="16" class="info-row">
                <a-col :span="8">
                  <div class="info-item">
                    <span class="label">可返货：</span>
                    <span class="value">{{ item.back_num }}</span>
                  </div>
                </a-col>
                <a-col :span="8">
                  <div class="info-item">
                    <span class="label">可退货：</span>
                    <span class="value">{{ item.return_num }}</span>
                  </div>
                </a-col>
                <a-col :span="8">
                  <div class="info-item">
                    <span class="label">可质返：</span>
                    <span class="value">{{ item.rework_num }}</span>
                  </div>
                </a-col>
              </a-row>

              <!-- 详情信息 -->
              <div class="info-row time-info">
                <span class="time-text">
                  <UnorderedListOutlined />
                  查看详情
                </span>
              </div>
            </div>
          </a-card>
        </div>

        <!-- 空状态 -->
        <div v-if="foodList.length == 0 && !isLoading" class="empty-state">
          <a-empty :description="getEmptyDescription()">
            <template #image>
              <FileTextOutlined style="font-size: 48px; color: #d9d9d9" />
            </template>
          </a-empty>
        </div>
      </div>
      <!-- 加载提示 -->
      <view v-if="isLoading" style="text-align: center; padding: 10px"
        >正在加载更多数据...</view
      >
      <view
        v-if="limitValue == '加载完成' && foodList.length != 0"
        style="text-align: center; padding: 10px"
        >没有更多数据了</view
      >
    </a-config-provider>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { message, Modal } from "ant-design-vue";
import { onShow, onLoad, onReachBottom } from "@dcloudio/uni-app";
import { FileTextOutlined, UnorderedListOutlined } from "@ant-design/icons-vue";
import zhCN from "ant-design-vue/es/locale/zh_CN";
import { getFoodListApi } from "/api/workbench/switch/index";

// 中文语言包
const locale = zhCN;

// 搜索关键词
const searchKeyword = ref("");
const limit = ref(0);
const limitValue = ref("加载更多");
const isLoading = ref(false);

// 模拟数据
const foodList = ref([]);

//获取列表数据
const getList = async () => {
  const res = await getFoodListApi(limit.value, searchKeyword.value);
  console.log(res);
  if (res.data.code == 200) {
    if (res.data.result[0].length < 50) {
      limitValue.value = "加载完成";
    }
    if (res.data.result[0] && res.data.result[0].length > 0) {
      foodList.value = res.data.result[0].map((item) => ({
        id: item.max_product_code,
        product: item.Small_boss_price_result_product_name,
        order_num: item.distinct_posts_order,
        car_order: item.distinct_posts_che_ding,
        sale_num: item.distinct_posts_sale,
        back_num: item.distinct_posts_back,
        return_num: item.distinct_posts_return,
        rework_num: item.distinct_posts_zhifan,
      }));
      console.log("列表数据", foodList.value);
    }
    isLoading.value = false;
  } else {
    message.error("获取列表信息失败");
    isLoading.value = false;
  }
};

onShow(async () => {
  try {
    console.log("加载");
    isLoading.value = true;
    limit.value = 0;
    foodList.value = [];
    limitValue.value = "加载更多";
    await getList();
  } catch (error) {
    isLoading.value = false;
    message.error("获取列表信息失败");
    console.error(error);
  }
});
// 加载更多数据
const getDatalist = async () => {
  try {
    isLoading.value = true;
    const res = await getQfListApi(limit.value, searchKeyword.value);
    // console.log('数据',res.data.result);
    if (res.data.code == 200) {
      if (res.data.result[0] && res.data.result[0].length > 0) {
        if (res.data.result[0].length < 50) {
          limitValue.value = "加载完成";
        }
        const lists = res.data.result[0].map((item) => ({
          id: item.max_product_code,
          product: item.Small_boss_price_result_product_name,
          order_num: item.distinct_posts_order,
          car_order: item.distinct_posts_che_ding,
          sale_num: item.distinct_posts_sale,
          back_num: item.distinct_posts_back,
          return_num: item.distinct_posts_return,
          rework_num: item.distinct_posts_zhifan,
        }));

        foodList.value = foodList.value.concat(lists);
        console.log("列表数据", foodList.value);
      }
      isLoading.value = false;
    } else {
      isLoading.value = false;
      message.error("获取列表信息失败");
    }
  } catch (error) {
    isLoading.value = false;
    message.error("获取列表信息失败");
    console.error(error);
  }
};
//页面触底时自动触发;
onReachBottom(() => {
  console.log("limitValue.value", limitValue.value);
  if (limitValue.value != "加载完成") {
    limit.value = Number(limit.value) + 50;
    getDatalist();
  }
});

// 空状态描述
const getEmptyDescription = () => {
  if (searchKeyword.value) {
    return "未找到相关产品";
  }
  return "暂无数据";
};

// 搜索处理
const keywordSearch = async (e) => {
  console.log("搜索", e);

  if (e) {
    searchKeyword.value = e;
  } else {
    searchKeyword.value = null;
  }
  limit.value = 0;
  foodList.value = [];
  limitValue.value = "加载更多";
  isLoading.value = true;
  await getList();
};
const keywordClear = async () => {
  console.log("清空");
  searchKeyword.value = null;
  isLoading.value = true;
  await getList();
};

// 进入详情页
const toDetail = (data) => {
  console.log("data", data);
  uni.navigateTo({
    url: `/pages/workbench/OrderSwitch/Detail?product=${data.product}&code=${data.id}`,
  });
};

// 页面生命周期
onMounted(() => {});
</script>

<style scoped>
.exchange-rules-page {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding: 0;
}

/* 头部操作区 */
.header-section {
  background: #fff;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.add-section {
  margin-bottom: 16px;
}

.search-section {
  width: 100%;
}

/* 列表容器 */
.list-container {
  padding: 16px;
}

/* 规则卡片列表 */
.rules-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.rule-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.rule-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
}

.rule-title {
  display: flex;
  /* flex-direction: column; */
  gap: 6px;
  flex: 1;
  align-items: center;
}

.serial-number {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1.3;
}

.status-tag {
  align-self: flex-start;
}

/* 卡片内容 */
.card-content {
  margin-top: 12px;
}

.info-row {
  margin-bottom: 12px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-item {
  display: flex;
  /* flex-direction: column; */
  gap: 2px;
  /* font-size: 30px; */
}

.info-item.full-width {
  width: 100%;
}

.label {
  font-size: 16px;
  color: #909399;
  line-height: 1.2;
}

.value {
  font-size: 14px;
  color: #2c3e50;
  font-weight: 500;
  line-height: 1.3;
  word-break: break-all;
}

.value.highlight {
  color: #fa8c16;
  font-weight: 600;
}

.scope-text {
  color: #606266;
  font-size: 13px;
  line-height: 1.3;
}

.time-info {
  margin-top: 6px;
  padding-top: 8px;
  border-top: 1px solid #f5f7fa;
}

.time-text {
  font-size: 12px;
  color: #c0c4cc;
  line-height: 1.2;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 空状态 */
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .header-section {
    padding: 12px;
  }

  .list-container {
    padding: 8px 12px;
  }

  .rule-card {
    border-radius: 8px;
  }

  .serial-number {
    font-size: 15px;
  }

  .value {
    font-size: 13px;
  }

  .scope-text {
    font-size: 12px;
  }

  .info-row {
    margin-bottom: 10px;
  }

  .card-content {
    margin-top: 8px;
  }

  .time-info {
    margin-top: 4px;
    padding-top: 6px;
  }
}

/* PC端适配 */
@media (min-width: 769px) {
  .exchange-rules-page {
    max-width: 1200px;
    margin: 0 auto;
  }

  .header-section {
    padding: 20px 24px;
    display: flex;
    align-items: center;
    gap: 20px;
  }

  .add-section {
    margin-bottom: 0;
    flex-shrink: 0;
  }

  .search-section {
    max-width: 400px;
  }

  .list-container {
    padding: 20px 24px;
  }

  .rules-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 16px;
  }

  .serial-number {
    font-size: 17px;
  }
}

/* 中等屏幕适配 */
@media (min-width: 1024px) {
  .exchange-rules-page {
    max-width: 1400px;
  }

  .rules-list {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 18px;
  }
}

/* 大屏幕适配 */
@media (min-width: 1280px) {
  .exchange-rules-page {
    max-width: 1600px;
  }

  .rules-list {
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
    gap: 20px;
  }
}

/* 超大屏幕适配 */
@media (min-width: 1600px) {
  .exchange-rules-page {
    max-width: 1800px;
  }

  .rules-list {
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 24px;
  }
}

/* antd 组件样式重写 */
:deep(.ant-card-head) {
  border-bottom: 1px solid #f5f7fa;
  padding: 12px 16px 8px;
}

:deep(.ant-card-body) {
  padding: 0 16px 16px;
}

:deep(.ant-empty-image) {
  margin-bottom: 16px;
}

:deep(.ant-tag) {
  border-radius: 6px;
  font-size: 12px;
}

:deep(.ant-dropdown-menu) {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

:deep(.ant-dropdown-menu-item) {
  padding: 8px 12px;
}

:deep(.ant-dropdown-menu-item-icon) {
  margin-right: 8px;
}

/* 移动端卡片样式调整 */
@media (max-width: 768px) {
  :deep(.ant-card-head) {
    padding: 10px 12px 6px;
  }

  :deep(.ant-card-body) {
    padding: 0 12px 12px;
  }
}
</style>
