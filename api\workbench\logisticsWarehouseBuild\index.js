import request from "../../index";

// 获取经销区列表
export function getAreaListApi() {
  return request({
    url: "/outRegion/getOutRegionList",
    method: "get",
  });
}

// 保存仓库
export function saveWarehouseApi(data) {
  return request({
    url: "/outRegion/saveOutRegion",
		method: "post",
		data,
  });
}

// 获取物流分仓
export function get_wlfc(page, page_size) {
  return request({
    url: `/outRegion/getLogisticsList?page=${page}&page_size=${page_size}`,
    method: "get",
  });
}

// 签约物流分仓
export function sign_wlfc(data) {
  return request({
    url: "/boss/add_qy",
    method: "post",
    data,
  });
}

// 获取物流分仓位置
export function get_wlfc_wz(data) {
  return request({
    url: "/outRegion/getWarehouseLocation",
    method: "get",
    data,
  });
}

export function get_rzkf_wlfc(id) {
  return request({
    url: `/boss/get_rzkf_wlfc?id=${id}`,
    method: "get",
  });
}
