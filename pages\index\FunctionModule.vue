<template>
  <div class="container">
    <div
      class="work-list"
      v-for="item in workbenchList"
      :key="item.id"
      @click="toPage(item)"
    >
      <div class="title">
        <image :src="item.imgSrc" class="icon-img"></image>
        <span class="title-text">{{ item.function_name }}</span>
      </div>
      <div class="footer">
        <span class="footer-text">{{ item.functional_description }}</span>
        <u-icon name="arrow-right" color="#22264C" size="14" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";

import { getWorkbenchFunction } from "../../api/login/login";
import { useUserInfo } from "/store/user/userInfo";

const userStore = useUserInfo();

const workbenchList = ref([]);

async function getAllWorkbench() {
  const res = await getWorkbenchFunction(userStore.userInfo.id);
  workbenchList.value = res.data.result[0].sort(
    (a, b) => a.FunctionalModuleOrderBy - b.FunctionalModuleOrderBy
  );

  workbenchList.value.forEach((item) => {
    item.imgSrc = "/static/icon/functionmodule/addorder.svg";
  });
}
getAllWorkbench();

function toPage(page) {
  uni.navigateTo({
    url: page.function_address,
  });
}
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  overflow-y: auto;
  margin-bottom: 100rpx;

  .work-list {
    padding: 20rpx;
    display: flex;
    align-items: center;
    border-bottom: 1rpx solid #dbd8d8;
    justify-content: space-between;
    .title,
    .footer {
      display: flex;
      gap: 20rpx;
      align-items: center;
    }

    .title-text {
      font-weight: bold;
    }

    .footer-text {
      font-size: 24rpx;
      color: rgb(200, 200, 200);
    }
    .icon-img {
      width: 78rpx;
      height: 78rpx;
    }
  }
}
</style>
