<template>
  <div class="container">
    <a-card title="库长签约详情" class="main-card">
      <a-form
        :model="formData"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
        ref="formRef"
      >
        <!-- 基本信息区域 -->
        <a-divider orientation="left">基本信息</a-divider>
        <a-row :gutter="24">
          <a-col :span="12" v-for="item in basicFields" :key="item.key">
            <a-form-item :label="item.label" :name="item.key">
              <a-input
                v-model:value="formData[item.key]"
                :placeholder="item.label"
                disabled
                :maxlength="getMaxLength(item.key)"

              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 法人信息区域 -->
        <a-divider orientation="left">法人信息</a-divider>
        <a-row :gutter="24">
          <a-col :span="12" v-for="item in legalFields" :key="item.key">
            <a-form-item :label="item.label" :name="item.key">
              <a-input
                v-model:value="formData[item.key]"
                :placeholder="item.label"
                disabled
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 委托执行人信息区域 -->
        <a-divider orientation="left">委托执行人信息</a-divider>
        <a-row :gutter="24">
          <a-col :span="12" v-for="item in executorFields" :key="item.key">
            <a-form-item :label="item.label" :name="item.key">
              <a-input
                v-model:value="formData[item.key]"
                :placeholder="item.label"
                disabled
              />
            </a-form-item>
          </a-col>
          <!-- 性别字段 -->
          <a-col :span="12">
            <a-form-item label="性别" name="sex">
              <a-radio-group v-model:value="formData.sex" disabled>
                <a-radio value="男">男</a-radio>
                <a-radio value="女">女</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 银行信息区域 -->
        <a-divider orientation="left">银行信息</a-divider>
        <a-row :gutter="24">
          <a-col :span="12" v-for="item in bankFields" :key="item.key">
            <a-form-item :label="item.label" :name="item.key">
              <a-input
                v-model:value="formData[item.key]"
                :placeholder="item.label"
                disabled
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 保证金信息 -->
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="保证金金额" name="bzjje">
              <a-input
                v-model:value="formData.bzjje"
                placeholder="保证金金额"
                disabled
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 保证金打款截图展示 -->
        <a-row :gutter="24" v-if="formData.bzjjefktp">
          <a-col :span="12">
            <a-form-item label="保证金打款截图" name="bzjjefktp">
              <div class="image-preview-container">
                <div class="image-preview-item">
                  <img :src="formData.bzjjefktp" alt="保证金打款截图" @click="previewImage(formData.bzjjefktp)" />
                </div>
              </div>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 区域信息 -->
        <a-divider orientation="left">区域信息</a-divider>
        <a-row :gutter="24">
          <a-col :span="12" v-for="item in regionFields" :key="item.key">
            <a-form-item :label="item.label" :name="item.key">
              <a-input
                v-model:value="formData[item.key]"
                :placeholder="item.label"
                disabled
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 图片展示区域 -->
        <a-divider orientation="left">证件照片</a-divider>

        <!-- 营业执照照片展示 -->
        <a-row :gutter="24" v-if="formData.yyzz">
          <a-col :span="12">
            <a-form-item label="营业执照照片" name="yyzz">
              <div class="image-preview-container">
                <div class="image-preview-item">
                  <img :src="formData.yyzz" alt="营业执照照片" @click="previewImage(formData.yyzz)" />
                </div>
              </div>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 法人身份证照片展示 -->
        <a-row :gutter="24">
          <a-col :span="12" v-if="formData.frsfzzm">
            <a-form-item label="法人身份证正面照片" name="frsfzzm">
              <div class="image-preview-container">
                <div class="image-preview-item">
                  <img :src="formData.frsfzzm" alt="法人身份证正面照片" @click="previewImage(formData.frsfzzm)" />
                </div>
              </div>
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if="formData.ffsfzfm">
            <a-form-item label="法人身份证反面照片" name="ffsfzfm">
              <div class="image-preview-container">
                <div class="image-preview-item">
                  <img :src="formData.ffsfzfm" alt="法人身份证反面照片" @click="previewImage(formData.ffsfzfm)" />
                </div>
              </div>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 银行卡照片展示 -->
        <a-row :gutter="24">
          <a-col :span="12" v-if="formData.yhkzm">
            <a-form-item label="银行卡正面照片" name="yhkzm">
              <div class="image-preview-container">
                <div class="image-preview-item">
                  <img :src="formData.yhkzm" alt="银行卡正面照片" @click="previewImage(formData.yhkzm)" />
                </div>
              </div>
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if="formData.yhkfm">
            <a-form-item label="银行卡反面照片" name="yhkfm">
              <div class="image-preview-container">
                <div class="image-preview-item">
                  <img :src="formData.yhkfm" alt="银行卡反面照片" @click="previewImage(formData.yhkfm)" />
                </div>
              </div>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 返回按钮 -->
        <a-form-item :wrapper-col="{ span: 24 }" style="text-align: center; margin-top: 32px">
          <a-button
            size="large"
            @click="goBack"
            style="width: 200px"
          >
            返回
          </a-button>
        </a-form-item>
      </a-form>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive } from "vue";
import { getTrainmasterDetailApi } from "/api/workbench/trainmastersigning/index.js";
import { onLoad } from "@dcloudio/uni-app";

const formRef = ref(null);
const formData = reactive({
  rzkf: null, //入职库房
  jxqy: null, //经销区域
  kzxtmc: null, //库长系统名称
  yfyyzzmc: null, //营业执照名称
  yfyyzzh: null, //营业执照号
  yfjyz: null, //法定代表人
  yflxdh: null, //法人电话
  yfjycs: null, //营业执照经营场所
  rzrymc: null, //委托执行人系统名称
  rzryjs: "库长", //委托执行人角色，默认值为"库长"
  wtzxrxm: null, //委托执行人姓名
  sex: null, //性别
  sfzh: null, //身份证号
  lxdh: null, //联系电话
  khyh: null, //开户银行
  zhmc: null, //账户名称
  yxkh: null, //银行账号
  bzjje: null, //保证金金额
  bzjjefktp: null, //保证金打款截图
  ssdq: null, //所属大区
  sssq: null, //所属省区
  kfwz: null, //库房位置
  yyzz: null, //营业执照照片
  frsfzzm: null, //法人身份证正面照片
  ffsfzfm: null, //法人身份证反面照片
  yhkzm: null, //银行卡正面照片
  yhkfm: null, //银行卡反面照片
});

// 字段分组配置
const basicFields = [
  { label: "入职库房", key: "rzkf" },
  { label: "经销区域", key: "jxqy" },
  { label: "库长系统名称", key: "kzxtmc" }
];

const legalFields = [
  { label: "营业执照名称", key: "yfyyzzmc" },
  { label: "营业执照号", key: "yfyyzzh" },
  { label: "法定代表人", key: "yfjyz" },
  { label: "法人电话", key: "yflxdh" },
  { label: "营业执照经营场所", key: "yfjycs" }
];

const executorFields = [
  { label: "委托执行人系统名称", key: "rzrymc" },
  { label: "委托执行人角色", key: "rzryjs" },
  { label: "委托执行人姓名", key: "wtzxrxm" },
  { label: "身份证号", key: "sfzh" },
  { label: "联系电话", key: "lxdh" }
];

const bankFields = [
  { label: "开户银行", key: "khyh" },
  { label: "账户名称", key: "zhmc" },
  { label: "银行账号", key: "yxkh" }
];

const regionFields = [
  { label: "所属大区", key: "ssdq" },
  { label: "所属省区", key: "sssq" },
  { label: "库房位置", key: "kfwz" }
];

// 获取字段最大长度
const getMaxLength = (key) => {
  // 库长系统名称不限制字数
  return null;
};

// 图片预览功能
const previewImage = (imageUrl) => {
  uni.previewImage({
    urls: [imageUrl],
    current: imageUrl
  });
};

// 返回功能
const goBack = () => {
  uni.navigateBack();
};

const detailId = ref("");

async function getDetailMessage() {
  try {
    const { data } = await getTrainmasterDetailApi(detailId.value);
    formData.id = detailId.value;
    const message = data.result[0];

    for (let key in message) {
      if (key === "xyqsrq") {
        formData[key] = message["xyqsrq_f"];
      } else if (key === "htksrq") {
        formData[key] = message["htksrq_f"];
      } else if (key === "htjsrq") {
        formData[key] = message["htjsrq_f"];
      } else if (["bzjjefktp", "yyzz", "frsfzzm", "ffsfzfm", "yhkzm", "yhkfm"].includes(key)) {
        // 处理图片字段，现在是字符串格式
        formData[key] = message[key] || null;
      } else if (!["xyqsrq", "htksrq", "htjsrq"].includes(key)) {
        formData[key] = message[key];
      }
    }
  } catch (error) {
    console.error('获取详情失败:', error);
    uni.showToast({
      title: '获取数据失败',
      icon: 'none',
      duration: 2000,
    });
  }
}



onLoad(options => {
  detailId.value = options.id;
  getDetailMessage();
});
</script>

<style lang="scss" scoped>
.container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.main-card {
  max-width: 1200px;
  margin: 0 auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 8px;

  :deep(.ant-card-head) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px 8px 0 0;

    .ant-card-head-title {
      color: white;
      font-size: 18px;
      font-weight: 600;
    }
  }

  :deep(.ant-card-body) {
    padding: 32px;
  }
}

// 分割线样式
:deep(.ant-divider) {
  margin: 24px 0 16px 0;

  .ant-divider-inner-text {
    font-size: 16px;
    font-weight: 600;
    color: #1890ff;
  }
}

// 表单项样式
:deep(.ant-form-item) {
  margin-bottom: 16px;

  .ant-form-item-label {
    > label {
      font-weight: 500;
      color: #262626;
    }
  }

  .ant-input {
    border-radius: 6px;
    background-color: #f5f5f5;
    color: #8c8c8c;
    cursor: not-allowed;

    &:disabled {
      background-color: #f5f5f5;
      color: #8c8c8c;
      border-color: #d9d9d9;
    }
  }
}

// 图片预览容器样式
.image-preview-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;

  .image-preview-item {
    width: 100px;
    height: 100px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      border-color: #40a9ff;
      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}

// 按钮样式
:deep(.ant-btn) {
  border-radius: 6px;
  font-weight: 500;

  &:not(.ant-btn-primary) {
    background: #f5f5f5;
    border-color: #d9d9d9;
    color: #595959;

    &:hover, &:focus {
      background: #e6f7ff;
      border-color: #40a9ff;
      color: #1890ff;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }

  .main-card {
    :deep(.ant-card-body) {
      padding: 16px;
    }
  }

  :deep(.ant-col) {
    &.ant-col-12 {
      width: 100% !important;
    }
  }

  :deep(.ant-form-item) {
    .ant-form-item-label {
      text-align: left !important;
    }
  }

  .image-preview-container {
    .image-preview-item {
      width: 80px;
      height: 80px;
    }
  }
}
</style>
