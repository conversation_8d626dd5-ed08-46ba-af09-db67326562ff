<template>
  <a-config-provider :locale="zhCN">
    <view class="container">
      <view style="color: rgba(0, 0, 0, 0.4)">
        最后计算时间：{{ collectData?.last_update_datetime }}
      </view>
      <view class="top-data">
        <view class="right">
          <view class="data-item">
            <span class="title">总门店数</span>
            <span class="value">{{ collectData?.total_store_num }}</span>
          </view>
          <view class="data-item">
            <span class="title">主阵地</span>
            <span class="value">{{ collectData?.main_store_num }}</span>
          </view>
          <view class="data-item">
            <span class="title">补强品</span>
            <span class="value">{{ collectData?.reinforce_store_num }}</span>
          </view>
          <view class="data-item">
            <span class="title">促销门店</span>
            <span class="value">{{ collectData?.promotional_store_if }}</span>
          </view>
          <view class="data-item">
            <span class="title">促销跟进</span>
            <span class="value">{{ collectData?.promotional_time }}</span>
          </view>
          <view class="data-item">
            <span class="title">散货门店</span>
            <span class="value">{{ collectData?.bulk_store_num }}</span>
          </view>
          <view class="data-item">
            <span class="title">季节品</span>
            <span class="value">{{ collectData?.season_store_num }}</span>
          </view>
        </view>
        <view class="left" id="main"></view>
      </view>
      <view class="checked-list">
        <text>筛选条件：</text>
        <u-checkbox
          v-for="(item, index) in checkedList"
          v-model:checked="item.checked"
          :key="item.label"
          :label="item.label"
          labelSize="14"
          usedAlone
          @change="checkboxChange($event, index)"
        />
      </view>
      <view class="checked-list">
        <text>选择车长：</text>
        <a-select
          v-model:value="selectUser"
          style="width: 200px"
          :options="userListOptions"
          @change="handleChange"
          showSearch
        ></a-select>
      </view>
      <view class="store-list">
        <view class="store-item" v-for="(item, index) in dataList">
          <view class="title">
            <a-image
              style="height: 250rpx; width: 200rpx"
              :src="item.Store_photo"
            />
            <view class="title-message">
              <view class="store-name">
                <span>{{ item.Store_name }}</span>
                <span style="font-weight: normal">
                  {{ item.Master_data_person_name }}
                </span>
              </view>
              <view class="store-detail">
                <span class="key">销售额:</span>
                <span class="value">{{ item.Sales_past_eight }}</span>
                <span class="key">返货额:</span>
                <span class="value">{{ item.Return_past_eight }}</span>
                <span class="key">返货率:</span>
                <span class="value">{{
                  item.Return_past_eight
                    ? (
                        (Number(item.Return_past_eight) /
                          Number(item.Sales_past_eight)) *
                        100
                      ).toFixed(2) + "%"
                    : null
                }}</span>
                <span class="key">促销次数:</span>
                <span class="value">{{ item.itmes_of_promotional }}</span>
                <span class="key">主阵地:</span>
                <a-checkbox
                  v-model:checked="item.main_position_complete"
                  disabled
                ></a-checkbox>
                <span class="key">补强:</span>
                <a-checkbox
                  v-model:checked="item.reinforcement_completed"
                  disabled
                ></a-checkbox>
                <span class="key">散货:</span>
                <a-checkbox
                  v-model:checked="item.Bulk_completed"
                  disabled
                ></a-checkbox>
                <span class="key">季节:</span>
                <a-checkbox
                  v-model:checked="item.season_completed"
                  disabled
                ></a-checkbox>
              </view>
            </view>
          </view>
          <view class="bottom">
            <a-image
              v-if="item.Sales_next_pic"
              style="height: 100px"
              :src="item.Sales_next_pic"
            />
            <view class="img-none" v-else>无</view>
            <a-image
              v-if="item.Sales_next_pic3"
              style="height: 100px"
              :src="item.Sales_next_pic3"
            />
            <view class="img-none" v-else>无</view>
            <a-image
              v-if="item.Sales_next_pic4"
              style="height: 100px"
              :src="item.Sales_next_pic4"
            />
            <view class="img-none" v-else>无</view>
            <a-image
              v-if="item.Sales_next_pic5"
              style="height: 100px"
              :src="item.Sales_next_pic5"
            />
            <view class="img-none" v-else>无</view>
          </view>
        </view>
      </view>
    </view>
    <u-loadmore :status="loading" style="padding: 20rpx" />
  </a-config-provider>
</template>

<script setup>
import * as echarts from "echarts";
import { nextTick, ref, reactive, onMounted } from "vue";
import { onReachBottom } from "@dcloudio/uni-app";
import {
  getCollectDataKuZhang,
  getCollectDataHeHuoRen,
  getChannelConstructionListHeHuoRen,
  getUnderlingsHeHuoRen,
  getUnderlings,
  getChannelConstructionList,
} from "/api/workbench/channelbuilding";
import dayjs from "dayjs";
import zhCN from "ant-design-vue/es/locale/zh_CN";
import "dayjs/locale/zh-cn";
dayjs.locale("zh-cn");

import { useUserInfo } from "/store/user/userInfo";
const userStore = useUserInfo();
const dataParams = reactive({
  page: 1,
  pageSize: 10,
  userCode: userStore.userInfo.userCode,
  checkedList: [],
});
const dataList = ref([]);
const collectData = ref(null);
const lastDataLength = ref(0);
const loading = ref("loading");

const checkedList = ref([
  {
    checked: false,
    label: "主阵地已完成",
    key: "main_position_complete",
    flag: 1,
  },
  {
    checked: false,
    label: "主阵地未完成",
    key: "main_position_complete",
    flag: 0,
  },
  {
    checked: false,
    label: "补强品已完成",
    key: "reinforcement_completed",
    flag: 1,
  },
  {
    checked: false,
    label: "补强品未完成",
    key: "reinforcement_completed",
    flag: 0,
  },

  {
    checked: false,
    label: "散货已完成",
    key: "Bulk_completed",
    flag: 1,
  },
  {
    checked: false,
    label: "散货未完成",
    key: "Bulk_completed",
    flag: 0,
  },
  {
    checked: false,
    label: "季节品已完成",
    key: "season_completed",
    flag: 1,
  },
  {
    checked: false,
    label: "季节品未完成",
    key: "season_completed",
    flag: 0,
  },
  {
    checked: false,
    label: "有促销品",
    key: "itmes_of_promotional",
    flag: 1,
  },
  {
    checked: false,
    label: "无促销品",
    key: "itmes_of_promotional",
    flag: 0,
  },
]);

function checkboxChange(event, index) {
  dataParams.page = 1;
  dataList.value = [];
  checkedList.value[index].checked = event;
  if (userStore.userInfo.Role_name === "合伙人") {
    getListHeHuoRen();
  }
  if (userStore.userInfo.Role_name === "库长") {
    getList();
  }
}

const userListOptions = ref([]);
const selectUser = ref("全部");

async function getUnderlingList() {
  const { data } = await getUnderlings(userStore.userInfo.userCode);
  userListOptions.value = data.result.map(item => {
    return {
      label: item.Master_data_person_name,
      value: item.Master_data_person_name,
      id: item.id,
      code: item.Master_data_person_code,
    };
  });
  userListOptions.value.unshift({ label: "全部", value: "全部" });
}

async function getUnderlingListHeHuoRen() {
  const { data } = await getUnderlingsHeHuoRen(userStore.userInfo.fz_area_id);
  userListOptions.value = data.result.map(item => {
    return {
      label: item.Master_data_person_name,
      value: item.Master_data_person_name,
      id: item.id,
      code: item.Master_data_person_code,
    };
  });
  userListOptions.value = userListOptions.value.filter(
    (item, index, self) => index === self.findIndex(t => t.value === item.value)
  );
  userListOptions.value.unshift({ label: "全部", value: "全部" });
}

function handleChange(value, option) {
  dataParams.userCode = option.code;
  dataParams.page = 1;
  dataList.value = [];

  if (userStore.userInfo.Role_name === "合伙人") {
    dataParams.user_id = option.id;
    getListHeHuoRen();
  }
  if (userStore.userInfo.Role_name === "库长") {
    if (value === "全部") {
      dataParams.userCode = userStore.userInfo.userCode;
      dataParams.user_id = null;
    }
    getList();
  }
}
// 获取渠道汇总数据-库长
async function getAllCollectDataKuZhang() {
  const currentDate = dayjs().format("YYYY-MM-DD");
  const { data } = await getCollectDataKuZhang(
    userStore.userInfo.userCode,
    currentDate
  );
  if (data.result.length > 0) {
    collectData.value = data.result[0];
  }
  initEchart();
}

// 获取渠道汇总数据-合伙人
async function getAllCollectDataHeHuoRen() {
  const currentDate = dayjs().format("YYYY-MM-DD");
  const { data } = await getCollectDataHeHuoRen(
    userStore.userInfo.fz_area_id,
    currentDate
  );
  if (data.result.length > 0) {
    collectData.value = data.result[0];
  }
  initEchart();
}
async function getList() {
  loading.value = "loading";
  dataParams.checkedList = checkedList.value
    .filter(item => item.checked)
    .map(item => {
      return {
        checked: 1,
        label: item.label,
        key: item.key,
        flag: item.flag,
      };
    });
  const { data } = await getChannelConstructionList(dataParams);
  dataList.value = dataList.value.concat(data.result);
  dataList.value.forEach(item => {
    item.main_position_complete = item.main_position_complete === 1;
    item.reinforcement_completed = item.reinforcement_completed === 1;
    item.Bulk_completed = item.Bulk_completed === 1;
    item.season_completed = item.season_completed === 1;
  });
  lastDataLength.value = data.result.length;
  if (data.result.length < dataParams.pageSize) {
    loading.value = "nomore";
  } else {
    loading.value = "loadmore";
  }
}

async function getListHeHuoRen() {
  loading.value = "loading";
  dataParams.checkedList = checkedList.value
    .filter(item => item.checked)
    .map(item => {
      return {
        checked: 1,
        label: item.label,
        key: item.key,
        flag: item.flag,
      };
    });
  dataParams.area_id = userStore.userInfo.fz_area_id;
  const { data } = await getChannelConstructionListHeHuoRen(dataParams);
  dataList.value = dataList.value.concat(data.result[0]);
  dataList.value.forEach(item => {
    item.main_position_complete = item.main_position_complete === 1;
    item.reinforcement_completed = item.reinforcement_completed === 1;
    item.Bulk_completed = item.Bulk_completed === 1;
    item.season_completed = item.season_completed === 1;
  });
  lastDataLength.value = data.result[0].length;
  if (data.result[0].length < dataParams.pageSize) {
    loading.value = "nomore";
  } else {
    loading.value = "loadmore";
  }
}

function initEchart() {
  var myChart = echarts.init(document.getElementById("main"));
  // 绘制图表
  myChart.setOption({
    tooltip: {
      trigger: "item",
    },
    legend: {
      orient: "vertical",
      left: "right",
    },
    series: [
      {
        name: "Access From",
        type: "pie",
        radius: "80%",
        data: [
          { value: collectData.value?.main_store_num, name: "主阵地" },
          { value: collectData.value?.reinforce_store_num, name: "补强品" },
          { value: collectData.value?.promotional_store_if, name: "促销门店" },
          { value: collectData.value?.bulk_store_num, name: "散货门店" },
          { value: collectData.value?.season_store_num, name: "季节品" },
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)",
          },
        },
      },
    ],
  });
}

nextTick(() => {
  initEchart();
});

onMounted(() => {
  if (userStore.userInfo.Role_name === "合伙人") {
    getAllCollectDataHeHuoRen();
    getUnderlingListHeHuoRen();
    getListHeHuoRen();
  }
  if (userStore.userInfo.Role_name === "库长") {
    getAllCollectDataKuZhang();
    getUnderlingList();
    getList();
  }
});

onReachBottom(async () => {
  if (lastDataLength.value === dataParams.pageSize) {
    dataParams.page += 1;
    if (userStore.userInfo.Role_name === "合伙人") {
      getListHeHuoRen();
    }
    if (userStore.userInfo.Role_name === "库长") {
      getList();
    }
  }
  if (lastDataLength.value < dataParams.pageSize) {
    loading.value = "nomore";
  }
});
</script>

<style lang="scss" scoped>
.container {
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  .top-data {
    display: flex;
    gap: 20rpx;
    .right {
      flex: 1;
      display: grid;
      padding: 20rpx;
      grid-template-columns: repeat(3, minmax(0, 1fr));
      gap: 20rpx;
      justify-items: center;
      box-shadow: 1px 0px 80px rgba(0, 0, 0, 0.21);
      border-radius: 20rpx;
      .data-item {
        display: flex;
        flex-direction: column;
        gap: 20rpx;
        align-items: center;
        justify-content: center;
        height: 200rpx;
        width: 100%;
        border-radius: 20rpx;
        border: 1px solid #000;
        .title {
          font-size: 24px;
          font-weight: bold;
        }
        .value {
          font-size: 24px;
          font-weight: bold;
          color: green;
        }
      }
    }
    .left {
      padding: 20rpx;
      flex: 1;
      box-shadow: 1px 0px 80px rgba(0, 0, 0, 0.21);
      border-radius: 20rpx;
    }
  }
  .checked-list {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 20rpx;
    padding: 30rpx;
    box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.1);
  }
  .store-list {
    padding: 20rpx;
    display: grid;
    gap: 20rpx;
    grid-template-columns: repeat(4, minmax(0, 1fr));
    justify-items: center;
    .store-item {
      width: 100%;
      border-radius: 10px;
      box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.1);
      .title {
        display: flex;
        padding: 10px;
        gap: 10rpx;
        border-bottom: 1px solid #000;
        .title-message {
          flex: 1;
          .store-name {
            display: flex;
            justify-content: space-between;
            font-weight: bold;
            padding-bottom: 10rpx;
            border-bottom: 1px solid rgba(0, 0, 0, 0.2);
          }
          .store-detail {
            padding-top: 10rpx;
            display: grid;
            gap: 10rpx;
            grid-template-columns: repeat(4, minmax(0, 1fr));
            color: rgba(0, 0, 0, 0.7);
            .key {
              text-align: right;
              font-size: 14px;
            }
          }
        }
      }
      .bottom {
        padding: 10px;
        display: grid;
        gap: 20rpx;
        grid-template-columns: repeat(2, minmax(0, 1fr));
      }
      .img-none {
        height: 100px;
        display: flex;
        justify-content: center;
        align-items: center;
        border: 1px solid rgba(0, 0, 0, 0.3);
      }
    }
  }
}
</style>
