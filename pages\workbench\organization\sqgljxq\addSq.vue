<template>
  <div class="form-container">
    <h2 class="form-title">新建数据</h2>
    <form @submit.prevent="handleSubmit" class="form-grid">
      <div class="form-group">
        <label for="title">标题：</label>
        <input
          type="text"
          id="title"
          v-model.trim="formData.title"
          required
          class="form-input"
          placeholder="请输入标题"
        />
      </div>

      <div class="form-group">
        <label for="content">内容：</label>
        <textarea
          id="content"
          v-model.trim="formData.content"
          required
          class="form-textarea"
          rows="4"
          placeholder="请输入详细内容"
        ></textarea>
      </div>

      <div class="action-buttons">
        <button
          type="button"
          @click="handleCancel"
          class="cancel-button"
        >
          取消
        </button>
        <button
          type="submit"
          :disabled="submitting"
          class="submit-button"
        >
          {{ submitting ? '提交中...' : '确认创建' }}
        </button>
      </div>
    </form>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 响应式表单数据
const formData = ref({
  title: '',
  content: ''
})

// 提交状态控制
const submitting = ref(false)

// 表单提交处理
const handleSubmit = async () => {
  if (!formData.value.title || !formData.value.content) {
    alert('请完整填写所有必填字段')
    return
  }

  submitting.value = true
  try {
    // 此处替换为实际API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    alert('创建成功')
    router.push({ name: 'list-page' })
  } catch (error) {
    console.error('提交失败:', error)
    alert('创建失败，请重试')
  } finally {
    submitting.value = false
  }
}

// 取消操作处理
const handleCancel = () => {
  if (confirm('确定放弃当前编辑内容？')) {
    router.go(-1)
  }
}
</script>

<style scoped>
.form-container {
  max-width: 800px;
  margin: 2rem auto;
  padding: 2rem;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.form-title {
  margin-bottom: 1.5rem;
  color: #2c3e50;
  font-size: 1.8rem;
  text-align: center;
}

.form-grid {
  display: grid;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-input,
.form-textarea {
  padding: 0.8rem;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1.5rem;
}

.submit-button,
.cancel-button {
  padding: 0.8rem 1.5rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: transform 0.2s ease, opacity 0.2s ease;
}

.submit-button {
  background-color: #3498db;
  color: white;
}

.cancel-button {
  background-color: #f0f0f0;
  color: #666;
}

.submit-button:hover:not(:disabled) {
  background-color: #2980b9;
  transform: translateY(-1px);
}

.cancel-button:hover {
  background-color: #e0e0e0;
}

.submit-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}
</style>