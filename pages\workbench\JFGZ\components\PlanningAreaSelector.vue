<template>
  <div class="planning-area-selector">
    <!-- 左侧信息卡片 -->
    <div class="info-card">
      <a-form
        :model="formState"
        name="planning"
        :label-col="{ span: 8 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-item
          label="经销区域"
          name="selectArea"
          :rules="[{ required: true, message: '请选择经销区域！' }]"
        >
          <a-select
            v-model:value="formState.selectArea"
            :options="areaOptions"
            showSearch
            placeholder="请选择经销区域"
            @change="selectAreaChange"
          />
        </a-form-item>

        <a-form-item
          label="完成目标销售额"
          name="targetSales"
          :rules="[{ required: true, message: '请输入完成目标销售额！' }]"
        >
          <a-input-number
            v-model:value="formState.targetSales"
            :min="0"
            :max="99999999"
            :precision="2"
            placeholder="请输入完成目标销售额"
            style="width: 100%"
          />
        </a-form-item>

        <a-form-item
          label="有效年"
          name="validYear"
          :rules="[{ required: true, message: '请选择有效年！' }]"
        >
          <a-select
            v-model:value="formState.validYear"
            placeholder="请选择有效年"
            :options="validYearOptions"
            @change="handleValidYearChange"
          />
        </a-form-item>

        <a-form-item
          label="有效月"
          name="validMonth"
          :rules="[{ required: true, message: '请选择有效月！' }]"
        >
          <a-select
            v-model:value="formState.validMonth"
            placeholder="请选择有效月"
            :options="validMonthOptions"
          />
        </a-form-item>

        <!-- 地图控制功能 -->
        <div class="map-controls-section">
          <div class="controls-title">地图控制</div>
          <div class="control-item">
            <span>卫星地图</span>
            <a-switch
              v-model:checked="openSatellite"
              @change="changeSatellite"
            />
          </div>
          <div class="control-item">
            <span>浏览模式</span>
            <a-switch v-model:checked="openView" @change="changeView" />
          </div>
          <div class="control-item">
            <span>显示街镇</span>
            <a-switch v-model:checked="openStreet" @change="changeOpenStreet" />
          </div>
        </div>

        <!-- 门店状态图例 -->
        <div class="store-legend-section">
          <div class="legend-title">门店状态图例</div>
          <div class="legend-items">
            <div class="legend-item" v-for="legend in storeLegends" :key="legend.id">
              <img :src="legend.icon" :alt="legend.label" class="legend-icon" />
              <span class="legend-label">{{ legend.label }}</span>
            </div>
          </div>
        </div>

        <div class="text-hint">
          <span>绘制：鼠标左键点击及移动即可绘制图形</span>
          <span>结束绘制：鼠标左键双击即可结束绘制折线、多边形会自动闭合</span>
          <span>中断：绘制过程中按下esc键可中断该过程</span>
        </div>

        <a-form-item :wrapper-col="{ offset: 0, span: 24 }">
          <div class="button-group">
            <a-button @click="reset('plan')">规划重置</a-button>
            <a-button @click="reset('all')">全部重置</a-button>
            <a-button
              type="primary"
              @click="confirmSelection"
              :loading="buttonLoading"
            >
              确认选择
            </a-button>
          </div>
        </a-form-item>
      </a-form>

      <!-- 区域信息展示 -->
      <div v-if="showDetail" class="area-detail">
        <div class="area-message">
          <span style="font-weight: bold">范围面积：</span>
          <span>{{ detailMessage.acreage + "㎡" }}</span>
        </div>
        <div class="area-message">
          <span style="font-weight: bold">经纬度范围：</span>
          <span>{{ coordinateRange }}</span>
        </div>
      </div>
    </div>

    <!-- 右侧地图容器 -->
    <div class="map-container">
      <div class="map" id="planningMap"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, defineEmits, watch, nextTick } from "vue";
import { message } from "ant-design-vue";
import dayjs from "dayjs";
import { useUserInfo } from "/store/user/userInfo";
import { getAllAreas_sell } from "/api/map/index";
import { transtionCopy } from "/utils/map/index";
import {
  getNationalAreas,
  getStoreNotPerson,
} from "/api/workingplanning";
import { getAreaStreets } from "/api/workingplanning";
import { useMarking } from "/store/markingplanning/index";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

const emits = defineEmits(['area-selected', 'close-modal']);

const userStore = useUserInfo();
const useMarkingStore = useMarking();

// 表单数据
const formState = reactive({
  selectArea: null,
  targetSales: null,
  validYear: dayjs().year(),
  validMonth: null,
});

// 区域选项
const areaOptions = ref([]);

// 门店状态图例数据
const storeLegends = ref([
  {
    id: 'marker2',
    icon: '/static/icon/map/store44.png',
    label: '已开门店'
  },
  {
    id: 'marker5',
    icon: '/static/icon/map/store11.png',
    label: '未开门店'
  },
  {
    id: 'marker1',
    icon: '/static/icon/map/store77.png',
    label: '已拜访门店'
  },
  {
    id: 'marker3',
    icon: '/static/icon/map/store33.png',
    label: '有意向有柜体'
  },
  {
    id: 'marker4',
    icon: '/static/icon/map/store22.png',
    label: '无意向门店'
  },
  {
    id: 'marker6',
    icon: '/static/icon/map/store66.png',
    label: '超期未谈门店'
  }
]);

// 地图相关
const openSatellite = ref(false);
const openView = ref(false);
const openStreet = ref(false);
const showDetail = ref(false);
const buttonLoading = ref(false);

const detailMessage = reactive({
  acreage: 0,
});

let map;
let mapCenter;
let editLayer; // 规划图层
let areaLayer; // 区域图层
let storeLayer; // 门店图层
let streetLayer; // 街镇图层

// 计算属性
const validYearOptions = computed(() => {
  const currentYear = dayjs().year();
  const years = [];
  for (let i = currentYear; i <= currentYear + 5; i++) {
    years.push({ label: i.toString(), value: i });
  }
  return years;
});

const validMonthOptions = computed(() => {
  const currentYear = dayjs().year();
  const currentMonth = dayjs().month() + 1;
  const months = [];

  if (formState.validYear === currentYear) {
    for (let i = currentMonth; i <= 12; i++) {
      months.push({ label: i + "月", value: i });
    }
  } else {
    for (let i = 1; i <= 12; i++) {
      months.push({ label: i + "月", value: i });
    }
  }

  return months;
});

const coordinateRange = computed(() => {
  if (!showDetail.value) return "待计算";

  // 获取绘制的区域数据来计算经纬度范围
  try {
    const overlays = editLayer?.getOverlayList();
    if (!overlays || overlays.length === 0) return "待计算";

    const polygonOverlay = overlays.find(item => item.id === "polygon");
    if (!polygonOverlay) return "待计算";

    const geometries = polygonOverlay.overlay.getGeometries();
    if (geometries.length === 0) return "待计算";

    const paths = geometries[0].paths;
    if (!paths || paths.length === 0) return "待计算";

    // 计算经纬度范围
    let minLat = paths[0].lat, maxLat = paths[0].lat;
    let minLng = paths[0].lng, maxLng = paths[0].lng;

    paths.forEach(point => {
      minLat = Math.min(minLat, point.lat);
      maxLat = Math.max(maxLat, point.lat);
      minLng = Math.min(minLng, point.lng);
      maxLng = Math.max(maxLng, point.lng);
    });

    return `纬度:${minLat.toFixed(6)}~${maxLat.toFixed(6)}, 经度:${minLng.toFixed(6)}~${maxLng.toFixed(6)}`;
  } catch (error) {
    console.error("计算经纬度范围失败:", error);
    return "计算失败";
  }
});

// 监听弹窗显示状态
watch(() => props.visible, async (newVal) => {
  if (newVal) {
    // 弹窗打开时重新初始化
    await nextTick();

    // 等待一小段时间确保DOM完全渲染
    await new Promise(resolve => setTimeout(resolve, 50));

    // 确保地图容器存在且可见
    const mapContainer = document.getElementById("planningMap");
    if (mapContainer) {
      // 重新初始化地图
      if (map) {
        try {
          map.destroy();
        } catch (error) {
          console.warn("地图销毁失败:", error);
        }
        map = null;
        editLayer = null;
        areaLayer = null;
        storeLayer = null;
        streetLayer = null;
      }

      try {
        await initMap();
        console.log("地图重新初始化成功");
      } catch (error) {
        console.error("地图重新初始化失败:", error);
      }
    }

    if (areaOptions.value.length === 0) {
      await getAreaOptions();
    }
  } else {
    // 弹窗关闭时清理地图资源
    if (map) {
      try {
        map.destroy();
      } catch (error) {
        console.warn("地图销毁失败:", error);
      }
      map = null;
      editLayer = null;
      areaLayer = null;
      storeLayer = null;
      streetLayer = null;
    }
  }
});

// 生命周期
onMounted(async () => {
  try {
    await nextTick(); // 确保DOM已渲染
    await initMap();
    await getAreaOptions();
  } catch (error) {
    console.error("组件初始化失败:", error);
  }
});

onUnmounted(() => {
  if (map) {
    map.destroy();
  }
});

// 方法
const initMap = async () => {
  try {
    // 确保地图容器存在且可见
    const mapContainer = document.getElementById("planningMap");
    if (!mapContainer) {
      console.error("地图容器不存在");
      return;
    }

    // 确保容器有尺寸
    if (mapContainer.offsetWidth === 0 || mapContainer.offsetHeight === 0) {
      console.warn("地图容器尺寸为0，等待容器准备");
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    mapCenter = new TMap.LatLng(39.984104, 116.307503);
    map = new TMap.Map("planningMap", {
      center: mapCenter,
      zoom: 12,
    });

    // 等待地图加载完成
    await new Promise(resolve => {
      map.on("idle", resolve);
    });
  } catch (error) {
    console.error("地图初始化失败:", error);
    return;
  }

  try {
    // 初始化街镇图层
    streetLayer = new TMap.MultiMarker({
      id: "street-layer",
      map,
      styles: {
        label: new TMap.MarkerStyle({
          width: 16, // 宽度
          height: 16, // 高度
          anchor: { x: 17, y: 23 }, // 锚点
          faceTo: "map", // 标注朝向
          src: "/static/icon/map/redStar.png", // 标注点图片url或base64地址
          color: "#fb410e",
          size: 20, // 标注点文本文字大小
          direction: "bottom", // 标注点文本文字相对于标注点图片的方位
          strokeColor: "#fff", // 标注点文本描边颜色
          strokeWidth: 2, // 标注点文本描边宽度
        }),
      },
      zIndex: 0,
    });
    streetLayer.setVisible(false); // 默认关闭街道数据显示
  } catch (error) {
    console.error("街镇图层初始化失败:", error);
  }

  // 初始化编辑图层
  editLayer = new TMap.tools.GeometryEditor({
    map,
    overlayList: [
      {
        id: "polygon",
        overlay: new TMap.MultiPolygon({
          id: "polygon",
          map,
          styles: {
            polygon: new TMap.PolygonStyle({
              color: "rgba(41,91,255,0.16)",
              showBorder: true,
              borderColor: "rgba(41,91,255,1)",
              borderWidth: 2,
            }),
          },
        }),
        actionMode: TMap.tools.constants.EDITOR_ACTION.DRAW,
      },
    ],
  });

  // 初始化区域图层
  areaLayer = new TMap.MultiPolygon({
    id: "area-layer",
    map,
    styles: {
      bgColor: new TMap.PolygonStyle({
        color: "rgba(3, 169, 244, 0.1)",
        showBorder: true,
        borderColor: "rgba(3, 169, 244, 1)",
        borderWidth: 1,
      }),
    },
    zIndex: 1,
  });

  // 初始化门店图层
  storeLayer = new TMap.MultiMarker({
    id: "store-layer",
    map,
    styles: {
      // 已开门店
      marker2: new TMap.MarkerStyle({
        width: 15,
        height: 15,
        src: "/static/icon/map/store44.png",
      }),
      // 未开门店
      marker5: new TMap.MarkerStyle({
        width: 15,
        height: 15,
        src: "/static/icon/map/store11.png",
      }),
      // 已拜访
      marker1: new TMap.MarkerStyle({
        width: 15,
        height: 15,
        src: "/static/icon/map/store77.png",
      }),
      // 有意向有柜体
      marker3: new TMap.MarkerStyle({
        width: 15,
        height: 15,
        src: "/static/icon/map/store33.png",
      }),
      // 无意向
      marker4: new TMap.MarkerStyle({
        width: 15,
        height: 15,
        src: "/static/icon/map/store22.png",
      }),
      // 超期未谈
      marker6: new TMap.MarkerStyle({
        width: 15,
        height: 15,
        src: "/static/icon/map/store66.png",
      }),
    },
    zIndex: 3,
  });

  // 监听绘制完成事件
  editLayer.on("draw_complete", (geometry) => {
    console.log("绘制完成:", geometry);
    calculateArea(geometry);
  });
};

// 获取区域选项
const getAreaOptions = async () => {
  try {
    // 获取所有的经销区列表
    const { data } = await getNationalAreas();
    const options = data.result.map(item => {
      return {
        label: item.DistributionOrganizationName,
        value: item.DistributionOrganizationName,
        id: item.Distribution_territory,
        code: item.id,
      };
    });
    areaOptions.value = Array.from(new Set(options.map(item => item.id))).map(
      id => options.find(item => item.id === id)
    );
  } catch (error) {
    console.error("获取区域选项失败:", error);
    message.error("获取区域选项失败");
  }
};

// 区域选择变化处理
const selectAreaChange = async (value, option) => {
  try {
    // 清除之前的地图数据
    if (editLayer) {
      // 使用正确的方法清除编辑图层
      const overlays = editLayer.getOverlayList();
      overlays.forEach(overlay => {
        if (overlay.overlay && overlay.overlay.setGeometries) {
          overlay.overlay.setGeometries([]);
        }
      });
    }
    showDetail.value = false;
    detailMessage.acreage = 0;

    const { data } = await getAllAreas_sell(option.id);
    // 处理区域信息
    await disposeAreaMessage(data);
    // 处理门店信息
    await disposeStoreMessage(data);
    // 处理街道信息
    await disposeStreetMessage(option);

    // 确保在加载完数据后，如果不是浏览模式，则设置为绘制模式
    if (!openView.value && editLayer) {
      editLayer.setActionMode(TMap.tools.constants.EDITOR_ACTION.DRAW);
    }
  } catch (error) {
    console.error("区域切换失败:", error);
    message.error("区域切换失败");
  }
};

const disposeAreaMessage = async (data) => {
  let geometries = [];
  let center;
  
  data.result.forEach(item => {
    center = new TMap.LatLng(item.Resident_latitude, item.Station_longitude);
    JSON.parse(item.Region_profile).forEach(_item => {
      const outlinePath = transtionCopy(_item).map(
        _item_ => new TMap.LatLng(_item_.latitude, _item_.longitude)
      );
      const path = {
        styleId: "bgColor",
        paths: outlinePath,
      };
      geometries.push(path);
    });
  });
  
  areaLayer.setGeometries(geometries);
  map.setCenter(center);
  map.setZoom(12);

  // 设置正确的交互模式
  if (openView.value) {
    editLayer.setActionMode(TMap.tools.constants.EDITOR_ACTION.INTERACT);
  } else {
    editLayer.setActionMode(TMap.tools.constants.EDITOR_ACTION.DRAW);
  }
};

// 处理门店信息
const disposeStoreMessage = async (data) => {
  try {
    let storeGeometries = [];
    // 先清空之前的门店数据
    if (storeLayer) {
      storeLayer.setGeometries([]);
    }

    for (const item of data.result) {
      const address = item.Provincial_name + item.City_name + item.District_name;
      const { data: storeData } = await getStoreNotPerson(address);
      useMarkingStore.storeList = storeData.result;

      storeData.result
        .filter(
          _item =>
            _item.Store_Latitude !== null &&
            _item.Store_Latitude !== "" &&
            _item.Store_monthly_sales === "0"
        )
        .forEach(_item => {
          storeGeometries.push({
            position: new TMap.LatLng(
              _item.Store_Latitude,
              _item.Store_longitude
            ),
            id: _item.id,
            styleId: judgeColor(_item),
          });
        });
    }

    // 设置门店图层
    if (storeLayer) {
      storeLayer.setGeometries(storeGeometries);
    }
  } catch (error) {
    console.error("处理门店信息失败:", error);
    message.error("加载门店数据失败");
  }
};

// 判断门店标记点颜色
const judgeColor = (store) => {
  if (!store.Master_data_person_name_crm_really) {
    return "marker5"; // 未开门店蓝色
  }
  if (store.Master_data_person_name_crm_really) {
    return "marker2"; // 已开门店浅绿色
  }

  // 超期未谈深蓝色
  if (store.endDate) {
    const currentDate = dayjs();
    const endDate = dayjs(store.endDate);
    if (currentDate > endDate) {
      return "marker6";
    }
  }

  if (!store.is_stork) {
    return "marker4"; // 无意向红色
  }

  if (store.is_stork && store.is_drop_cabinet) {
    return "marker3"; // 有意向有柜体深绿色
  }
  if (store.Last_visittime) {
    return "marker1"; // 已拜访黄色
  }
};

const calculateArea = (geometry) => {
  try {
    // 计算面积
    const area = TMap.geometry.computeArea(geometry.paths);
    detailMessage.acreage = Math.round(area);
    showDetail.value = true;
    console.log("计算区域面积:", area);
  } catch (error) {
    console.error("计算面积失败:", error);
    message.error("计算面积失败");
  }
};

// 处理街道信息
const disposeStreetMessage = async (option) => {
  try {
    // 获取该经销区的街道数据
    const { data } = await getAreaStreets(option.code);
    let geometries = [];
    data.result.forEach(item => {
      geometries.push({
        id: "label", // 点图形数据的标志信息
        styleId: "label", // 样式id
        position: new TMap.LatLng(item.latitude, item.longitude), // 标注点位置
        content: `${item.place_name}${
          item.population ? "|" + item.population : ""
        }`, // 标注文本
      });
    });

    // 设置街道数据到图层
    streetLayer.setGeometries(geometries);
    console.log("街道数据加载完成:", geometries.length, "个街镇");
  } catch (error) {
    console.error("获取街道信息失败:", error);
    message.error("获取街道信息失败");
  }
};

const handleValidYearChange = (value) => {
  const currentYear = dayjs().year();
  const currentMonth = dayjs().month() + 1;

  if (
    value === currentYear &&
    formState.validMonth &&
    formState.validMonth < currentMonth
  ) {
    formState.validMonth = null;
  }
};

// 地图控制方法
const changeSatellite = (checked) => {
  if (checked) {
    map.setBaseMap({ type: "satellite" });
  } else {
    map.setBaseMap({ type: "vector" });
  }
};

const changeView = (checked) => {
  if (checked) {
    editLayer.setActionMode(TMap.tools.constants.EDITOR_ACTION.INTERACT);
    // 清除当前绘制内容
    const overlays = editLayer.getOverlayList();
    overlays.forEach(overlay => {
      if (overlay.overlay && overlay.overlay.setGeometries) {
        overlay.overlay.setGeometries([]);
      }
    });
    showDetail.value = false;
  } else {
    editLayer.setActionMode(TMap.tools.constants.EDITOR_ACTION.DRAW);
  }
};

const changeOpenStreet = (checked) => {
  const areaLayerData = areaLayer.getGeometries();

  // 如果未选择经销区，则不打开街道图层
  if (areaLayerData.length === 0) {
    openStreet.value = false;
    message.warn("未选择经销区");
    return;
  }

  if (checked) {
    streetLayer.setVisible(true);
  } else {
    streetLayer.setVisible(false);
  }
};

const reset = (type) => {
  if (type === "all") {
    formState.selectArea = null;
    formState.targetSales = null;
    formState.validYear = dayjs().year();
    formState.validMonth = null;
    showDetail.value = false;
    detailMessage.acreage = 0;
  }

  if (type === "plan" || type === "all") {
    // 清除编辑图层的绘制内容
    if (editLayer) {
      const overlays = editLayer.getOverlayList();
      overlays.forEach(overlay => {
        if (overlay.overlay && overlay.overlay.setGeometries) {
          overlay.overlay.setGeometries([]);
        }
      });
    }
    showDetail.value = false;
    detailMessage.acreage = 0;
  }
};

const confirmSelection = () => {
  try {
    if (!showDetail.value) {
      message.error("请先绘制规划区域！");
      return;
    }

    if (!formState.targetSales) {
      message.error("请输入完成目标销售额！");
      return;
    }

    if (!formState.validYear || !formState.validMonth) {
      message.error("请选择有效年月！");
      return;
    }

    // 获取绘制的区域数据
    const overlays = editLayer.getOverlayList();
    if (!overlays || overlays.length === 0) {
      message.error("请先绘制规划区域！");
      return;
    }

    const polygonOverlay = overlays.find(item => item.id === "polygon");
    if (!polygonOverlay) {
      message.error("未找到规划区域数据！");
      return;
    }

    const geometries = polygonOverlay.overlay.getGeometries();
    if (geometries.length === 0) {
      message.error("请先绘制规划区域！");
      return;
    }

    const planningAreaPoints = geometries[0].paths.map(point => [point.lat, point.lng]);

    // 获取选择的区域信息
    const selectedAreaOption = areaOptions.value.find(item => item.value === formState.selectArea);

    const areaInfo = {
      targetSales: formState.targetSales,
      validYear: formState.validYear,
      validMonth: formState.validMonth,
      acreage: detailMessage.acreage,
      coordinateRange: coordinateRange.value,
      planningAreaPoints: JSON.stringify(planningAreaPoints),
      areaId: selectedAreaOption?.id,
      areaName: selectedAreaOption?.label,
      areaCode: selectedAreaOption?.code
    };

    console.log("确认选择的区域信息:", areaInfo);
    emits('area-selected', areaInfo);
    message.success("规划区域选择成功！");

    // 关闭弹窗
    emits('close-modal');
  } catch (error) {
    console.error("确认选择失败:", error);
    message.error("确认选择失败，请重试");
  }
};
</script>

<style lang="scss" scoped>
.planning-area-selector {
  display: flex;
  height: 600px;
  gap: 16px;
}

.info-card {
  width: 400px;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  border: 1px solid #e1dbf4;
  overflow-y: auto;

  .map-controls-section {
    margin-bottom: 20px;
    padding: 16px;
    background-color: rgba(112, 77, 233, 0.05);
    border-radius: 6px;
    border: 1px solid rgba(112, 77, 233, 0.2);

    .controls-title {
      font-size: 14px;
      font-weight: 600;
      color: #1b1728;
      margin-bottom: 12px;
    }

    .control-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      font-size: 13px;

      &:last-child {
        margin-bottom: 0;
      }

      span {
        color: #767590;
      }
    }
  }

  .store-legend-section {
    margin-bottom: 20px;
    padding: 16px;
    background-color: rgba(3, 169, 244, 0.05);
    border-radius: 6px;
    border: 1px solid rgba(3, 169, 244, 0.2);

    .legend-title {
      font-size: 14px;
      font-weight: 600;
      color: #1b1728;
      margin-bottom: 12px;
    }

    .legend-items {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 8px;
    }

    .legend-item {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 12px;

      .legend-icon {
        width: 15px;
        height: 15px;
        flex-shrink: 0;
      }

      .legend-label {
        color: #767590;
        line-height: 1.2;
      }
    }
  }

  .text-hint {
    display: flex;
    flex-direction: column;
    gap: 5px;
    color: green;
    margin-bottom: 15px;
    font-size: 12px;
  }

  .area-detail {
    margin-top: 20px;
    padding: 16px;
    background-color: rgba(112, 77, 233, 0.05);
    border-radius: 6px;
    border: 1px solid rgba(112, 77, 233, 0.2);

    .area-message {
      margin-bottom: 8px;
      font-size: 13px;

      span:first-child {
        color: #767590;
        margin-right: 8px;
      }

      span:last-child {
        color: #1b1728;
        font-weight: 500;
      }
    }
  }
}

.map-container {
  flex: 1;
  position: relative;
  border-radius: 8px;
  overflow: hidden;

  .map {
    width: 100%;
    height: 100%;
  }
}

.button-group {
  display: flex;
  gap: 12px;
  justify-content: flex-start;
  align-items: center;

  .ant-btn {
    flex: 1;
    min-width: 80px;
    padding: 0
  }
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}
</style>
