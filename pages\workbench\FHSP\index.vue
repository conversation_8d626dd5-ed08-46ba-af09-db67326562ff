<template>
  <div class="list-container">
    <!-- Search Box -->
    <div class="search-container">
      <u-input
          v-model="searchQuery"
          placeholder="搜索..."
          class="search-input"

      ></u-input>
      <button class="add-btn" @click="seach()">搜索</button>
    </div>
	<view style="margin-top: 10px" >
	  <u-subsection
	    :list="typeTabList"
	    :current="typeCur"
	    @change="typeChange"
	  />
	</view>
 
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-indicator">
      <i class="fas fa-spinner fa-spin"></i> 数据加载中...
    </div>

    <!-- 数据列表 -->
    <div v-else class="card-list">
      <article v-for="item in aReturnGoodsList" :key="item.id" class="list-item" @mouseenter="hoverItem = item.id" 
               @click="toDetail(item)"
               @mouseleave="hoverItem = null">
       <div class="item-header" style="
         background-color: #4ea28d;
         padding: 10rpx 20rpx;
         display: flex;
         justify-content: space-between;
         font-size: 26rpx;
         color: #fff;
       ">
           <h2 class="item-title">{{ item.billCode }}</h2>
           <span class="item-title">{{ item.state }}</span>
         </div>
		<div class="item-header" >
		    <h2 class="item-titles">下单日期: {{ item.billDate }}</h2>
		    <span class="item-titles">下单人: {{ item.startPerson }}</span>
		</div>
		<div class="item-header" >
		    <h2 class="item-titles">产品金额: ￥{{ item.totalMoney.toFixed(2) }}</h2>
		    <span class="item-titles">物流分仓: {{ item.warehouseName }}</span>
		</div>
		<div class="item-header" >
		    <h2 class="item-titles">分仓联系人: {{ item.warehouseManager }}</h2>
		    <span class="item-titles">经销商: {{ item.salePerson }}</span>
		</div>
		<div class="item-header" >
		    <h2 class="item-titles">周转筐数量: {{ item.totalBasket }}</h2>
		    <span class="item-titles">筐总金额:￥{{ item.totalBasketMoney.toFixed(2) }}</span>
		</div>
		

   
      </article>
    </div>
	
  </div>
</template>
<script setup>
	import {
		onShow,
		onLoad,
		onReachBottom
	} from '@dcloudio/uni-app'
	import {
		ref
	} from 'vue'

	import { useUserInfo } from "/store/user/userInfo";
	import {
		postReturnGoodsBillListFK,
		postReturnGoodsBillListFKYS,
		postReturnGoodsBillListFKDQ,
		postReturnGoodsBillListFKDQYS,
		postReturnGoodsBillListFKSS,
		postReturnGoodsBillListFKYSSS,
		postReturnGoodsBillListFKDQSS,
		postReturnGoodsBillListFKDQYSSS,
		
		get_return_lists_addFK
	} from '@/api/workbench/FHSP/index.js'
	import dayjs from 'dayjs'


	// #region ------------ 获取个人信息 start ------------
	const storePerson = useUserInfo();
	//人物信息放在store里
	const userInfo = storePerson.userInfo;
	
	// #endregion ------------ 获取个人信息 end ------------

	// 初始加载中
	let bFirstLoading = ref(false)
	// 返货单列表
	let aReturnGoodsList = ref([])
	// 商品列表正在加载中
	let loadStatus = ref('nomore')
	let pagination = {
		page: 1,
		pageSize: 20,
		loadAll: false,
	}
	const currentDate = new Date();
	const year = currentDate.getFullYear();
	const month = String(currentDate.getMonth() + 1).padStart(2, '0');
	const day = String(currentDate.getDate()).padStart(2, '0');
	const hour = String(currentDate.getHours()).padStart(2, '0');
	const minute = String(currentDate.getMinutes()).padStart(2, '0');
	const second = String(currentDate.getSeconds()).padStart(2, '0');
	const milliseconds = String(currentDate.getMilliseconds()).padStart(3, '0'); // 获取毫秒数，并补零

	const result = ref(`${year}${month}${day}${hour}${minute}${second}${milliseconds}`);
	const showTime = ref(`${year}-${month}-${day}`);
	const showTime1 = ref(`${year}-${month}-${day}`);
	const showTime2 = ref(`${year}-${month}-${day} 00:00:00`);
	const showTime3 = ref(`${year}-${month}-${day} 23:59:59`);
	const showMoney = ref(0);
	const userId = ref(userInfo.id);
	const searchQuery = ref();
	onLoad(async (options) => {
    console.log('userInfo',userInfo)
		if(options.userId){
			// userInfo.id= options.userId
			userId.value= options.userId
		}else{
			userId.value= userInfo.id
		}
	})
	// 获取返货单列表
	let fnGetReturnGoodsList = async ({
		page = 1,
		pageSize = 20
	} = {}) => {
		try {
			let temp = {
				user_id: userId.value,
				type: '返货到工厂',
				page,
				pageSize,
			}
			let res = ''
      console.log('userInfo.Role_grade',userInfo.Role_grade)
      aReturnGoodsList.value = []
			if(userInfo.Role_grade == '省区总'){
				if(typeCur.value == 0){
					let res2 = await postReturnGoodsBillListFK(temp)
					res = res2.data
				}else{
					let res2 = await postReturnGoodsBillListFKYS(temp)
					res = res2.data
				}
				
			}else if(userInfo.Role_grade == '大区总'){
				
				if(typeCur.value == 0){
					let res2 = await postReturnGoodsBillListFKDQ(temp)
					res = res2.data
				}else{
					let res2 = await postReturnGoodsBillListFKDQYS(temp)
					res = res2.data
				}
			}
			console.log('res',res);
			let temp2 = {
				user_id:  userId.value,
				type: '返货到工厂',
				stime: showTime2.value,
				etime: showTime3.value
			}
			if (res.code === 200 && res.result.length > 0) {
				let goodsList = res.result.map((item) => {
					let currentState = '待审批'
					if (item.sq_status == '0' && item.dq_status == '0') {
						currentState = '待审批'
					}else if (item.sq_status == '1' && item.dq_status == '0') {
						currentState = '待大区总审批'
					}else if (item.sq_status == '1' && item.dq_status == '1') {
						currentState = '已审批'
					}
					if (item.bills_state == '1') {
						currentState = '同意'
					} else if (item.bills_state == '2') {
						currentState = '驳回'
					} else if (item.bills_state == '3') {
						currentState = '已取消'
					} else if (item.bills_state == '7') {
						currentState = '重新提交'
					}
					
					if (item.bills_state == '5') {
						currentState = '货已结清筐未结清'
					}else if (item.bills_state == '6') {
						currentState = '货未结清筐已结清'
					}
					return {
						id: item.id, // 主表id
						billCode: item.Return_goods_bills_code, // 返货单号
						billDate: dayjs(item.Return_goods_start_date).subtract(8, 'h').format(
							'YYYY-MM-DD HH:mm:ss'), // 下单日期
						startPerson: item.Return_goods_start_person_name, // 下单人
						warehouseName: item.Return_goods_warehouse_name, // 物流分仓
						warehouseManager: item.Return_goods_warehouse_manager_name, // 物流分仓联系人
						businessManager: item.business_manager_name, // 业务经理
						salePerson: item.sale_person_name, // 经销商
						totalMoney: item.Total_return_good_money ?? 0, // 订单金额
						totalBasket: item.Total_kuang_number ?? 0, // 标准筐数
						totalBasketMoney: item.Total_kuang_money ?? 0, // 标准筐总金额
						imgUrls: item.Upload_img_urls?.split(',') || [], // 图片
						state: currentState, // 订单状态
						did: item.did,
						state_id: item.bills_state, //订单状态码
						billType: item.bill_type, // 业务结算方式
						kuang_status: item.kuang_status,
						huo_status: item.huo_status, //订单状态码
						bzp_status: item.bzp_status,
						Deliver_master_basket_amount: -1 * Number(item.Deliver_master_basket_amount),
						Deliver_master_new_basket_amount: -1 * Number(item.Deliver_master_new_basket_amount),
						Deliver_master_product_amount: -1 * Number(item.Deliver_master_product_amount),
					}
				})
				aReturnGoodsList.value = aReturnGoodsList.value.concat(goodsList)
			} else {
				pagination.loadAll = true
			}
		} catch (error) {
			throw new Error(error)
		}
	}
	
	let seach = async ({
			page = 1,
			pageSize = 20
		} = {}) => {
			try {
				aReturnGoodsList.value = []
				bFirstLoading.value = true
				loadStatus.value = 'loading'
				let temp = {
					user_id: userId.value,
					type: '返货到工厂',
					page,
					pageSize,
					test: searchQuery.value
				}
				let res = ''
				if(userInfo.Role_grade == '省区总'){
					if(typeCur.value == 0){
						let res2 = await postReturnGoodsBillListFKSS(temp)
						res = res2.data
					}else{
						let res2 = await postReturnGoodsBillListFKYSSS(temp)
						res = res2.data
					}
					
				}else if(userInfo.Role_grade == '大区总'){
					
					if(typeCur.value == 0){
						let res2 = await postReturnGoodsBillListFKDQSS(temp)
						res = res2.data
					}else{
						let res2 = await postReturnGoodsBillListFKDQYSSS(temp)
						res = res2.data
					}
				}
				console.log('res',res);
				let temp2 = {
					user_id:  userId.value,
					type: '返货到工厂',
					stime: showTime2.value,
					etime: showTime3.value
				}
				if (res.code === 200 && res.result.length > 0) {
					let goodsList = res.result.map((item) => {
						let currentState = '待审批'
						if (item.sq_status == '0' && item.dq_status == '0') {
							currentState = '待审批'
						}else if (item.sq_status == '1' && item.dq_status == '0') {
							currentState = '待大区总审批'
						}else if (item.sq_status == '1' && item.dq_status == '1') {
							currentState = '已审批'
						}
						if (item.bills_state == '1') {
							currentState = '同意'
						} else if (item.bills_state == '2') {
							currentState = '驳回'
						} else if (item.bills_state == '3') {
							currentState = '已取消'
						} else if (item.bills_state == '7') {
							currentState = '重新提交'
						}
						
						if (item.bills_state == '5') {
							currentState = '货已结清筐未结清'
						}else if (item.bills_state == '6') {
							currentState = '货未结清筐已结清'
						}
						return {
							id: item.id, // 主表id
							billCode: item.Return_goods_bills_code, // 返货单号
							billDate: dayjs(item.Return_goods_start_date).subtract(8, 'h').format(
								'YYYY-MM-DD HH:mm:ss'), // 下单日期
							startPerson: item.Return_goods_start_person_name, // 下单人
							warehouseName: item.Return_goods_warehouse_name, // 物流分仓
							warehouseManager: item.Return_goods_warehouse_manager_name, // 物流分仓联系人
							businessManager: item.business_manager_name, // 业务经理
							salePerson: item.sale_person_name, // 经销商
							totalMoney: item.Total_return_good_money ?? 0, // 订单金额
							totalBasket: item.Total_kuang_number ?? 0, // 标准筐数
							totalBasketMoney: item.Total_kuang_money ?? 0, // 标准筐总金额
							imgUrls: item.Upload_img_urls?.split(',') || [], // 图片
							state: currentState, // 订单状态
							did: item.did,
							state_id: item.bills_state, //订单状态码
							billType: item.bill_type, // 业务结算方式
							kuang_status: item.kuang_status,
							huo_status: item.huo_status, //订单状态码
							bzp_status: item.bzp_status,
							Deliver_master_basket_amount: -1 * Number(item.Deliver_master_basket_amount),
							Deliver_master_new_basket_amount: -1 * Number(item.Deliver_master_new_basket_amount),
							Deliver_master_product_amount: -1 * Number(item.Deliver_master_product_amount),
						}
					})
					aReturnGoodsList.value = aReturnGoodsList.value.concat(goodsList)
				} else {
					pagination.loadAll = true
				}
			} catch (error) {
				throw new Error(error)
			}
		}
	// 新建返货单
	const toOrdergood = () => {
		uni.navigateTo({
			url: '/pages/Workbench/components/ReturnGoodsMag/OrderDetails',
		})
	}

	// 详情跳转事件
	const toDetail = (item) => {
		const types = ref('列表')
		uni.navigateTo({
			url: `/pages/workbench/FHSP/CheckDetail?mainId=${item.id}&billCode=${item.did}&type=${types.value}`,
		})
	}

	// #region ============================= 钩子函数 start =============================
	onShow(async () => {
		try {
			// 列表清空，防止每次重复添加
			aReturnGoodsList.value = []
			bFirstLoading.value = true
			loadStatus.value = 'loading'
			await fnGetReturnGoodsList()
		} catch (error) {
			console.error(error)
			
		} finally {
			bFirstLoading.value = false
			loadStatus.value = 'nomore'
		}
	})

	// 分页加载
	onReachBottom(() => {
		if (pagination.loadAll) return
		loadStatus.value = 'loading'
		pagination.page++
		if(searchQuery.value == null){
			fnGetReturnGoodsList(pagination)
		}else{
			seach(pagination)
		}
		
		loadStatus.value = 'nomore'
	})

	//查看发车单
	const truck = (item) => {
		console.log('查看装车单')
		console.log(item)
		uni.navigateTo({
			url: `/pages/OthersGoods/TruckDetail?billCode=${item.did}`,
		})
	}
	// 日期选择器
	const showDate = ref(false);
	const showindex = ref(false);
	const showDates = (e) => {
		showDate.value = true;
		showindex.value = e
	};
	// 日期选择器确定事件
	let formatted = ref(Date.now());
	const maxValue = ref(Date.now());
	const getChangedate = async (data) => {
		console.log('data', data);
		// 处理时间格式
		const timestamp = data.value;
		const date = new Date(timestamp);
		const day = String(date.getDate()).padStart(2, '0');
		const year = date.getFullYear();
		const yearnext = date.getFullYear();
		const month = (date.getMonth() + 1).toString().padStart(2, '0');
		let monthnext = (date.getMonth() + 2).toString().padStart(2, '0');
		if (Number(monthnext) == 13) {
			yearnext = Number(yearnext) + 1
		}
		if (showindex.value === 1) {
			showTime.value = `${yearnext}-${month}-${day}`;
			showTime2.value = `${yearnext}-${month}-${day} 00:00:00`
		} else {
			showTime1.value = `${yearnext}-${month}-${day}`;
			showTime3.value = `${yearnext}-${month}-${day} 23:59:59`
		}

		let temp2 = {
			user_id:  userId.value,
			type: '返货到工厂',
			stime: showTime2.value,
			etime: showTime3.value
		}
		// const ress = await get_return_lists_addFK(temp2)
		// showMoney.value = ress.data.result[0].num
		console.log('showTime.value', showTime.value);
		showDate.value = false;
	};
	// 取消事件
	const closeDate = () => {
		showDate.value = false;
	};
	// #endregion ============================= 钩子函数 end =============================
	
	const typeTabList = ["未审批", "已审批"];
	const typeCur = ref(0);
	async function typeChange(index) {
	  typeCur.value = index;
	 aReturnGoodsList.value = []
	 bFirstLoading.value = true
	 loadStatus.value = 'loading'
	 await fnGetReturnGoodsList()
	}
</script>
<style scoped>
.search-container {
  display: flex;
  align-items: center;
  gap: 10px; /* 设置元素之间的间距 */
  margin-bottom: 1.5rem;
}

.search-input {
  flex: 1; /* 让输入框占据剩余空间 */
  min-width: 0; /* 防止内容溢出 */
}

.add-btn {
  padding: 0.4rem 0.8rem; /* 缩小按钮尺寸 */
  font-size: 0.9rem; /* 减小字体大小 */
  white-space: nowrap; /* 防止按钮文字换行 */
}
.list-container {
  max-width: 1000px;
  margin: 2rem auto;
  padding: 0 1rem;
}




.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #7f8c8d;
}

.card-list {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: repeat(auto-fill, minmax(1000px, 1fr));
}

.list-item {
  background: #fff;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.list-item:hover {
  transform: translateY(-4px);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.item-title {
  font-size: 1.25rem;
  color: #2c3e50;
}
.item-titles {
  font-size: 1.000rem;
  color: #2c3e50;
}

.item-meta {
  font-size: 0.875rem;
  color: #7f8c8d;
}

.item-content {
  color: #34495e;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.item-actions {
  opacity: 0;
  transition: opacity 0.2s ease;
  display: flex;
  gap: 0.5rem;
}

.item-actions.visible {
  opacity: 1;
}

button {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  background: #3498db;
  color: white;
  cursor: pointer;
  transition: background 0.2s ease;
}

button:hover {
  background: #2980b9;
}

.loading-indicator {
  text-align: center;
  padding: 2rem;
  color: #7f8c8d;
}

.org-name {
  margin-right: 200px;
}
</style>