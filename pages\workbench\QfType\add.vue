<template>
  <view class="list-container">
    <view
      style="display: flex; justify-content: flex-end"
      v-if="detail && showBtn"
    >
      <u-button
        v-if="!changeIf"
        type="primary"
        style="width: 100px; margin: 20px 0"
        @click="changeEdit"
      >
        修改
      </u-button>
      <view style="display: flex; gap: 10px" v-if="changeIf">
        <u-button
          type="primary"
          style="width: 100px; margin: 20px 0"
          @click="cancelEdit"
        >
          取消
        </u-button>
        <u-button
          type="primary"
          style="width: 100px; margin: 20px 0"
          @click="saveEdit"
        >
          保存
        </u-button>
      </view>
    </view>
    <a-config-provider :locale="locale">
      <u-form
        labelPosition="left"
        labelWidth="auto"
        :model="formData"
        ref="formRole"
        :rules="rules"
      >
        <template v-for="item in formObject" :key="item.key">
          <!-- 输入类型 -->
          <u-form-item :label="item.label" :prop="item.key" borderBottom>
            <template #label>
              {{ item.label }}
              <span :style="{ color: item.required ? 'red' : 'inherit' }">
                * </span
              >：
            </template>
            <template v-if="item.key === 'qfType' || item.key === 'area'">
              <a-select
                v-model:value="formData[item.key]"
                show-search
                placeholder="请选择..."
                style="width: 100%"
                :options="columns[item.key]"
                option-filter-prop="label"
                :not-found-content="
                  columns[item.key].length === 0 ? '加载中...' : '无匹配数据'
                "
                :disabled="detail && !changeIf"
              ></a-select>
            </template>
            <template
              v-else-if="
                item.key === 'start' ||
                item.key === 'end' ||
                item.key === 'approveTime'
              "
            >
              <a-date-picker
                v-model:value="formData[item.key]"
                style="width: 100%"
                show-time
                placeholder="请选择时间"
                @change="handleTimeChange($event, item.key)"
                @openChange="handlePanelChange($event, item.key)"
                :disabled="detail && !changeIf"
              />
            </template>
            <template v-else>
              <a-input
                v-model:value="formData[item.key]"
                style="width: 100%"
                :disabled="detail && !changeIf"
              />
            </template>
          </u-form-item>
        </template>
      </u-form>
    </a-config-provider>

    <view class="confirm-button" v-if="!detail">
      <u-button
        text="提交"
        shape="circle"
        color="#4ea28d"
        :loading="buttonLoading"
        @click="submit"
        style="font-size: 1.05rem"
      ></u-button>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import {
  getAllAreaApi,
  addQfApi,
  editQfApi,
} from "/api/workbench/qfType/index.js";
import zhCN from "ant-design-vue/es/locale/zh_CN"; // 引入中文包
import dayjs from "dayjs";
import "dayjs/locale/zh-cn"; // 引入 dayjs 中文语言包
import { message } from "ant-design-vue";

import { useUserInfo } from "/store/user/userInfo";
import { onBackPress } from "@dcloudio/uni-app";

// 自定义返回逻辑
onBackPress(() => {
  // 返回到指定页面
  uni.redirectTo({
    url: `/pages/workbench/QfType/index`,
  });
  // 返回 true 表示阻止默认返回行为
  return true;
});

const userStore = useUserInfo();
const props = defineProps({
  type: {
    type: String,
    default: "",
  },
  data: {
    type: String,
    default: "",
  },
});

// 用户名称
const user_name = ref("");
const user_id = ref("");
// 基础数据
const formObject = reactive([
  {
    label: "清分类型",
    key: "qfType",
    required: true,
  },
  {
    label: "经销区",
    key: "area",
    required: true,
  },
  {
    label: "开始时间",
    key: "start",
    required: true,
  },
  {
    label: "结束时间",
    key: "end",
    required: true,
  },
]);
const formData = reactive({
  qfType: null, //入职库房
  area: null, //物流分仓,
  start: null, //开始时间
  end: null, //结束时间
  id: "",
});

const columns = reactive({
  qfType: [
    {
      label: "月度提点",
      value: "月度提点",
    },
    {
      label: "粗清分",
      value: "粗清分",
    },
    {
      label: "细清分",
      value: "细清分",
    },
  ],
  area: [],
});

// 详情页进入
const detail = ref(false);
const detailData = ref({});
//显示修改按钮
const showBtn = ref(false);
onMounted(() => {
  if (!userStore.userInfo) {
    const userInfo = sessionStorage.getItem("userInfo");
    userStore.userInfo = JSON.parse(userInfo);
  }
  // console.log("userStore.userInfo", userStore.userInfo);
  user_name.value = userStore.userInfo?.userName;
  user_id.value = userStore.userInfo?.id;

  if (props?.type && props.type === "detail") {
    detail.value = true;
  }
  if (props?.data && props.data != "") {
    detailData.value = JSON.parse(props.data);
  }
  console.log("type", detail.value, detailData.value);
  formData.qfType = detailData.value?.clean_type;
  formData.area = detailData.value?.area_id;
  formData.start = detailData.value?.start_time
    ? dayjs(detailData.value.start_time)
    : null;
  formData.end = detailData.value?.end_time
    ? dayjs(detailData.value.end_time)
    : null;
  formData.id = detailData.value?.id;

  if (detailData.value?.approve_state === "待审批") {
    showBtn.value = true;
  }
  if (detail.value && !showBtn.value) {
    formObject.push({
      label: "审批状态：",
      key: "status",
    });
    formObject.push({
      label: "审批人账号：",
      key: "personCode",
    });
    formObject.push({
      label: "审批时间：",
      key: "approveTime",
    });
    formData.status = detailData.value?.approve_state;
    formData.personCode = detailData.value?.approve_person;
    formData.approveTime = detailData.value?.approve_time
      ? dayjs(detailData.value.approve_time)
      : null;
  }
  getTypes();
});
// 获取类型列表
const getTypes = async () => {
  const res = await getAllAreaApi();
  if (res.data.code == 200) {
    if (res.data?.result && res.data.result.length > 0) {
      columns.area = res.data.result.map((item) => {
        return {
          name: item.DistributionOrganizationName,
          id: item.id,
          value: item.id,
          label: item.DistributionOrganizationName,
        };
      });
    }
  }
};
// =================================表单验证规则#start#================
const formRole = ref(null);
const rules = {
  qfType: [{ required: true, message: "请选择清分类型", trigger: "blur" }],
  area: [{ required: true, message: "请选择经销区", trigger: "blur" }],
  start: [
    {
      required: true,
      message: "请选择开始时间",
      trigger: ["change", "blur"],
      validator: (rule, value) => {
        return value instanceof dayjs;
      },
    },
  ],
  end: [
    {
      required: true,
      message: "请选择结束时间",
      trigger: ["change", "blur"],
      validator: (rule, value) => {
        return value instanceof dayjs;
      },
    },
  ],
};
// =================================表单验证规则#end#================
// =================================处理日期时间#start#==================
// 设置 dayjs 使用中文
dayjs.locale("zh-cn");
// 创建响应式的 locale 对象
const locale = ref(zhCN);
// 日期选择器事件处理
// 存储面板状态
const panelState = reactive({});
// 处理时间变化
const handleTimeChange = (value, key) => {
  panelState[key] = value;
  if (value === null) {
    onOkTime(null, key);
    delete panelState[key];
  }
};
// 处理面板状态变化
const handlePanelChange = (open, key) => {
  if (!open && panelState[key]) {
    onOkTime(panelState[key], key);
    delete panelState[key];
  }
};
const onOkTime = (value, key) => {
  if (value === null) {
    if (key === "start") {
      formData.start = null;
    } else if (key === "end") {
      formData.end = null;
    }
    return;
  }
  if (key === "start") {
    formData.start = value;
  } else if (key === "end") {
    formData.end = value;
  }
};
// =================================处理日期时间#end#==================
// =================================修改信息#start#==================
// 是否修改
const changeIf = ref(false);
const changeEdit = () => {
  changeIf.value = !changeIf.value;
};
// 取消修改
const cancelEdit = () => {
  formData.qfType = detailData.value?.clean_type;
  formData.area = detailData.value?.area_id;
  formData.start = detailData.value?.start_time
    ? dayjs(detailData.value.start_time)
    : null;
  formData.end = detailData.value?.end_time
    ? dayjs(detailData.value.end_time)
    : null;
  formData.id = detailData.value?.id;
  changeIf.value = !changeIf.value;
};
// 保存修改
const saveEdit = async () => {
  buttonLoading.value = true;
  if (tjIf.value === false) {
    try {
      const valid = await formRole.value.validate();

      if (valid) {
        if (formData.start > formData.end) {
          message.error("结束时间不能早于开始时间，请重新选择");
          return;
        }
        const timeFields = ["start", "end"];
        const payload = { ...formData };

        timeFields.forEach((field) => {
          const value = payload[field];
          if (value === null || value === undefined) {
            payload[field] = null;
          } else if (dayjs.isDayjs(value)) {
            payload[field] = value.format("YYYY-MM-DD HH:mm:ss");
          } else {
            payload[field] = null;
          }
        });
        const toData = {
          user_name: user_name.value,
          qf_type: payload.qfType,
          area_id: payload.area,
          start_time: payload.start ?? null,
          end_time: payload.end ?? null,
          data_id: payload.id,
          user_id: user_id.value,
        };

        tjIf.value = true;
        const res = await editQfApi(toData);
        if (res.data.code === 200) {
          message.success("修改成功");
          changeIf.value = !changeIf.value;
        } else {
          message.error("修改失败");
        }
      } else {
        console.log("表单验证失败");
      }
    } catch (error) {
      console.error("表单验证出错", error);
    } finally {
      buttonLoading.value = false;
    }
  }
};
// ==============================新建提交#start#================
const buttonLoading = ref(false);

// 是否已经提交过程中
const tjIf = ref(false);

const submit = async () => {
  buttonLoading.value = true;
  if (tjIf.value === false) {
    try {
      // 使用 await 等待验证结果
      const valid = await formRole.value.validate();

      if (valid) {
        if (formData.start > formData.end) {
          message.error("结束时间不能早于开始时间，请重新选择");
          return;
        }
        const toData = {
          user_id: user_id.value,
          user_name: user_name.value,
          qf_type: formData.qfType,
          area_id: formData.area,
          start_time: formData.start
            ? formData.start.format("YYYY-MM-DD HH:mm:ss")
            : null,
          end_time: formData.end
            ? formData.end.format("YYYY-MM-DD HH:mm:ss")
            : null,
        };

        // 这里可以添加实际的提交逻辑
        tjIf.value = true;
        const res = await addQfApi(toData);
        if (res.data.code === 200) {
          message.success("提交成功");
          uni.redirectTo({
            url: `/pages/workbench/QfType/index`,
          });
        } else {
          message.error("提交失败");
        }
      } else {
        console.log("表单验证失败");
      }
    } catch (error) {
      console.error("表单验证出错", error);
    } finally {
      buttonLoading.value = false;
    }
  }
};
// ==============================新建提交#end#================
</script>

<style lang="scss" scoped>
.list-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 1rem;
  height: calc(100vh - 4rem);
  box-sizing: border-box !important;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  font-size: 1.05rem;

  .confirm-button {
    display: flex;
    gap: 50rpx;
    padding: 80rpx 0;
  }
  :deep(.u-form-item__body__right__message) {
    //margin-left: 70px !important;
    font-size: 14px !important;
  }
}
</style>
