<template>
  <view class="box" style="position: relative">
    <view class="head-class">
      <view v-if="roleName != '省区总' && roleName != '经销商'">
        大区：
        <view style="width: 200px">
          <a-select
            style="width: 100%"
            v-model:value="dqValue"
            allow-clear
            :options="dqOption"
            placeholder="请选择"
            show-search
            @select="selectDq"
            @change="dqChange"
            option-filter-prop="label"
          />
        </view>
      </view>
      <view v-if="roleName != '经销商'">
        省区：
        <view style="width: 200px">
          <a-select
            style="width: 100%"
            v-model:value="sqValue"
            allow-clear
            :options="sqOption"
            placeholder="请选择"
            show-search
            @select="selectSq"
            @change="sqChange"
            option-filter-prop="label"
            :field-names="{
              label: 'label',
              value: 'value',
            }"
          />
        </view>
      </view>
      <view>
        经销区：
        <view style="width: 200px">
          <a-select
            style="width: 100%"
            v-model:value="jxqValue"
            allow-clear
            :options="jxqOption"
            placeholder="请选择"
            show-search
            @select="selectJxq"
            @change="jxqChange"
            option-filter-prop="label"
          />
        </view>
      </view>
      <view>
        车长：<view style="width: 200px">
          <a-select
            style="width: 100%"
            v-model:value="carValue"
            allow-clear
            :options="carOption"
            placeholder="请选择"
            show-search
            @select="selectCar"
            @change="carChange"
            option-filter-prop="label"
            @click="getCarlist"
          /> </view
      ></view>
      <a-config-provider :locale="locale">
        <view>
          日期：
          <view style="width: 180px">
            <a-date-picker
              v-model:value="dateVal"
              @change="choose_date"
              :allowClear="false"
            />
          </view>
        </view>
      </a-config-provider>
      <view>
        显示规则：<view style="width: 200px">
          <a-select
            style="width: 100%"
            v-model:value="ruleValue"
            :options="ruleOption"
            placeholder="请选择"
            show-search
            @select="selectRule"
          /> </view
      ></view>
      <view>
        筛选规则：<view style="width: 200px">
          <a-select
            style="width: 100%"
            v-model:value="statusRuleValue"
            :options="statusRuleOption"
            placeholder="请选择"
            show-search
            @select="selectStatusRule"
          /> </view
      ></view>
      <view>
        检查任务：<view style="width: 200px">
          <a-select
            style="width: 100%"
            v-model:value="taskValue"
            :options="taskOption"
            placeholder="请选择"
            show-search
            @select="selectTask"
            @change="taskChange"
            allow-clear
          /> </view
      ></view>
    </view>
    <view class="head-class" style="margin-top: 20px">
      <view style="color: gray; margin-right: 10px">
        此页照片数量：<view style="color: black"> {{ photoNum }}</view></view
      >
      <view style="color: gray; margin-right: 10px">
        已审核数量：<view style="color: black"> {{ photoShNum }}</view></view
      >
      <view style="color: gray; margin-right: 10px">
        未审核数量：<view style="color: black"> {{ photoNshNum }}</view></view
      >
      <view style="color: gray; margin-right: 10px">
        每页数量：<view style="color: black"> {{ pageSize }}</view></view
      >
      <!-- <view style="flex: 1; display: flex; justify-content: end; width: auto"> -->

      <view
        style="margin: 0 24px; display: flex; gap: 8px"
        v-if="roleName == '公司运营' || roleName == '决策层'"
      >
        批量审核：
        <view style="margin-right: 20px">
          <checkbox :checked="isAllSelected" @click="toggleAllSelection">
            本页全选
          </checkbox>
        </view>
        <a-button
          type="primary"
          @click="batchApprove('合格')"
          :loading="btnLoad1"
          >合格</a-button
        >

        <a-button
          type="primary"
          danger
          @click="batchApprove('不合格')"
          :loading="btnLoad2"
          >不合格</a-button
        >
      </view>
      <!-- </view> -->
    </view>
    <view class="photo-part">
      <view
        class="photo-list"
        :style="{ gridTemplateColumns: `repeat(${parseInt(ruleValue)}, 1fr)` }"
      >
        <view
          class="photo-item"
          v-for="(item, index) in photoList"
          :key="index"
          :class="{ selected: selectedPhotoIds.includes(item.id) }"
        >
          <image
            :src="item.Photo_url"
            mode="aspectFill"
            class="photo-image"
            :class="{ selected: selectedPhotoIds.includes(item.id) }"
            @click="togglePhotoSelection(item)"
          />
          <!-- 状态标签 -->
          <view
            class="status-tag"
            :style="{
              width: '40%',
              aspectRatio: '1 / 1',
            }"
          >
            <image
              v-if="item.Approve_state == '待审核'"
              src="/static/icon/photo/approve.png"
              mode="aspectFill"
              class="photo-image"
            />
            <image
              v-if="item.Approve_state == '合格'"
              src="/static/icon/photo/hg.png"
              mode="aspectFill"
              class="photo-image"
            />
            <image
              v-if="item.Approve_state == '不合格'"
              src="/static/icon/photo/noHg.png"
              mode="aspectFill"
              class="photo-image"
            />
          </view>
          <view class="photo-title" style="padding-top: 10px">
            {{ item.Task_name }}
          </view>
          <view class="photo-title" @click="() => showModal(item)">
            <a-button type="primary">查看照片</a-button></view
          >
          <view
            class="photo-title2"
            v-if="roleName == '公司运营' || roleName == '决策层'"
          >
            <a-button
              type="primary"
              @click="batchApprove2('合格', item)"
              :loading="item.loadTy"
              :disabled="item.Approve_state != '待审核'"
              >合格</a-button
            >
            <a-button
              type="primary"
              danger
              @click="batchApprove2('不合格', item)"
              :loading="item.loadBh"
              :disabled="item.Approve_state != '待审核'"
              >不合格</a-button
            ></view
          >
        </view>
      </view>
      <!-- 加载提示 -->
      <view v-if="isLoading" style="text-align: center; padding: 10px"
        >正在加载更多数据...</view
      >
      <view
        v-if="photoList.length == 0 && !isLoading"
        style="text-align: center; padding: 10px"
        >没有更多数据了</view
      >
    </view>

    <view style="padding: 14px; display: flex; justify-content: flex-end">
      <a-config-provider :locale="locale">
        <a-pagination
          v-model:current="current1"
          v-model:pageSize="pageSize"
          :total="totalNum"
          :showSizeChanger="true"
          :pageSizeOptions="['50', '100', '200']"
          @showSizeChange="onShowSizeChange"
          :responsive="false"
          :show-total="(total) => `共 ${totalNum} 张`"
        />
      </a-config-provider>
    </view>
  </view>

  <!-- 放大模态框 -->
  <a-modal v-model:visible="modalVisible" :footer="null" width="90vw">
    <div class="image-detail-container">
      <!-- 左侧图片 -->
      <div class="image-container">
        <image
          v-if="isPortrait"
          :src="currentImage.url"
          mode="widthFix"
          class="preview-image2"
        />
        <image
          v-if="!isPortrait"
          :src="currentImage.url"
          mode="widthFix"
          class="preview-image"
        />
      </div>

      <!-- 右侧信息 -->
      <div class="info-container">
        <div class="info-item">
          <span class="info-label">拍照人：</span>
          <span class="info-content">{{
            currentImage.Master_data_person_name
          }}</span>
        </div>
        <div style="width: 200px">
          <div class="info-item">
            <span class="info-label">门店名称：</span>
            <span class="info-content">{{ currentImage.Shop_name }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">照片检查任务名称：</span>
            <span class="info-content">{{ currentImage.Task_name }}</span>
          </div>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, reactive, onMounted, computed, watch } from "vue";
import zhCN from "ant-design-vue/es/locale/zh_CN"; // 引入中文包
import dayjs from "dayjs";
import "dayjs/locale/zh-cn"; // 引入 dayjs 中文语言包
import { message } from "ant-design-vue";
import { useUserInfo } from "/store/user/userInfo";
import { onShow, onLoad, onReachBottom } from "@dcloudio/uni-app";
import {
  get_user_xiangce,
  getAllRegionApi,
  getAllRegionApi_dqz,
  getAllProvincesApi,
  getAllJxqApi,
  getAllCarApi,
  get_all_xc,
  get_user_xiangce_user,
  getAllProvincesApi_sqz,
  getAllJxqApiCS,
  change_photo_status,
  getAllTaskApi,
} from "/api/workbench/PhotoCheck/index.js";

const userStore = useUserInfo();

const isLoading = ref(false);

// 人员角色
const roleName = ref("");
const userName = ref("");
onMounted(async () => {
  const userInfo = sessionStorage.getItem("userInfo");
  userStore.userInfo = JSON.parse(userInfo);
  roleName.value = userStore.userInfo.Role_grade;
  console.log("人物", userStore.userInfo);
  console.log("roleName.value", roleName.value);
  userName.value = userStore.userName;
  dateVal.value = dayjs();
  //   loadMore();
  current1.value = 1;
  getAllPhoto();
  if (
    roleName.value != "省区总" &&
    roleName.value != "经销商" &&
    roleName.value != "大区总"
  ) {
    getAllDq();
    getAllSq();
    getAllJxq();
    getAllTask();
  }
  if (roleName.value == "大区总") {
    getAllDqDqz(userStore.userInfo.id);
    getAllSqSQZ(userStore.userInfo.id);
    getAllJxqCS(userStore.userInfo.id);
    getAllTask();
  }
  if (roleName.value == "省区总") {
    getAllSqSQZ(userStore.userInfo.id);
    getAllJxqCS(userStore.userInfo.id);
    getAllTask();
  }
  if (roleName.value == "城市经理") {
    getAllJxqCS(userStore.userInfo.id);
    getAllTask();
  }
  uni.getSystemInfo({
    success: function (res) {
      // 判断设备平台
      if (res.platform === "windows" || res.platform === "mac") {
        ruleValue.value = 8;
      } else {
        ruleValue.value = 4;
      }
    },
    fail: function (err) {
      console.error("获取系统信息失败", err);
    },
  });

  // if (roleName.value != "省区总" && roleName.value != "经销商" && roleName.value != "大区总") {
  //
  // }else if(roleName.value == "大区总"){
  //   getAllDqDqz(userStore.userInfo.id);
  // }
  // if (roleName.value != "经销商" && roleName.value != "省区总") {
  //   getAllSq();
  // }else if(roleName.value == "省区总"){
  //   getAllSqSQZ(userStore.userInfo.id);
  // }
  // if (roleName.value != "经销商"){
  //   getAllJxq();
  // }else{
  //   getAllJxqCS(userStore.userInfo.id);
  // }
});

const isPortrait = ref();
const checkImageOrientation = async (Photo_url) => {
  const img = new Image();
  img.src = Photo_url;

  img.onload = () => {
    console.log("img.heighti", img.height);
    console.log("img.width", img.width);
    isPortrait.value = img.height > img.width;
  };
  console.log(isPortrait.value);
};
// 设置 dayjs 使用中文
dayjs.locale("zh-cn");
// 创建响应式的 locale 对象
const locale = ref(zhCN);

// 大区选择
const getAllDq = async () => {
  const { data } = await getAllRegionApi();
  console.log("data", data);
  if (data.code == 200 && data.result.length > 0) {
    dqOption.value = data.result.map((item) => {
      return {
        label: item.Organization_Name,
        value: item.id,
      };
    });
    dqOptionOld.value = data.result.map((item) => {
      return {
        label: item.Organization_Name,
        value: item.id,
      };
    });
  }
};

const getAllDqDqz = async (id) => {
  const { data } = await getAllRegionApi_dqz(id);
  console.log("data", data);
  if (data.code == 200 && data.result.length > 0) {
    dqOption.value = data.result.map((item) => {
      return {
        label: item.Organization_Name,
        value: item.id,
      };
    });
    dqOptionOld.value = data.result.map((item) => {
      return {
        label: item.Organization_Name,
        value: item.id,
      };
    });
    await getAllSq();
    await getAllJxq();
  }
};

const dqValue = ref("");
const dqOption = ref([]);
const dqOptionOld = ref([]);
const selectDq = (e) => {
  // console.log("e", e);
  page.value = 1;
  hasMore.value = true;
  photoList.value = [];
  current1.value = 1;
  getAllPhoto();
};
const dqChange = (value) => {
  if (value == null) {
    // console.log("用户清空了");
    dqValue.value = null;
    page.value = 1;
    hasMore.value = true;
    photoList.value = [];
    current1.value = 1;
    getAllPhoto();
  } else {
    console.log("用户选择了:", value);
    const filteredData = sqOptionOld.value.filter(
      (item) => item.Region == value
    );
    sqOption.value = filteredData;
    const filteredData2 = jxqOptionOld.value.filter(
      (item) => item.Region == value
    );
    jxqOption.value = filteredData2;
  }
};

// 省区选择
const getAllSq = async () => {
  const { data } = await getAllProvincesApi();
  console.log("data", data);
  if (data.code == 200 && data.result.length > 0) {
    sqOption.value = data.result.map((item) => {
      return {
        label: item.DistributionOrganizationName,
        value: item.id,
        Region: item.Region,
        Province: item.Province,
      };
    });
    sqOptionOld.value = data.result.map((item) => {
      return {
        label: item.DistributionOrganizationName,
        value: item.id,
        Region: item.Region,
        Province: item.Province,
      };
    });
  }
  if (roleName.value == "大区总") {
    const ids = dqOption.value.map((item) => item.value);
    const filteredData = sqOption.value.filter((item) =>
      ids.includes(item.Region)
    );
    sqOption.value = filteredData;
  }
};
const getAllSqSQZ = async (id) => {
  const { data } = await getAllProvincesApi_sqz(id);
  console.log("data", data);
  if (data.code == 200 && data.result.length > 0) {
    sqOption.value = data.result.map((item) => {
      return {
        label: item.Organization_Name,
        value: item.id,
        Region: item.Region,
        Province: item.Province,
      };
    });
    sqOptionOld.value = data.result.map((item) => {
      return {
        label: item.Organization_Name,
        value: item.id,
        Region: item.Region,
        Province: item.Province,
      };
    });
  }
};
const sqValue = ref("");
const sqOption = ref([]);
const sqOptionOld = ref([]);
const selectSq = (e) => {
  // console.log("e", e);
  page.value = 1;
  hasMore.value = true;
  current1.value = 1;
  photoList.value = [];
  getAllPhoto();
};
const sqChange = (value) => {
  if (value == null) {
    // console.log("用户清空了");
    sqValue.value = null;
    page.value = 1;
    hasMore.value = true;
    photoList.value = [];
    current1.value = 1;
    getAllPhoto();
  } else {
    console.log("用户选择了:", value);
    const filteredData2 = jxqOptionOld.value.filter(
      (item) => item.Province == value
    );
    jxqOption.value = filteredData2;
  }
};

// 经销区选择
const getAllJxq = async () => {
  const { data } = await getAllJxqApi();
  console.log("data", data);
  if (data.code == 200 && data.result.length > 0) {
    jxqOption.value = data.result.map((item) => {
      return {
        label: item.DistributionOrganizationName,
        value: item.id,
        Region: item.Region,
        Province: item.Province,
      };
    });
    jxqOptionOld.value = data.result.map((item) => {
      return {
        label: item.DistributionOrganizationName,
        value: item.id,
        Region: item.Region,
        Province: item.Province,
      };
    });
  }

  if (roleName.value == "大区总") {
    const ids = dqOption.value.map((item) => item.value);
    const filteredData = jxqOption.value.filter((item) =>
      ids.includes(item.Region)
    );
    jxqOption.value = filteredData;
  }
  if (roleName.value == "省区总") {
    const ids = sqOption.value.map((item) => item.value);
    const filteredData = jxqOption.value.filter((item) =>
      ids.includes(item.Province)
    );
    jxqOption.value = filteredData;
  }
};
const getAllJxqCS = async (id) => {
  const { data } = await getAllJxqApiCS(id);
  console.log("data222", data);
  if (data.code == 200 && data.result.length > 0) {
    jxqOption.value = data.result.map((item) => {
      return {
        label: item.DistributionOrganizationName,
        value: item.id,
        Region: item.Region,
        Province: item.Province,
      };
    });
    jxqOptionOld.value = data.result.map((item) => {
      return {
        label: item.DistributionOrganizationName,
        value: item.id,
        Region: item.Region,
        Province: item.Province,
      };
    });
  }
};
const jxqValue = ref("");
const jxqOption = ref([]);
const jxqOptionOld = ref([]);
const selectJxq = (e) => {
  // console.log("e", e);
  page.value = 1;
  hasMore.value = true;
  photoList.value = [];
  current1.value = 1;
  getAllPhoto();
};
const jxqChange = (value) => {
  if (value == null) {
    console.log("用户清空了");
    jxqValue.value = "";
    page.value = 1;
    hasMore.value = true;
    photoList.value = [];
    current1.value = 1;
    getAllPhoto();
  } else {
    console.log("用户选择了:", value);
  }
};

// 车长选择
const carValue = ref("");
const getAllCar = async () => {
  const { data } = await getAllCarApi(jxqValue.value);
  console.log("data", data);
  if (data.code == 200 && data.result.length > 0) {
    carOption.value = data.result.map((item) => {
      return {
        label: item.Master_data_person_name,
        value: item.id,
      };
    });
  }
};

const getCarlist = () => {
  console.log("点击");
  if (!jxqValue.value) {
    message.warning("请先选择经销区");
  } else {
    getAllCar();
  }
};
const carOption = ref([]);
const selectCar = (e) => {
  console.log("e", e);
  page.value = 1;
  hasMore.value = true;
  photoList.value = [];
  current1.value = 1;
  getAllPhoto();
};
const carChange = (value) => {
  if (value == null) {
    console.log("用户清空了");
    carValue.value = null;
    page.value = 1;
    hasMore.value = true;
    photoList.value = [];
    current1.value = 1;
    getAllPhoto();
  } else {
    console.log("用户选择了:", value);
  }
};

// 日期选择
const dateVal = ref(null);
const choose_date = () => {
  page.value = 1;
  current1.value = 1;
  hasMore.value = true;
  photoList.value = [];
  getAllPhoto();
};

// 排列规则
const ruleValue = ref("8");
const ruleOption = ref([
  {
    label: "4列",
    value: "4",
  },
  {
    label: "8列",
    value: "8",
  },
  {
    label: "16列",
    value: "16",
  },
]);
const selectRule = (e) => {
  console.log("e", e);
};

// 筛选规则
const statusRuleValue = ref("全部");
const statusRuleOption = ref([
  {
    label: "全部",
    value: "全部",
  },
  {
    label: "合格",
    value: "合格",
  },
  {
    label: "不合格",
    value: "不合格",
  },
  {
    label: "待审核",
    value: "待审核",
  },
]);
const selectStatusRule = (e) => {
  console.log("e", e);
  page.value = 1;
  current1.value = 1;
  hasMore.value = true;
  photoList.value = [];
  getAllPhoto();
};

// 照片任务选择
const taskValue = ref(null);
const taskOption = ref([]);
const getAllTask = async () => {
  const { data } = await getAllTaskApi();
  console.log("data", data);
  if (data.code == 200 && data.result.length > 0) {
    taskOption.value = data.result.map((item) => {
      return {
        label: item.Task_name,
        value: item.id,
      };
    });
  }
};
const selectTask = () => {
  page.value = 1;
  current1.value = 1;
  hasMore.value = true;
  photoList.value = [];
  getAllPhoto();
};
const taskChange = (value) => {
  if (value == null) {
    taskValue.value = null;
    page.value = 1;
    hasMore.value = true;
    photoList.value = [];
    current1.value = 1;
    getAllPhoto();
  }
};
// 图片列表显示
// 当前页码
const page = ref(1);
// 每页大小
const pageSize = ref(50);

const photoList = ref([]);

// 是否还有更多数据
const hasMore = ref(true);

// 照片数量
const photoNum = ref(0);
const photoShNum = ref(0);
const photoNshNum = ref(0);

// 获取所有相片
const getAllPhoto = async () => {
  if (!hasMore.value) return;
  let res;
  let dateNew = ref("");
  if (dateVal.value) {
    dateNew.value = dayjs(dateVal.value).format("YYYY-MM-DD");
  }
  if (carValue.value) {
    isLoading.value = true;
    // 车长有值
    res = await get_user_xiangce_user({
      user: userStore.userInfo.id,
      page: page.value,
      pagesize: pageSize.value,
      cz: carValue.value,
      time: dateNew.value,
      status_type: statusRuleValue.value,
      task_id:
        taskValue.value != "" && taskValue.value ? taskValue.value : "全部",
    });
    if (res.data.code == 200) {
      totalNum.value = res.data.result[0][0].total_count;
      if (res.data.result[1] && res.data.result[1].length > 0) {
        const newPhotos = res.data.result[1].map((item) => {
          return {
            ...item,
            loadTy: false,
            loadBh: false,
          };
        });
        photoList.value = newPhotos;
        console.log("列表数据", photoList.value);
      }
      photoNum.value = photoList.value.length;
      photoShNum.value = JSON.parse(JSON.stringify(photoList.value)).filter(
        (item) => item.Approve_state != "待审核" && item.Approve_state
      ).length;
      photoNshNum.value = JSON.parse(JSON.stringify(photoList.value)).filter(
        (item) => item.Approve_state == "待审核" || !item.Approve_state
      ).length;
      // 更新当前页中已选中的状态
      photoList.value.forEach((item) => {
        item.isSelected = selectedPhotoIds.value.includes(item.id);
      });
      isLoading.value = false;
    } else {
      isLoading.value = false;
      message.error("获取列表信息失败");
    }
  } else if (
    jxqValue.value ||
    sqValue.value ||
    dqValue.value ||
    dateVal.value
  ) {
    isLoading.value = true;

    res = await get_all_xc({
      dq: dqValue.value,
      sq: sqValue.value,
      jxq: jxqValue.value,
      user: userStore.userInfo.id,
      Role_name: roleName.value,
      page: page.value,
      pagesize: pageSize.value,
      time: dateNew.value,
      status_type: statusRuleValue.value,
      task_id:
        taskValue.value != "" && taskValue.value ? taskValue.value : "全部",
    });
    if (res.data.code == 200) {
      totalNum.value = res.data.result[0][0].total_count;
      if (res.data.result[1] && res.data.result[1].length > 0) {
        const newPhotos = res.data.result[1].map((item) => {
          return {
            ...item,
            loadTy: false,
            loadBh: false,
          };
        });
        photoList.value = newPhotos;
        console.log("列表数据", photoList.value);
      }
      photoNum.value = photoList.value.length;
      photoShNum.value = JSON.parse(JSON.stringify(photoList.value)).filter(
        (item) => item.Approve_state != "待审核" && item.Approve_state
      ).length;
      photoNshNum.value = JSON.parse(JSON.stringify(photoList.value)).filter(
        (item) => item.Approve_state == "待审核" || !item.Approve_state
      ).length;
      isLoading.value = false;
      // 更新当前页中已选中的状态
      photoList.value.forEach((item) => {
        item.isSelected = selectedPhotoIds.value.includes(item.id);
      });
    } else {
      isLoading.value = false;

      message.error("获取列表信息失败");
    }
  }
  console.log("res", res);
};

const modalVisible = ref(false);
const currentImage = ref({
  url: "",
  uploader: "",
});

// 修改showModal方法
const showModal = (item) => {
  currentImage.value = {
    url: item.Photo_url,
    Shop_name: item.Shop_name,
    Task_name: item.Task_name,
    Master_data_person_name: item.Master_data_person_name,
  };
  modalVisible.value = true;
  checkImageOrientation(item.Photo_url);
};

// ===================================切换页数事件===================================
const totalNum = ref(0);
const current1 = ref(1);
const onShowSizeChange = (current, pageSize) => {
  console.log(current, pageSize);
};
watch(pageSize, () => {
  console.log("pageSize", pageSize.value);
  page.value = 1;
  current1.value = 1;
  hasMore.value = true;
  photoList.value = [];
  getAllPhoto();
});
watch(current1, () => {
  page.value = current1.value;
  hasMore.value = true;
  photoList.value = [];
  getAllPhoto();
  isAllSelected.value = false;
  console.log("current", current1.value);
});

// 监听照片列表变化，更新全选状态
watch(photoList, (newList) => {
  if (newList.length === 0) {
    isAllSelected.value = false;
  } else {
    isAllSelected.value = newList
      .filter((item) => item.Approve_state == "待审核")
      .every((item) => selectedPhotoIds.value.includes(item.id));
  }
});

//=====================================审批数据事件====================================
const selectedPhotoIds = ref([]); // 已选中的照片 ID 数组
const isAllSelected = ref(false); // 全选状态
const togglePhotoSelection = (item) => {
  if (roleName.value == "公司运营" || roleName.value == "决策层") {
    if (item.Approve_state != "待审核") {
      return;
    }
    const index = selectedPhotoIds.value.indexOf(item.id);
    if (index === -1) {
      selectedPhotoIds.value.push(item.id);
    } else {
      selectedPhotoIds.value.splice(index, 1);
    }

    // 自动更新全选按钮状态
    isAllSelected.value =
      selectedPhotoIds.value.length ===
      photoList.value.filter((item) => item.Approve_state == "待审核").length;
  } else {
    showModal(item);
  }
};
const toggleAllSelection = () => {
  isAllSelected.value = !isAllSelected.value;
  if (isAllSelected.value) {
    // 全选当前页
    const currentIds = photoList.value
      .filter((item) => item.Approve_state == "待审核")
      .map((item) => item.id);

    selectedPhotoIds.value = [
      ...new Set([...selectedPhotoIds.value, ...currentIds]),
    ];
  } else {
    // 仅取消当前页的选择
    const currentIds = photoList.value
      .filter((item) => item.Approve_state == "待审核")
      .map((item) => item.id);
    selectedPhotoIds.value = selectedPhotoIds.value.filter(
      (id) => !currentIds.includes(id)
    );
  }
};

// 批量审批
const btnLoad1 = ref(false);
const btnLoad2 = ref(false);
const batchApprove = async (status) => {
  if (selectedPhotoIds.value.length === 0) {
    message.warning("请先选择照片");
    return;
  }
  if (status == "合格") {
    btnLoad1.value = true;
  } else if (status == "不合格") {
    btnLoad2.value = true;
  }
  const ids = selectedPhotoIds.value;
  const idObjects = ids.map((id) => ({ id }));

  const res = await change_photo_status({
    user_name: userName.value,
    photo_arr: idObjects,
    approve_type: status,
  });
  if (res.data.code == 200) {
    btnLoad1.value = false;
    btnLoad2.value = false;
    message.success("审批成功");
    hasMore.value = true;
    // photoList.value = [];
    getAllPhoto();
  } else {
    btnLoad1.value = false;
    btnLoad2.value = false;
    message.error("审批失败");
  }

  // 重置选择
  selectedPhotoIds.value = [];
  isAllSelected.value = false;
};
// 单独审批
const batchApprove2 = async (status, data) => {
  const idObjects = [
    {
      id: data.id,
    },
  ];

  if (status == "合格") {
    data.loadTy = true;
  } else if (status == "不合格") {
    data.loadBh = true;
  }
  const res = await change_photo_status({
    user_name: userName.value,
    photo_arr: idObjects,
    approve_type: status,
  });
  if (res.data.code == 200) {
    data.loadTy = false;
    data.loadBh = false;
    message.success("审批成功");
    hasMore.value = true;
    // photoList.value = [];
    getAllPhoto();
  } else {
    data.loadTy = false;
    data.loadBh = false;
    message.error("审批失败");
  }

  // 重置选择
  selectedPhotoIds.value = selectedPhotoIds.value.filter((id) => id != data.id);
  isAllSelected.value = false;
};

// 触底
onReachBottom(() => {
  console.log("触底了");
  getAllPhoto();
});
</script>

<style lang="scss" scoped>
.box {
  padding: 20px;
  height: 95vh;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  .head-class {
    display: flex;
    //   flex-direction: column;
    flex-wrap: wrap;
    gap: 10px;
    align-items: center;
    view {
      display: flex;
      align-items: center;
    }
  }
  .photo-part {
    margin-top: 20px;
    width: 100%;
    flex: 1;
    overflow-y: scroll;

    .empty-state {
      text-align: center;
      padding: 40px;
      color: #999;
    }

    .photo-list {
      display: grid;
      gap: 8px; /* 减小间隙 */
      min-width: 100%;
      grid-template-columns: repeat(
        auto-fill,
        minmax(60px, 1fr)
      ); /* 弹性列宽 */

      .photo-item {
        min-width: 0; /* 关键 */
        aspect-ratio: 3/4; /* 固定宽高比 */

        .photo-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .photo-title {
          font-size: 12px; /* 缩小字体 */
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          display: flex;
          justify-content: center;
          :deep(.ant-btn) {
            margin-top: 14px;
            width: 96% !important;
            background-color: #ff7875 !important;
          }
        }
        .photo-title2 {
          font-size: 12px; /* 缩小字体 */
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          display: flex;
          justify-content: space-between;
          :deep(.ant-btn) {
            margin-top: 14px;
            width: 46% !important;
          }
        }
      }
    }
  }
}
.image-detail-container {
  display: flex;
}

.image-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  border-right: 1px solid #f0f0f0;
}
.preview-image {
  max-width: 100%;
  height: auto;
  width: 100%;
}
.preview-image2 {
  max-height: 80vh; /* 稍微小一点，避免被浏览器限制 */

  display: block;
  margin: 0 auto;
}

.info-container {
  flex: 0 0 300px;
  padding: 20px;
  overflow-y: auto;
}

.info-item {
  margin-bottom: 16px;
}

.info-label {
  font-weight: bold;
  color: #666;
}

.info-content {
  color: #333;
}
.image-detail-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.image-container,
.info-container {
  flex: 1;
  min-width: 220px; /* 最小宽度，防止过小 */
}
.preview-image {
  max-width: 100%;
  height: auto;
  width: 100%;
}
.preview-image2 {
  max-height: 80vh; /* 稍微小一点，避免被浏览器限制 */

  display: block;
  margin: 0 auto;
}

.info-content {
  white-space: normal;
  word-break: break-word;
}

/* 小屏幕适配 */
@media (max-width: 768px) {
  .image-detail-container {
    flex-direction: column;
  }

  .image-container,
  .info-container {
    width: 100%;
  }
}

:deep(.a-modal) {
  max-width: 90vw;
  width: 90vw;
}

.image-detail-container {
  display: flex;
  flex-wrap: wrap; // 保留换行能力
  gap: 10px;
}

.image-container {
  flex: 1; // 占据剩余空间
  min-width: 0; // 允许弹性收缩
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  border-right: 1px solid #f0f0f0;
}
.preview-image {
  max-width: 100%;
  height: auto;
  width: 100%;
}
.preview-image2 {
  max-height: 80vh; /* 稍微小一点，避免被浏览器限制 */

  display: block;
  margin: 0 auto;
}

.info-container {
  flex: 0 0 300px; // 固定宽度为100px
  min-width: 100px; // 最小宽度限制
  padding: 20px;
  overflow-y: auto;
}

.photo-item {
  position: relative; /* 确保子元素可以绝对定位 */
  padding: 5px; /* 为标签留出空间 */
}

.status-tag {
  position: absolute;
  top: 5px;
  right: 5px;
  color: white;
  border-radius: 4px;
}

// 选择照片样式
.photo-image.selected {
  border: 3px solid #1890ff; /* 边框 */
  box-shadow: 4px 4px 8px rgba(24, 144, 255, 0.5); /* 阴影 */
  transform: scale(1.05); /* 微微放大 */
  transition: all 0.3s ease; /* 动画 */
  border-radius: 12px;
  padding: 4px;
}

/* 全选按钮样式 */
.select-all {
  margin-bottom: 10px;
}

/* 移除原有的300px固定宽度定义 */
// 原来的 .info-container { flex: 0 0 300px; } 需要删除或覆盖

/* 小屏幕适配 */
@media (max-width: 768px) {
  .image-detail-container {
    flex-direction: column;
  }

  .image-container,
  .info-container {
    width: 100%;
    flex: 1 1 100%; // 强制全宽
  }
  .ant-pagination {
    flex-wrap: wrap;
  }
  .ant-pagination-options {
    display: block !important;
    width: 100%;
  }
}
</style>
