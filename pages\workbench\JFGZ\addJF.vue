<template>
  <div class="page-container">
    <!-- 表单内容区域 -->
    <div class="form-container">
      <a-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        layout="vertical"
        :label-col="{ span: 24 }"
        :wrapper-col="{ span: 24 }"
      >
        <!-- 基础信息组 -->
        <div class="form-section">
          <div class="section-title">
            <span class="title-text">基础信息</span>
            <div class="title-line"></div>
          </div>
          <div class="section-content">
            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-item label="积分规则" name="ruleName">
                  <a-input
                    v-model:value="formData.ruleName"
                    placeholder="请输入积分规则"
                    :maxlength="100"
                    show-count
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="积分编码" name="ruleCode">
                  <a-input
                    v-model:value="formData.ruleCode"
                    placeholder="自动生成"
                    disabled
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-item label="年份" name="year">
                  <a-select
                    v-model:value="formData.year"
                    placeholder="请选择年份"
                    :options="yearOptions"
                    @change="handleYearChange"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="月份" name="month">
                  <a-select
                    v-model:value="formData.month"
                    placeholder="请选择月份"
                    :options="monthOptions"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </div>

        <!-- 区域信息组 -->
        <div class="form-section">
          <div class="section-title">
            <span class="title-text">区域信息</span>
            <div class="title-line"></div>
          </div>
          <div class="section-content">
            <a-row :gutter="24">
              <a-col :span="8">
                <a-form-item label="级别" name="level">
                  <a-select
                    v-model:value="formData.level"
                    placeholder="请选择级别"
                    :options="levelOptions"
                    :disabled="formData.scoreType === '物料投放' || formData.scoreType === '返货'"
                    @change="handleLevelChange"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="区域名称" name="areaName">
                  <a-input
                    v-model:value="formData.areaName"
                    placeholder="请选择区域名称"
                    readonly
                    @click="handleAreaSelect"
                    style="cursor: pointer"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="区域编码" name="areaCode">
                  <a-input
                    v-model:value="formData.areaCode"
                    placeholder="根据选择的区域自动填入"
                    disabled
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </div>

        <!-- 积分配置组 -->
        <div class="form-section">
          <div class="section-title">
            <span class="title-text">积分配置</span>
            <div class="title-line"></div>
          </div>
          <div class="section-content">
            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-item label="积分类型" name="scoreType">
                  <a-select
                    v-model:value="formData.scoreType"
                    placeholder="请选择积分类型"
                    :options="scoreTypeOptions"
                    @change="handleScoreTypeChange"
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <!-- 拍照检查任务区域 -->
            <div v-if="showPhotoCheckFields" class="photo-check-fields">
              <div class="photo-check-header">
                <span class="header-title">拍照检查任务</span>

                <!-- 加载状态 -->
                <div v-if="taskDataLoading" class="loading-container">
                  <a-spin size="small" />
                  <span class="loading-text">正在加载任务列表...</span>
                </div>

                <!-- 添加任务按钮 - 只有在数据加载完成后显示 -->
                <a-button
                  v-else-if="taskList.length > 0"
                  type="primary"
                  @click="handleAddTask"
                  :loading="taskLoading"
                >
                  <template #icon>
                    <plus-outlined />
                  </template>
                  添加任务
                </a-button>

                <!-- 无任务数据提示 -->
                <span v-else class="no-task-text">暂无可用任务</span>
              </div>

              <!-- 已选择的任务列表 -->
              <div v-if="selectedTasks.length > 0" class="selected-tasks">
                <div class="selected-tasks-title">已选择的任务：</div>
                <div class="task-list">
                  <a-tag
                    v-for="task in selectedTasks"
                    :key="task.Task_code"
                    closable
                    @close="removeTask(task.Task_code)"
                    class="task-tag"
                  >
                    {{ task.Task_name }}
                  </a-tag>
                </div>
              </div>
            </div>

            <!-- 物料投放字段 -->
            <div v-if="showMaterialFields" class="material-fields">
              <div class="material-header">
                <span class="header-title">物料投放单据</span>
              </div>

              <a-row :gutter="24">
                <a-col :span="12">
                  <a-form-item label="选择日期" name="materialDate">
                    <a-date-picker
                      v-model:value="formData.materialDate"
                      placeholder="请选择日期"
                      style="width: 100%"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                      @change="handleMaterialDateChange"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="选择单据">
                    <a-button
                      type="primary"
                      @click="handleSelectMaterial"
                      :loading="materialLoading"
                      :disabled="!formData.materialDate"
                      style="height: 32px;"
                    >
                      选择单据
                    </a-button>
                  </a-form-item>
                </a-col>
              </a-row>

              <!-- 已选择的单据信息 -->
              <div v-if="formData.materialRecord" class="selected-material">
                <div class="selected-material-title">已选择的单据：</div>
                <div class="material-info">
                  <div class="material-code">
                    <span class="label">物料申请编码：</span>
                    <span class="value">{{ formData.materialRecord.Small_boss_materiel_apply_code }}</span>
                  </div>
                  <div class="material-amount">
                    <span class="label">金额总计：</span>
                    <span class="value">{{ formData.materialRecord.total_amount }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 业务动作的动态字段 -->
            <div v-if="showBusinessActionFields" class="business-action-fields">
              <a-row :gutter="24">
                <a-col :span="8">
                  <a-form-item label="规则类型" name="ruleType">
                    <a-select
                      v-model:value="formData.ruleType"
                      placeholder="请选择规则类型"
                      :options="ruleTypeOptions"
                      @change="handleRuleTypeChange"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="运算符" name="operator">
                    <a-select
                      v-model:value="formData.operator"
                      placeholder="请选择运算符"
                      :options="operatorOptions"
                      :disabled="formData.ruleType && (formData.ruleType === '出车时间' || formData.ruleType === '首店时间' || formData.ruleType === '行驶里程' || formData.ruleType === '成交家数')"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="阈值" name="threshold">
                    <!-- 时间类型的下拉选择 -->
                    <a-select
                      v-if="isTimeType"
                      v-model:value="formData.threshold"
                      placeholder="请选择时间"
                      :options="timeOptions"
                    />
                    <!-- 数值类型的输入框 -->
                    <a-input-number
                      v-else
                      v-model:value="formData.threshold"
                      :min="thresholdConfig.min"
                      :max="thresholdConfig.max"
                      :precision="thresholdConfig.precision"
                      :placeholder="thresholdConfig.placeholder"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </div>

            <!-- 空白门店开拓字段 -->
            <div v-if="showBlankStoreFields" class="blank-store-fields">
              <div class="blank-store-header">
                <span class="header-title">空白门店开拓配置</span>
                <a-button
                  type="primary"
                  @click="handleSelectPlanningArea"
                >
                  选择规划区域
                </a-button>
              </div>

              <!-- 已选择的规划区域信息 -->
              <div v-if="formData.planningAreaInfo" class="selected-planning-area">
                <div class="selected-area-title">已选择的规划区域：</div>
                <div class="area-info">
                  <div class="area-coordinates">
                    <span class="label">经纬度范围：</span>
                    <span class="value">{{ formData.planningAreaInfo.coordinateRange }}</span>
                  </div>
                  <div class="area-size">
                    <span class="label">区域面积：</span>
                    <span class="value">{{ formData.planningAreaInfo.acreage }}㎡</span>
                  </div>
                  <div class="area-target">
                    <span class="label">完成目标销售额：</span>
                    <span class="value">{{ formData.planningAreaInfo.targetSales }}</span>
                  </div>
                  <div class="area-validity">
                    <span class="label">有效期：</span>
                    <span class="value">{{ formData.planningAreaInfo.validYear }}年{{ formData.planningAreaInfo.validMonth }}月</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 积分字段 - 返货类型时隐藏 -->
            <a-row :gutter="24" v-if="formData.scoreType !== '返货'">
              <a-col :span="12">
                <a-form-item label="最大获得积分" name="maxScore">
                  <a-input-number
                    v-model:value="formData.maxScore"
                    :min="0"
                    :max="999999"
                    :precision="0"
                    placeholder="请输入最大获得积分"
                    :disabled="showMaterialFields && formData.materialRecord || showBlankStoreFields"
                    :readonly="showBlankStoreFields"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="每次获得积分" name="perScore">
                  <a-input-number
                    v-model:value="formData.perScore"
                    :min="0"
                    :max="999999"
                    :precision="0"
                    :placeholder="showMaterialFields && formData.materialRecord ? '投放物料分配积分' : showBlankStoreFields ? '自动填充目标销售额' : '请输入每次获得积分'"
                    :disabled="showMaterialFields && formData.materialRecord || showBlankStoreFields"
                    :readonly="showBlankStoreFields"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </div>

        <!-- 规则描述组 -->
        <div class="form-section">
          <div class="section-title">
            <span class="title-text">规则描述</span>
            <div class="title-line"></div>
          </div>
          <div class="section-content">
            <a-form-item label="获取积分规则" name="scoreRule">
              <a-textarea
                v-model:value="formData.scoreRule"
                placeholder="请输入获取积分规则"
                :rows="4"
                :maxlength="500"
                show-count
              />
            </a-form-item>
          </div>
        </div>

        <!-- 表单按钮 -->
        <div class="form-actions">
          <a-space size="large">
            <a-button
              type="primary"
              @click="handleSubmit"
              :loading="submitLoading"
              size="large"
            >
              提交
            </a-button>
            <a-button @click="handleReset" size="large"> 重置 </a-button>
          </a-space>
        </div>
      </a-form>
    </div>

    <!-- 区域选择弹窗 -->
    <CustomModal
      :show="showAreaModal"
      :title="areaModalTitle"
      :width="800"
      @close="showAreaModal = false"
    >
      <MultiSelectListOne
        :items="areaOptions"
        :items2="areaHeaders"
        :single-select="true"
        @update:confirm="handleAreaConfirm"
      />
    </CustomModal>

    <!-- 拍照检查任务选择弹窗 -->
    <a-modal
      v-model:open="showTaskModal"
      title="选择拍照检查任务"
      :width="1000"
      @ok="handleTaskConfirm"
      @cancel="handleTaskCancel"
      :confirm-loading="taskConfirmLoading"
    >
      <div class="task-modal-content">
        <a-spin :spinning="taskDataLoading">
          <div v-if="taskList.length > 0" class="task-table-container">
            <a-table
              :columns="taskColumns"
              :data-source="taskList"
              :pagination="false"
              row-key="Task_code"
              :row-selection="{
                selectedRowKeys: selectedTaskKeys,
                onChange: handleTaskSelectionChange,
                type: 'checkbox',
              }"
              size="middle"
            >
              <template #bodyCell="{ column, record }">
                <template
                  v-if="
                    column.key === 'Start_time' || column.key === 'End_time'
                  "
                >
                  {{ formatDateTime(record[column.key]) }}
                </template>
                <template v-else-if="column.key === 'Photo_require'">
                  <a-tooltip :title="record.Photo_require">
                    <div class="text-ellipsis">{{ record.Photo_require }}</div>
                  </a-tooltip>
                </template>
                <template v-else-if="column.key === 'Release_scope'">
                  <a-tooltip :title="record.Release_scope">
                    <div class="text-ellipsis">{{ record.Release_scope }}</div>
                  </a-tooltip>
                </template>
              </template>
            </a-table>
          </div>
          <div v-else class="empty-state">
            <a-empty description="暂无任务数据" />
          </div>
        </a-spin>
      </div>
    </a-modal>

    <!-- 物料投放单据选择弹窗 -->
    <CustomModal
      :show="showMaterialModal"
      title="选择物料投放单据"
      :width="800"
      @close="showMaterialModal = false"
    >
      <MultiSelectListOne
        :items="materialList"
        :items2="materialHeaders"
        :single-select="true"
        @update:confirm="handleMaterialConfirm"
      />
    </CustomModal>

    <!-- 规划区域选择弹窗 -->
    <a-modal
      v-model:open="showPlanningModal"
      title="选择规划区域"
      :width="1600"
      :height="1000"
      :footer="null"
      :mask-closable="false"
    >
      <PlanningAreaSelector
        v-if="showPlanningModal"
        ref="planningAreaRef"
        :visible="showPlanningModal"
        @area-selected="handleAreaSelected"
        @close-modal="showPlanningModal = false"
      />
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from "vue";
import { message } from "ant-design-vue";
import { PlusOutlined } from "@ant-design/icons-vue";
import dayjs from "dayjs";
import { useUserInfo } from "/store/user/userInfo";
import {
  getAllRegionApi,
  getAllRegionApi_dqz,
  getAllProvincesApi,
  getAllJxqApi,
  getProvinceApi_dqz,
  getJxqApi_dqz,
  getDealerListApi,
} from "/api/workbench/storeCarManage/index.js";

import { addScoreRuleApi, photoCheckApi, materialRecordApi } from "/api/workbench/JFGZ/index.js";
import CustomModal from "/pages/workbench/partnerSign/components/CustomModal.vue";
import MultiSelectListOne from "/pages/workbench/partnerSign/components/MultiSelectListOne.vue";
import PlanningAreaSelector from "./components/PlanningAreaSelector.vue";

const userStore = useUserInfo();

// 表单引用
const formRef = ref();

// 表单数据
const formData = reactive({
  ruleName: "",
  ruleCode: "",
  year: null,
  month: null,
  level: "",
  areaName: "",
  areaCode: "",
  areaId: "",
  scoreType: "",
  ruleType: "",
  operator: "",
  threshold: null,
  maxScore: 0,
  perScore: 0,
  scoreRule: "",
  selectedTasks: [], // 选中的拍照检查任务
  materialDate: null, // 物料投放日期
  materialRecord: null, // 选中的物料单据
  material_record_id: "", // 物料单据ID
  material_code: "", // 物料申请编码
  ERP_code: "", // 人员ERP编码
  // 空白门店开拓相关字段
  targetSales: null, // 完成目标销售额
  validYear: null, // 有效年
  validMonth: null, // 有效月
  planningAreaInfo: null, // 规划区域信息
  planningAreaPoints: null, // 规划区域坐标点
});

// 用户角色信息
const userRole = ref("");
const userId = ref("");

// 弹窗相关
const showAreaModal = ref(false);
const areaModalTitle = ref("选择区域");
const areaOptions = ref([]);
const areaHeaders = ref([]);
const submitLoading = ref(false);

// 拍照检查任务相关
const showTaskModal = ref(false);
const taskList = ref([]);
const selectedTasks = ref([]);
const selectedTaskKeys = ref([]);
const taskLoading = ref(false);
const taskDataLoading = ref(false);
const taskConfirmLoading = ref(false);

// 物料投放相关
const showMaterialModal = ref(false);
const materialList = ref([]);
const materialHeaders = ref([
  { value: 1, label: "物料申请编码", label2: "积分总计", label3: "" }
]);
const materialLoading = ref(false);

// 规划区域相关
const showPlanningModal = ref(false);
const planningAreaRef = ref();
const planningConfirmLoading = ref(false);

// 生成积分编码
const generateRuleCode = () => {
  const now = dayjs();
  return "JF" + now.format("YYYYMMDDHHmmss");
};

// 年份选项（从当前年份开始，往后5年）
const yearOptions = computed(() => {
  const currentYear = dayjs().year();
  const years = [];
  for (let i = currentYear; i <= currentYear + 5; i++) {
    years.push({ label: i.toString(), value: i });
  }
  return years;
});

// 月份选项（根据选择的年份动态过滤）
const monthOptions = computed(() => {
  const currentYear = dayjs().year();
  const currentMonth = dayjs().month() + 1; // dayjs的月份从0开始，需要+1
  const months = [];

  // 如果选择的是当前年份，只显示当前月份及以后的月份
  if (formData.year === currentYear) {
    for (let i = currentMonth; i <= 12; i++) {
      months.push({ label: i + "月", value: i });
    }
  } else {
    // 如果选择的是未来年份，显示所有月份
    for (let i = 1; i <= 12; i++) {
      months.push({ label: i + "月", value: i });
    }
  }

  return months;
});

// 级别选项（根据角色权限和积分类型动态生成）
const levelOptions = computed(() => {
  const options = [];

  // 如果积分类型是物料投放或返货，只能选择经销商
  if (formData.scoreType === "物料投放" || formData.scoreType === "返货") {
    options.push({ label: "经销商", value: "经销商" });
    return options;
  }

  // 其他积分类型按角色权限生成选项
  if (userRole.value === "决策层") {
    options.push({ label: "公司", value: "公司" });
  }

  if (["决策层", "it信息部", "渠道运营", "大区总"].includes(userRole.value)) {
    options.push({ label: "大区", value: "大区" });
  }

  if (
    ["决策层", "it信息部", "渠道运营", "大区总", "省区总"].includes(
      userRole.value
    )
  ) {
    options.push({ label: "省区", value: "省区" });
  }

  options.push({ label: "经销区", value: "经销区" });
  options.push({ label: "经销商", value: "经销商" });

  return options;
});

// 积分类型选项
const scoreTypeOptions = ref([
  { label: "市场活动", value: "市场活动" },
  { label: "业务动作", value: "业务动作" },
  { label: "拍照检查", value: "拍照检查" },
  { label: "空白门店开拓", value: "空白门店开拓" },
  { label: "物料投放", value: "物料投放" },
  { label: "返货", value: "返货" },
]);

// 规则类型选项
const ruleTypeOptions = ref([
  { label: "出车时间", value: "出车时间" },
  { label: "首店时间", value: "首店时间" },
  { label: "行驶里程", value: "行驶里程" },
  { label: "成交家数", value: "成交家数" },
]);

// 运算符选项（根据规则类型动态生成）
const operatorOptions = computed(() => {
  if (formData.ruleType === "出车时间" || formData.ruleType === "首店时间") {
    return [{ label: "小于", value: "小于" }];
  } else if (formData.ruleType === "行驶里程" || formData.ruleType === "成交家数") {
    return [{ label: "大于", value: "大于" }];
  } else {
    // 默认情况或未选择规则类型时显示所有选项
    return [
      { label: "大于", value: "大于" },
      { label: "小于", value: "小于" },
    ];
  }
});

// 时间选项（1-24小时）
const timeOptions = computed(() => {
  const options = [];
  for (let i = 1; i <= 24; i++) {
    options.push({ label: `${i}点`, value: `${i}` });
  }
  return options;
});

// 是否显示业务动作的动态字段
const showBusinessActionFields = computed(() => {
  return formData.scoreType === "业务动作";
});

// 是否显示拍照检查字段
const showPhotoCheckFields = computed(() => {
  return formData.scoreType === "拍照检查";
});

// 是否显示物料投放字段
const showMaterialFields = computed(() => {
  return formData.scoreType === "物料投放" && formData.level === "经销商" && formData.areaCode;
});

// 是否显示空白门店开拓字段
const showBlankStoreFields = computed(() => {
  return formData.scoreType === "空白门店开拓";
});



// 获取上个月1号的日期
const getLastMonthFirstDay = () => {
  const now = dayjs();
  return now.subtract(1, 'month').startOf('month').format('YYYY-MM-DD');
};

// 任务表格列定义
const taskColumns = [
  {
    title: "任务代码",
    dataIndex: "Task_code",
    key: "Task_code",
    width: 120,
  },
  {
    title: "任务名称",
    dataIndex: "Task_name",
    key: "Task_name",
    width: 150,
  },
  {
    title: "拍照要求",
    dataIndex: "Photo_require",
    key: "Photo_require",
    width: 200,
    ellipsis: true,
  },
  {
    title: "开始时间",
    dataIndex: "Start_time",
    key: "Start_time",
    width: 150,
  },
  {
    title: "结束时间",
    dataIndex: "End_time",
    key: "End_time",
    width: 150,
  },
  {
    title: "发布范围",
    dataIndex: "Release_scope",
    key: "Release_scope",
    width: 200,
    ellipsis: true,
  },
];

// 是否为时间类型
const isTimeType = computed(() => {
  return ["出车时间", "首店时间"].includes(formData.ruleType);
});

// 阈值配置
const thresholdConfig = computed(() => {
  if (formData.ruleType === "行驶里程") {
    return {
      min: 0,
      max: 9999,
      precision: 1,
      placeholder: "请输入里程数（公里）",
    };
  } else if (formData.ruleType === "成交家数") {
    return {
      min: 0,
      max: 999,
      precision: 0,
      placeholder: "请输入成交家数",
    };
  }
  return {
    min: 0,
    max: 999,
    precision: 0,
    placeholder: "请输入数值",
  };
});

// 自定义验证器
const validateScoreRange = (_rule, value) => {
  return new Promise((resolve, reject) => {
    if (value === null || value === undefined) {
      reject(new Error("请输入积分值"));
    } else if (value < 0) {
      reject(new Error("积分值不能小于0"));
    } else if (value > 999999) {
      reject(new Error("积分值不能超过999999"));
    } else if (!Number.isInteger(value)) {
      reject(new Error("积分值必须为整数"));
    } else {
      resolve();
    }
  });
};

const validateScoreComparison = (_rule, value) => {
  return new Promise((resolve, reject) => {
    if (value === null || value === undefined) {
      resolve();
      return;
    }

    // 检查每次获得积分不能大于最大获得积分
    if (formData.maxScore !== null && formData.perScore !== null) {
      if (formData.perScore > formData.maxScore) {
        reject(new Error("每次获得积分不能大于最大获得积分"));
      } else {
        resolve();
      }
    } else {
      resolve();
    }
  });
};

// 表单验证规则
const rules = computed(() => ({
  ruleName: [
    { required: true, message: "请输入积分规则", trigger: "blur" },
    { min: 2, message: "积分规则至少需要2个字符", trigger: "blur" },
    { max: 100, message: "积分规则不能超过100个字符", trigger: "blur" },
    {
      pattern: /^[\u4e00-\u9fa5a-zA-Z0-9\s\-_()（）]+$/,
      message: "积分规则只能包含中文、英文、数字、空格和常用符号",
      trigger: "blur",
    },
  ],
  year: [{ required: true, message: "请选择年份", trigger: "change" }],
  month: [{ required: true, message: "请选择月份", trigger: "change" }],
  level: [{ required: true, message: "请选择级别", trigger: "change" }],
  areaName: [{ required: true, message: "请选择区域名称", trigger: "blur" }],
  scoreType: [{ required: true, message: "请选择积分类型", trigger: "change" }],
  ruleType: showBusinessActionFields.value
    ? [{ required: true, message: "请选择规则类型", trigger: "change" }]
    : [],
  operator: showBusinessActionFields.value
    ? [{ required: true, message: "请选择运算符", trigger: "change" }]
    : [],
  threshold: showBusinessActionFields.value
    ? [{ required: true, message: "请输入阈值", trigger: "blur" }]
    : [],
  maxScore: formData.scoreType !== "返货" ? [
    { required: true, message: "请输入最大获得积分", trigger: "blur" },
    { validator: validateScoreRange, trigger: "blur" },
    { validator: validateScoreComparison, trigger: "blur" },
  ] : [],
  perScore: formData.scoreType !== "返货" ? [
    { required: true, message: "请输入每次获得积分", trigger: "blur" },
    { validator: validateScoreRange, trigger: "blur" },
    { validator: validateScoreComparison, trigger: "blur" },
  ] : [],
  scoreRule: [
    { required: true, message: "请输入获取积分规则", trigger: "blur" },
    { min: 5, message: "获取积分规则至少需要5个字符", trigger: "blur" },
    { max: 500, message: "获取积分规则不能超过500个字符", trigger: "blur" },
  ],
  // 空白门店开拓验证规则
  targetSales: showBlankStoreFields.value ? [
    { required: true, message: "请输入完成目标销售额", trigger: "blur" },
    { type: "number", min: 0, message: "完成目标销售额不能小于0", trigger: "blur" },
    { type: "number", max: 99999999, message: "完成目标销售额不能超过99999999", trigger: "blur" },
  ] : [],
  validYear: showBlankStoreFields.value ? [
    { required: true, message: "请选择有效年", trigger: "change" }
  ] : [],
  validMonth: showBlankStoreFields.value ? [
    { required: true, message: "请选择有效月", trigger: "change" }
  ] : [],
}));

// 年份变化处理
const handleYearChange = (value) => {
  const currentYear = dayjs().year();
  const currentMonth = dayjs().month() + 1;

  // 如果选择的是当前年份，且当前选择的月份小于当前月份，则清空月份选择
  if (
    value === currentYear &&
    formData.month &&
    formData.month < currentMonth
  ) {
    formData.month = null;
  }

  console.log("年份变化:", value);
};

// 级别变化处理
const handleLevelChange = (value) => {
  // 如果积分类型是物料投放或返货，不允许改变级别
  if (formData.scoreType === "物料投放" || formData.scoreType === "返货") {
    if (value !== "经销商") {
      message.warning("物料投放和返货类型只能选择经销商级别");
      formData.level = "经销商";
      return;
    }
  }

  // 清空区域相关字段
  formData.areaName = "";
  formData.areaCode = "";
  formData.areaId = "";
  formData.ERP_code = "";

  console.log("级别变化:", value);
};

// 积分类型变化处理
const handleScoreTypeChange = async (value) => {
  // 如果不是业务动作，清空相关字段
  if (value !== "业务动作") {
    formData.ruleType = "";
    formData.operator = "";
    formData.threshold = null;
  }

  // 如果不是拍照检查，清空任务相关字段
  if (value !== "拍照检查") {
    selectedTasks.value = [];
    formData.selectedTasks = [];
    taskList.value = [];
  } else {
    // 如果是拍照检查，立即加载任务列表
    await loadTaskList();
  }

  // 如果不是空白门店开拓，清空相关字段
  if (value !== "空白门店开拓") {
    formData.targetSales = null;
    formData.validYear = null;
    formData.validMonth = null;
    formData.planningAreaInfo = null;
    formData.planningAreaPoints = null;
  } else {
    // 如果是空白门店开拓，设置默认年份为当前年份
    formData.validYear = dayjs().year();
  }

  // 如果是物料投放或返货，强制设置级别为经销商并清空区域信息
  if (value === "物料投放" || value === "返货") {
    formData.level = "经销商";
    // 清空区域相关字段，需要重新选择
    formData.areaName = "";
    formData.areaCode = "";
    formData.areaId = "";
    formData.ERP_code = "";

    // 如果是物料投放，设置默认日期为上个月1号
    if (value === "物料投放") {
      formData.materialDate = getLastMonthFirstDay();
    }

    // 如果是返货，清空积分相关字段
    if (value === "返货") {
      formData.maxScore = 0;
      formData.perScore = 0;
    }
  } else {
    // 如果不是物料投放，清空相关字段
    formData.materialDate = null;
    formData.materialRecord = null;
    formData.material_record_id = "";
    formData.material_code = "";
    materialList.value = [];
  }

  console.log("积分类型变化:", value);
};

// 物料投放日期变化处理
const handleMaterialDateChange = (date) => {
  formData.materialDate = date;
  // 清空已选择的单据
  formData.materialRecord = null;
  formData.material_record_id = "";
  formData.material_code = "";
  formData.maxScore = null;
  console.log("物料投放日期变化:", date);
};

// 选择物料投放单据
const handleSelectMaterial = async () => {
  if (!formData.materialDate) {
    message.error("请先选择日期");
    return;
  }

  if (!formData.areaCode) {
    message.error("请先选择区域");
    return;
  }

  try {
    materialLoading.value = true;

    // 构造API参数
    const params = {
      code: formData.ERP_code,
      startDate: `${formData.materialDate} 00:00:00`
    };

    console.log("调用materialRecordApi参数:", params);

    const response = await materialRecordApi(params);

    if (response.data.code === 200 && response.data.result) {
      // 转换数据格式以适配MultiSelectListOne组件
      materialList.value = response.data.result.map(item => ({
        value: item.id,
        label: item.Small_boss_materiel_apply_code,
        label2: item.total_amount.toString(),
        originalData: item
      }));

      if (materialList.value.length > 0) {
        showMaterialModal.value = true;
      } else {
        message.warning("暂无物料投放单据数据");
      }
    } else {
      message.warning("暂无物料投放单据数据");
      materialList.value = [];
    }
  } catch (error) {
    console.error("获取物料投放单据失败:", error);
    message.error("获取物料投放单据失败");
    materialList.value = [];
  } finally {
    materialLoading.value = false;
  }
};

// 物料投放单据确认选择
const handleMaterialConfirm = (item) => {
  formData.materialRecord = item.originalData;
  formData.material_record_id = item.originalData.id;
  formData.material_code = item.originalData.Small_boss_materiel_apply_code;
  formData.maxScore = item.originalData.total_amount;
  formData.perScore = 0;

  showMaterialModal.value = false;
  message.success("物料投放单据选择成功");
  console.log("选择的物料投放单据:", item.originalData);
};



// 选择规划区域
const handleSelectPlanningArea = () => {
  showPlanningModal.value = true;
};

// 规划区域选择回调
const handleAreaSelected = (areaInfo) => {
  formData.planningAreaInfo = areaInfo;
  formData.planningAreaPoints = areaInfo.planningAreaPoints;
  formData.targetSales = areaInfo.targetSales;
  formData.validYear = areaInfo.validYear;
  formData.validMonth = areaInfo.validMonth;
  formData.maxScore = areaInfo.targetSales;
  formData.perScore = areaInfo.targetSales
  formData.sellWarehouseId = areaInfo.areaId;
  
  console.log("选择的规划区域:", areaInfo);
};


// 规则类型变化处理
const handleRuleTypeChange = (value) => {
  // 清空阈值
  formData.threshold = null;

  // 根据规则类型自动设置运算符
  if (value === "出车时间" || value === "首店时间") {
    formData.operator = "小于";
  } else if (value === "行驶里程" || value === "成交家数") {
    formData.operator = "大于";
  } else {
    // 清空运算符，让用户选择
    formData.operator = "";
  }

  console.log("规则类型变化:", value, "自动设置运算符:", formData.operator);
};

// 添加任务按钮处理
const handleAddTask = () => {
  try {
    // 同步已选择任务的状态到表格选择
    selectedTaskKeys.value = selectedTasks.value.map(task => task.Task_code);
    showTaskModal.value = true;
    console.log('打开任务选择弹窗，当前已选择的任务:', selectedTaskKeys.value);
  } catch (error) {
    console.error("打开任务选择弹窗失败:", error);
    message.error("打开任务选择失败");
  }
};

// 加载任务列表
const loadTaskList = async () => {
  try {
    taskDataLoading.value = true;

    // 构造当前日期 + "00:00:00" 格式
    const currentDate = dayjs().format("YYYY-MM-DD");
    const dateTimeParam = `${currentDate} 00:00:00`;

    const response = await photoCheckApi({ startDate: dateTimeParam });

    if (response.data.code === 200 && response.data.result) {
      taskList.value = response.data.result;
      console.log("任务列表数据:", taskList.value);
    } else {
      taskList.value = [];
      message.warning("暂无任务数据");
    }
  } catch (error) {
    console.error("获取任务列表失败:", error);
    message.error("获取任务列表失败");
    taskList.value = [];
  } finally {
    taskDataLoading.value = false;
  }
};

// 任务选择变化处理
const handleTaskSelectionChange = (selectedRowKeys, selectedRows) => {
  selectedTaskKeys.value = selectedRowKeys;
  console.log("选中的任务:", selectedRows);
};

// 任务弹窗确认
const handleTaskConfirm = () => {
  try {
    taskConfirmLoading.value = true;

    // 获取选中的任务数据
    const selectedTaskData = taskList.value.filter((task) =>
      selectedTaskKeys.value.includes(task.Task_code)
    );

    // 更新选中的任务
    selectedTasks.value = selectedTaskData;
    formData.selectedTasks = selectedTaskData.map((task) => ({
      Task_name: task.Task_name,
      Task_code: task.Task_code,
    }));

    showTaskModal.value = false;
    // 保持selectedTaskKeys与selectedTasks同步，不清空
    selectedTaskKeys.value = selectedTaskData.map(task => task.Task_code);

    message.success(`已选择 ${selectedTaskData.length} 个任务`);
  } catch (error) {
    console.error("确认任务选择失败:", error);
    message.error("确认任务选择失败");
  } finally {
    taskConfirmLoading.value = false;
  }
};

// 任务弹窗取消
const handleTaskCancel = () => {
  showTaskModal.value = false;
  // 恢复到已选择任务的状态，不清空
  selectedTaskKeys.value = selectedTasks.value.map(task => task.Task_code);
};

// 移除任务
const removeTask = (taskCode) => {
  selectedTasks.value = selectedTasks.value.filter(
    (task) => task.Task_code !== taskCode
  );
  formData.selectedTasks = formData.selectedTasks.filter(
    (task) => task.Task_code !== taskCode
  );
  // 同步更新selectedTaskKeys
  selectedTaskKeys.value = selectedTaskKeys.value.filter(
    (code) => code !== taskCode
  );
  message.success("任务已移除");
};

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return "-";
  return dayjs(dateTime).subtract(8, "hour").format("YYYY-MM-DD HH:mm:ss");
};

// 区域选择处理
const handleAreaSelect = async () => {
  if (!formData.level) {
    message.error("请先选择级别");
    return;
  }

  try {
    areaOptions.value = [];
    areaHeaders.value = [];

    if (formData.level === "公司") {
      // 公司级别固定选项
      areaOptions.value = [
        { value: "company", label: "公司", label2: "", label3: "" },
      ];
      areaHeaders.value = [{ value: 1, label: "名称", label2: "", label3: "" }];
    } else if (formData.level === "大区") {
      // 调用大区接口
      let res;
      if (userRole.value === "大区总") {
        res = await getAllRegionApi_dqz(userId.value);
      } else {
        res = await getAllRegionApi();
      }

      areaOptions.value = res.data.result.map((item) => ({
        value: item.id,
        label: item.Organization_Name || item.name,
        label2: item.Organization_Code || "",
      }));

      areaHeaders.value = [
        { value: 1, label: "大区名称", label2: "", label3: "" },
      ];
    } else if (formData.level === "省区") {
      let res;

      if (userRole.value === "大区总") {
        res = await getProvinceApi_dqz(userId.value);
      } else {
        res = await getAllProvincesApi();
      }

      areaOptions.value = res.data.result.map((item) => ({
        value: item.id,
        label: item.Organization_Name || item.name,
        label2: item.Organization_Code || "",
      }));

      areaHeaders.value = [
        { value: 1, label: "省区名称", label2: "", label3: "" },
      ];
    } else if (formData.level === "经销区") {
      // 调用经销区接口，根据角色权限调用不同接口
      let res;

      if (userRole.value === "大区总") {
        res = await getJxqApi_dqz(userId.value);
      } else {
        res = await getAllJxqApi();
      }

      areaOptions.value = res.data.result.map((item) => ({
        value: item.id,
        label: item.Organization_Name || item.name,
        label2: item.Organization_Code || "",
      }));

      areaHeaders.value = [
        { value: 1, label: "名称", label2: "所属省区", label3: "所属大区" },
      ];
    } else if (formData.level === "经销商") {
      // 调用经销商接口（这里需要根据实际API调整）
      const params = {
        id: userId.value,
        Role_name: userRole.value,
      };
      const res = await getDealerListApi(params);

      areaOptions.value = res.data.result[0].map((item) => ({
        value: item.id,
        label: item.Master_data_person_name || "未知姓名",
        label2: item.Master_data_person_code || "",
        label3: item.Role_name || "",
        ERP_code: item.ERP_code || "",
      }));

      areaHeaders.value = [
        {
          value: 1,
          label: "经销商名称",
          label2: "所属经销区",
          label3: "所属省区",
        },
      ];
    }

    areaModalTitle.value = `选择${formData.level}`;
    showAreaModal.value = true;
  } catch (error) {
    console.error("获取区域数据失败:", error);
    message.error("获取区域数据失败");
  }
};

// 区域确认选择
const handleAreaConfirm = (item) => {
  formData.areaName = item.label;
  formData.areaId = item.value;
  formData.areaCode = item.label2; // 这里可以根据实际需求调整编码逻辑
  formData.ERP_code = item.ERP_code;
  showAreaModal.value = false;
  console.log("选择的区域:", item);
};

// 表单提交
const handleSubmit = async () => {
  try {
    // 基础表单验证
    await formRef.value.validate();

    // 额外的业务验证
    if (!validateBusinessRules()) {
      return;
    }

    submitLoading.value = true;

    // 构造提交数据
    const submitData = {
      ...formData,
      ruleCode: formData.ruleCode || generateRuleCode(),
    };

    // 如果是物料投放类型，添加额外参数
    if (formData.scoreType === "物料投放" && formData.materialRecord) {
      submitData.material_record_id = formData.material_record_id;
      submitData.material_code = formData.material_code;
    }

    // 如果是空白门店开拓类型，添加额外参数
    if (formData.scoreType === "空白门店开拓" && formData.planningAreaInfo) {
      submitData.targetSales = formData.targetSales;
      submitData.validYear = formData.validYear;
      submitData.validMonth = formData.validMonth;
      submitData.planningAreaPoints = formData.planningAreaPoints;
      submitData.acreage = formData.planningAreaInfo.acreage;
      submitData.coordinateRange = formData.planningAreaInfo.coordinateRange;
    }

    console.log("提交数据:", submitData);

    // 调用API提交数据
    const response = await addScoreRuleApi(submitData);

    if (response.data.code == 200) {
      message.success("积分规则添加成功！");
      handleReset();
    } else {
      message.error(response.data.message || "添加失败");
    }
  } catch (error) {
    console.error("表单验证失败或提交失败:", error);
    if (error.errorFields) {
      message.error("请检查表单填写是否完整");
    } else {
      message.error("提交失败，请重试");
    }
  } finally {
    submitLoading.value = false;
  }
};

// 业务规则验证
const validateBusinessRules = () => {
  // 验证积分类型为业务动作时必须填写完整的动态字段
  if (showBusinessActionFields.value) {
    if (!formData.ruleType) {
      message.error("请选择规则类型");
      return false;
    }
    if (!formData.operator) {
      message.error("请选择运算符");
      return false;
    }
    if (formData.threshold === null || formData.threshold === undefined) {
      message.error("请输入阈值");
      return false;
    }
  }

  // 验证积分类型为拍照检查时必须选择任务
  if (showPhotoCheckFields.value) {
    if (!formData.selectedTasks || formData.selectedTasks.length === 0) {
      message.error("请选择拍照检查任务");
      return false;
    }
  }

  // 验证积分类型为物料投放时必须选择单据
  if (showMaterialFields.value) {
    if (!formData.materialDate) {
      message.error("请选择物料投放日期");
      return false;
    }
    if (!formData.materialRecord) {
      message.error("请选择物料投放单据");
      return false;
    }
  }

  // 验证积分类型为空白门店开拓时必须填写完整信息
  if (showBlankStoreFields.value) {
    if (!formData.targetSales) {
      message.error("请输入完成目标销售额");
      return false;
    }
    if (!formData.validYear) {
      message.error("请选择有效年");
      return false;
    }
    if (!formData.validMonth) {
      message.error("请选择有效月");
      return false;
    }
    if (!formData.planningAreaInfo) {
      message.error("请选择规划区域");
      return false;
    }
  }

  // 验证每次获得积分不能大于最大获得积分（返货类型跳过此验证）
  if (formData.scoreType !== "返货" && formData.perScore > formData.maxScore) {
    message.error("每次获得积分不能大于最大获得积分");
    return false;
  }

  // 验证年月组合的合理性
  const currentDate = dayjs();
  const selectedDate = dayjs(
    `${formData.year}-${formData.month.toString().padStart(2, "0")}-01`
  );

  if (selectedDate.isBefore(currentDate.subtract(1, "year"))) {
    message.error("选择的年月不能早于一年前");
    return false;
  }

  if (selectedDate.isAfter(currentDate.add(1, "year"))) {
    message.error("选择的年月不能晚于一年后");
    return false;
  }

  // 验证区域选择的完整性
  if (!formData.areaId || !formData.areaCode) {
    message.error("请完整选择区域信息");
    return false;
  }

  return true;
};

// 表单重置
const handleReset = () => {
  formRef.value.resetFields();
  Object.assign(formData, {
    ruleName: "",
    ruleCode: "",
    year: null,
    month: null,
    level: "",
    areaName: "",
    areaCode: "",
    areaId: "",
    ERP_code: "",
    scoreType: "",
    ruleType: "",
    operator: "",
    threshold: null,
    maxScore: null,
    perScore: null,
    scoreRule: "",
    selectedTasks: [],
    materialDate: null,
    materialRecord: null,
    material_record_id: "",
    material_code: "",
    ERP_code: "",
    // 重置空白门店开拓相关字段
    targetSales: null,
    validYear: null,
    validMonth: null,
    planningAreaInfo: null,
    planningAreaPoints: null,
  });

  // 重置任务相关状态
  selectedTasks.value = [];
  selectedTaskKeys.value = [];
  taskList.value = [];

  // 重置物料投放相关状态
  materialList.value = [];
  showMaterialModal.value = false;

  // 重置规划区域相关状态
  showPlanningModal.value = false;
};

// 初始化用户信息和权限
const initUserInfo = () => {
  const userInfo = sessionStorage.getItem("userInfo");
  if (userInfo) {
    userStore.userInfo = JSON.parse(userInfo);
    userRole.value = userStore.userInfo.Role_name || "";
    userId.value = userStore.userInfo.id || "";
  }
};

// 生命周期钩子
onMounted(() => {
  initUserInfo();
  // 自动生成积分编码
  formData.ruleCode = generateRuleCode();
  // 设置默认年份为当前年份
  formData.year = dayjs().year();
});
</script>

<style lang="scss" scoped>
// 主要配色方案 - 参考 JFGZ/index.vue
$primary-color: #704de9;
$light-purple: #e1dbf4;
$white: #ffffff;
$title-color: #1b1728;
$text-color: #767590;
$border-color: #e1dbf4;

.page-container {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding: 0;
}

.page-header {
  background: linear-gradient(
    135deg,
    $primary-color 0%,
    lighten($primary-color, 10%) 100%
  );
  padding: 32px 40px;
  color: $white;

  .page-title {
    margin: 0;
    font-size: 28px;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

.form-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 25px;
}

.form-section {
  background-color: $white;
  border-radius: 12px;
  border: 1px solid $border-color;
  margin-bottom: 32px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  }
}

.section-title {
  display: flex;
  align-items: center;
  padding: 20px 32px;
  background: linear-gradient(
    90deg,
    $light-purple 0%,
    rgba($light-purple, 0.3) 100%
  );
  border-bottom: 1px solid $border-color;

  .title-text {
    font-size: 16px;
    font-weight: 600;
    color: $title-color;
    margin-right: 16px;
  }

  .title-line {
    flex: 1;
    height: 2px;
    background: linear-gradient(90deg, $primary-color 0%, transparent 100%);
    border-radius: 1px;
  }
}

.section-content {
  padding: 32px;
}

.business-action-fields {
  margin: 12px 0;
  padding: 24px;
  background-color: rgba($light-purple, 0.2);
  border-radius: 8px;
  border: 1px dashed $border-color;
}

.photo-check-fields,
.material-fields,
.blank-store-fields {
  margin: 12px 0;
  padding: 24px;
  background-color: rgba($light-purple, 0.1);
  border-radius: 8px;
  border: 1px solid $border-color;

  .photo-check-header,
  .material-header,
  .blank-store-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .header-title {
      font-size: 14px;
      font-weight: 600;
      color: $title-color;
    }

    .loading-container {
      display: flex;
      align-items: center;
      gap: 8px;

      .loading-text {
        font-size: 13px;
        color: $text-color;
      }
    }

    .no-task-text {
      font-size: 13px;
      color: $text-color;
      font-style: italic;
    }
  }

  .selected-tasks {
    margin-top: 16px;

    .selected-tasks-title {
      font-size: 14px;
      font-weight: 500;
      color: $title-color;
      margin-bottom: 12px;
    }

    .task-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .task-tag {
        margin: 0;
        padding: 4px 12px;
        border-radius: 6px;
        background-color: rgba($primary-color, 0.1);
        border-color: rgba($primary-color, 0.3);
        color: $primary-color;
        font-size: 13px;

        &:hover {
          background-color: rgba($primary-color, 0.15);
        }
      }
    }
  }

  .selected-material {
    margin-top: 16px;
    padding: 16px;
    background-color: rgba($primary-color, 0.05);
    border-radius: 6px;
    border: 1px solid rgba($primary-color, 0.2);

    .selected-material-title {
      font-size: 14px;
      font-weight: 500;
      color: $title-color;
      margin-bottom: 12px;
    }

    .material-info {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .material-code,
      .material-amount {
        display: flex;
        align-items: center;

        .label {
          font-size: 13px;
          color: $text-color;
          margin-right: 8px;
          min-width: 100px;
        }

        .value {
          font-size: 13px;
          color: $title-color;
          font-weight: 500;
        }
      }

      .material-amount .value {
        color: $primary-color;
        font-weight: 600;
      }
    }
  }

  .selected-planning-area {
    margin-top: 16px;
    padding: 16px;
    background-color: rgba($primary-color, 0.05);
    border-radius: 6px;
    border: 1px solid rgba($primary-color, 0.2);

    .selected-area-title {
      font-size: 14px;
      font-weight: 500;
      color: $title-color;
      margin-bottom: 12px;
    }

    .area-info {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .area-coordinates,
      .area-size {
        display: flex;
        align-items: center;

        .label {
          font-size: 13px;
          color: $text-color;
          margin-right: 8px;
          min-width: 100px;
        }

        .value {
          font-size: 13px;
          color: $title-color;
          font-weight: 500;
        }
      }

      .area-size .value,
      .area-target .value {
        color: $primary-color;
        font-weight: 600;
      }

      .area-validity .value {
        color: $title-color;
        font-weight: 500;
      }
    }
  }
}

.task-modal-content {
  .task-table-container {
    max-height: 500px;
    overflow-y: auto;
  }

  .text-ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 200px;
  }

  .empty-state {
    text-align: center;
    padding: 40px 0;
  }
}

.form-actions {
  text-align: center;
  padding: 40px 0;
  background-color: $white;
  border-radius: 12px;
  border: 1px solid $border-color;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

:deep(.ant-form-item) {
  margin-bottom: 24px;
}

:deep(.ant-form-item-label) {
  padding-bottom: 8px;

  > label {
    font-weight: 500;
    color: $title-color;
    font-size: 14px;

    &.ant-form-item-required::before {
      color: #ff4d4f;
    }
  }
}

:deep(.ant-input),
:deep(.ant-select-selector),
:deep(.ant-input-number),
:deep(.ant-picker) {
  border-radius: 8px;
  border: 1px solid $border-color;
  transition: all 0.3s ease;

  &:hover {
    border-color: $primary-color;
  }

  &:focus,
  &.ant-input-focused,
  &.ant-select-focused .ant-select-selector,
  &.ant-picker-focused {
    border-color: $primary-color;
    box-shadow: 0 0 0 2px rgba($primary-color, 0.2);
  }
}

:deep(.ant-input-number) {
  width: 100%;
}

:deep(.ant-btn) {
  border-radius: 8px;
  height: 44px;
  padding: 0 32px;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.3s ease;

  &.ant-btn-primary {
    background-color: $primary-color;
    border-color: $primary-color;

    &:hover {
      background-color: lighten($primary-color, 5%);
      border-color: lighten($primary-color, 5%);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba($primary-color, 0.3);
    }

    &:active {
      transform: translateY(0);
    }
  }

  &.ant-btn-default {
    border-color: $border-color;
    color: $text-color;

    &:hover {
      color: $primary-color;
      border-color: $primary-color;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba($primary-color, 0.15);
    }
  }
}

:deep(.ant-space) {
  .ant-space-item {
    &:not(:last-child) {
      margin-right: 24px;
    }
  }
}

// 特殊字段样式
:deep(.ant-input[disabled]) {
  background-color: rgba($light-purple, 0.3);
  color: $text-color;
  cursor: not-allowed;
  border-color: $border-color;
}

:deep(.ant-input[readonly]) {
  background-color: rgba($light-purple, 0.1);
  cursor: pointer;

  &:hover {
    background-color: rgba($light-purple, 0.2);
    border-color: $primary-color;
  }
}

// 数字输入框样式
:deep(.ant-input-number) {
  .ant-input-number-input {
    text-align: left;
  }
}

// 文本域样式
:deep(.ant-input) {
  &.ant-input-textarea {
    resize: vertical;
    min-height: 100px;
  }
}

// 加载状态样式
:deep(.ant-btn-loading) {
  pointer-events: none;
}

// 表单项间距优化
:deep(.ant-row) {
  .ant-col {
    &:not(:last-child) {
      padding-right: 12px;
    }

    &:not(:first-child) {
      padding-left: 12px;
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .page-header {
    padding: 24px 20px;

    .page-title {
      font-size: 24px;
    }
  }

  .form-container {
    padding: 20px;
  }

  .section-content {
    padding: 20px;
  }

  .business-action-fields {
    padding: 16px;
  }

  .photo-check-fields {
    padding: 16px;

    .photo-check-header {
      flex-direction: column;
      align-items: stretch;
      gap: 12px;
    }

    .task-list {
      .task-tag {
        font-size: 12px;
        padding: 2px 8px;
      }
    }
  }

  :deep(.ant-form-item) {
    margin-bottom: 20px;
  }

  :deep(.ant-btn) {
    height: 40px;
    padding: 0 20px;
    font-size: 14px;
  }

  :deep(.ant-row) {
    .ant-col {
      padding-left: 0 !important;
      padding-right: 0 !important;
      margin-bottom: 16px;
    }
  }
}

@media (max-width: 480px) {
  .page-header {
    padding: 20px 16px;

    .page-title {
      font-size: 20px;
    }
  }

  .form-container {
    padding: 16px;
  }

  .section-content {
    padding: 16px;
  }

  .section-title {
    padding: 16px 20px;

    .title-text {
      font-size: 14px;
    }
  }
}
</style>
