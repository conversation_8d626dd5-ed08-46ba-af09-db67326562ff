import request from "../index";

export function getMapKey() {
  return request({
    method: "get",
    url: "/get/map_key",
  });
}

// 获取经销商列表
export function carMapGetAllJxs() {
  return request({
    method: "get",
    url: "/carMap/getAlljxs",
  });
}

// 获取大区列表
export function GetAllDq() {
  return request({
    method: "get",
    url: "/get/all_region",
  });
}
// 获取大区列表
export function GetAllDqNew() {
  return request({
    method: "get",
    url: "/get/all_region_new",
  });
}

// 获取省区列表
export function GetAllSqNew() {
  return request({
    method: "get",
    url: "/get/all_provinces_new",
  });
}

export function GetAllJxqNew() {
  return request({
    method: "get",
    url: "/get/all_jxq_new",
  });
}

// 获取大区列表-经销商
export function all_region_sell(userId) {
  return request({
    method: "get",
    url: `/get/all_region_sell?userId=${userId}`,
  });
}
// 获取大区列表-经销商
export function all_region_sell_new(userId) {
  return request({
    method: "get",
    url: `/get/all_region_sell_new?userId=${userId}`,
  });
}
// 获取省区列表-经销商
export function all_provinces_sell_new(userId) {
  return request({
    method: "get",
    url: `/get/all_provinces_sell_new?userId=${userId}`,
  });
}

// 获取大区列表-大区总
export function all_region_dqz(userId) {
  return request({
    method: "get",
    url: `/get/all_region_dqz?userId=${userId}`,
  });
}

export function all_region_dqz_new(userId) {
  return request({
    method: "get",
    url: `/get/all_region_dqz_new?userId=${userId}`,
  });
}

export function all_provinces_dqz_new(userId) {
  return request({
    method: "get",
    url: `/get/all_provinces_dqz_new?userId=${userId}`,
  });
}

// 获取大区列表-省区总
export function all_region_sqz(userId) {
  return request({
    method: "get",
    url: `/get/all_region_sqz?userId=${userId}`,
  });
}
// 获取大区列表-省区总
export function all_region_sqz_new(userId) {
  return request({
    method: "get",
    url: `/get/all_region_sqz_new?userId=${userId}`,
  });
}

// 获取大区列表-省区总
export function all_provinces_sqz_new(userId) {
  return request({
    method: "get",
    url: `/get/all_provinces_sqz_new?userId=${userId}`,
  });
}

// 获取所有的绑定经销区的区域
export function getNationalAreas() {
  return request({
    method: "get",
    url: "/national/getAllAreas",
  });
}

// 获取所有的绑定经销区的区域
export function getNationalAreasNew() {
  return request({
    method: "get",
    url: "/national/getAllAreas_new",
  });
}

// 获取所有的绑定经销区的区域-经销商
export function getAllAreas_sell(jxqId) {
  return request({
    method: "get",
    url: `/national/getAllAreas_sell?jxqId=${jxqId}`,
  });
}

// 获取绑定经销区的所有区县-大区总
export function getAllAreas_region(jxqIdList) {
  return request({
    method: "post",
    url: `/national/getAllAreas_region`,
    data: {
      jxqIdList,
    },
  });
}
export function getAllAreas_regionnew(jxqIdList) {
  return request({
    method: "post",
    url: `/national/getAllAreas_region_new`,
    data: {
      jxqIdList,
    },
  });
}

// 根据区域编码id获取区域信息
export function getAreaInfoBycode(code) {
  return request({
    method: "get",
    url: "/carMap/getAreaInfoBycode",
    data: {
      code,
    },
  });
}

// 获取门店所有的物料库存
export function getStoreMMI(storeId) {
  return request({
    method: "get",
    url: "/carMap/getStoreMMI",
    data: {
      storeId,
    },
  });
}

//一次获取全部门店
export function getAllStores(area, region) {
  return request({
    method: "post",
    // url: "/map/getAllStores",
    url: "/map/getAllStores2",
    data: {
      area,
      region,
    },
  });
}

// 根据缓存数据ID获取门店
export function getStoresListByCoreId(id) {
  return request({
    method: "get",
    url: "/cache/get_store_slice",
    data: {
      id,
    },
  });
}

//一次获取产品全部门店
export function get_pro_stores_all(
  product_class,
  product,
  product_group,
  area,
  region
) {
  return request({
    method: "post",
    // url: "/map/getProductStores",
    url: "/map/getProductStores2",
    data: {
      product_class: product_class,
      product: product,
      product_group: product_group,
      area,
      region,
    },
  });
}

// 获得缓存id
export function get_cache(product_class, product, product_group, area, region) {
  return request({
    method: "post",
    url: "/cache/store_position",
    data: {
      product_class: product_class,
      product: product,
      product_group: product_group,
      area,
      region,
    },
  });
}

// 通过缓存id调用接口
export function get_work(task_id) {
  return request({
    method: "get",
    url: "/async_work/status",
    data: {
      task_id: task_id,
    },

    header: {
      Authorization: "Bearer token_42c6c4614bc346f2b7b99c44c66e296b_str",
    },
  });
}
//经销区内所有的人员
export function get_all_users(id) {
  return request({
    method: "post",
    url: "/store/getAllUsers",
    data: {
      jxqId: id,
    },
  });
}

//经销区内所有的人员-大区总
export function getAllUsers_dqz(jxqIdList) {
  return request({
    method: "get",
    url: "/store/getAllUsers_dqz",
    data: {
      jxqIdList,
    },
  });
}

//大区下的经销区
export function getRegionUnderling(dqId) {
  return request({
    method: "get",
    url: "/get/region_underling",
    data: {
      dqId: dqId,
    },
  });
}
export function getRegionUnderlingNew(dqId) {
  return request({
    method: "get",
    url: "/get/region_underling_new",
    data: {
      dqId: dqId,
    },
  });
}

export function getProvincesUnderlingNew(dqId) {
  return request({
    method: "get",
    url: "/get/provinces_underling_new",
    data: {
      dqId: dqId,
    },
  });
}

export function getRegionUnderlingNew2(dqId) {
  return request({
    method: "get",
    url: "/get/region_underling2_new",
    data: {
      dqId: dqId,
    },
  });
}

//经销区内所有的车俩
export function get_all_cars() {
  return request({
    method: "get",
    url: "/gps/getAllCars",
  });
}

//刷新获取车辆
export function get_update_cars(imei_list) {
  return request({
    method: "post",
    url: "/gps/carUpdateList",
    data: {
      imei_list,
    },
  });
}

// 获取gps台账表中imei数据
export function get_noperson_imeis(area_id_list) {
  return request({
    method: "post",
    url: "/gps/get_noperson_imeis",
    data: {
      area_id_list,
    },
  });
}

// 获取gps台账表中imei数据-所有
export function get_all_imeis() {
  return request({
    method: "get",
    url: "/gps/get_all_imeis",
  });
}

// 获取所有区域
export function get_all_country_area() {
  return request({
    method: "get",
    url: `/get/allCountryArea`,
  });
}

//获取当前车辆轨迹
export function get_cars_tracks(imei, startTime, endTime) {
  return request({
    method: "get",
    url: "/gps/getCarTrack",
    data: {
      imei: imei,
      startTime: startTime,
      endTime: endTime,
    },
  });
}

//获取用户详细信息
export function get_user_info(userId) {
  return request({
    method: "get",
    url: "/store/getUserInfo",
    data: {
      userId: userId,
    },
  });
}

//获取gps时间段中的门店打卡记录
export function get_sings_stores(userId, startTime, endTime) {
  return request({
    method: "get",
    url: "/store/getStoreBySignTime",
    data: {
      userid: userId,
      startTime: startTime,
      endTime: endTime,
    },
  });
}

export function carMapGetStoreInfo(storeId) {
  return request({
    method: "get",
    url: "/carMap/getStoreInfo",
    data: {
      storeId,
    },
  });
}

export function carMapGetStoreGoodsInfo(storeId, month, year) {
  return request({
    method: "get",
    url: "/carMap/getStoreGoodsInfo",
    data: {
      storeId,
      month,
      year,
    },
  });
}

// 获取照片接口
export function carMapGetStoreInfoPic(storeId, start_date, end_date) {
  return request({
    method: "get",
    url: "/carMap/getStoreInfoPic",
    data: {
      storeId,
      start_date,
      end_date,
    },
  });
}

// 今日销售明细
export function carMapGetStoreGoodsInfoDetail(store_id, start_date, end_date) {
  return request({
    method: "get",
    url: "/carMap/getStoreGoodsDay",
    data: {
      store_id,
      start_date,
      end_date,
    },
  });
}

// 获取门店销货单最新图片信息
export function getStoreSalesImgs(storeId) {
  return request({
    method: "get",
    url: "/map/getSalesImgs",
    data: {
      storeId,
    },
  });
}

// 今日返货明细
export function carMapGetStoreGoodsInfoReturnDetail(
  store_id,
  start_date,
  end_date
) {
  return request({
    method: "get",
    url: "/carMap/getStoreGoodsReturnDay",
    data: {
      store_id,
      start_date,
      end_date,
    },
  });
}

export function carMapGetStoreGoodsInfo01(storeId, month, year) {
  return request({
    method: "get",
    url: "/carMap/getStoreGoodsInfo01",
    data: {
      storeId,
      month,
      year,
    },
  });
}

export function carMapGetStoreSsaleroom(storeId, month, year) {
  return request({
    method: "get",
    url: "/carMap/getStoreSaleroom",
    data: {
      storeId,
      month,
      year,
    },
  });
}

export function carMapGetStoreEditHistory(storeId) {
  return request({
    method: "get",
    url: "/boss/get_all_edithistorys",
    data: {
      id: storeId,
    },
  });
}

// 获取物料投放
export function getMaterialDeliveryApi(storeId) {
  return request({
    method: "get",
    url: "/get/materialDelivery",
    data: {
      id: storeId,
    },
  });
}

// 获取物料撤回
export function getMaterialWithdrawApi(storeId) {
  return request({
    method: "get",
    url: "/get/materialWithdraw",
    data: {
      id: storeId,
    },
  });
}
// 获取物料盘点明细
export function getStoreMaterialDetail(storeId, month, year) {
  return request({
    method: "get",
    url: "/get/storeMaterialDetail",
    data: {
      storeId,
      month,
      year,
    },
  });
}

// 获取门店拜访情况
export function getVisitSituationApi(storeId, month, year) {
  return request({
    method: "get",
    url: "/get/storeVisitDetail",
    data: {
      storeId,
      month,
      year,
    },
  });
}

// 获取盘筐明细
export function getBasketDetail(storeId, month, year) {
  return request({
    method: "get",
    url: "/get/storeBasketDetail",
    data: {
      storeId,
      month,
      year,
    },
  });
}

// 获取全国门店
export function getAllCountryStores() {
  return request({
    method: "get",
    url: "/get/all_couontry_stores",
  });
}

// 获取所有产品
export function getAllProd(category) {
  return request({
    method: "get",
    url: `/get/all_product?category=${category}`,
  });
}

// 获取所有产品分类
export function getAllProdCategory() {
  return request({
    method: "get",
    url: "/get/prodect_category",
  });
}

export function getProductGroup() {
  return request({
    method: "get",
    url: "/get/product_group",
  });
}
