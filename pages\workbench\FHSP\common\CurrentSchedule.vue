<template>
  <!-- #region ---------------------- 当前进度 start ---------------------- -->
  <view class="main-info">
    <view class="deal-state">
      <view style="font-weight: bold; margin-bottom: 10rpx; font-size: 26rpx">处理状态</view>
      <view class="approval-process">
        <u-steps :current="0">
          <u-steps-item title="发起"> </u-steps-item>
          <u-steps-item title="审批"></u-steps-item>
          <u-steps-item title="完成"></u-steps-item>
        </u-steps>
      </view>
    </view>
    <view class="basic-info">
      <view class="info-item">
        <text class="info-item-title">产品金额：</text>
        <text style="font-size: 24rpx; color: #ff2d1a">￥</text>
        <text class="info-item-content"> {{ fnFloorToFixed2(totalMoney) }}</text>
      </view>
      <view class="info-item">
        <text class="info-item-title">筐总金额：</text>
        <text style="font-size: 24rpx; color: #ff2d1a">￥</text>
        <text class="info-item-content"> {{ fnFloorToFixed2(kuangMoney) }}</text>
      </view>
    </view>
  </view>
  <!-- #endregion ---------------------- 当前进度 end ---------------------- -->
</template>

<script>
  import { fnFloorToFixed2 } from '@/api/workbench/return-common.js'

  export default {
    options: {
      styleIsolation: 'shared', // 解除样式隔离
    },
  }
</script>
<script setup>
  // import { ref } from 'vue';

  const props = defineProps({
    totalMoney: {
      type: Number,
      default: 0,
    },
    kuangMoney: {
      type: Number,
      default: 0,
    },
  })
</script>

<style lang="scss" scoped>
  .main-info {
    height: 240rpx;
    padding: 10rpx 20rpx;
    box-shadow: 0rpx 10rpx 20rpx rgba(215, 215, 215, 0.349019607843137);
    box-sizing: border-box;
    .deal-state {
      margin-bottom: 20rpx;
      .approval-process {
        :deep(.u-steps) {
          align-items: center !important;
        }
        //连接线
        :deep(.u-steps-item__line--row) {
          top: 25rpx !important;
        }
        //步骤圆圈
        :deep(.u-steps-item__wrapper__circle) {
          width: 50rpx !important;
          height: 50rpx !important;
        }
        :deep(.u-steps-item__wrapper__circle__text) {
          font-size: 26rpx !important;
        }
        :deep(.u-text__value--main) {
          font-size: 26rpx !important;
        }
        :deep(.u-text__value--content) {
          font-size: 26rpx !important;
        }
        :deep(.u-steps-item__wrapper--row) {
          width: 50rpx !important;
          height: 50rpx !important;
        }
        :deep(.u-icon__icon) {
          font-size: 26rpx !important;
        }
        :deep(.u-steps-item__content) {
          margin-left: 0 !important;
        }
      }
    }
    .basic-info {
      display: flex;
      // justify-content: space-between;
      font-size: 26rpx;
      .info-item {
        width: 50%;
        box-sizing: border-box;
        padding-bottom: 10rpx;
        white-space: nowrap;
        overflow: hidden;
        .info-item-title {
          color: #606266;
        }
        .info-item-content {
          flex: 1;
          font-size: 32rpx;
          color: #ff2d1a;
          font-weight: bold;
          margin-left: 4rpx;
        }
      }
    }
  }
</style>
