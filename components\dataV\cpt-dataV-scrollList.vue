<template>
  <div style="max-height: 400px; width: 100%">
    <div class="scrollTable">
      <div class="scroll-item">
        <div class="items" v-for="item in itemValueList">
          <span style="height: 20px; min-width: 50px; text-align: right">{{
            item.name
          }}</span>
          <div class="progress-bar" :style="{ width: item.value + 'px' }"></div>
          <span style="font-size: 12px">{{ item.value + attribute.unit }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, nextTick } from "vue";

const props = defineProps(["data"]);

const attribute = ref(props.data.attribute);

const itemValueList = ref(JSON.parse(props.data.cptDataForm.dataText));

//监听滚动到底部
function watchScrollBottom() {
  const scrollItem = document.querySelector(".scroll-item");
  console.log("scrollItem", scrollItem);
  // 每秒向下滚动40px
  const scrollInterval = window.setInterval(() => {
    scrollItem.scrollTop += 50;
  }, 2000);
  // 监听滚动事件
  scrollItem.addEventListener("scroll", () => {
    const scrollTop = scrollItem.scrollTop;
    const scrollHeight = scrollItem.scrollHeight;
    const clientHeight = scrollItem.clientHeight;

    if (scrollTop + clientHeight >= scrollHeight) {
      scrollItem.scrollTop = 0;
    }
  });
}

nextTick(() => {
  watchScrollBottom();
});
</script>

<style scoped lang="scss">
.scrollTable {
  height: 100%;
  background-color: #fff;
  //   border: 1px solid blue;
  background-color: rgba(0, 0, 0, 0);

  .scroll-item {
    max-height: 400px;
    overflow-y: auto;
    display: flex;
    gap: 10px;
    flex-direction: column;
    scroll-behavior: smooth;
    .items {
      height: 40px;
      display: flex;
      gap: 15px;
      color: #fff;
      align-items: center;
      .progress-bar {
        height: 20px;
        background-color: #039aee;
        width: 350px;
        text-align: right;
        padding-right: 10px;
        font-size: 12px;
        line-height: 20px;
      }
    }
  }
}
</style>
