<template>
  <div class="page-container">
    <!-- 左侧可伸缩侧边栏 -->
    <div class="sidebar" :class="{ 'sidebar-collapsed': !sidebarExpanded }">
      <!-- 收起状态的展开按钮 -->
      <div v-if="!sidebarExpanded" class="sidebar-collapsed-content">
        <div class="collapsed-header">
          <a-button type="text" @click="toggleSidebar" class="toggle-btn">
            <template #icon>
              <menu-unfold-outlined />
            </template>
          </a-button>
        </div>
        <div class="collapsed-menu">
          <div class="collapsed-menu-indicator">
            <div class="indicator-dot"></div>
            <div class="indicator-dot"></div>
            <div class="indicator-dot"></div>
          </div>
        </div>
      </div>

      <!-- 展开状态的侧边栏内容 -->
      <div v-if="sidebarExpanded" class="sidebar-expanded-content">
        <!-- 侧边栏头部 -->
        <div class="sidebar-header">
          <div class="header-content">
            <span class="sidebar-title">积分类型</span>
            <a-button
              type="text"
              @click="toggleSidebar"
              class="header-toggle-btn"
            >
              <template #icon>
                <menu-fold-outlined />
              </template>
            </a-button>
          </div>
        </div>

        <!-- 可滚动的菜单区域 -->
        <div class="sidebar-menu-container">
          <div class="sidebar-menu">
            <div
              v-for="item in scoreTypeOptions"
              :key="item.value"
              class="menu-item"
              :class="{ 'menu-item-active': filterType === item.value }"
              @click="handleSidebarFilter(item.value)"
            >
              <div class="menu-item-content">
                <component :is="item.icon" class="menu-icon" />
                <span class="menu-text">{{ item.label }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧内容区域 -->
    <div
      class="main-content"
      :class="{ 'main-content-expanded': !sidebarExpanded }"
    >
      <!-- 顶部标题和搜索 -->
      <div class="content-header">
        <h1 class="page-title">积分规则管理</h1>
        <div class="header-actions">
          <a-input
            class="custom-search-input"
            placeholder="搜索积分规则..."
            v-model:value="searchQuery"
            @pressEnter="handleSearch"
            allow-clear
          >
            <template #prefix>
              <search-outlined @click="handleSearch" class="search-icon" />
            </template>
          </a-input>
          <a-button
            size="large"
            @click="handleRefresh"
            class="refresh-btn"
            :loading="loading"
          >
            <template #icon>
              <reload-outlined />
            </template>
            刷新
          </a-button>
          <a-button
            type="primary"
            size="large"
            @click="handleCreate"
            class="add-btn"
          >
            <template #icon>
              <plus-outlined />
            </template>
            新增规则
          </a-button>
        </div>
      </div>

      <!-- 可滚动的内容区域 -->
      <div class="content-body">
        <!-- 卡片列表 -->
        <div class="card-list" v-if="!loading && list.length > 0">
          <div v-for="item in list" :key="item.id" class="rule-card">
            <!-- 卡片头部 -->
            <div class="card-header">
              <div class="rule-title">
                <h3 class="rule-name">
                  {{ item.score_rule_text || "未命名规则" }}
                </h3>
                <div class="tag-group">
                  <a-tag
                    :color="getTypeColor(item.belong_score_type)"
                    class="type-tag"
                  >
                    {{ item.belong_score_type || "未分类" }}
                  </a-tag>
                  <a-tag
                    v-if="
                      item.belong_score_type === '业务动作' &&
                      item.business_action_type
                    "
                    :color="getBusinessActionColor(item.business_action_type)"
                    class="business-action-tag"
                  >
                    {{ item.business_action_type }}
                  </a-tag>
                  <a-tag
                    :color="item.active === 1 ? 'green' : 'red'"
                    class="status-tag"
                  >
                    {{ item.active === 1 ? "已开启" : "未开启" }}
                  </a-tag>
                </div>
              </div>
              <div class="card-actions" @click.stop>
                <a-dropdown :trigger="['click']" placement="bottomRight">
                  <a-button type="text" shape="circle" @click.prevent>
                    <template #icon>
                      <more-outlined />
                    </template>
                  </a-button>
                  <template #overlay>
                    <a-menu @click="handleMenuClick($event, item)">
                      <a-menu-item key="edit">
                        <edit-outlined />
                        编辑
                      </a-menu-item>
                      <a-menu-item key="toggle">
                        <template v-if="item.active === 1">
                          <stop-outlined />
                          禁用
                        </template>
                        <template v-else>
                          <play-circle-outlined />
                          启用
                        </template>
                      </a-menu-item>
                      <a-menu-item key="delete" danger>
                        <delete-outlined />
                        删除
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </div>
            </div>

            <!-- 卡片内容 -->
            <div class="rule-content">
              <div class="info-row">
                <div class="info-item">
                  <span class="label">积分代码：</span>
                  <span class="value">{{ item.score_code || "-" }}</span>
                </div>
                <div class="info-item">
                  <span class="label">级别：</span>
                  <span class="value">{{ item.level || "-" }}</span>
                </div>
              </div>

              <div class="info-row">
                <div class="info-item">
                  <span class="label">公司/区域：</span>
                  <span class="value">{{
                    item.company_or_area_name || "-"
                  }}</span>
                </div>
                <div class="info-item">
                  <span class="label">年月：</span>
                  <span class="value">{{ item.year }}-{{ item.month }}</span>
                </div>
              </div>

              <div class="info-row">
                <div class="info-item">
                  <span class="label">最大积分：</span>
                  <span class="value score-value">{{
                    item.max_score || 0
                  }}</span>
                </div>
                <div class="info-item">
                  <span class="label">每次积分：</span>
                  <span class="value score-value">{{
                    item.every_score || 0
                  }}</span>
                </div>
              </div>

              <div class="info-row full-width">
                <div class="info-item">
                  <span class="label">积分规则：</span>
                  <span class="value rule-text">{{
                    item.score_rule || "-"
                  }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div
          v-else-if="!loading && list.length === 0"
          class="empty-state"
        >
          <a-empty description="暂无积分规则数据">
            <a-button type="primary" @click="handleCreate">新增规则</a-button>
          </a-empty>
        </div>

        <!-- 加载状态 -->
        <div v-if="loading" class="loading-state">
          <a-spin size="large" tip="加载中...">
            <div class="loading-placeholder"></div>
          </a-spin>
        </div>
      </div>

      <!-- 分页组件 -->
      <div
        v-if="!loading && list.length > 0"
        class="pagination-wrapper"
      >
        <a-pagination
          v-model:current="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :show-size-changer="false"
          :show-quick-jumper="true"
          :show-total="(total) => `共 ${total} 条记录`"
          @change="handlePageChange"
          class="pagination"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { message, Modal } from "ant-design-vue";
import {
  PlusOutlined,
  MoreOutlined,
  EditOutlined,
  DeleteOutlined,
  StopOutlined,
  PlayCircleOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  AppstoreOutlined,
  ShoppingOutlined,
  CameraOutlined,
  ShopOutlined,
  InboxOutlined,
  UndoOutlined,
  DollarOutlined,
  UnorderedListOutlined,
  SearchOutlined,
  ReloadOutlined,
} from "@ant-design/icons-vue";
import {
  getScoreRuleListApi,
  deleteScoreRuleApi,
  toggleScoreRuleApi,
} from "/api/workbench/JFGZ/index.js";

// 响应式数据
const loading = ref(false);
const searchQuery = ref("");
const filterType = ref("all");
const list = ref([]);
const currentPage = ref(1);
const pageSize = ref(12); // 调整为12个，适合3列布局
const total = ref(0);
const sidebarExpanded = ref(true);

// 积分类型选项配置
const scoreTypeOptions = [
  { value: "all", label: "全部", icon: UnorderedListOutlined },
  { value: "市场活动", label: "市场活动", icon: AppstoreOutlined },
  { value: "业务动作", label: "业务动作", icon: ShoppingOutlined },
  { value: "拍照检查", label: "拍照检查", icon: CameraOutlined },
  { value: "空白门店开拓", label: "空白门店开拓", icon: ShopOutlined },
  { value: "物料投放", label: "物料投放", icon: InboxOutlined },
  { value: "返货", label: "返货", icon: UndoOutlined },
];

// 查询参数
const queryParams = reactive({
  page: 1,
  limit: 12,
  search: "",
  type: "all",
});


// 获取积分类型颜色
const getTypeColor = (type) => {
  const colorMap = {
    市场活动: "blue",
    业务动作: "green",
    拍照检查: "orange",
    空白门店开拓: "purple",
    物料投放: "cyan",
    返货: "red",
  };
  return colorMap[type] || "default";
};

// 获取业务动作类型颜色
const getBusinessActionColor = (type) => {
  const colorMap = {
    出车时间: "geekblue",    // 深蓝色 - 代表时间管理
    首店时间: "cyan",        // 青色 - 代表首次访问
    行驶里程: "volcano",     // 火山红 - 代表行驶动态
    成交家数: "gold",        // 金色 - 代表成交成果
  };
  return colorMap[type] || "default";
};

// 获取积分规则列表
const fetchData = async () => {
  try {
    loading.value = true;

    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      search: queryParams.search,
      type: queryParams.type,
    };

    const response = await getScoreRuleListApi(params);

    if (response && response.data) {
      // 根据实际API返回结构调整数据处理
      list.value = response.data.result[0] || [];
      total.value = response.data.result[0][0].total_count || 0;

      // 更新查询参数
      queryParams.page = currentPage.value;
      queryParams.limit = pageSize.value;
    }
  } catch (error) {
    console.error("获取积分规则列表失败:", error);
    message.error("获取数据失败，请稍后重试");
  } finally {
    loading.value = false;
  }
};

// 搜索处理
const handleSearch = () => {
  queryParams.search = searchQuery.value || "";
  currentPage.value = 1; // 重置到第一页
  fetchData();
};

// 刷新处理
const handleRefresh = () => {
  // 重置搜索条件
  searchQuery.value = "";
  queryParams.search = "";
  // 重置筛选条件
  filterType.value = "all";
  queryParams.type = "all";
  // 重置到第一页
  currentPage.value = 1;
  // 重新获取数据
  fetchData();
  message.success("数据已刷新");
};

// 侧边栏展开/收起
const toggleSidebar = () => {
  sidebarExpanded.value = !sidebarExpanded.value;
};

// 侧边栏筛选
const handleSidebarFilter = (value) => {
  filterType.value = value;
  queryParams.type = value === "all" ? "all" : value;
  currentPage.value = 1; // 重置到第一页
  fetchData();
};

// 分页处理
const handlePageChange = (page, size) => {
  currentPage.value = page;
  if (size) {
    pageSize.value = size;
  }
  fetchData();
};

// 新增规则
const handleCreate = () => {
  uni.navigateTo({
    url: "/pages/workbench/JFGZ/addJF",
  });
};

// 菜单点击处理
const handleMenuClick = async ({ key }, item) => {
  switch (key) {
    case "edit":
      handleEdit(item);
      break;
    case "toggle":
      await handleToggle(item);
      break;
    case "delete":
      handleDelete(item);
      break;
  }
};

// 编辑规则
const handleEdit = (item) => {
  uni.navigateTo({
    url: `/pages/workbench/JFGZ/addJF?id=${item.id}`,
  });
};

// 启用/禁用规则
const handleToggle = async (item) => {
  try {
    const newStatus = item.active === 1 ? 0 : 1;
    const action = newStatus === 1 ? "启用" : "禁用";

    Modal.confirm({
      title: `确认${action}规则`,
      content: `确定要${action}规则"${item.score_rule_text}"吗？`,
      onOk: async () => {
        await toggleScoreRuleApi(item.id, newStatus);
        message.success(`${action}成功`);
        item.active = newStatus;
      },
    });
  } catch (error) {
    console.error("切换规则状态失败:", error);
    message.error("操作失败，请稍后重试");
  }
};

// 删除规则
const handleDelete = (item) => {
  Modal.confirm({
    title: "确认删除",
    content: `确定要删除规则"${item.score_rule_text}"吗？此操作不可恢复。`,
    okType: "danger",
    onOk: async () => {
      try {
        await deleteScoreRuleApi(item.id);
        message.success("删除成功");
        fetchData();
      } catch (error) {
        console.error("删除规则失败:", error);
        message.error("删除失败，请稍后重试");
      }
    },
  });
};

// 页面加载时获取数据
onMounted(() => {
  fetchData();
});
</script>

<style lang="scss" scoped>
// 主要配色方案
$primary-color: #704de9;
$light-purple: #e1dbf4;
$white: #ffffff;
$title-color: #1b1728;
$text-color: #767590;
$border-color: #e1dbf4;

html, body { 
  overflow: hidden;
}

.page-container {
  display: flex;
  height: 100vh;
  background-color: #f8f9fa;
  overflow: hidden; // 防止整个页面滚动
}

// 侧边栏样式
.sidebar {
  width: 240px;
  height: 100vh;
  background: linear-gradient(180deg, #ffffff 0%, #fafbfc 100%);
  border-right: 1px solid $border-color;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  flex-shrink: 0;
  overflow: hidden;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);

  &.sidebar-collapsed {
    width: 64px;
  }

  // 收起状态的内容
  .sidebar-collapsed-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 16px 0;

    .collapsed-header {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 4px;
      border-bottom: 1px solid $border-color;
      margin-bottom: 16px;

      .toggle-btn {
        width: 40px;
        height: 40px;
        border-radius: 12px;
        color: $primary-color;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
        margin-bottom: 8px;

        &:hover {
          background-color: $light-purple;
          transform: scale(1.05);
        }

        .anticon {
          font-size: 18px;
        }
      }

      .collapsed-title {
        font-size: 12px;
        font-weight: 600;
        color: $title-color;
        text-align: center;
        line-height: 1.2;
        writing-mode: vertical-rl;
        text-orientation: mixed;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .collapsed-menu {
      flex: 1;
      display: flex;
      justify-content: center;
      padding-top: 20px;

      .collapsed-menu-indicator {
        display: flex;
        flex-direction: column;
        gap: 8px;
        align-items: center;

        .indicator-dot {
          width: 6px;
          height: 6px;
          border-radius: 50%;
          background-color: $border-color;
          opacity: 0.6;
          transition: all 0.2s ease;

          &:nth-child(1) {
            animation: pulse 2s infinite;
          }

          &:nth-child(2) {
            animation: pulse 2s infinite 0.5s;
          }

          &:nth-child(3) {
            animation: pulse 2s infinite 1s;
          }
        }
      }
    }
  }

  @keyframes pulse {
    0%,
    100% {
      opacity: 0.6;
      transform: scale(1);
    }
    50% {
      opacity: 1;
      transform: scale(1.2);
    }
  }

  // 展开状态的内容
  .sidebar-expanded-content {
    height: 100vh;
    display: flex;
    flex-direction: column;
    position: relative;

    // 侧边栏头部
    .sidebar-header {
      flex-shrink: 0;
      background: $white;
      border-bottom: 1px solid $border-color;
      position: relative;
      z-index: 10;

      .header-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20px 16px;

        .sidebar-title {
          font-size: 18px;
          font-weight: 700;
          color: $title-color;
          letter-spacing: -0.02em;
        }

        .header-toggle-btn {
          width: 32px;
          height: 32px;
          border-radius: 8px;
          color: $text-color;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.2s ease;

          &:hover {
            background-color: $light-purple;
            color: $primary-color;
          }

          .anticon {
            font-size: 16px;
          }
        }
      }
    }

    // 可滚动的菜单容器
    .sidebar-menu-container {
      flex: 1;
      overflow: hidden;
      position: relative;
      min-height: 0;

      .sidebar-menu {
        height: 100%;
        overflow-y: auto;
        overflow-x: hidden;
        padding: 12px 8px;

        // 优化的滚动条样式
        &::-webkit-scrollbar {
          width: 6px;
        }

        &::-webkit-scrollbar-track {
          background: transparent;
          border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb {
          background: rgba(112, 77, 233, 0.2);
          border-radius: 3px;
          transition: background 0.2s ease;

          &:hover {
            background: rgba(112, 77, 233, 0.4);
          }
        }

        .menu-item {
          margin-bottom: 12px;
          border-radius: 12px;
          cursor: pointer;
          transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
          position: relative;
          overflow: hidden;

          &:hover {
            transform: translateX(4px);
            box-shadow: 0 4px 12px rgba(112, 77, 233, 0.15);
          }

          &.menu-item-active {
            background: linear-gradient(
              135deg,
              $primary-color 0%,
              lighten($primary-color, 10%) 100%
            );
            box-shadow: 0 4px 16px rgba(112, 77, 233, 0.3);
            transform: translateX(4px);

            .menu-item-content {
              .menu-icon,
              .menu-text {
                color: $white;
              }
            }
          }

          .menu-item-content {
            display: flex;
            align-items: center;
            padding: 14px 16px;
            position: relative;

            .menu-icon {
              font-size: 18px;
              margin-right: 14px;
              color: $text-color;
              transition: all 0.2s ease;
              flex-shrink: 0;
            }

            .menu-text {
              font-size: 14px;
              font-weight: 500;
              color: $text-color;
              transition: all 0.2s ease;
              line-height: 1.4;
            }
          }
        }
      }
    }

    // 侧边栏底部
    .sidebar-footer {
      flex-shrink: 0;
      background: $white;
      position: relative;
      z-index: 10;

      .footer-divider {
        height: 1px;
        background: linear-gradient(
          90deg,
          transparent 0%,
          $border-color 50%,
          transparent 100%
        );
        margin: 0 16px;
      }

      .footer-content {
        padding: 16px;
        text-align: center;

        .footer-text {
          font-size: 12px;
          color: $text-color;
          opacity: 0.7;
        }
      }
    }
  }
}

// 主内容区域样式
.main-content {
  flex: 1;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: all 0.3s ease;

  // 移除负边距，让内容区域自然填充剩余空间
  // 侧边栏收缩时，主内容区域会自动填充剩余空间

  .content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    flex-wrap: wrap;
    gap: 16px;
    flex-shrink: 0; // 防止头部被压缩
    border-bottom: 1px solid $border-color;

    .page-title {
      font-size: 24px;
      font-weight: 600;
      color: $title-color;
      margin: 0;
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 16px;

      // Ant Design 搜索框组件样式
      .custom-search-input {
        --width-of-input: 300px;
        --border-color: #704de9;
        --border-radius: 20px;
        --height-of-input: 40px;
        position: relative;
        width: var(--width-of-input);

        :deep(.ant-input-affix-wrapper) {
          height: var(--height-of-input);
          border: 1px solid #e1dbf4;
          border-radius: var(--border-radius);
          background-color: white;
          transition: all 0.3s ease;
          overflow: hidden;

          // 鼠标悬停时的效果
          &:hover {
            border-color: var(--border-color);
            box-shadow: 0 4px 12px rgba(112, 77, 233, 0.15);
            transform: translateY(-1px);
          }

          // 聚焦时的效果
          &:focus-within,
          &.ant-input-affix-wrapper-focused {
            border-color: var(--border-color) !important;
            box-shadow: 0 4px 16px rgba(112, 77, 233, 0.25) !important;
            outline: none;
          }

          // 移除 Ant Design 默认的聚焦样式
          &.ant-input-affix-wrapper-focused {
            border-color: var(--border-color) !important;
            box-shadow: 0 4px 16px rgba(112, 77, 233, 0.25) !important;
          }
        }

        // 底部边框动画效果
        &::after {
          content: "";
          position: absolute;
          bottom: -2px;
          left: 50%;
          width: 100%;
          height: 3px;
          background: linear-gradient(
            90deg,
            transparent 0%,
            var(--border-color) 50%,
            transparent 100%
          );
          border-radius: 2px;
          transform: translateX(-50%) scaleX(0);
          transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
          z-index: 2;
        }

        // 聚焦时显示底部边框动画
        &:focus-within::after {
          transform: translateX(-50%) scaleX(1);
        }

        // 悬停时显示轻微的底部边框效果
        &:hover::after {
          transform: translateX(-50%) scaleX(0.3);
          opacity: 0.6;
        }

        // 输入框样式
        :deep(.ant-input) {
          border: none;
          outline: none;
          box-shadow: none;
          font-size: 16px;
          color: $title-color;
          background: transparent;
          padding: 0 8px;
          transition: all 0.3s ease;

          &::placeholder {
            color: $text-color;
            opacity: 0.7;
            transition: all 0.3s ease;
          }

          &:focus {
            border: none;
            outline: none;
            box-shadow: none;
            color: $title-color;

            &::placeholder {
              opacity: 0.5;
              transform: translateX(4px);
            }
          }
        }

        // 整个搜索框聚焦时的额外效果
        &:focus-within {
          :deep(.ant-input-affix-wrapper) {
            background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
          }
        }

        // 前缀图标（搜索图标）
        :deep(.ant-input-prefix) {
          margin-right: 8px;

          .search-icon {
            color: #767590;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;

            &:hover {
              color: var(--border-color);
              transform: scale(1.1);
            }
          }
        }

        // 当搜索框聚焦时，图标变为主题色
        &:focus-within :deep(.ant-input-prefix) {
          .search-icon {
            color: var(--border-color);
          }
        }

        // 当搜索框悬停时，图标颜色变化
        &:hover :deep(.ant-input-prefix) {
          .search-icon {
            color: var(--border-color);
          }
        }

        // 后缀图标（清除图标）
        :deep(.ant-input-suffix) {
          margin-left: 8px;

          .clear-icon {
            color: $text-color;
            cursor: pointer;
            transition: color 0.2s ease;
            font-size: 14px;

            &:hover {
              color: #ef4444;
            }
          }
        }

        // 清除按钮样式
        :deep(.ant-input-clear-icon) {
          color: $text-color;

          &:hover {
            color: #ef4444;
          }
        }
      }

      .refresh-btn {
        border-color: #d9d9d9;
        color: #595959;

        &:hover {
          border-color: $primary-color;
          color: $primary-color;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba($primary-color, 0.15);
        }

        &:active {
          transform: translateY(0);
        }
      }

      .add-btn {
        background-color: $primary-color;
        border-color: $primary-color;

        &:hover {
          background-color: darken($primary-color, 10%);
          border-color: darken($primary-color, 10%);
        }
      }
    }
  }

  // 可滚动的内容区域
  .content-body {
    height: calc(100vh - 200px);
    overflow-y: auto;
    padding: 20px 24px;
    min-height: 0; // 确保 flex 子元素可以收缩

    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: $border-color;
      border-radius: 3px;

      &:hover {
        background: $primary-color;
      }
    }
  }
}

// 卡片列表样式
.card-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
  padding-bottom: 20px;

  // 限制最多显示3列
  @media (min-width: 1400px) {
    grid-template-columns: repeat(3, 1fr);
  }
}

.rule-card {
  background-color: $white;
  border: 1px solid $border-color;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(112, 77, 233, 0.15);
    border-color: $primary-color;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 16px;

    .rule-title {
      flex: 1;
      min-width: 0;

      .rule-name {
        font-size: 18px;
        font-weight: 600;
        color: $title-color;
        margin: 0 0 12px 0;
        line-height: 1.4;
        word-break: break-word;
      }

      .tag-group {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        .type-tag {
          font-size: 12px;
          border-radius: 6px;
        }

        .business-action-tag {
          font-size: 11px;
          border-radius: 4px;
        }

        .status-tag {
          font-size: 11px;
          font-weight: 500;
          border-radius: 4px;
        }
      }
    }

    .card-actions {
      flex-shrink: 0;

      .ant-btn {
        color: $text-color;

        &:hover {
          color: $primary-color;
          background-color: $light-purple;
        }
      }
    }
  }
}

.rule-content {
  .info-row {
    display: flex;
    gap: 24px;
    margin-bottom: 14px;

    &.full-width {
      flex-direction: column;
      gap: 6px;
    }

    .info-item {
      flex: 1;
      min-width: 0;

      .label {
        font-size: 13px;
        color: $text-color;
        font-weight: 500;
        margin-right: 8px;
        display: inline-block;
        min-width: 80px;
      }

      .value {
        font-size: 14px;
        color: $title-color;
        word-break: break-word;

        &.score-value {
          font-weight: 600;
          color: $primary-color;
        }

        &.rule-text {
          line-height: 1.6;
          color: $text-color;
          margin-top: 4px;
          display: block;
        }
      }
    }
  }
}

// 空状态样式
.empty-state {
  text-align: center;
  padding: 80px 20px;
  background-color: $white;
  border-radius: 12px;
  border: 1px solid $border-color;

  .ant-btn-primary {
    background-color: $primary-color;
    border-color: $primary-color;

    &:hover {
      background-color: darken($primary-color, 10%);
      border-color: darken($primary-color, 10%);
    }
  }
}

// 加载状态样式
.loading-state {
  text-align: center;
  padding: 80px 20px;
  background-color: $white;
  border-radius: 12px;
  border: 1px solid $border-color;

  .loading-placeholder {
    height: 200px;
  }
}

// 分页组件样式
.pagination-wrapper {
  flex: 1;
  padding: 20px 24px;
  text-align: center;
  border-top: 1px solid $border-color;
  background-color: $white;
  flex-shrink: 0; // 防止分页组件被压缩

  .pagination {
    :deep(.ant-pagination-item) {
      border-color: $border-color;

      &:hover {
        border-color: $primary-color;
      }

      &.ant-pagination-item-active {
        background-color: #ffffff;
        border-color: $border-color;
      }
    }

    :deep(.ant-pagination-next),
    :deep(.ant-pagination-prev) {
      &:hover {
        border-color: $primary-color;
        color: $primary-color;
      }
    }

    :deep(.ant-pagination-jump-next),
    :deep(.ant-pagination-jump-prev) {
      &:hover {
        color: $primary-color;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .card-list {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  }
}

@media (max-width: 768px) {
  .page-container {
    flex-direction: column;
    height: 100vh;
  }

  .sidebar {
    width: 100%;
    height: auto;
    max-height: 200px;
    border-right: none;
    border-bottom: 1px solid $border-color;
    flex-shrink: 0;
    background: $white;

    &.sidebar-collapsed {
      width: 100%;
      height: 60px;
      max-height: 60px;
    }

    .sidebar-collapsed-content {
      flex-direction: row;
      justify-content: center;
      padding: 10px 0;

      .collapsed-toggle {
        padding: 0;

        .toggle-btn {
          width: 36px;
          height: 36px;
        }
      }
    }

    .sidebar-expanded-content {
      height: auto;
      max-height: 200px;

      .sidebar-header {
        .header-content {
          padding: 12px 16px;

          .sidebar-title {
            font-size: 16px;
          }

          .header-toggle-btn {
            width: 28px;
            height: 28px;
          }
        }
      }

      .sidebar-menu-container {
        max-height: 120px;

        .sidebar-menu {
          display: flex;
          overflow-x: auto;
          overflow-y: hidden;
          padding: 8px 12px;
          gap: 8px;

          &::-webkit-scrollbar {
            height: 4px;
            width: auto;
          }

          &::-webkit-scrollbar-track {
            background: transparent;
          }

          &::-webkit-scrollbar-thumb {
            background: rgba(112, 77, 233, 0.2);
            border-radius: 2px;
          }

          .menu-item {
            flex-shrink: 0;
            margin-bottom: 0;
            min-width: 120px;
            border-radius: 8px;

            &:hover {
              transform: translateY(-2px);
            }

            &.menu-item-active {
              transform: translateY(-2px);

              &::before {
                display: none;
              }

              &::after {
                content: "";
                position: absolute;
                bottom: 0;
                left: 50%;
                transform: translateX(-50%);
                width: 20px;
                height: 3px;
                background: $white;
                border-radius: 2px 2px 0 0;
              }
            }

            .menu-item-content {
              padding: 10px 12px;
              flex-direction: column;
              text-align: center;
              gap: 4px;

              .menu-icon {
                margin-right: 0;
                font-size: 16px;
              }

              .menu-text {
                font-size: 12px;
                white-space: nowrap;
              }
            }
          }
        }
      }

      .sidebar-footer {
        display: none;
      }
    }
  }

  .main-content {
    flex: 1;
    height: auto;
    // 移动端不需要特殊的展开样式

    .content-header {
      flex-direction: column;
      align-items: stretch;
      gap: 16px;
      padding: 16px 16px 0;

      .page-title {
        font-size: 20px;
        text-align: center;
      }

      .header-actions {
        flex-direction: column;
        gap: 12px;

        .custom-search-input {
          --width-of-input: 100%;
          --height-of-input: 36px;

          :deep(.ant-input) {
            font-size: 14px;
          }

          :deep(.ant-input-prefix) {
            .search-icon {
              font-size: 14px;
            }
          }

          :deep(.ant-input-suffix) {
            .clear-icon {
              font-size: 12px;
            }
          }
        }
      }
    }

    .content-body {
      padding: 0 16px;
    }
  }

  .card-list {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .rule-content {
    .info-row {
      flex-direction: column;
      gap: 8px;
      margin-bottom: 12px;

      .info-item {
        .label {
          min-width: auto;
          display: block;
          margin-bottom: 4px;
        }
      }
    }
  }

  .pagination-wrapper {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .main-content {
    .content-header {
      padding: 12px 12px 0;

      .header-actions {
        .custom-search-input {
          --height-of-input: 32px;

          :deep(.ant-input) {
            font-size: 13px;
          }

          :deep(.ant-input-prefix) {
            .search-icon {
              font-size: 12px;
            }
          }

          :deep(.ant-input-suffix) {
            .clear-icon {
              font-size: 10px;
            }
          }
        }
      }
    }

    .content-body {
      padding: 0 12px;
    }
  }

  .rule-card {
    padding: 16px;

    .card-header {
      .rule-title {
        .rule-name {
          font-size: 16px;
        }

        .tag-group {
          gap: 6px;
        }
      }
    }
  }

  .rule-content {
    .info-row {
      .info-item {
        .label {
          font-size: 12px;
        }

        .value {
          font-size: 13px;
        }
      }
    }
  }

  .sidebar {
    .sidebar-expanded-content {
      .sidebar-menu-container {
        .sidebar-menu {
          .menu-item {
            min-width: 100px;

            .menu-item-content {
              padding: 8px 10px;

              .menu-icon {
                font-size: 14px;
              }

              .menu-text {
                font-size: 11px;
              }
            }
          }
        }
      }
    }
  }

  .pagination-wrapper {
    padding: 12px;
  }
}
</style>
