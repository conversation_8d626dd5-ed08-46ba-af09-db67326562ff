<template>
  <div id="main" style="height: 400px; width: 100%"></div>
</template>

<script setup>
import * as echarts from "echarts";
import { nextTick } from "vue";

const props = defineProps(["data"]);

nextTick(() => {
  // 基于准备好的dom，初始化echarts实例
  var myChart = echarts.init(document.getElementById("main"));
  // 绘制图表
  myChart.setOption({
    tooltip: {},
    xAxis: {
      data: ["衬衫", "羊毛衫", "雪纺衫", "裤子", "高跟鞋", "袜子"],
    },
    yAxis: {},
    series: [
      {
        name: "销量",
        type: "bar",
        data: [5, 20, 36, 10, 10, 20],
      },
    ],
  });
});
</script>

<style></style>
