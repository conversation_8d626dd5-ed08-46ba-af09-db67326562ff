import { color } from "echarts";

export const legendMessageStore = [
  {
    id: "all",
    img: "../../static/icon/map/storeAll.png",
    name: "全部",
  },
  {
    id: "4",
    img: "../../static/icon/map/store44.png",
    name: "已开门店",
  },
  {
    id: "5",
    img: "../../static/icon/map/store55.png",
    name: "净销售额超5000",
  },
  {
    id: "1",
    img: "../../static/icon/map/store11.png",
    name: "未开拓周边有已开门店",
  },
  {
    id: "2",
    img: "../../static/icon/map/store22.png",
    name: "未开拓周边未有已开门店",
  },
  {
    id: "3",
    img: "../../static/icon/map/store33.png",
    name: "返货率大于20%",
  },
];

export const legendMessageProStore = [
  {
    id: "all",
    img: "../../static/icon/map/storeAll.png",
    name: "全部",
  },
  {
    id: "2",
    img: "../../static/icon/map/store11.png",
    name: "未开门店",
  },
  {
    id: "1",
    img: "../../static/icon/map/store44.png",
    name: "已开门店",
  },
  {
    id: "3",
    img: "../../static/icon/map/store88.png",
    name: "已卖货门店",
  },
  {
    id: "4",
    img: "../../static/icon/map/store99.png",
    name: "净销售额大于1000",
  },
  {
    id: "5",
    img: "../../static/icon/map/store33.png",
    name: "单类/单品返货大于20%",
  },
];
export const legendMessageCar = [
  {
    id: "1",
    img: "../../static/icon/map/che1.png",
    name: "离线",
  },
  {
    id: "2",
    img: "../../static/icon/map/che2.png",
    name: "正在行驶",
  },
  {
    id: "3",
    img: "../../static/icon/map/che3.png",
    name: "停车",
  },
  {
    id: "4",
    img: "../../static/icon/map/che4.png",
    name: "熄火停车",
  },
];

export const legendMessageTypeStore = [
  {
    id: "all",
    img: "../../static/icon/map/storeAll.png",
    name: "全部",
  },
  {
    id: "1",
    img: "../../static/icon/storetype/圆圈1.png",
    name: "学校工厂食堂",
  },
  {
    id: "2",

    img: "../../static/icon/storetype/圆圈2.png",
    name: "市区20店",
  },
  {
    id: "3",

    img: "../../static/icon/storetype/圆圈3.png",
    name: "蛋糕店",
  },
  {
    id: "4",

    img: "../../static/icon/storetype/圆圈4.png",
    name: "主食店",
  },
  {
    id: "5",

    img: "../../static/icon/storetype/圆圈5.png",
    name: "RKA",
  },
  {
    id: "6",

    img: "../../static/icon/storetype/圆圈6.png",
    name: "工地超市",
  },
  {
    id: "7",

    img: "../../static/icon/storetype/圆圈7.png",
    name: "地方连锁生鲜",
  },
  {
    id: "8",

    img: "../../static/icon/storetype/圆圈8.png",
    name: "奶站",
  },
  {
    id: "9",

    img: "../../static/icon/storetype/圆圈9.png",
    name: "村80店",
  },
  {
    id: "10",

    img: "../../static/icon/storetype/圆圈10.png",
    name: "镇80店",
  },
  {
    id: "11",

    img: "../../static/icon/storetype/圆圈11.png",
    name: "地方连锁卖场",
  },
  {
    id: "12",

    img: "../../static/icon/storetype/圆圈12.png",
    name: "熟食店",
  },
  {
    id: "13",

    img: "../../static/icon/storetype/圆圈13.png",
    name: "NKA",
  },
  {
    id: "14",

    img: "../../static/icon/storetype/圆圈14.png",
    name: "学校工厂超市",
  },
  {
    id: "15",

    img: "../../static/icon/storetype/圆圈15.png",
    name: "市区80店",
  },
  {
    id: "16",

    img: "../../static/icon/storetype/圆圈16.png",
    name: "LKA",
  },
  {
    id: "17",

    img: "../../static/icon/storetype/圆圈17.png",
    name: "快递站",
  },
  {
    id: "18",

    img: "../../static/icon/storetype/圆圈18.png",
    name: "镇20店",
  },
  {
    id: "19",

    img: "../../static/icon/storetype/圆圈19.png",
    name: "村20店",
  },
  {
    id: "20",

    img: "../../static/icon/storetype/圆圈20.png",
    name: "蛋糕店",
  },
  {
    id: "21",

    img: "../../static/icon/storetype/圆圈21.png",
    name: "cvs(连锁超市)",
  },
  {
    id: "22",

    img: "../../static/icon/storetype/圆圈22.png",
    name: "其他",
  },
];

export const markerTypeStoreStyle = () => {
  let styles = {};
  legendMessageTypeStore.forEach((item, index) => {
    if (index === 0) return; //样式全部时跳过
    styles[item.name] = new TMap.MarkerStyle({
      width: 15, // 样式宽
      height: 15, // 样式高
      src: "../../static/icon/storetype/圆圈" + index + ".png",
    });
  });
  return styles;
};
