<template>
  <div style="position: relative">
    <a-spin tip="加载中..." :spinning="store.loading">
      <a-radio-group
          style="position: absolute; right: 10px; top: 400rpx; z-index: 999"
          v-model:value="mapType"
          @change="changeMapType"
      >
        <a-radio-button value="vector">地图</a-radio-button>
        <a-radio-button value="satellite">卫星</a-radio-button>
      </a-radio-group>
      <div class="map-container" id="map"></div>
      <Legend
          :storeMarker="storeMarker"
          :carMarker="carMarker"
          @changeMapCenter="changeMapCenter"
      />
      <SelectList
          @changeChecked="changeChecked"
          @chooseJxs="chooseJxs"
          @chooseFxs="chooseFxs"
          @chooseArea="chooseArea"
          @chooseProType="chooseProType"
          @choosePro="choosePro"
          @chooseDq="chooseDq"
          @chooseJxq="chooseJxq"
      />
    </a-spin>
  </div>

  <u-overlay
      style="z-index: 998"
      :show="show"
      mask-click-able="false"
  ></u-overlay>
  <a-spin
      v-if="show"
      size="large"
      tip="加载中..."
      style="
      z-index: 999;
      position: absolute;
      top: 50%;
      left: 50%;
      color: #76d8ff;
    "
  />
  <a-modal
      v-model:open="store.selectObj.modalShow"
      :footer="null"
      title="门店信息"
      width="80%"
      :bodyStyle="Style"
  >
    <StoreInfoModal v-if="store.selectObj.modalShow" />
  </a-modal>
</template>

<script setup>
import { nextTick, ref } from "vue";
import Legend from "./components/legend/Legend";
import SelectList from "./components/select/SelectList.vue";
import StoreInfoModal from "./components/modal/StoreInfoModal.vue";
import { useMapStore } from "../../store/map";
import { onHide } from "@dcloudio/uni-app";
const store = useMapStore();
onHide(() => {
  store.clearCarTimer();
});

let map;
let polygon;
let storeMarker;
let carMarker;
let infoWindow;
let allView;
let label;
let streetLayer; //街道图层
let show = ref(false); // 遮罩
let mapType = ref("vector");

let Style = {
  overflowY: "scroll",
};
const initMap = async () => {
  const center = new TMap.LatLng(39.984104, 116.307503);
  map = new TMap.Map("map", {
    center: center,
    zoom: 6.5,
    viewMode: "2D",
    baseMap: {
      type: mapType.value, //类型：失量底图
    },
  });
  streetLayer = new TMap.MultiMarker({
    id: "street-layer",
    map,
    styles: {
      label: new TMap.MarkerStyle({
        width: 16, // 宽度
        height: 16, // 高度
        anchor: { x: 17, y: 23 }, // 标注点图片的锚点位置
        src: "/static/icon/map/redStar.png", // 标注点图片url或base64地址
        color: "#fb410e",
        size: 20, // 标注点文本文字大小
        direction: "bottom", // 标注点文本文字相对于标注点图片的方位
        offset: { x: 0, y: 8 }, // 标注点文本文字基于direction方位的偏移属性
        strokeColor: "#fff", // 标注点文本描边颜色
        strokeWidth: 2, // 标注点文本描边宽度
      }),
    },
    zIndex: 9,
  });
  streetLayer.setVisible(false);

  polygon = new TMap.MultiPolygon({
    id: "national-polygon",
    map: map,
    zIndex: 1,
  });

  storeMarker = new TMap.MultiMarker({
    map: map,
    zIndex: 10,
    // styles: markerTypeStoreStyle(),
  });

  carMarker = new TMap.MultiMarker({
    map: map,
    zIndex: 15,
  });

  infoWindow = new TMap.InfoWindow({
    map: map,
    position: new TMap.LatLng(0, 0),
    offset: {
      x: 0,
      y: -22,
    },
    content: "",
  });

  allView = new TMap.MultiPolygon({
    id: "notionWide",
    map: map,
    zIndex: 4,
  });

  label = new TMap.MultiLabel({
    map: map,
    styles: {
      lable: new TMap.LabelStyle({
        color: "#000",
        fontSize: "14px",
        fontWeight: "bold",
        wrapOptions: {}, // 设置换行显示
      }),
    },
    zIndex: 5,
  });

  // 获取区域底图
  await store.getAllAreas(polygon, map);
  // 获取所有大区
  await store.getAllRegion();
  // 获取所有经销区
  await store.getAllOutRegion(streetLayer);
  // 获取所有经销商
  await store.getAllJxsUserList();
  // show.value = false;
};

function changeMapCenter(lat, lng) {
  map.setCenter(new TMap.LatLng(lat, lng));
  map.setZoom(16);
}

//地图图层
async function changeChecked() {
  await store.changeChecked(
      polygon,
      storeMarker,
      carMarker,
      infoWindow,
      allView,
      label,
      streetLayer
  );
}
//选择大区
async function chooseDq(e, v) {
  store.chooseDq(map, polygon, storeMarker, carMarker, infoWindow, v);
}

//选择省区
async function chooseJxq(e, v) {
  store.chooseJxq(
      map,
      polygon,
      storeMarker,
      carMarker,
      infoWindow,
      v,
      streetLayer
  );
}

//选择经销商
async function chooseJxs(e, v) {
  store.chooseJxs(map, polygon, storeMarker, carMarker, infoWindow, v);
}

// 选择分销商
async function chooseFxs(e, v) {
  store.chooseFxs(storeMarker, infoWindow, carMarker, v);
}

// 选择省市区
async function chooseArea(e, v) {
  store.chooseArea(map, polygon, storeMarker, carMarker, infoWindow, e);
  show.value = true;
  setTimeout(() => {
    show.value = false;
  }, 1000);
}

// 选择产品类别
async function chooseProType(e, v) {
  store.chooseProType(storeMarker, infoWindow, v);
}

// 选择产品
async function choosePro(e, v) {
  store.choosePro(storeMarker, infoWindow, store.selectObj.productClass, v);
}

// 切换地图底图
async function changeMapType(val) {
  map.setBaseMap({ type: val.target.value });
}

nextTick(() => {
  initMap();
});
</script>

<style lang="scss" scoped>
.map-container {
  height: calc(100vh - 50px);
  width: 100vw;
  z-index: 1;
}
</style>
