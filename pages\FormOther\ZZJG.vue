<template>
  <div class="table-container">
    <!-- 筛选条件区域 -->
    <div class="filter-container">

<!--      <el-date-picker-->
<!--          v-model="filterDate"-->
<!--          type="date"-->
<!--          placeholder="选择日期"-->
<!--          @change="handleDateFilter">-->
<!--      </el-date-picker>-->
      <el-input
          v-model="dq"
          placeholder="请输入大区"
          @click="dqclick('dq')"
          style="flex: 2;width: 200px;margin-left: 20px;">
      </el-input>
      <!--      <el-button-->
      <!--          type="primary"-->
      <!--          @click="dqclick">-->
      <!--        大区筛选-->
      <!--      </el-button>-->
      <el-input
          v-model="sq"
          placeholder="请输入省区"
          @click="dqclick('sq')"
          style="flex: 2;width: 200px;margin-left: 20px;">
      </el-input>
      <!--      <el-button-->
      <!--          type="primary"-->
      <!--          @click="showRegionDialog = true">-->
      <!--        省区筛选-->
      <!--      </el-button>-->
      <el-input
          v-model="jxq"
          placeholder="请输入经销区"
          @click="dqclick('jxq')"
          style="flex: 2;width: 200px;margin-left: 20px;">
      </el-input>
      <el-input
          v-model="jxkf"
          placeholder="请输入经销库房"
          @click="dqclick('jxkf')"
          style="flex: 2;width: 200px;margin-left: 20px;">
      </el-input>
      <!--      <el-button-->
      <!--          type="primary"-->
      <!--          @click="showRegionDialog = true">-->
      <!--        经销库房筛选-->
      <!--      </el-button>-->


      <el-button
          type="info"
          @click="resetFilters">
        重置筛选
      </el-button>
    </div>

    <!-- 大区筛选弹出框 -->
    <el-dialog
        title="选择大区"
        :visible.sync="showRegionDialog"
        width="30%"
        append-to-body>
      <el-checkbox-group v-model="selectedRegions">
        <el-checkbox
            v-for="region in regions"
            :key="region.value"
            :label="region.value">
          {{ region.label }}
        </el-checkbox>
      </el-checkbox-group>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showRegionDialog = false">取消</el-button>
        <el-button type="primary" @click="handleRegionFilter">确定</el-button>
      </span>
    </el-dialog>

    <!-- 表格区域 -->
    <el-table
        :data="filteredTableData"
        style="width: 100%"
        border
        stripe
        @row-click="handleRowClick">

<!--      <el-table-column type="expand">-->
<!--        <template #default="props">-->
<!--          <div v-if="props.row.DistributionOrganizationName">-->
<!--            &lt;!&ndash; 这里放你的展开内容 &ndash;&gt;-->
<!--            <p>详情数据1: </p>-->
<!--            <p>详情数据2: </p>-->
<!--            &lt;!&ndash; 可以根据需要自定义内容 &ndash;&gt;-->
<!--          </div>-->
<!--          <div v-else>-->
<!--            加载中...-->
<!--          </div>-->
<!--        </template>-->
<!--      </el-table-column>-->

      <el-table-column
          prop="DistributionOrganizationName"
          label="经销库房"
          width="220">
      </el-table-column>

      <el-table-column
          prop="Organization_Name2"
          label="大区"
          width="220">
      </el-table-column>

      <el-table-column
          prop="Organization_Name3"
          label="省区"
          width="220">
      </el-table-column>
      <el-table-column
          prop="Organization_Name"
          label="经销区"
          width="220">
      </el-table-column>
      <el-table-column
          label="操作"
          width="120"
          fixed="right">
        <template #default="scope">
          <el-button
              size="small"
              @click.stop="toggleExpand(scope.row, scope.$index)">
            {{ scope.row.expanded ? '收起' : '详情' }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
  <Modal
      v-if="showModal"
      :show="showModal"
      title="请选择"
      @close="showModal = false"
      @confirm="handleConfirm"
  >
    <MultiSelectListOne
        :items="allItems"
        :items2="allItems2"
        @update:selected-items="update($event)"
        @update:close="close($event)"
        @update:confirm="confirm($event)"
        v-model:selected-items="tempSelectedItems"
        :items-per-page="pageSize"
        :current-page="currentPage"
        :total-pages="totalPages"
        :single-select="true"
    />
  </Modal>

  <Modal
      v-if="showModal2"
      :show="showModal2"
      title="请选择"
      @close="showModal2 = false"
      @confirm="handleConfirm"
  >
    <MultiSelectListView
        :items="allItems3"
        :items2="allItems4"
        @update:selected-items="update($event)"
        @update:close="close($event)"
        @update:confirm="confirm($event)"
        v-model:selected-items="tempSelectedItems"
        :items-per-page="pageSize"
        :current-page="currentPage"
        :total-pages="totalPages"
        :single-select="true"
    />
  </Modal>

</template>

<script>
import {GetAllDqNew, GetAllJxqNew, GetAllSqNew} from "/api/Map/index.js";
import { get_rzkf, } from "/api/workbench/trainmastersigning/index.js";
import {get_zzjg, get_zzjg_cz_new} from "/api/workbench/JXYL/index.js";
import Modal from "@/pages/workbench/mod/Modal.vue";
import MultiSelectListOne from "@/pages/workbench/mod/MultiSelectListOne.vue";
import MultiSelectListView from "@/pages/workbench/mod/MultiSelectListView.vue";
import {ref} from "vue";
import dayjs from "dayjs";
export default {
  components: {
    Modal,
    MultiSelectListOne,
    MultiSelectListView
  },
  data() {
    const today = new Date();
    const formattedDate = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;

    return {
      tableData: [], // 初始为空数组，将在created钩子中填充数据
      filterDate: formattedDate, // 单日期筛选值
      dq: null, // 大区
      sq: null, // 省区
      jxq: null, // 经销区
      dqid: '', // 大区
      sqid: '', // 省区
      jxqid: '', // 经销区
      jxkf: null, // 经销库房
      jxkfid: '', // 经销库房
      selectedRegions: [], // 选中大区
      allItems: [],
      allItems2: [],
      allItems3: [],
      allItems4: [],
      pageType: 1,
      currentPage: 1,
      pageSize: 999,
      totalPages: 1,
      showModal: false,
      showModal2: false,
      showRegionDialog: false, // 是否显示大区筛选弹窗
      regions: [ // 大区选项
        { label: '华东', value: '华东' },
        { label: '华北', value: '华北' },
        { label: '华南', value: '华南' },
        { label: '西南', value: '西南' },
        { label: '西北', value: '西北' },
        { label: '东北', value: '东北' }
      ],
      activeFilters: { // 当前生效的筛选条件
        date: null,
        regions: []
      },
      tempSelectedItems: [],
      loading: false // 新增加载状态
    }
  },

  // 新增的生命周期钩子
  created() {
    this.fetchJxylData();
  },

  computed: {
    // 筛选后的表格数据
    filteredTableData() {
      return this.tableData;
    }
  },

  methods: {
    // 新增的获取数据方法
    async fetchJxylData() {
      try {
        this.loading = true;
        const response = await get_zzjg(this.dqid,this.sqid,this.jxqid,this.jxkfid);
        // 假设API返回的数据结构是{ data: [...] }
        this.tableData = response.data.result[0]
      } catch (error) {
        console.error('获取经销一览数据失败:', error);
        // 可以根据需要添加错误处理，如显示提示消息
        this.$message.error('获取数据失败，请稍后重试');
      } finally {
        this.loading = false;
      }
    },

    // 日期筛选
    async handleDateFilter(val) {
      // console.log(val)
      // console.log(this.filterDate)
      const year = val.getFullYear();
      const month = String(val.getMonth() + 1).padStart(2, '0');
      const day = String(val.getDate()).padStart(2, '0');
      const formattedDate = `${year}-${month}-${day}`;

      this.activeFilters.date = formattedDate;
      this.filterDate= formattedDate;
      try {
        this.loading = true;
        const response = await get_zzjg(this.dqid,this.sqid,this.jxqid,this.jxkfid);
        // 假设API返回的数据结构是{ data: [...] }
        console.log('response.data.result',response.data.result)
        this.tableData = response.data.result[0]
      } catch (error) {
        console.error('获取经销一览数据失败:', error);
        // 可以根据需要添加错误处理，如显示提示消息
        this.$message.error('获取数据失败，请稍后重试');
      } finally {
        this.loading = false;
      }

    },

    // 大区筛选
    handleRegionFilter() {
      this.activeFilters.regions = [...this.selectedRegions];
      this.showModal = false;
    },
    async dqclick(value){
      console.log('this.showRegionDialog',this.showRegionDialog)
      this.pageType = value
      if(value == 'dq'){
        let res = await GetAllDqNew();
        this.allItems = [];
        for (var i = 0; i < res.data.result.length; i++) {
          this.allItems.push({
            value: res.data.result[i].id,
            label: res.data.result[i].DistributionOrganizationName,
            label2: res.data.result[i].DistributionOrganizationName2,
            label3: res.data.result[i].DistributionOrganizationName3,
            radio: 0,
          });
        }
        this.allItems2 = [];
        this.allItems2.push({
          value: 1,
          label: "大区名称",


          radio: 0,
        });
        console.log("ssss");

      }else if(value == 'sq'){
        let res = await GetAllSqNew();
        this.allItems = [];
        for (var i = 0; i < res.data.result.length; i++) {
          this.allItems.push({
            value: res.data.result[i].id,
            label: res.data.result[i].DistributionOrganizationName,
            label2: res.data.result[i].DistributionOrganizationName2,
            label3: res.data.result[i].DistributionOrganizationName3,
            radio: 0,
          });
        }
        this.allItems2 = [];
        this.allItems2.push({
          value: 1,
          label: "省区名称",
          radio: 0,
        });
        console.log("ssss");
      }else if(value == 'jxq'){
        let res = await GetAllJxqNew();
        this.allItems = [];
        for (var i = 0; i < res.data.result.length; i++) {
          this.allItems.push({
            value: res.data.result[i].id,
            label: res.data.result[i].DistributionOrganizationName,
            label2: res.data.result[i].DistributionOrganizationName2,
            label3: res.data.result[i].DistributionOrganizationName3,
            radio: 0,
          });
        }
        this.allItems2 = [];
        this.allItems2.push({
          value: 1,
          label: "经销区名称",
          label2: "所属省区",
          label3: "所属大区",
          radio: 0,
        });
        console.log("ssss");
      }else if(value == 'jxkf'){
        let res = await get_rzkf();
        this.allItems = [];
        for (var i = 0; i < res.data.result.length; i++) {
          this.allItems.push({
            value: res.data.result[i].id,
            label: res.data.result[i].DistributionOrganizationName,
            label2: res.data.result[i].DistributionOrganizationName2,
            label3: res.data.result[i].DistributionOrganizationName3,
            radio: 0,
          });
        }
        this.allItems2 = [];
        this.allItems2.push({
          value: 1,
          label: "名称",
          label2: "所属省区",
          label3: "所属大区",
          radio: 0,
        });
        console.log("ssss");
      }


      this.showModal = false;
      this.$nextTick(() => {
        this.showModal = true;
      });
    },
    // 重置筛选
    async resetFilters() {
      this.filterDate = null;
      this.dq = null;
      this.sq = null;
      this.jxq = null;
      this.jxkf = null;

      this.dqid = '';
      this.sqid = '';
      this.jxqid = '';
      this.jxkfid = '';

      this.selectedRegions = [];
      this.activeFilters = {
        date: null,
        regions: []
      };
      const response = await get_zzjg(this.dqid,this.sqid,this.jxqid,this.jxkfid);
      // 假设API返回的数据结构是{ data: [...] }
      this.tableData = response.data.result[0]
    },
    close(index, row) {
      this.showModal2 = false;
    },
    handleEdit(index, row) {
      console.log(index, row);
      // 这里可以添加编辑逻辑
    },

    handleDelete(index, row) {
      console.log(index, row);
      // 这里可以添加删除逻辑
    },
    async toggleExpand(row, index) {
      // // 如果点击的是已展开的行，则收起
      // if (row.expanded) {
      //   row.expanded = false;
      //   this.currentExpandRow = null;
      //   return;
      // }
      //
      // // 收起其他已展开的行
      // if (this.currentExpandRow !== null && this.currentExpandRow !== index) {
      //   this.filteredTableData[this.currentExpandRow].expanded = false;
      // }
      //
      // // 展开当前行
      // row.expanded = true;
      // this.currentExpandRow = index;
      //
      // // 如果还没有加载过数据，则加载
      // if (!row.expandData) {
      //   this.loadExpandData(row);
      // }
      console.log(row)
      this.showModal2 = true;
      this.allItems3 = [];
      let res = await get_zzjg_cz_new(row.DistributionOrganizationNumber);

      for (var i = 0; i < res.data.result.length; i++) {
        this.allItems3.push({
          value: res.data.result[i].id,
          label: res.data.result[i].Master_data_person_name,
          label2: res.data.result[i].Role_name,
          label3: res.data.result[i].Master_data_person_tel,
          label4: res.data.result[i].Licence_number,
          radio: 0,
        });
      }
      this.allItems4 = [];
      this.allItems4.push({
        value: 1,
        label: "名称",
        label2: "车长",
        label3: "手机号",
        label4: "车牌号",
        radio: 0,
      });
      console.log("ssss");

    },

    async loadExpandData(row) {
      // 模拟异步加载数据
      try {
        // 这里替换为你的实际API调用
        // const res = await getDetailData(row.id);
        row.expandData = {
          detail1: '详情内容1',
          detail2: '详情内容2'
          // ...从API获取的实际数据
        };
      } catch (error) {
        console.error('加载详情数据失败:', error);
      }
    },
    handleRowClick(row, column, event) {
      console.log('行点击事件', row);
    },
    handleConfirm(row) {
      console.log('行点击事件', row);
    },
    async confirm(item) {
      console.log('行点击事件', item);

      if(this.pageType == 'dq'){
        this.dq = item.label
        this.dqid = item.value
      }else if(this.pageType == 'sq'){
        this.sq = item.label
        this.sqid = item.value
      }else if(this.pageType == 'jxq'){
        this.jxq = item.label
        this.jxqid = item.value
      }
      this.showModal = false;
      const response = await get_zzjg(this.dqid,this.sqid,this.jxqid,this.jxkfid);
      // 假设API返回的数据结构是{ data: [...] }
      this.tableData = response.data.result[0]
    },
    //
    // const confirm = (item) => {
    //   console.log("item", item);
    //   formData[info.value] = item.label;
    //   if (info.value == "rzkf") {
    //     rzkfid.value = item.value;
    //     formData["sssq"] = item.label2;
    //     formData["ssdq"] = item.label3;
    //   } else if (info.value == "wlfcmc") {
    //     // 存储物流分仓ID，以便后续使用
    //     formData["wlfcId"] = item.value;
    //     formData["wlfcbm"] = `WLFC${dayjs().format("YYYYMMDDHHmmss")}`;
    //   }
    //
    //   showModal.value = false;
    // };
  }
}
</script>

<style scoped>
.table-container {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.filter-container > * {
  margin-right: 10px;
}
</style>