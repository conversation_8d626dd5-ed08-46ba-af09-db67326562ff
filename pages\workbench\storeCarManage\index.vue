<template>
  <view class="store-car-manage">
    <!-- 左侧筛选面板 -->
    <view
      class="left-panel"
      :class="{ collapsed: isCollapsed, expanded: !isCollapsed }"
    >
      <!-- PC端：窄条状态的展开图标 -->
      <view
        class="sidebar-toggle"
        v-if="!isMobile && isCollapsed"
        @click="toggleSidebar"
      >
        <svg class="expand-icon" viewBox="0 0 24 24" width="24" height="24">
          <path
            d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"
            fill="currentColor"
          />
          <path
            d="M12.59 16.59L17.17 12 12.59 7.41 14 6l6 6-6 6-1.41-1.41z"
            fill="currentColor"
          />
        </svg>
      </view>

      <!-- 筛选卡片内容 -->
      <view class="filter-card" v-show="!isCollapsed || isMobile">
        <!-- PC端：展开状态的收起按钮 -->
        <view
          class="collapse-button"
          v-if="!isMobile && !isCollapsed"
          @click="toggleSidebar"
        >
          <svg class="collapse-icon" viewBox="0 0 24 24" width="16" height="16">
            <path
              d="M15.41 16.59L10.83 12l4.58-4.59L14 6l-6 6 6 6 1.41-1.41z"
              fill="currentColor"
            />
            <path
              d="M9.41 16.59L4.83 12l4.58-4.59L8 6l-6 6 6 6 1.41-1.41z"
              fill="currentColor"
            />
          </svg>
          <span>收起</span>
        </view>

        <view class="filter-title">筛选条件</view>

        <!-- 大区选择 -->
        <view class="filter-item" v-if="canSelectDq">
          <view class="filter-label">大区：</view>
          <a-select
            v-model:value="dqValue"
            style="width: 100%"
            placeholder="请选择大区"
            allow-clear
            :options="dqOption"
            @change="onDqChange"
            show-search
            option-filter-prop="label"
          />
        </view>

        <!-- 省区选择 -->
        <view class="filter-item" v-if="canSelectSq">
          <view class="filter-label">省区：</view>
          <a-select
            v-model:value="sqValue"
            style="width: 100%"
            placeholder="请选择省区"
            allow-clear
            :options="sqOption"
            @change="onSqChange"
            show-search
            option-filter-prop="label"
          />
        </view>

        <!-- 经销区选择 -->
        <view class="filter-item" v-if="canSelectJxq">
          <view class="filter-label">经销区：</view>
          <a-select
            v-model:value="jxqValue"
            style="width: 100%"
            placeholder="请选择经销区"
            allow-clear
            :options="jxqOption"
            @change="onJxqChange"
            show-search
            option-filter-prop="label"
          />
        </view>

        <!-- 合伙企业选择 -->
        <view class="filter-item">
          <view class="filter-label">合伙企业：</view>
          <a-select
            v-model:value="partnerValue"
            style="width: 100%"
            placeholder="请选择合伙企业"
            allow-clear
            :options="partnerOption"
            @change="onPartnerChange"
            show-search
            option-filter-prop="label"
            :disabled="!jxqValue"
          />
        </view>

        <!-- 库长选择 -->
        <view class="filter-item">
          <view class="filter-label">库长：</view>
          <a-select
            v-model:value="managerValue"
            style="width: 100%"
            placeholder="请选择库长"
            allow-clear
            :options="managerOption"
            @change="onManagerChange"
            show-search
            option-filter-prop="label"
            :disabled="!partnerValue"
          />
        </view>

        <!-- 车长选择 -->
        <view class="filter-item">
          <view class="filter-label">车长：</view>
          <a-select
            v-model:value="driverValue"
            style="width: 100%"
            placeholder="请选择车长"
            allow-clear
            :options="driverOption"
            @change="onDriverChange"
            show-search
            option-filter-prop="label"
            :disabled="!managerValue"
          />
        </view>

        <!-- 日期范围选择 -->
        <view class="filter-item">
          <view class="filter-label">日期范围：</view>
          <a-config-provider :locale="locale">
            <a-range-picker
              v-model:value="dateRange"
              style="width: 100%"
              :disabled-date="disabledDate"
              @change="onDateRangeChange"
              format="YYYY-MM-DD"
            />
          </a-config-provider>
        </view>

        <!-- 查询按钮 -->
        <view class="filter-item">
          <a-button
            type="primary"
            block
            @click="handleQuery"
            :loading="loading"
          >
            查询
          </a-button>
        </view>
      </view>
    </view>

    <!-- 右侧内容区域 -->
    <view class="right-panel">
      <!-- 统计表格 -->
      <view class="stats-section">
        <view class="stats-table">
          <!-- 第一行表头 -->
          <view class="stats-row header-row">
            <view class="stats-cell">IMEI总数量</view>
            <view class="stats-cell">今天出车数</view>
            <view class="stats-cell clickable" @click="showUnusedCarModal"
              >未出车数</view
            >
          </view>
          <!-- 第二行数值 -->
          <view class="stats-row value-row">
            <view class="stats-cell">
              <span class="mobile-only">IMEI总数量：</span>
              <span class="stats-value">{{ statsData.totalImei }}</span>
            </view>
            <view class="stats-cell">
              <span class="mobile-only">今天出车数：</span>
              <span class="stats-value">{{ statsData.todayUsed }}</span>
            </view>
            <view class="stats-cell clickable" @click="showUnusedCarModal">
              <span class="mobile-only">未出车数：</span>
              <span class="stats-value">{{ statsData.unused }}</span>
            </view>
          </view>
          <!-- 第三行表头 -->
          <view class="stats-row header-row">
            <view class="stats-cell">应出勤次数</view>
            <view class="stats-cell">出勤时长不合格次数</view>
            <view class="stats-cell">有效工时不合格次数</view>
          </view>
          <!-- 第四行数值 -->
          <view class="stats-row value-row">
            <view class="stats-cell">
              <span class="mobile-only">应出勤次数：</span>
              <span class="stats-value">{{ statsData.shouldAttend }}</span>
            </view>
            <view class="stats-cell">
              <span class="mobile-only">出勤时长不合格次数：</span>
              <span class="stats-value">{{ statsData.timeUnqualified }}</span>
            </view>
            <view class="stats-cell">
              <span class="mobile-only">有效工时不合格次数：</span>
              <span class="stats-value">{{
                statsData.workTimeUnqualified
              }}</span>
            </view>
          </view>
        </view>
      </view>

      <!-- 折线图 -->
      <view class="chart-section">
        <view class="chart-container" id="chartContainer">
          <view v-if="!chartInstance" class="chart-loading">
            <a-spin size="large" />
            <div style="margin-top: 10px">图表加载中...</div>
          </view>
        </view>
      </view>

      <!-- 单车出勤工时趋势图 -->
      <view class="chart-section">
        <view class="chart-container" id="workTimeChartContainer">
          <view v-if="!workTimeChartInstance" class="chart-loading">
            <a-spin size="large" />
            <div style="margin-top: 10px">图表加载中...</div>
          </view>
        </view>
      </view>

      <!-- 单车有效工时趋势图 -->
      <view class="chart-section">
        <view class="chart-container" id="validWorkTimeChartContainer">
          <view v-if="!validWorkTimeChartInstance" class="chart-loading">
            <a-spin size="large" />
            <div style="margin-top: 10px">图表加载中...</div>
          </view>
        </view>
      </view>

      <!-- 车辆出勤有效时长对比图 -->
      <view class="chart-section">
        <view class="chart-container" id="stackedChartContainer">
          <view v-if="!stackedChartInstance" class="chart-loading">
            <a-spin size="large" />
            <div style="margin-top: 10px">图表加载中...</div>
          </view>
        </view>
      </view>

      <!-- 车辆拜访情况表格 -->
      <view class="chart-section">
        <view class="chart-title">车辆拜访情况表格</view>
        <a-config-provider :locale="locale">
          <div class="visit-table-wrapper">
            <!-- 表格滚动容器 -->
            <div class="visit-table-scroll-container">
              <a-table
                :columns="visitTableColumns"
                :data-source="visitTableData"
                :pagination="false"
                size="small"
                :scroll="{ x: 'max-content' }"
                :locale="{
                  emptyText: '暂无数据',
                }"
                class="visit-table"
                bordered
              />
            </div>
          </div>
        </a-config-provider>
      </view>

      <!-- 车辆管理情况表格 -->
      <view class="chart-section chart-section-last">
        <!-- 标题与查询控件水平布局 -->
        <view class="chart-header-with-controls">
          <view class="chart-title">车辆管理情况</view>
          <view class="table-query-controls">
            <a-config-provider :locale="locale">
              <a-date-picker
                v-model:value="carManageTableDate"
                style="width: 200px; margin-right: 10px"
                format="YYYY-MM-DD"
                placeholder="选择日期"
              />
            </a-config-provider>
            <a-button
              type="primary"
              @click="handleCarManageTableQuery"
              :loading="carManageTableLoading"
              style="margin-right: 10px"
            >
              查询
            </a-button>
            <a-button
              type="primary"
              @click="handleExportExcel"
              :loading="exportLoading"
            >
              <template #icon>
                <DownloadOutlined />
              </template>
              导出Excel
            </a-button>
          </view>
        </view>
        <!-- 车辆管理表格 -->
        <a-config-provider :locale="locale">
          <div class="car-manage-table-wrapper">
            <!-- 表格滚动容器 -->
            <div class="car-manage-table-scroll-container">
              <a-table
                :columns="carManageTableColumns"
                :data-source="carManageTableData"
                :pagination="false"
                :loading="carManageTableLoading"
                :locale="{
                  emptyText: '暂无数据',
                }"
                :scroll="{ x: 'max-content' }"
                :show-header="true"
                class="car-manage-table"
                bordered
              />
            </div>

            <!-- 分页组件 -->
            <div class="car-manage-pagination-wrapper">
              <a-pagination
                v-model:current="carManageTablePagination.current"
                v-model:page-size="carManageTablePagination.pageSize"
                :total="carManageTablePagination.total"
                :show-size-changer="true"
                :show-quick-jumper="true"
                :page-size-options="['10', '20', '50', '100']"
                size="small"
                @change="onCarManageTablePageChange"
                @show-size-change="onCarManageTablePageChange"
              />
            </div>
          </div>
        </a-config-provider>
      </view>
    </view>

    <!-- 未出车明细弹窗 -->
    <a-modal
      v-model:open="unusedCarModalVisible"
      title="未出车明细"
      width="1000px"
      :footer="null"
      :destroy-on-close="true"
    >
      <a-config-provider :locale="locale">
        <a-table
          :columns="unusedCarColumns"
          :data-source="unusedCarData"
          :pagination="{
            current: unusedCarPagination.current,
            pageSize: unusedCarPagination.pageSize,
            total: unusedCarPagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            pageSizeOptions: ['10', '20', '50', '100'],
            onChange: onUnusedCarPageChange,
          }"
          :loading="unusedCarLoading"
          size="small"
          :scroll="{ x: 600 }"
        />
      </a-config-provider>
    </a-modal>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, nextTick, watch } from "vue";
import { message } from "ant-design-vue";
import zhCN from "ant-design-vue/es/locale/zh_CN"; // 引入中文包
import dayjs from "dayjs";
import "dayjs/locale/zh-cn"; // 引入 dayjs 中文语言包
import * as echarts from "echarts";
import * as XLSX from "xlsx";
import { saveAs } from "file-saver";
import { useUserInfo } from "/store/user/userInfo";
import {
  getAllRegionApi,
  getAllRegionApi_dqz,
  getAllProvincesApi,
  getAllProvincesApi_sqz,
  getAllJxqApi,
  getAllJxqApiCS,
  getPersonListApi,
  getUnusedCarListApi,
  getAllManageCarListApi,
  getManageCarListApi,
  getUserManageCarListApi,
  getCarManageTableApi,
} from "/api/workbench/storeCarManage/index.js";
import { DownloadOutlined } from "@ant-design/icons-vue";

const userStore = useUserInfo();

// 响应式侧边栏状态
const isCollapsed = ref(false); // PC端默认展开
const isMobile = ref(false); // 是否为移动端

// 用户角色信息
const userRole = ref("");
const userId = ref("");

// 筛选条件
const dqValue = ref(undefined);
const sqValue = ref(undefined);
const jxqValue = ref(undefined);
const partnerValue = ref(undefined);
const managerValue = ref(undefined);
const driverValue = ref(undefined);
const dateRange = ref([]);

// 下拉选项
const dqOption = ref([]);
const sqOption = ref([]);
const jxqOption = ref([]);
const partnerOption = ref([]);
const managerOption = ref([]);
const driverOption = ref([]);

// 人员原始数据
const partnerDataAll = ref([]);
const managerDataAll = ref([]);
const driverDataAll = ref([]);

// 原始数据备份
const dqOptionAll = ref([]);
const sqOptionAll = ref([]);
const jxqOptionAll = ref([]);

// 权限控制
const canSelectDq = ref(true); // 是否可以选择大区
const canSelectSq = ref(true); // 是否可以选择省区
const canSelectJxq = ref(true); // 是否可以选择经销区
const showAllCompany = ref(false); // 是否显示全公司选项

// 统计数据
const statsData = reactive({
  totalImei: 0,
  todayUsed: 0,
  unused: 0,
  shouldAttend: 0,
  timeUnqualified: 0,
  workTimeUnqualified: 0,
});

// 未出车明细弹窗
const unusedCarModalVisible = ref(false);
const unusedCarData = ref([]);
const unusedCarLoading = ref(false);
const unusedCarPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  pageSizeOptions: ["10", "20", "50", "100"],
  showTotal: false, // 不显示总数
});

const unusedCarColumns = [
  {
    title: "设备编码",
    dataIndex: "IMEI_code",
    key: "IMEI_code",
    width: 150,
  },
  {
    title: "车牌号",
    dataIndex: "Licence_number",
    key: "Licence_number",
    width: 120,
  },
  {
    title: "经销商姓名",
    dataIndex: "retailer_r_name",
    key: "retailer_r_name",
    width: 150,
  },
  {
    title: "最后心跳时间",
    dataIndex: "last_heartbeat",
    key: "last_heartbeat",
    width: 180,
  },
];

// 图表相关
let chartInstance = null;
let workTimeChartInstance = null;
let validWorkTimeChartInstance = null;
let stackedChartInstance = null;

// 加载状态
const loading = ref(false);

// 保存最后一次成功查询的条件（用于未出车明细查询）
const lastSuccessfulQueryConditions = reactive({
  dqValue: undefined,
  sqValue: undefined,
  jxqValue: undefined,
  partnerValue: undefined,
  managerValue: undefined,
  driverValue: undefined,
  dateRange: [],
  selectedPersonInfo: {
    userId: null,
    userType: null,
  },
});

// 车辆拜访情况表格数据
const visitTableData = ref([]);
const visitTableColumns = [
  {
    title: "日期",
    dataIndex: "route_date",
    key: "route_date",
    width: 120,
  },
  {
    title: "公里数",
    dataIndex: "km",
    key: "km",
    width: 100,
  },
  {
    title: "应访数",
    dataIndex: "yf_num",
    key: "yf_num",
    width: 100,
  },
  {
    title: "实访数",
    dataIndex: "sf_num",
    key: "sf_num",
    width: 100,
  },
  {
    title: "未访数",
    dataIndex: "wf_num",
    key: "wf_num",
    width: 100,
  },
  {
    title: "拜访率(%)",
    dataIndex: "bfl",
    key: "bfl",
    width: 120,
    customRender: ({ text }) => {
      return text ? `${text}%` : "0%";
    },
  },
];

// 车辆管理情况表格数据
const carManageTableData = ref([]);
const carManageTableLoading = ref(false);
const carManageTableDate = ref(dayjs()); // 独立的日期选择器，默认为当天
const exportLoading = ref(false); // 导出Excel的loading状态
const carManageTablePagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  pageSizeOptions: ["10", "20", "50", "100"],
  showTotal: false,
});

const carManageTableColumns = [
  {
    title: "IMEI号",
    dataIndex: "imei",
    key: "imei",
    width: 150,
  },
  {
    title: "车牌号",
    dataIndex: "license_plate",
    key: "license_plate",
    width: 120,
  },
  {
    title: "车长",
    dataIndex: "driver_name",
    key: "driver_name",
    width: 100,
  },
  {
    title: "日期",
    dataIndex: "date",
    key: "date",
    width: 120,
  },
  {
    title: "负责人",
    dataIndex: "manager_name",
    key: "manager_name",
    width: 100,
  },
  {
    title: "里程（km）",
    dataIndex: "mileage",
    key: "mileage",
    width: 100,
  },
  {
    title: "应访家数",
    dataIndex: "should_visit",
    key: "should_visit",
    width: 100,
  },
  {
    title: "应付超期门店",
    dataIndex: "overdue_stores",
    key: "overdue_stores",
    width: 120,
  },
  {
    title: "实访家数",
    dataIndex: "actual_visit",
    key: "actual_visit",
    width: 100,
  },
  {
    title: "线外拜访",
    dataIndex: "offline_visit",
    key: "offline_visit",
    width: 100,
  },
  {
    title: "新增家数",
    dataIndex: "new_stores",
    key: "new_stores",
    width: 100,
  },
  {
    title: "成交家数",
    dataIndex: "deal_stores",
    key: "deal_stores",
    width: 100,
  },
  {
    title: "成交金额",
    dataIndex: "deal_amount",
    key: "deal_amount",
    width: 120,
  },
  {
    title: "首店时间",
    dataIndex: "first_store_time",
    key: "first_store_time",
    width: 120,
  },
  {
    title: "末店时间",
    dataIndex: "last_store_time",
    key: "last_store_time",
    width: 120,
  },
  {
    title: "有效工时",
    dataIndex: "effective_hours",
    key: "effective_hours",
    width: 100,
  },
  {
    title: "在店时长",
    dataIndex: "store_duration",
    key: "store_duration",
    width: 100,
  },
  {
    title: "出车时间",
    dataIndex: "departure_time",
    key: "departure_time",
    width: 120,
  },
  {
    title: "收车时间",
    dataIndex: "return_time",
    key: "return_time",
    width: 120,
  },
  {
    title: "出勤时长",
    dataIndex: "attendance_duration",
    key: "attendance_duration",
    width: 100,
  },
];

// 车辆管理情况表格分页变化处理（提前定义以避免Vue警告）
const onCarManageTablePageChange = (page, pageSize) => {
  carManageTablePagination.current = page;
  carManageTablePagination.pageSize = pageSize;
  loadCarManageTableData(page, pageSize);
};

// 设置 dayjs 使用中文
dayjs.locale("zh-cn");
// 创建响应式的 locale 对象
const locale = ref(zhCN);

// 侧边栏切换函数
const toggleSidebar = () => {
  if (!isMobile.value) {
    isCollapsed.value = !isCollapsed.value;

    // 等待DOM更新完成后重绘图表
    nextTick(() => {
      setTimeout(() => {
        resizeAllCharts();
      }, 350); // 稍微延迟以确保CSS过渡动画完成
    });
  }
};

// 检测屏幕尺寸
const checkScreenSize = () => {
  const width = window.innerWidth;
  const wasMobile = isMobile.value;
  isMobile.value = width < 768;

  // 移动端强制展开侧边栏内容
  if (isMobile.value) {
    isCollapsed.value = false;
  }

  // 如果从移动端切换到PC端或反之，重绘图表
  if (wasMobile !== isMobile.value) {
    nextTick(() => {
      setTimeout(() => {
        resizeAllCharts();
        console.log(
          "设备类型变化，重绘图表:",
          isMobile.value ? "移动端" : "PC端"
        );
      }, 100);
    });
  }
};

// 窗口大小变化监听
const handleWindowResize = () => {
  checkScreenSize();
  handleResize(); // 原有的图表重绘函数
};

onMounted(async () => {
  // 初始化屏幕尺寸检测
  checkScreenSize();
  window.addEventListener("resize", handleWindowResize);

  // 初始化用户信息
  const userInfo = sessionStorage.getItem("userInfo");
  if (userInfo) {
    userStore.userInfo = JSON.parse(userInfo);
    userRole.value = userStore.userInfo.Role_name || "";
    userId.value = userStore.userInfo.id || "";
  }

  // 设置默认日期范围为当天
  const today = dayjs();
  dateRange.value = [today, today];

  // 设置权限控制
  setPermissions();

  await initData();

  // 初始化所有图表
  setTimeout(() => {
    initChart();
    initWorkTimeChart();
    initValidWorkTimeChart();
    initStackedChart();
  }, 1000);

  // 页面初始化时调用一次接口
  try {
    await loadStatsData();
    // 页面初始化成功后，保存初始查询条件
    saveCurrentQueryConditions();

    // 初始化车辆管理情况表格数据，使用筛选卡片的结束日期
    carManageTableDate.value = dateRange.value?.[1] || dayjs();
    await loadCarManageTableData();
  } catch (error) {
    console.error("初始化数据加载失败:", error);
  }
});

// 监听侧边栏状态变化，自动重绘图表
watch(isCollapsed, (newValue, oldValue) => {
  // 只在PC端且状态确实发生变化时执行
  if (!isMobile.value && newValue !== oldValue) {
    // 等待CSS过渡动画完成后重绘图表
    nextTick(() => {
      setTimeout(() => {
        resizeAllCharts();
        console.log("侧边栏状态变化，重绘图表:", newValue ? "收起" : "展开");
      }, 350); // 350ms确保CSS过渡动画完成
    });
  }
});

// 设置权限控制
const setPermissions = () => {
  const role = userRole.value;

  // 重置权限
  canSelectDq.value = true;
  canSelectSq.value = true;
  canSelectJxq.value = true;
  showAllCompany.value = false;

  switch (role) {
    case "决策层":
    case "it信息部":
    case "渠道运营":
      // 这三个角色都可以操作大区、省区、经销区
      canSelectDq.value = true;
      canSelectSq.value = true;
      canSelectJxq.value = true;
      // 只有决策层有全公司选项
      showAllCompany.value = role === "决策层";
      break;

    case "大区总":
      // 大区总只能看自己大区、下属省区和经销区
      canSelectDq.value = true;
      canSelectSq.value = true;
      canSelectJxq.value = true;
      showAllCompany.value = false;
      break;

    case "省区总":
      // 省区总大区禁用，可以选择自己省区和下属经销区
      canSelectDq.value = false;
      canSelectSq.value = true;
      canSelectJxq.value = true;
      showAllCompany.value = false;
      break;

    case "城市经理":
    case "合伙人":
      // 城市经理和合伙人大区省区禁用，可以选择自己所属经销区
      canSelectDq.value = false;
      canSelectSq.value = false;
      canSelectJxq.value = true;
      showAllCompany.value = false;
      break;

    default:
      // 默认权限
      canSelectDq.value = true;
      canSelectSq.value = true;
      canSelectJxq.value = true;
      showAllCompany.value = false;
  }
};

// 初始化数据
const initData = async () => {
  try {
    const currentUserId = userId.value || "admin";
    const role = userRole.value;

    // 根据角色获取大区数据
    if (canSelectDq.value) {
      let dqRes;
      if (role === "大区总") {
        dqRes = await getAllRegionApi_dqz(currentUserId);
      } else {
        dqRes = await getAllRegionApi();
      }

      if (dqRes.data.code === 200) {
        const dqData = dqRes.data.result.map((item) => ({
          label: item.Organization_Name,
          value: item.id,
          data: item,
        }));

        // 根据角色决定是否添加全公司选项
        if (showAllCompany.value) {
          const allCompanyOption = { label: "全公司", value: "all" };
          dqOptionAll.value = [allCompanyOption, ...dqData];
        } else {
          dqOptionAll.value = dqData;
        }
        dqOption.value = [...dqOptionAll.value];

        // 如果是大区总，自动选择自己的大区
        if (role === "大区总" && dqData.length > 0) {
          dqValue.value = dqData[0].value;
        }
      }
    }

    // 根据角色获取省区数据
    if (canSelectSq.value) {
      let sqRes;
      if (role === "省区总") {
        sqRes = await getAllProvincesApi_sqz(currentUserId);
      } else {
        sqRes = await getAllProvincesApi();
      }

      if (sqRes.data.code === 200) {
        sqOptionAll.value = sqRes.data.result.map((item) => ({
          label: item.Organization_Name,
          value: item.id,
          data: item,
        }));
        sqOption.value = [...sqOptionAll.value];

        // 如果是省区总，自动选择自己的省区
        if (role === "省区总" && sqOptionAll.value.length > 0) {
          sqValue.value = sqOptionAll.value[0].value;
        }
      }
    }

    // 根据角色获取经销区数据
    if (canSelectJxq.value) {
      let jxqRes;
      if (role === "城市经理" || role === "合伙人") {
        jxqRes = await getAllJxqApiCS(currentUserId);
      } else {
        jxqRes = await getAllJxqApi();
      }

      if (jxqRes.data.code === 200) {
        jxqOptionAll.value = jxqRes.data.result.map((item) => ({
          label: item.Organization_Name,
          value: item.id,
          data: item,
        }));
        jxqOption.value = [...jxqOptionAll.value];

        // 如果是城市经理或合伙人，自动选择自己的经销区
        if (
          (role === "城市经理" || role === "合伙人") &&
          jxqOptionAll.value.length > 0
        ) {
          jxqValue.value = jxqOptionAll.value[0].value;
          // 自动填充后调用onJxqChange获取合伙企业数据
          await onJxqChange(jxqOptionAll.value[0].value);
        }
      }
    }
  } catch (error) {
    console.error("初始化数据失败:", error);
    message.error("数据加载失败");
  }
};

// 大区变化处理
const onDqChange = (value) => {
  // 清空人员选择
  partnerValue.value = undefined;
  managerValue.value = undefined;
  driverValue.value = undefined;
  partnerOption.value = [];
  managerOption.value = [];
  driverOption.value = [];

  if (!value || value === "all") {
    // 4.清除大区,省区和经销区自动清除
    sqValue.value = undefined;
    jxqValue.value = undefined;
    // 恢复所有选项
    sqOption.value = [...sqOptionAll.value];
    jxqOption.value = [...jxqOptionAll.value];
  } else {
    // 1.选择大区时,筛选该大区下属的省区
    sqValue.value = undefined;
    jxqValue.value = undefined;

    // 筛选该大区下属的省区
    sqOption.value = sqOptionAll.value.filter(
      (item) => item.data.Region === value
    );

    // 筛选该大区下属的经销区
    const regionProvinces = sqOption.value.map((sq) => sq.value);
    jxqOption.value = jxqOptionAll.value.filter((item) =>
      regionProvinces.includes(item.data.Province)
    );
  }
};

// 省区变化处理
const onSqChange = (value) => {
  // 清空人员选择
  partnerValue.value = undefined;
  managerValue.value = undefined;
  driverValue.value = undefined;
  partnerOption.value = [];
  managerOption.value = [];
  driverOption.value = [];

  if (!value) {
    // 5.清除省区,经销区自动清除,大区保留
    jxqValue.value = undefined;
    // 恢复经销区选项（根据当前大区选择）
    if (dqValue.value && dqValue.value !== "all") {
      const regionProvinces = sqOptionAll.value
        .filter((sq) => sq.data.Region === dqValue.value)
        .map((sq) => sq.value);
      jxqOption.value = jxqOptionAll.value.filter((item) =>
        regionProvinces.includes(item.data.Province)
      );
    } else {
      jxqOption.value = [...jxqOptionAll.value];
    }
  } else {
    // 3.先选省区,填充所属大区和筛选下属经销区
    const selectedSq = sqOptionAll.value.find((item) => item.value === value);
    if (selectedSq && selectedSq.data.Region) {
      // 自动填充所属大区
      if (!dqValue.value) {
        dqValue.value = selectedSq.data.Region;
        // 同时更新省区选项为该大区下的所有省区
        sqOption.value = sqOptionAll.value.filter(
          (item) => item.data.Region === selectedSq.data.Region
        );
      }
    }

    // 筛选该省区下属的经销区
    jxqOption.value = jxqOptionAll.value.filter(
      (item) => item.data.Province === value
    );

    // 清空经销区选择，等待用户选择
    jxqValue.value = undefined;
  }
};

// 经销区变化处理
const onJxqChange = async (value) => {
  // 清空人员选择
  partnerValue.value = undefined;
  managerValue.value = undefined;
  driverValue.value = undefined;
  partnerOption.value = [];
  managerOption.value = [];
  driverOption.value = [];

  if (!value) {
    // 6.清除经销区,大区省区保留
    return;
  }

  // 2.选择经销区,自动填充所属的省区和大区
  const selectedJxq = jxqOptionAll.value.find((item) => item.value === value);
  if (selectedJxq && selectedJxq.data.Province) {
    // 查找省区对应的大区
    const selectedSq = sqOptionAll.value.find(
      (item) => item.value === selectedJxq.data.Province
    );

    // 自动填充所属大区
    if (!dqValue.value && selectedSq && selectedSq.data.Region) {
      dqValue.value = selectedSq.data.Region;
      // 更新省区选项为该大区下的所有省区
      sqOption.value = sqOptionAll.value.filter(
        (item) => item.data.Region === selectedSq.data.Region
      );
      // 更新经销区选项为该大区下的所有经销区
      const regionProvinces = sqOption.value.map((sq) => sq.value);
      jxqOption.value = jxqOptionAll.value.filter((item) =>
        regionProvinces.includes(item.data.Province)
      );
    }

    // 自动填充所属省区
    if (!sqValue.value) {
      sqValue.value = selectedJxq.data.Province;
      // 更新经销区选项为该省区下的所有经销区
      jxqOption.value = jxqOptionAll.value.filter(
        (item) => item.data.Province === selectedJxq.data.Province
      );
    }
  }

  // 获取人员数据
  try {
    const personRes = await getPersonListApi({ jxq: value });
    if (personRes.data.code === 200 && personRes.data.result) {
      const result = personRes.data.result;

      // 保存原始数据
      driverDataAll.value = result[0] || []; // 车长数据
      managerDataAll.value = result[1] || []; // 库长数据
      partnerDataAll.value = result[2] || []; // 合伙企业数据

      // 初始化合伙企业选项
      partnerOption.value = partnerDataAll.value.map((item) => ({
        label: item.really_name || item.name,
        value: item.id,
        data: item,
      }));

      console.log("人员数据加载成功:", {
        partners: partnerDataAll.value.length,
        managers: managerDataAll.value.length,
        drivers: driverDataAll.value.length,
      });
    }
  } catch (error) {
    console.error("获取人员数据失败:", error);
    message.error("获取人员数据失败");
  }
};

// 合伙企业变化处理
const onPartnerChange = (value) => {
  // 清空下级选择
  managerValue.value = undefined;
  driverValue.value = undefined;
  managerOption.value = [];
  driverOption.value = [];

  if (!value) {
    return;
  }

  // 根据选择的合伙企业筛选库长
  managerOption.value = managerDataAll.value
    .filter((item) => item.hhqy === value)
    .map((item) => ({
      label: item.really_name || item.name,
      value: item.id,
      data: item,
    }));

  console.log(
    "合伙企业变化:",
    value,
    "可选库长数量:",
    managerOption.value.length
  );
};

// 库长变化处理
const onManagerChange = (value) => {
  // 清空下级选择
  driverValue.value = undefined;
  driverOption.value = [];

  if (!value) {
    return;
  }

  // 根据选择的库长筛选车长
  driverOption.value = driverDataAll.value
    .filter((item) => item.Master_data_person_superior === value)
    .map((item) => ({
      label: item.really_name || item.name,
      value: item.id,
      data: item,
    }));

  console.log("库长变化:", value, "可选车长数量:", driverOption.value.length);
};

// 车长变化处理
const onDriverChange = (value) => {
  console.log("车长变化:", value);
};

// 日期范围变化处理
const onDateRangeChange = (dates) => {
  // 当筛选卡片日期范围变化时，同步更新车辆管理情况表格的独立日期选择器
  if (dates && dates.length === 2) {
    // 将表格的独立日期选择器更新为新的结束日期
    carManageTableDate.value = dates[1];

    // 自动调用车辆管理情况表格接口获取新日期的数据
    setTimeout(() => {
      loadCarManageTableData();
    }, 100);
  }
};

// 日期禁用逻辑
const disabledDate = (current) => {
  if (!dateRange.value || dateRange.value.length === 0) {
    return false;
  }

  const [start, end] = dateRange.value;
  if (start && !end) {
    // 如果只选择了开始时间，结束时间不能早于开始时间
    return current && current < start.startOf("day");
  }

  return false;
};

// 查询处理
const handleQuery = async () => {
  if (loading.value) return; // 防止重复点击

  loading.value = true;

  // 检查是否选择了人员
  const selectedPersonInfo = getSelectedPersonInfo();

  console.log("查询条件:", {
    dq: dqValue.value,
    sq: sqValue.value,
    jxq: jxqValue.value,
    partner: partnerValue.value,
    manager: managerValue.value,
    driver: driverValue.value,
    selectedPerson: selectedPersonInfo,
    dateRange: dateRange.value,
  });

  try {
    if (selectedPersonInfo.userId) {
      // 如果选择了人员，调用人员管理车辆接口
      console.log("调用人员管理车辆接口");
      await loadUserStatsData(selectedPersonInfo);
    } else {
      // 如果没有选择人员，调用原有的统计数据接口
      console.log("调用原有统计数据接口");
      await loadStatsData();
    }

    // 查询成功后，保存当前查询条件
    saveCurrentQueryConditions();
    message.success("查询成功");
  } catch (error) {
    console.error("查询失败:", error);
    message.error("查询失败，请重试");
  } finally {
    loading.value = false;
  }
};

// 获取选择的人员信息
const getSelectedPersonInfo = () => {
  // 优先级：车长 > 库长 > 合伙企业
  if (driverValue.value) {
    return {
      userId: driverValue.value,
      userType: "车长",
    };
  }
  if (managerValue.value) {
    return {
      userId: managerValue.value,
      userType: "库长",
    };
  }
  if (partnerValue.value) {
    return {
      userId: partnerValue.value,
      userType: "合伙企业",
    };
  }
  return {
    userId: null,
    userType: null,
  };
};

// 保存当前查询条件到最后成功查询条件
const saveCurrentQueryConditions = () => {
  const selectedPersonInfo = getSelectedPersonInfo();

  lastSuccessfulQueryConditions.dqValue = dqValue.value;
  lastSuccessfulQueryConditions.sqValue = sqValue.value;
  lastSuccessfulQueryConditions.jxqValue = jxqValue.value;
  lastSuccessfulQueryConditions.partnerValue = partnerValue.value;
  lastSuccessfulQueryConditions.managerValue = managerValue.value;
  lastSuccessfulQueryConditions.driverValue = driverValue.value;
  lastSuccessfulQueryConditions.dateRange = [...(dateRange.value || [])];
  lastSuccessfulQueryConditions.selectedPersonInfo = {
    userId: selectedPersonInfo.userId,
    userType: selectedPersonInfo.userType,
  };

  console.log("保存查询条件:", lastSuccessfulQueryConditions);
};

// 获取最后一次成功查询时的最低级别区域ID
const getLastSuccessfulMinimalRegionId = () => {
  // 优先级：经销区 > 省区 > 大区
  if (lastSuccessfulQueryConditions.jxqValue) {
    return lastSuccessfulQueryConditions.jxqValue;
  }
  if (lastSuccessfulQueryConditions.sqValue) {
    return lastSuccessfulQueryConditions.sqValue;
  }
  if (
    lastSuccessfulQueryConditions.dqValue &&
    lastSuccessfulQueryConditions.dqValue !== "all"
  ) {
    return lastSuccessfulQueryConditions.dqValue;
  }
  return null;
};

// 加载人员管理车辆统计数据
const loadUserStatsData = async (selectedPersonInfo) => {
  try {
    // 构建时间参数
    const timeParams = {
      time1:
        dateRange.value?.[0]?.format("YYYY-MM-DD") + " 00:00:00" ||
        dayjs().subtract(6, "day").format("YYYY-MM-DD") + " 00:00:00",
      time2:
        dateRange.value?.[1]?.format("YYYY-MM-DD") + " 23:59:59" ||
        dayjs().format("YYYY-MM-DD") + " 23:59:59",
      user: selectedPersonInfo.userId,
      js: selectedPersonInfo.userType,
    };

    console.log("调用人员管理车辆接口，参数:", timeParams);

    const response = await getUserManageCarListApi(timeParams);

    if (response && response.data && response.data.result) {
      const result = response.data.result;

      // 处理统计数据 result[0][0]
      if (result[0] && result[0][0]) {
        const statsInfo = result[0][0];
        statsData.todayUsed = statsInfo.cc_num || 0;
        statsData.unused = statsInfo.wcc_num || 0;
        statsData.shouldAttend = statsInfo.ycq_num || 0;
        statsData.timeUnqualified = statsInfo.cqbhg_num || 0;
        statsData.workTimeUnqualified = statsInfo.yybhg_num || 0;
      }

      // 处理图表数据 result[1]
      if (result[1]) {
        updateChartWithData(result[1]);
      }

      // 处理出勤时长对比图数据 result[2]
      if (result[2]) {
        updateWorkTimeChart(result[2]);
      }

      // 处理单车有效工时趋势图数据 result[3]
      if (result[3]) {
        updateValidWorkTimeChart(result[3]);
      }

      // 处理车辆出勤有效时长对比图数据 result[4]
      if (result[4]) {
        updateStackedChart(result[4]);
      }

      // 处理车辆拜访情况表格数据 result[5]
      if (result[5]) {
        updateVisitTable(result[5]);
      }

      // 处理IMEI总数量
      if (result[7]) {
        statsData.totalImei = result[7][0].IMEI_num || 0;
      }
    }
  } catch (error) {
    console.error("加载人员管理车辆统计数据失败:", error);
    throw error;
  }
};

// 显示未出车明细弹窗
const showUnusedCarModal = async () => {
  // 重置分页
  unusedCarPagination.current = 1;
  unusedCarPagination.total = 0;
  unusedCarData.value = [];

  // 显示弹窗
  unusedCarModalVisible.value = true;

  // 加载数据
  await loadUnusedCarData();
};

// 加载未出车明细数据
const loadUnusedCarData = async (page = 1, pageSize = 10) => {
  try {
    unusedCarLoading.value = true;

    // 使用最后一次成功查询的条件构建请求参数
    const queryParams = {
      time1:
        lastSuccessfulQueryConditions.dateRange?.[0]?.format("YYYY-MM-DD") +
          " 00:00:00" ||
        dayjs().subtract(6, "day").format("YYYY-MM-DD") + " 00:00:00",
      time2:
        lastSuccessfulQueryConditions.dateRange?.[1]?.format("YYYY-MM-DD") +
          " 23:59:59" || dayjs().format("YYYY-MM-DD") + " 23:59:59",
      qy: getLastSuccessfulMinimalRegionId(), // 使用最后一次成功查询的区域ID
      page: page,
      pageSize: pageSize,
    };

    // 如果最后一次查询时选择了人员，添加人员相关参数
    if (lastSuccessfulQueryConditions.selectedPersonInfo.userId) {
      queryParams.user =
        lastSuccessfulQueryConditions.selectedPersonInfo.userId;
      queryParams.js =
        lastSuccessfulQueryConditions.selectedPersonInfo.userType;
    }

    console.log("获取未出车明细参数（使用最后一次成功查询条件）:", queryParams);
    console.log("最后一次成功查询条件:", lastSuccessfulQueryConditions);

    const response = await getUnusedCarListApi(queryParams);

    if (response && response.data && response.data.code === 200) {
      const result = response.data.result;

      if (Array.isArray(result)) {
        // 直接使用返回的数组数据
        unusedCarData.value = result[0].map((item, index) => ({
          key: index,
          IMEI_code: item.IMEI_code || item.imei || "",
          Licence_number:
            item.Licence_number || item.plateNumber || item.plate_number || "",
          retailer_r_name: item.retailer_r_name || item.retailerName || "",
          last_heartbeat: item.last_heartbeat || item.lastHeartbeat || "",
        }));

        // 更新分页信息 - 使用数组第一项的total值
        unusedCarPagination.current = page;
        // 优先使用数组第一项的total值，其次使用response.data.total，最后使用数组长度
        const totalFromFirstItem =
          result.length > 0 && result[0][0].total ? result[0][0].total : null;
        unusedCarPagination.total =
          totalFromFirstItem || response.data.total || result.length;

        console.log("未出车明细分页信息:", {
          totalFromFirstItem,
          responseTotal: response.data.total,
          arrayLength: result.length,
          finalTotal: unusedCarPagination.total,
        });
      } else {
        console.warn("未出车明细数据格式异常:", result);
        unusedCarData.value = [];
        unusedCarPagination.total = 0;
      }
    } else {
      console.error("获取未出车明细失败:", response);
      message.error("获取未出车明细失败");
      unusedCarData.value = [];
      unusedCarPagination.total = 0;
    }
  } catch (error) {
    console.error("获取未出车明细失败:", error);
    message.error("获取未出车明细失败，请重试");
    unusedCarData.value = [];
    unusedCarPagination.total = 0;
  } finally {
    unusedCarLoading.value = false;
  }
};

// 分页变化处理
const onUnusedCarPageChange = (page, pageSize) => {
  unusedCarPagination.current = page;
  unusedCarPagination.pageSize = pageSize;
  loadUnusedCarData(page, pageSize);
};

// 车辆管理情况表格查询处理
const handleCarManageTableQuery = async () => {
  if (carManageTableLoading.value) return;

  // 重置分页
  carManageTablePagination.current = 1;
  carManageTablePagination.total = 0;

  await loadCarManageTableData();
};

// 加载车辆管理情况表格数据
const loadCarManageTableData = async (page = 1, pageSize = 10) => {
  try {
    carManageTableLoading.value = true;

    // 构建请求参数
    const selectedPersonInfo = getSelectedPersonInfo();
    const queryParams = {
      time:
        carManageTableDate.value?.format("YYYY-MM-DD") ||
        dayjs().format("YYYY-MM-DD"),
      qy: getMinimalRegionId(), // 当前选择的最低级别区域ID
      page: page,
      pageSize: pageSize,
    };

    // 如果选择了人员，添加人员相关参数
    if (selectedPersonInfo.userId) {
      queryParams.user = selectedPersonInfo.userId;
      queryParams.js = selectedPersonInfo.userType;
    }

    console.log("获取车辆管理情况参数:", queryParams);

    const response = await getCarManageTableApi(queryParams);

    if (response && response.data && response.data.code === 200) {
      const result = response.data.result;

      if (
        Array.isArray(result) &&
        result.length > 0 &&
        Array.isArray(result[0])
      ) {
        // 处理表格数据 - 使用 result[0] 数组
        carManageTableData.value = result[0].map((item, index) => ({
          key: index,
          imei: item.IMEI_code || "",
          license_plate: item.Licence_number || "",
          driver_name: item.czmc || "",
          date: item.route_date || "",
          manager_name: item.fzr || "",
          mileage: item.km || 0,
          should_visit: item.yfjs || 0,
          overdue_stores: item.yfcqmd || 0,
          actual_visit: item.sfjs || 0,
          offline_visit: item.xwbf || 0,
          new_stores: item.xzjs || 0,
          deal_stores: item.cjjs || 0,
          deal_amount: item.cjje || 0,
          first_store_time: item.sdsj || "",
          last_store_time: item.mdsj || "",
          effective_hours: item.yxgs || 0,
          store_duration: item.zdsc || 0,
          departure_time: item.ccsj || "",
          return_time: item.scsj || "",
          attendance_duration: item.cqsc || 0,
        }));

        // 更新分页信息 - 使用数组第一项的total值
        carManageTablePagination.current = page;
        const totalFromFirstItem =
          result[0].length > 0 && result[0][0].total
            ? result[0][0].total
            : null;
        carManageTablePagination.total =
          totalFromFirstItem || response.data.total || result[0].length;

        console.log("车辆管理情况分页信息:", {
          totalFromFirstItem,
          responseTotal: response.data.total,
          arrayLength: result[0].length,
          finalTotal: carManageTablePagination.total,
        });
      } else if (Array.isArray(result)) {
        // 兼容旧的数据结构（直接是数组）
        carManageTableData.value = result.map((item, index) => ({
          key: index,
          imei: item.IMEI_code || "",
          license_plate: item.Licence_number || "",
          driver_name: item.czmc || "",
          date: item.route_date || "",
          manager_name: item.fzr || "",
          mileage: item.km || 0,
          should_visit: item.yfjs || 0,
          overdue_stores: item.yfcqmd || 0,
          actual_visit: item.sfjs || 0,
          offline_visit: item.xwbf || 0,
          new_stores: item.xzjs || 0,
          deal_stores: item.cjjs || 0,
          deal_amount: item.cjje || 0,
          first_store_time: item.sdsj || "",
          last_store_time: item.mdsj || "",
          effective_hours: item.yxgs || 0,
          store_duration: item.zdsc || 0,
          departure_time: item.ccsj || "",
          return_time: item.scsj || "",
          attendance_duration: item.cqsc || 0,
        }));

        // 更新分页信息 - 使用数组第一项的total值
        carManageTablePagination.current = page;
        const totalFromFirstItem =
          result.length > 0 && result[0].total ? result[0].total : null;
        carManageTablePagination.total =
          totalFromFirstItem || response.data.total || result.length;

        // 更新分页信息
        carManageTablePagination.current = page;
        carManageTablePagination.total = response.data.total || result.length;
      } else {
        console.warn("车辆管理情况数据格式异常:", result);
        carManageTableData.value = [];
        carManageTablePagination.total = 0;
      }
    } else {
      console.error("获取车辆管理情况失败:", response);
      message.error("获取车辆管理情况失败");
      carManageTableData.value = [];
      carManageTablePagination.total = 0;
    }
  } catch (error) {
    console.error("获取车辆管理情况失败:", error);
    message.error("获取车辆管理情况失败，请重试");
    carManageTableData.value = [];
    carManageTablePagination.total = 0;
  } finally {
    carManageTableLoading.value = false;
  }
};

// 获取车辆管理情况完整数据（用于导出）
const loadCarManageTableAllData = async () => {
  try {
    // 构建请求参数，不包含分页参数
    const selectedPersonInfo = getSelectedPersonInfo();
    const queryParams = {
      time:
        carManageTableDate.value?.format("YYYY-MM-DD") ||
        dayjs().format("YYYY-MM-DD"),
      qy: getMinimalRegionId(), // 当前选择的最低级别区域ID
      page: 1,
      pageSize: 999999, // 设置一个很大的数值来获取所有数据
    };

    // 如果选择了人员，添加人员相关参数
    if (selectedPersonInfo.userId) {
      queryParams.user = selectedPersonInfo.userId;
      queryParams.js = selectedPersonInfo.userType;
    }

    console.log("获取车辆管理情况完整数据参数:", queryParams);

    const response = await getCarManageTableApi(queryParams);

    if (response && response.data && response.data.code === 200) {
      const result = response.data.result;

      if (
        Array.isArray(result) &&
        result.length > 0 &&
        Array.isArray(result[0])
      ) {
        // 处理表格数据 - 使用 result[0] 数组
        return result[0].map((item) => ({
          imei: item.IMEI_code || "",
          license_plate: item.Licence_number || "",
          driver_name: item.czmc || "",
          date: item.route_date || "",
          manager_name: item.fzr || "",
          mileage: item.km || 0,
          should_visit: item.yfjs || 0,
          overdue_stores: item.yfcqmd || 0,
          actual_visit: item.sfjs || 0,
          offline_visit: item.xwbf || 0,
          new_stores: item.xzjs || 0,
          deal_stores: item.cjjs || 0,
          deal_amount: item.cjje || 0,
          first_store_time: item.sdsj || "",
          last_store_time: item.mdsj || "",
          effective_hours: item.yxgs || 0,
          store_duration: item.zdsc || 0,
          departure_time: item.ccsj || "",
          return_time: item.scsj || "",
          attendance_duration: item.cqsc || 0,
        }));
      } else if (Array.isArray(result)) {
        // 兼容旧的数据结构（直接是数组）
        return result.map((item) => ({
          imei: item.IMEI_code || "",
          license_plate: item.Licence_number || "",
          driver_name: item.czmc || "",
          date: item.route_date || "",
          manager_name: item.fzr || "",
          mileage: item.km || 0,
          should_visit: item.yfjs || 0,
          overdue_stores: item.yfcqmd || 0,
          actual_visit: item.sfjs || 0,
          offline_visit: item.xwbf || 0,
          new_stores: item.xzjs || 0,
          deal_stores: item.cjjs || 0,
          deal_amount: item.cjje || 0,
          first_store_time: item.sdsj || "",
          last_store_time: item.mdsj || "",
          effective_hours: item.yxgs || 0,
          store_duration: item.zdsc || 0,
          departure_time: item.ccsj || "",
          return_time: item.scsj || "",
          attendance_duration: item.cqsc || 0,
        }));
      } else {
        console.warn("车辆管理情况完整数据格式异常:", result);
        return [];
      }
    } else {
      console.error("获取车辆管理情况完整数据失败:", response);
      throw new Error("获取数据失败");
    }
  } catch (error) {
    console.error("获取车辆管理情况完整数据失败:", error);
    throw error;
  }
};

// 导出Excel功能
const handleExportExcel = async () => {
  if (exportLoading.value) return;

  try {
    exportLoading.value = true;

    // 获取完整数据
    const allData = await loadCarManageTableAllData();

    if (!allData || allData.length === 0) {
      message.warning("暂无数据可导出");
      return;
    }

    // 定义Excel列标题（中文）
    const headers = [
      "IMEI号",
      "车牌号",
      "车长",
      "日期",
      "负责人",
      "里程（km）",
      "应访家数",
      "应付超期门店",
      "实访家数",
      "线外拜访",
      "新增家数",
      "成交家数",
      "成交金额",
      "首店时间",
      "末店时间",
      "有效工时",
      "在店时长",
      "出车时间",
      "收车时间",
      "出勤时长",
    ];

    // 将数据转换为Excel格式
    const excelData = allData.map((item) => [
      item.imei,
      item.license_plate,
      item.driver_name,
      item.date,
      item.manager_name,
      item.mileage,
      item.should_visit,
      item.overdue_stores,
      item.actual_visit,
      item.offline_visit,
      item.new_stores,
      item.deal_stores,
      item.deal_amount,
      item.first_store_time,
      item.last_store_time,
      item.effective_hours,
      item.store_duration,
      item.departure_time,
      item.return_time,
      item.attendance_duration,
    ]);

    // 将标题行添加到数据前面
    const worksheetData = [headers, ...excelData];

    // 创建工作表
    const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);

    // 设置列宽
    const colWidths = [
      { wch: 15 }, // IMEI号
      { wch: 12 }, // 车牌号
      { wch: 10 }, // 车长
      { wch: 12 }, // 日期
      { wch: 10 }, // 负责人
      { wch: 10 }, // 里程
      { wch: 10 }, // 应访家数
      { wch: 12 }, // 应付超期门店
      { wch: 10 }, // 实访家数
      { wch: 10 }, // 线外拜访
      { wch: 10 }, // 新增家数
      { wch: 10 }, // 成交家数
      { wch: 12 }, // 成交金额
      { wch: 15 }, // 首店时间
      { wch: 15 }, // 末店时间
      { wch: 10 }, // 有效工时
      { wch: 10 }, // 在店时长
      { wch: 15 }, // 出车时间
      { wch: 15 }, // 收车时间
      { wch: 10 }, // 出勤时长
    ];
    worksheet["!cols"] = colWidths;

    // 创建工作簿
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "车辆管理情况");

    // 生成文件名
    const dateStr =
      carManageTableDate.value?.format("YYYY-MM-DD") ||
      dayjs().format("YYYY-MM-DD");
    const fileName = `车辆管理情况_${dateStr}.xlsx`;

    // 导出文件
    const excelBuffer = XLSX.write(workbook, {
      bookType: "xlsx",
      type: "array",
    });
    const blob = new Blob([excelBuffer], {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    });
    saveAs(blob, fileName);

    message.success(`导出成功！共导出 ${allData.length} 条数据`);
    console.log(`Excel导出成功: ${fileName}, 数据条数: ${allData.length}`);
  } catch (error) {
    console.error("导出Excel失败:", error);
    message.error("导出失败，请重试");
  } finally {
    exportLoading.value = false;
  }
};

// 加载统计数据
const loadStatsData = async () => {
  try {
    const role = userRole.value;
    let response;

    // 构建时间参数
    const timeParams = {
      time1:
        dateRange.value?.[0]?.format("YYYY-MM-DD") + " 00:00:00" ||
        dayjs().subtract(6, "day").format("YYYY-MM-DD") + " 00:00:00",
      time2:
        dateRange.value?.[1]?.format("YYYY-MM-DD") + " 23:59:59" ||
        dayjs().format("YYYY-MM-DD") + " 23:59:59",
    };

    // 根据用户角色调用不同接口
    if (role === "决策层" || role === "it信息部" || role === "渠道运营") {
      // 高级角色：检查是否选择了具体区域
      const hasRegionSelection =
        dqValue.value || sqValue.value || jxqValue.value;

      if (hasRegionSelection) {
        // 选择了具体区域，调用区域接口
        const qyId = getMinimalRegionId();
        if (qyId) {
          response = await getManageCarListApi({
            qy: qyId,
            ...timeParams,
          });
        } else {
          // 没有有效区域ID，调用全量接口
          response = await getAllManageCarListApi(timeParams);
        }
      } else {
        // 没有选择区域，调用全量接口
        response = await getAllManageCarListApi(timeParams);
      }
    } else {
      // 其他角色：调用区域接口
      const qyId = getMinimalRegionId();
      if (qyId) {
        response = await getManageCarListApi({
          qy: qyId,
          ...timeParams,
        });
      } else {
        throw new Error("无法获取区域ID");
      }
    }

    if (response && response.data && response.data.result) {
      const result = response.data.result;

      // 处理统计数据 result[0][0]
      if (result[0] && result[0][0]) {
        const statsInfo = result[0][0];
        statsData.todayUsed = statsInfo.cc_num || 0;
        statsData.unused = statsInfo.wcc_num || 0;
        statsData.shouldAttend = statsInfo.ycq_num || 0;
        statsData.timeUnqualified = statsInfo.cqbhg_num || 0;
        statsData.workTimeUnqualified = statsInfo.yybhg_num || 0;
      }

      // 处理图表数据 result[1]
      if (result[1]) {
        updateChartWithData(result[1]);
      }

      // 处理出勤时长对比图数据 result[2]
      if (result[2]) {
        updateWorkTimeChart(result[2]);
      }

      // 处理单车有效工时趋势图数据 result[3]
      if (result[3]) {
        updateValidWorkTimeChart(result[3]);
      }

      // 处理车辆出勤有效时长对比图数据 result[4]
      if (result[4]) {
        updateStackedChart(result[4]);
      }

      // 处理车辆拜访情况表格数据 result[5]
      if (result[5]) {
        updateVisitTable(result[5]);
      }

      // 处理IMEI总数量
      if (result[7]) {
        statsData.totalImei = result[7][0].IMEI_num || 0;
      }
    }
  } catch (error) {
    console.error("加载统计数据失败:", error);
    throw error;
  }
};

// 获取最小级别的区域ID
const getMinimalRegionId = () => {
  // 优先级：经销区 > 省区 > 大区
  if (jxqValue.value) {
    return jxqValue.value;
  }
  if (sqValue.value) {
    return sqValue.value;
  }
  if (dqValue.value && dqValue.value !== "all") {
    return dqValue.value;
  }
  return null;
};

// 更新图表数据
const updateChartWithData = (chartData) => {
  if (!chartInstance) return;

  // 生成完整的日期范围
  const startDate = dateRange.value?.[0] || dayjs().subtract(6, "day");
  const endDate = dateRange.value?.[1] || dayjs();

  const dates = [];
  const shouldAttendData = [];
  const actualAttendData = [];

  // 创建日期到数据的映射
  const dataMap = {};
  chartData.forEach((item) => {
    if (item.route_date) {
      dataMap[item.route_date] = {
        ycq_num: item.ycq_num || 0,
        sjcq_num: item.sjcq_num || 0,
      };
    }
  });

  // 遍历日期范围，填充数据
  let currentDate = startDate;
  while (currentDate.isSameOrBefore(endDate)) {
    const dateStr = currentDate.format("YYYY-MM-DD");
    dates.push(currentDate.format("MM-DD"));

    if (dataMap[dateStr]) {
      shouldAttendData.push(dataMap[dateStr].ycq_num);
      actualAttendData.push(dataMap[dateStr].sjcq_num);
    } else {
      shouldAttendData.push(0);
      actualAttendData.push(0);
    }

    currentDate = currentDate.add(1, "day");
  }

  const option = {
    title: {
      text: "日出勤车辆统计",
      left: "center",
    },
    tooltip: {
      trigger: "axis",
    },
    legend: {
      data: ["应出勤车辆", "实际出勤车辆"],
      top: 30,
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: dates,
    },
    yAxis: {
      type: "value",
    },
    series: [
      {
        name: "应出勤车辆",
        type: "line",
        data: shouldAttendData,
        itemStyle: {
          color: "#ff4d4f",
        },
        lineStyle: {
          color: "#ff4d4f",
        },
      },
      {
        name: "实际出勤车辆",
        type: "line",
        data: actualAttendData,
        itemStyle: {
          color: "#52c41a",
        },
        lineStyle: {
          color: "#52c41a",
        },
      },
    ],
  };

  chartInstance.setOption(option);
};

// 更新出勤时长对比图
const updateWorkTimeChart = (chartData) => {
  if (!workTimeChartInstance) return;

  // 生成完整的日期范围
  const startDate = dateRange.value?.[0] || dayjs().subtract(6, "day");
  const endDate = dateRange.value?.[1] || dayjs();

  const dates = [];
  const standardWorkTimeData = [];
  const actualWorkTimeData = [];

  // 创建日期到数据的映射
  const dataMap = {};
  chartData.forEach((item) => {
    if (item.route_date) {
      dataMap[item.route_date] = {
        jzcqgs: item.jzcqgs || 0,
        work_time: item.work_time || 0,
      };
    }
  });

  // 遍历日期范围，填充数据
  let currentDate = startDate;
  while (currentDate.isSameOrBefore(endDate)) {
    const dateStr = currentDate.format("YYYY-MM-DD");
    dates.push(currentDate.format("MM-DD"));

    if (dataMap[dateStr]) {
      standardWorkTimeData.push(dataMap[dateStr].jzcqgs);
      actualWorkTimeData.push(dataMap[dateStr].work_time);
    } else {
      standardWorkTimeData.push(0);
      actualWorkTimeData.push(0);
    }

    currentDate = currentDate.add(1, "day");
  }

  const option = {
    title: {
      text: "单车出勤工时趋势",
      left: "center",
    },
    tooltip: {
      trigger: "axis",
    },
    legend: {
      data: ["标准出勤工时", "出勤时长"],
      top: 30,
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: dates,
    },
    yAxis: {
      type: "value",
    },
    series: [
      {
        name: "标准出勤工时",
        type: "line",
        data: standardWorkTimeData,
        itemStyle: {
          color: "#ff4d4f",
        },
        lineStyle: {
          color: "#ff4d4f",
        },
      },
      {
        name: "出勤时长",
        type: "line",
        data: actualWorkTimeData,
        itemStyle: {
          color: "#1890ff",
        },
        lineStyle: {
          color: "#1890ff",
        },
      },
    ],
  };

  workTimeChartInstance.setOption(option);
};

// 更新单车有效工时趋势图
const updateValidWorkTimeChart = (chartData) => {
  if (!validWorkTimeChartInstance) return;

  // 生成完整的日期范围
  const startDate = dateRange.value?.[0] || dayjs().subtract(6, "day");
  const endDate = dateRange.value?.[1] || dayjs();

  const dates = [];
  const standardValidTimeData = [];
  const actualValidTimeData = [];

  // 创建日期到数据的映射
  const dataMap = {};
  chartData.forEach((item) => {
    if (item.route_date) {
      dataMap[item.route_date] = {
        jzyxgs: item.jzyxgs || 0,
        vaild_work_time: item.vaild_work_time || 0,
      };
    }
  });

  // 遍历日期范围，填充数据
  let currentDate = startDate;
  while (currentDate.isSameOrBefore(endDate)) {
    const dateStr = currentDate.format("YYYY-MM-DD");
    dates.push(currentDate.format("MM-DD"));

    if (dataMap[dateStr]) {
      standardValidTimeData.push(dataMap[dateStr].jzyxgs);
      actualValidTimeData.push(dataMap[dateStr].vaild_work_time);
    } else {
      standardValidTimeData.push(0);
      actualValidTimeData.push(0);
    }

    currentDate = currentDate.add(1, "day");
  }

  const option = {
    title: {
      text: "单车有效工时趋势",
      left: "center",
    },
    tooltip: {
      trigger: "axis",
    },
    legend: {
      data: ["标准工时", "有效工时"],
      top: 30,
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: dates,
    },
    yAxis: {
      type: "value",
    },
    series: [
      {
        name: "标准工时",
        type: "line",
        smooth: true,
        data: standardValidTimeData,
        itemStyle: {
          color: "#fa8c16",
        },
        lineStyle: {
          color: "#fa8c16",
        },
      },
      {
        name: "有效工时",
        type: "line",
        smooth: true,
        data: actualValidTimeData,
        itemStyle: {
          color: "#52c41a",
        },
        lineStyle: {
          color: "#52c41a",
        },
      },
    ],
  };

  validWorkTimeChartInstance.setOption(option);
};

// 更新车辆出勤有效时长对比图（堆叠柱状图）
const updateStackedChart = (chartData) => {
  if (!stackedChartInstance) return;

  // 生成完整的日期范围
  const startDate = dateRange.value?.[0] || dayjs().subtract(6, "day");
  const endDate = dateRange.value?.[1] || dayjs();

  const dates = [];
  const workTimeData = [];
  const validWorkTimeData = [];

  // 创建日期到数据的映射
  const dataMap = {};
  chartData.forEach((item) => {
    if (item.route_date) {
      dataMap[item.route_date] = {
        work_time: item.work_time || 0,
        vaild_work_time: item.vaild_work_time || 0,
      };
    }
  });

  // 遍历日期范围，填充数据
  let currentDate = startDate;
  while (currentDate.isSameOrBefore(endDate)) {
    const dateStr = currentDate.format("YYYY-MM-DD");
    dates.push(currentDate.format("MM-DD"));

    if (dataMap[dateStr]) {
      workTimeData.push(dataMap[dateStr].work_time);
      validWorkTimeData.push(dataMap[dateStr].vaild_work_time);
    } else {
      workTimeData.push(0);
      validWorkTimeData.push(0);
    }

    currentDate = currentDate.add(1, "day");
  }

  const option = {
    title: {
      text: "车辆出勤有效时长对比",
      left: "center",
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    legend: {
      data: ["出勤时长", "有效工时"],
      top: 30,
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      data: dates,
    },
    yAxis: {
      type: "value",
    },
    series: [
      {
        name: "出勤时长",
        type: "bar",
        stack: "total",
        data: workTimeData,
        itemStyle: {
          color: "#1890ff",
        },
      },
      {
        name: "有效工时",
        type: "bar",
        stack: "total",
        data: validWorkTimeData,
        itemStyle: {
          color: "#52c41a",
        },
      },
    ],
  };

  stackedChartInstance.setOption(option);
};

// 更新车辆拜访情况表格
const updateVisitTable = (tableData) => {
  // 生成完整的日期范围
  const startDate = dateRange.value?.[0] || dayjs().subtract(6, "day");
  const endDate = dateRange.value?.[1] || dayjs();

  // 创建日期到数据的映射
  const dataMap = {};
  tableData.forEach((item) => {
    if (item.route_date) {
      dataMap[item.route_date] = {
        route_date: item.route_date,
        km: item.km || 0,
        yf_num: item.yf_num || 0,
        sf_num: item.sf_num || 0,
        wf_num: item.wf_num || 0,
        bfl: item.bfl || 0,
      };
    }
  });

  // 生成完整的表格数据
  const fullTableData = [];
  let currentDate = startDate;
  while (currentDate.isSameOrBefore(endDate)) {
    const dateStr = currentDate.format("YYYY-MM-DD");

    if (dataMap[dateStr]) {
      fullTableData.push(dataMap[dateStr]);
    } else {
      fullTableData.push({
        route_date: dateStr,
        km: 0,
        yf_num: 0,
        sf_num: 0,
        wf_num: 0,
        bfl: 0,
      });
    }

    currentDate = currentDate.add(1, "day");
  }

  visitTableData.value = fullTableData;
};

// 初始化图表
const initChart = () => {
  nextTick(() => {
    const container = document.getElementById("chartContainer");
    if (container) {
      chartInstance = echarts.init(container);
      // 初始化时显示空图表
      updateChartWithData([]);
    }
  });
};

// 初始化单车出勤工时趋势图
const initWorkTimeChart = () => {
  nextTick(() => {
    const container = document.getElementById("workTimeChartContainer");
    if (container) {
      workTimeChartInstance = echarts.init(container);
      updateWorkTimeChart([]);
    }
  });
};

// 初始化单车有效工时趋势图
const initValidWorkTimeChart = () => {
  nextTick(() => {
    const container = document.getElementById("validWorkTimeChartContainer");
    if (container) {
      validWorkTimeChartInstance = echarts.init(container);
      updateValidWorkTimeChart([]);
    }
  });
};

// 初始化车辆出勤有效时长对比图
const initStackedChart = () => {
  nextTick(() => {
    const container = document.getElementById("stackedChartContainer");
    if (container) {
      stackedChartInstance = echarts.init(container);
      updateStackedChart([]);
    }
  });
};

// 重绘所有图表的通用函数
const resizeAllCharts = () => {
  if (chartInstance) {
    chartInstance.resize();
  }
  if (workTimeChartInstance) {
    workTimeChartInstance.resize();
  }
  if (validWorkTimeChartInstance) {
    validWorkTimeChartInstance.resize();
  }
  if (stackedChartInstance) {
    stackedChartInstance.resize();
  }
};

// 窗口大小变化时重绘图表
const handleResize = () => {
  resizeAllCharts();
};

// 组件销毁时清理图表实例和事件监听器
onUnmounted(() => {
  window.removeEventListener("resize", handleWindowResize);
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
  if (workTimeChartInstance) {
    workTimeChartInstance.dispose();
    workTimeChartInstance = null;
  }
  if (validWorkTimeChartInstance) {
    validWorkTimeChartInstance.dispose();
    validWorkTimeChartInstance = null;
  }
  if (stackedChartInstance) {
    stackedChartInstance.dispose();
    stackedChartInstance = null;
  }
});
</script>

<style scoped>
.store-car-manage {
  display: flex;
  height: 100vh;
  background-color: #f5f5f5;
}

/* 左侧面板基础样式 */
.left-panel {
  position: fixed;
  height: 100vh;
  overflow-y: auto;
  background-color: #fff;
  border-right: 1px solid #e8e8e8;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 100;
}

/* PC端：收起状态 */
.left-panel.collapsed {
  width: 60px;
  padding: 0;
}

/* PC端：展开状态 */
.left-panel.expanded {
  width: 300px;
  padding: 20px;
}

/* 侧边栏切换按钮 */
.sidebar-toggle {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  cursor: pointer;
  padding: 12px;
  border-radius: 8px;
  background-color: #f0f8ff;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sidebar-toggle:hover {
  background-color: #e6f7ff;
  border-color: #1890ff;
  transform: translate(-50%, -50%) translateX(3px);
}

.expand-icon {
  color: #1890ff;
  transition: color 0.3s ease;
}

.sidebar-toggle:hover .expand-icon {
  color: #096dd9;
}

/* 收起按钮 */
.collapse-button {
  position: absolute;
  top: 15px;
  right: 40px;
  cursor: pointer;
  padding: 6px 12px;
  border-radius: 6px;
  background-color: #f5f5f5;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #666;
  z-index: 10;
}

.collapse-button:hover {
  background-color: #e6f7ff;
  border-color: #1890ff;
  color: #1890ff;
}

.collapse-icon {
  transition: color 0.3s ease;
}

.filter-card {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  padding-top: 30px; /* 为收起按钮留出空间 */
  height: 100%;
  overflow-y: auto;
}

.filter-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 20px;
  color: #333;
  text-align: center;
}

.filter-item {
  margin-bottom: 20px;
}

.filter-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  font-weight: 500;
}

/* 右侧面板 */
.right-panel {
  padding: 20px;
  overflow-y: auto;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* PC端：根据侧边栏状态调整右侧面板 */
@media (min-width: 768px) {
  .left-panel.collapsed + .right-panel {
    margin-left: 60px;
    width: calc(100% - 60px);
  }

  .left-panel.expanded + .right-panel {
    margin-left: 300px;
    width: calc(100% - 300px);
  }

  /* PC端侧边栏阴影效果 */
  .left-panel {
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  }

  /* PC端收起状态的特殊样式 */
  .left-panel.collapsed {
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f0f8ff 0%, #e6f7ff 100%);
  }

  .left-panel.collapsed:hover {
    background: linear-gradient(135deg, #e6f7ff 0%, #d9f2ff 100%);
  }

  /* 确保展开状态下的正常背景 */
  .left-panel.expanded {
    background-color: #fff;
  }
}

.stats-section {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stats-table {
  width: 100%;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  overflow: hidden;
}

.stats-row {
  display: flex;
  width: 100%;
}

.header-row {
  background-color: #fafafa;
}

.value-row {
  background-color: #fff;
}

.stats-cell {
  flex: 1;
  padding: 12px 16px;
  text-align: center;
  border-right: 1px solid #e8e8e8;
  font-size: 14px;
}

.stats-cell:last-child {
  border-right: none;
}

.header-row .stats-cell {
  font-weight: 600;
  color: #333;
}

.value-row .stats-cell {
  font-size: 16px;
  font-weight: 500;
  color: #1890ff;
}

.stats-cell.clickable {
  cursor: pointer;
  color: #1890ff;
  transition: background-color 0.3s;
}

.stats-cell.clickable:hover {
  background-color: #e6f7ff;
}

.chart-section {
  width: 100%;
  position: relative;
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  overflow: visible;
}

.chart-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 20px;
  color: #333;
  text-align: center;
}

.chart-container {
  width: 100%;
  height: 400px;
  min-height: 400px;
  position: relative;
  background-color: #fff;
}

.chart-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #666;
  font-size: 14px;
}

/* 移动端交互优化 */
.filter-item .ant-select {
  min-height: 44px; /* 增加触摸目标大小 */
}

.filter-item .ant-btn {
  min-height: 44px;
  font-size: 16px;
}

/* 表格移动端优化 */
.stats-cell.clickable {
  min-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 移动端表格标签样式 */
.stats-label {
  display: inline;
}

.stats-value {
  font-weight: 500;
  color: #1890ff;
}

.mobile-only {
  display: none;
}

/* 弹窗移动端适配 */
@media (max-width: 768px) {
  .ant-modal {
    margin: 0;
    max-width: 100vw;
  }

  .ant-modal-content {
    border-radius: 8px 8px 0 0;
  }

  .ant-table-wrapper {
    overflow-x: auto;
  }

  .ant-table {
    min-width: 500px;
  }
}

/* 大屏幕适配 */
@media (min-width: 1200px) {
  .left-panel.expanded {
    width: 320px;
  }

  .left-panel.expanded + .right-panel {
    margin-left: 320px;
    width: calc(100% - 320px);
  }
}

/* 平板适配 */
@media (max-width: 992px) and (min-width: 769px) {
  .left-panel.expanded {
    width: 280px;
    padding: 15px;
  }

  .left-panel.expanded + .right-panel {
    margin-left: 280px;
    width: calc(100% - 280px);
    padding: 15px;
  }

  .filter-card {
    padding: 15px;
    padding-top: 45px; /* 为收起按钮留出空间 */
  }

  .stats-section,
  .chart-section {
    padding: 15px;
  }

  .chart-container {
    height: 300px;
  }
}

/* 手机端适配 */
@media (max-width: 768px) {
  .store-car-manage {
    flex-direction: column;
    height: auto;
    min-height: 100vh;
  }

  /* 移动端强制显示完整侧边栏 */
  .left-panel {
    width: 100% !important;
    position: relative !important;
    height: auto !important;
    padding: 15px !important;
    border-right: none;
    border-bottom: 1px solid #e8e8e8;
    transition: none;
  }

  .left-panel.collapsed,
  .left-panel.expanded {
    width: 100% !important;
    padding: 15px !important;
  }

  /* 移动端隐藏切换按钮 */
  .sidebar-toggle,
  .collapse-button {
    display: none !important;
  }

  .right-panel {
    width: 100% !important;
    margin-left: 0 !important;
    padding: 15px;
  }

  .filter-card {
    padding: 15px;
    padding-top: 15px; /* 移动端不需要为收起按钮留空间 */
    margin-bottom: 0;
    height: auto;
  }

  .filter-title {
    font-size: 16px;
    margin-bottom: 15px;
  }

  .filter-item {
    margin-bottom: 15px;
  }

  .filter-label {
    font-size: 13px;
    margin-bottom: 6px;
  }

  .stats-section {
    padding: 15px;
    margin-bottom: 15px;
  }

  .stats-table {
    overflow-x: auto;
  }

  .stats-row {
    flex-direction: column;
    min-width: 100%;
  }

  .header-row {
    display: none; /* 隐藏表头行 */
  }

  .stats-cell {
    border-right: none;
    border-bottom: 1px solid #e8e8e8;
    padding: 12px 15px;
    font-size: 14px;
    text-align: left;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #fff;
  }

  .stats-cell:last-child {
    border-bottom: none;
  }

  .value-row .stats-cell {
    font-size: 15px;
    font-weight: 500;
  }

  .mobile-only {
    display: inline;
    color: #666;
    font-weight: 500;
    font-size: 14px;
  }

  .stats-value {
    font-size: 16px;
    font-weight: 600;
    color: #1890ff;
  }

  .stats-cell.clickable {
    background-color: #f0f8ff;
    border-left: 3px solid #1890ff;
  }

  .stats-cell.clickable:active {
    background-color: #e6f7ff;
  }

  .chart-section {
    padding: 15px;
  }

  .chart-title {
    font-size: 15px;
    margin-bottom: 15px;
  }

  .chart-container {
    height: 250px;
  }
}

/* 小屏手机适配 */
@media (max-width: 480px) {
  .left-panel,
  .right-panel {
    padding: 10px;
  }

  .filter-card,
  .stats-section,
  .chart-section {
    padding: 12px;
    border-radius: 6px;
  }

  .filter-title {
    font-size: 15px;
    margin-bottom: 12px;
  }

  .filter-item {
    margin-bottom: 12px;
  }

  .stats-cell {
    padding: 8px 10px;
    font-size: 12px;
  }

  .value-row .stats-cell {
    font-size: 14px;
  }

  .chart-container {
    height: 200px;
  }

  .chart-title {
    font-size: 14px;
    margin-bottom: 12px;
  }
}

/* 标题与查询控件水平布局样式 */
.chart-header-with-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-header-with-controls .chart-title {
  margin-bottom: 0;
  flex-shrink: 0;
}

/* 表格查询控件样式 */
.table-query-controls {
  display: flex;
  align-items: center;
  margin-bottom: 0;
  padding: 0;
}

.table-query-controls .ant-picker {
  border-radius: 6px;
}

.table-query-controls .ant-btn {
  border-radius: 6px;
  height: 32px;
}

/* 最后一个chart-section的底部间距 */
.chart-section-last {
  margin-bottom: 40px;
}

/* 表格容器样式 */
.table-wrapper {
  width: 100%;
  overflow: visible;
  position: relative;
  max-width: 100%;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
}

/* 车辆拜访情况表格包装器样式 */
.visit-table-wrapper {
  width: 100%;
  background: #fff;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
  overflow: hidden;
}

/* 车辆拜访情况表格滚动容器样式 */
.visit-table-scroll-container {
  width: 100%;
  overflow-x: auto;
  overflow-y: visible;
  position: relative;
}

/* 车辆拜访情况表格样式 */
.visit-table {
  width: 100%;
  margin-bottom: 0;
  min-width: max-content;
}

.visit-table .ant-table {
  margin-bottom: 0;
  width: 100%;
}

.visit-table .ant-table-container {
  overflow: visible !important;
}

.visit-table .ant-table-body {
  overflow: visible !important;
}

.visit-table .ant-table-content {
  overflow: visible !important;
}

.visit-table .ant-table-thead > tr > th,
.visit-table .ant-table-tbody > tr > td {
  white-space: nowrap;
  min-width: 80px;
}

/* 车辆拜访情况表格滚动条样式 */
.visit-table-scroll-container::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

.visit-table-scroll-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.visit-table-scroll-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.visit-table-scroll-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 确保车辆拜访表格滚动条始终可见 */
.visit-table-scroll-container {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

/* 车辆管理表格包装器样式 */
.car-manage-table-wrapper {
  width: 100%;
  background: #fff;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
  overflow: hidden;
}

/* 车辆管理表格滚动容器样式 */
.car-manage-table-scroll-container {
  width: 100%;
  overflow-x: auto;
  overflow-y: visible;
  position: relative;
}

/* 车辆管理表格样式 */
.car-manage-table {
  width: 100%;
  margin-bottom: 0;
  min-width: max-content;
}

.car-manage-table .ant-table {
  margin-bottom: 0;
  width: 100%;
}

.car-manage-table .ant-table-container {
  overflow: visible !important;
}

.car-manage-table .ant-table-body {
  overflow: visible !important;
}

.car-manage-table .ant-table-content {
  overflow: visible !important;
}

.car-manage-table .ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
  white-space: nowrap;
  padding: 16px 12px;
  border-bottom: 2px solid #f0f0f0;
  font-size: 14px;
}

.car-manage-table .ant-table-tbody > tr > td {
  white-space: nowrap;
  padding: 14px 12px;
  border-bottom: 1px solid #f0f0f0;
  font-size: 13px;
}

.car-manage-table .ant-table-tbody > tr:hover > td {
  background: #f5f5f5;
}

/* 分页容器样式 */
.car-manage-pagination-wrapper {
  padding: 16px;
  background: #fff;
  border-top: 1px solid #f0f0f0;
  border-radius: 0 0 6px 6px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  overflow: visible;
}

/* 车辆管理表格滚动条样式 */
.car-manage-table-scroll-container::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

.car-manage-table-scroll-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.car-manage-table-scroll-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.car-manage-table-scroll-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 确保表格滚动条始终可见 */
.car-manage-table-scroll-container {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

/* 确保表格内容不被截断 */
.car-manage-table .ant-table-thead > tr > th,
.car-manage-table .ant-table-tbody > tr > td {
  min-width: 80px;
  white-space: nowrap;
}

/* 确保表格响应容器变化 */

.chart-section .table-wrapper {
  width: 100%;
  max-width: 100%;
}

.chart-section .car-manage-table-wrapper {
  width: 100%;
  max-width: 100%;
}

.chart-section .visit-table-wrapper {
  width: 100%;
  max-width: 100%;
}

/* 表格内容滚动条样式 */
.table-wrapper .ant-table-content::-webkit-scrollbar,
.table-wrapper .ant-table-body::-webkit-scrollbar,
.table-wrapper .ant-table-container::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

.table-wrapper .ant-table-content::-webkit-scrollbar-track,
.table-wrapper .ant-table-body::-webkit-scrollbar-track,
.table-wrapper .ant-table-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.table-wrapper .ant-table-content::-webkit-scrollbar-thumb,
.table-wrapper .ant-table-body::-webkit-scrollbar-thumb,
.table-wrapper .ant-table-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.table-wrapper .ant-table-content::-webkit-scrollbar-thumb:hover,
.table-wrapper .ant-table-body::-webkit-scrollbar-thumb:hover,
.table-wrapper .ant-table-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 表格内容不换行，确保滚动正常 */
.ant-table-thead > tr > th,
.ant-table-tbody > tr > td {
  white-space: nowrap;
}

/* 确保表格在小容器中也能滚动 */
.table-wrapper .ant-table-content {
  overflow-x: auto;
  overflow-y: visible;
}

.table-wrapper .ant-table-body {
  overflow-x: auto;
  overflow-y: visible;
}

/* 表格容器确保分页不被遮盖 */
.table-wrapper .ant-table-container {
  overflow-x: auto;
  overflow-y: visible;
}

/* 横屏手机适配 */
@media (max-width: 768px) and (orientation: landscape) {
  .store-car-manage {
    flex-direction: row;
    height: 100vh;
  }

  .left-panel {
    width: 35% !important;
    height: 100vh !important;
    overflow-y: auto;
    border-right: 1px solid #e8e8e8;
    border-bottom: none;
    position: relative !important;
    padding: 15px !important;
  }

  .left-panel.collapsed,
  .left-panel.expanded {
    width: 35% !important;
    padding: 15px !important;
  }

  .right-panel {
    width: 65% !important;
    height: 100vh;
    overflow-y: auto;
    margin-left: 0 !important;
  }

  .stats-row {
    flex-direction: row;
  }

  .stats-cell {
    border-right: 1px solid #e8e8e8;
    border-bottom: none;
    text-align: center;
    display: block;
  }

  .stats-cell:last-child {
    border-right: none;
  }

  /* 移动端标题与查询控件布局适配 */
  .chart-header-with-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }

  .chart-header-with-controls .chart-title {
    text-align: center;
  }

  /* 移动端查询控件适配 */
  .table-query-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }

  .table-query-controls .ant-picker {
    width: 100%;
    margin-right: 0;
    margin-bottom: 0;
  }

  .table-query-controls .ant-btn {
    width: 100%;
    margin-right: 0;
  }

  /* 移动端表格滚动优化 */
  .table-wrapper {
    overflow: visible !important;
  }

  .table-wrapper .ant-table-container {
    overflow-x: scroll !important;
    -webkit-overflow-scrolling: touch;
  }

  /* 移动端车辆管理表格滚动优化 */
  .car-manage-table-scroll-container {
    overflow-x: scroll !important;
    -webkit-overflow-scrolling: touch;
  }

  /* 移动端车辆拜访表格滚动优化 */
  .visit-table-scroll-container {
    overflow-x: scroll !important;
    -webkit-overflow-scrolling: touch;
  }

  .visit-table {
    min-width: 100%;
  }

  /* 移动端车辆管理表格样式 */

  .car-manage-table {
    min-width: 100%;
  }

  .car-manage-table .ant-table-thead > tr > th,
  .car-manage-table .ant-table-tbody > tr > td {
    padding: 8px 6px;
    font-size: 12px;
  }

  /* 移动端分页样式 */
  .car-manage-pagination-wrapper {
    padding: 12px;
    flex-direction: column;
    align-items: center;
    gap: 10px;
  }

  .visit-table .ant-table-thead > tr > th,
  .visit-table .ant-table-tbody > tr > td {
    padding: 6px 8px;
    font-size: 12px;
  }
}

/* 平板端适配 */
@media (max-width: 1024px) and (min-width: 769px) {
  .table-wrapper {
    overflow: visible;
  }

  .table-wrapper .ant-table-container {
    overflow-x: auto;
  }

  /* 平板端车辆管理表格滚动优化 */
  .car-manage-table-scroll-container {
    overflow-x: auto;
  }

  /* 平板端车辆拜访表格滚动优化 */
  .visit-table-scroll-container {
    overflow-x: auto;
  }

  /* 平板端车辆管理表格样式 */

  .car-manage-table .ant-table-thead > tr > th,
  .car-manage-table .ant-table-tbody > tr > td {
    padding: 12px 8px;
    font-size: 13px;
  }

  .car-manage-pagination-wrapper {
    padding: 14px;
  }

  .visit-table .ant-table-thead > tr > th,
  .visit-table .ant-table-tbody > tr > td {
    padding: 6px 10px;
    font-size: 13px;
  }
}

/* 小屏幕设备适配 */
@media (max-width: 480px) {
  .chart-header-with-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }

  .table-query-controls {
    flex-direction: column;
    gap: 8px;
  }

  .table-query-controls .ant-picker,
  .table-query-controls .ant-btn {
    width: 100%;
    margin-right: 0;
  }

  .car-manage-table {
    min-width: 1200px;
  }

  .car-manage-table .ant-table-thead > tr > th,
  .car-manage-table .ant-table-tbody > tr > td {
    padding: 4px 6px;
    font-size: 11px;
  }

  .visit-table .ant-table-thead > tr > th,
  .visit-table .ant-table-tbody > tr > td {
    padding: 4px 6px;
    font-size: 11px;
  }
}
</style>
