<template>
  <div class="multi-select-container">
    <!-- 搜索框 -->
    <div class="search-section">
      <a-input
        v-model:value="searchKeyword"
        placeholder="搜索..."
        allow-clear
        @change="handleSearch"
      >
        <template #prefix>
          <search-outlined />
        </template>
      </a-input>
    </div>

    <!-- 表头 -->
    <div class="table-header" v-if="items2 && items2.length > 0">
      <div class="header-row">
        <div class="header-cell checkbox-column">
          <a-checkbox
            :checked="isAllSelected"
            :indeterminate="isIndeterminate"
            @change="handleSelectAll"
          >
            <span class="checkbox-label">全选</span>
          </a-checkbox>
        </div>
        <div
          v-for="header in items2"
          :key="header.value"
          class="header-cell content-column"
          :class="{ 'main-column': header.label }"
        >
          {{ header.label }}
        </div>
      </div>
    </div>

    <!-- 列表内容 -->
    <div class="list-content">
      <div class="empty-state" v-if="filteredItems.length === 0">
        <a-empty description="暂无数据" />
      </div>
      
      <div v-else>
        <div 
          v-for="item in paginatedItems" 
          :key="item.value"
          class="list-item"
          :class="{ 'selected': isSelected(item) }"
          @click="handleItemClick(item)"
        >
          <div class="item-checkbox">
            <a-checkbox
              :checked="isSelected(item)"
              @click.stop="handleItemClick(item)"
            />
          </div>
          <div class="item-content">
            <div class="content-text">{{ item.label }}</div>
            <div class="sub-info" v-if="item.label2">{{ item.label2 }}</div>
            <div class="extra-info" v-if="item.label3">{{ item.label3 }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-section" v-if="totalPages > 1">
      <a-pagination
        v-model:current="currentPage"
        :total="filteredItems.length"
        :page-size="itemsPerPage"
        :show-size-changer="false"
        :show-quick-jumper="false"
        :show-total="(total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`"
        size="small"
        @change="handlePageChange"
      />
    </div>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <a-space>
        <a-button @click="handleClose">
          取消
        </a-button>
        <a-button 
          type="primary" 
          @click="handleConfirm"
          :disabled="selectedItems.length === 0"
        >
          确定 ({{ selectedItems.length }})
        </a-button>
      </a-space>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, defineProps, defineEmits } from 'vue'
import { SearchOutlined } from '@ant-design/icons-vue'

const props = defineProps({
  items: {
    type: Array,
    default: () => []
  },
  items2: {
    type: Array,
    default: () => []
  },
  selectedItems: {
    type: Array,
    default: () => []
  },
  itemsPerPage: {
    type: Number,
    default: 10
  },
  currentPage: {
    type: Number,
    default: 1
  }
})

const emit = defineEmits([
  'update:selected-items',
  'update:close', 
  'update:confirm'
])

const searchKeyword = ref('')
const currentPage = ref(props.currentPage)
const selectedItems = ref([...props.selectedItems])

// 过滤后的项目
const filteredItems = computed(() => {
  if (!searchKeyword.value) {
    return props.items
  }
  
  return props.items.filter(item => 
    item.label?.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
    item.label2?.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
    item.label3?.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

// 分页后的项目
const paginatedItems = computed(() => {
  const start = (currentPage.value - 1) * props.itemsPerPage
  const end = start + props.itemsPerPage
  return filteredItems.value.slice(start, end)
})

// 总页数
const totalPages = computed(() => {
  return Math.ceil(filteredItems.value.length / props.itemsPerPage)
})

// 检查项目是否被选中
const isSelected = (item) => {
  return selectedItems.value.some(selected => selected.value === item.value)
}

// 全选状态
const isAllSelected = computed(() => {
  return paginatedItems.value.length > 0 && 
         paginatedItems.value.every(item => isSelected(item))
})

// 半选状态
const isIndeterminate = computed(() => {
  const selectedCount = paginatedItems.value.filter(item => isSelected(item)).length
  return selectedCount > 0 && selectedCount < paginatedItems.value.length
})

// 处理项目点击
const handleItemClick = (item) => {
  const index = selectedItems.value.findIndex(selected => selected.value === item.value)
  
  if (index > -1) {
    selectedItems.value.splice(index, 1)
  } else {
    selectedItems.value.push(item)
  }
}

// 处理全选
const handleSelectAll = (e) => {
  if (e.target.checked) {
    // 全选当前页
    paginatedItems.value.forEach(item => {
      if (!isSelected(item)) {
        selectedItems.value.push(item)
      }
    })
  } else {
    // 取消当前页的选择
    paginatedItems.value.forEach(item => {
      const index = selectedItems.value.findIndex(selected => selected.value === item.value)
      if (index > -1) {
        selectedItems.value.splice(index, 1)
      }
    })
  }
}

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1
}

// 处理分页变化
const handlePageChange = (page) => {
  currentPage.value = page
}

// 处理关闭
const handleClose = () => {
  emit('update:close')
}

// 处理确认
const handleConfirm = () => {
  emit('update:confirm', selectedItems.value)
}

// 监听搜索关键词变化
watch(searchKeyword, () => {
  currentPage.value = 1
})

// 监听外部选中项变化
watch(() => props.selectedItems, (newVal) => {
  selectedItems.value = [...newVal]
}, { deep: true })
</script>

<style lang="scss" scoped>
.multi-select-container {
  width: 100%;
  max-height: 600px;
  display: flex;
  flex-direction: column;
}

.search-section {
  margin-bottom: 16px;
}

.table-header {
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 8px;

  .header-row {
    display: flex;
    align-items: center;
    padding: 12px;
    font-weight: 600;
    color: #262626;
    background-color: #fafafa;
    border-radius: 6px 6px 0 0;

    .header-cell {
      display: flex;
      align-items: center;

      &.checkbox-column {
        flex: 0 0 100px;

        .ant-checkbox-wrapper {
          white-space: nowrap;

          .checkbox-label {
            margin-left: 8px;
            font-size: 14px;
          }
        }
      }

      &.content-column {
        flex: 1;
        padding-left: 12px;

        &.main-column {
          flex: 2;
        }
      }
    }
  }
}

.list-content {
  flex: 1;
  overflow-y: auto;
  min-height: 200px;
  max-height: 350px;
  
  .empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
  }
  
  .list-item {
    display: flex;
    align-items: center;
    padding: 12px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
      background-color: #f5f5f5;
    }

    &.selected {
      background-color: #e6f7ff;
      border-color: #91d5ff;
    }

    &:last-child {
      border-bottom: none;
    }

    .item-checkbox {
      flex: 0 0 100px;
      display: flex;
      align-items: center;
      justify-content: flex-start;
    }

    .item-content {
      flex: 1;
      display: flex;
      align-items: center;
      padding-left: 12px;

      .content-text {
        font-size: 14px;
        color: #262626;
        flex: 1;
      }

      .sub-info {
        font-size: 12px;
        color: #8c8c8c;
        margin-left: 16px;
        flex: 0 0 auto;
      }

      .extra-info {
        font-size: 12px;
        color: #8c8c8c;
        margin-left: 16px;
        flex: 0 0 auto;
      }
    }
  }
}

.pagination-section {
  margin: 16px 0;
  display: flex;
  justify-content: center;
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

// 响应式设计
@media (max-width: 768px) {
  .multi-select-container {
    max-height: 500px;
  }

  .list-content {
    max-height: 300px;
  }

  .table-header {
    .header-row {
      padding: 8px;

      .header-cell {
        &.checkbox-column {
          flex: 0 0 80px;

          .ant-checkbox-wrapper {
            .checkbox-label {
              font-size: 12px;
            }
          }
        }

        &.content-column {
          font-size: 12px;
          padding-left: 8px;
        }
      }
    }
  }

  .list-item {
    padding: 8px;

    .item-checkbox {
      flex: 0 0 80px;
    }

    .item-content {
      padding-left: 8px;

      .content-text {
        font-size: 13px;
      }

      .sub-info,
      .extra-info {
        font-size: 11px;
        margin-left: 8px;
      }
    }
  }
}
</style>
