import request from "../../index";

// 获取渠道建设汇总数据-库长
export function getCollectDataKuZhang(userCode, current_date) {
  return request({
    url: "/channel/getConstructionListKuZhang",
    method: "post",
    data: {
      userCode,
      current_date,
    },
  });
}

// 获取渠道建设汇总数据-合伙人
export function getCollectDataHeHuoRen(area_id, current_date) {
  return request({
    url: "/channel/getConstructionListHeHuoRen",
    method: "post",
    data: {
      area_id,
      current_date,
    },
  });
}

// 库长获取下属
export function getUnderlings(userCode) {
  return request({
    url: "/channel/get_underling",
    method: "get",
    data: {
      userCode,
    },
  });
}

// 库长获取渠道建设列表
export function getChannelConstructionList(data) {
  return request({
    url: "/channel/getConstructionList",
    method: "post",
    data,
  });
}

// 合伙人获取渠道建设列表
export function getChannelConstructionListHeHuoRen(data) {
  return request({
    url: "/channel/getConstructionListAllHeHuoRen",
    method: "post",
    data,
  });
}

// 合伙人获取下属
export function getUnderlingsHeHuoRen(area_id) {
  return request({
    url: "/channel/get_underling_hehuoren",
    method: "get",
    data: {
      area_id,
    },
  });
}
