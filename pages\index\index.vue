<template>
  <!-- 正在加载 -->
  <div class="loading" v-if="lodignLogin">正在加载页面，请稍后</div>
  <div v-if="userStore.userInfo?.Role_grade === '决策层'">
    <PhotoCheck v-if="tabIndex === 0" />
    <!--    <PopularFeelings v-if="tabIndex === 1" />-->
    <ShopData v-if="tabIndex === 1" />
<!--    <FunctionModule v-if="tabIndex === 3" />-->
    <StoreCarManage v-if="tabIndex === 2" />
    <MapIndex v-if="tabIndex === 3" />
    <CarMap v-if="tabIndex === 4" />
    <FromData v-if="tabIndex === 5" />
    <MarkingPlanning v-if="tabIndex === 6" />
    <JXTB v-if="tabIndex === 7" />
    <JFGZ v-if="tabIndex === 8" />
  </div>
  <div v-if="userStore.userInfo?.Role_grade === '经销商'">
    <ShopData v-if="tabIndex === 0" />
    <MapIndex v-if="tabIndex === 1" />
    <FunctionModule v-if="tabIndex === 2" />
    <FromData v-if="tabIndex === 3" />
    <MarkingPlanning v-if="tabIndex === 4" />
    <JXTB v-if="tabIndex === 5" />
  </div>

  <div v-if="userStore.userInfo?.Role_grade === '大区总'">
    <PhotoCheck v-if="tabIndex === 0" />
    <ShopData v-if="tabIndex === 1" />
    <MapIndex v-if="tabIndex === 2" />
    <FunctionModule v-if="tabIndex === 3" />
    <FromData v-if="tabIndex === 4" />
    <JXTB v-if="tabIndex === 5" />
  </div>
  <div v-if="userStore.userInfo?.Role_grade === '省区总'">
    <PhotoCheck v-if="tabIndex === 0" />
    <ShopData v-if="tabIndex === 1" />
    <MapIndex v-if="tabIndex === 2" />
    <FunctionModule v-if="tabIndex === 3" />
    <FromData v-if="tabIndex === 4" />
    <JXTB v-if="tabIndex === 5" />
  </div>
  <div v-if="userStore.userInfo?.Role_grade === '公司运营'">
    <PhotoCheck v-if="tabIndex === 0" />
    <PopularFeelings v-if="tabIndex === 1" />
    <ShopData v-if="tabIndex === 2" />
    <FunctionModule v-if="tabIndex === 3" />
    <MapIndex v-if="tabIndex === 4" />
    <CarMap v-if="tabIndex === 5" />
    <FromData v-if="tabIndex === 6" />
    <MarkingPlanning v-if="tabIndex === 7" />
    <JXTB v-if="tabIndex === 8" />
  </div>
  <div v-if="userStore.userInfo?.Role_grade === 'it信息部'">
    <PhotoCheck v-if="tabIndex === 0" />
    <PopularFeelings v-if="tabIndex === 1" />
    <ShopData v-if="tabIndex === 2" />
    <FunctionModule v-if="tabIndex === 3" />
    <MapIndex v-if="tabIndex === 4" />
    <CarMap v-if="tabIndex === 5" />
    <FromData v-if="tabIndex === 6" />
    <MarkingPlanning v-if="tabIndex === 7" />
    <JXTB v-if="tabIndex === 8" />
  </div>
  <u-tabbar
    :value="tabIndex"
    @change="clickTab"
    fixed
    :placeholder="false"
    :safeAreaInsetBottom="false"
    activeColor="#4ea28d"
  >
    <u-tabbar-item
      v-for="item in tabList"
      :text="item.text"
      :icon="item.icon"
    />
  </u-tabbar>
</template>

<script setup>
import MapIndex from "../Map/index.vue";
import FromData from "../FormOther/index.vue";
import ShopData from "../shopxh/index.vue";
import CarMap from "../vehiclemonitoring/index.vue";
import PopularFeelings from "../popularfeelings/index.vue";
import PhotoCheck from "../workbench/PhotoCheck/index.vue";
import MarkingPlanning from "../markingplanning/index.vue";
import FunctionModule from "./FunctionModule.vue";
import manifest from "@/manifest.json";
import JXTB from "../workbench/JXTB/index.vue";
import JFGZ from "../workbench/JFGZ/index.vue";
import StoreCarManage from "../workbench/storeCarManage/index.vue";
import { computed, ref, onMounted } from "vue";
import { useUserInfo } from "../../store/user/userInfo";
const userStore = useUserInfo();

import { get_user_info_login, user_login } from "/api/login/login";
import { message } from "ant-design-vue";
import {
  getProvinceUnderAreaIds,
  getRegionUnderAreaIds,
  getToken,
} from "../../api/login/login";

const tabList = computed(() => {
  if (userStore.userInfo?.Role_grade === "决策层") {
    return [
      {
        text: "相册",
        icon: "home",
      },
      // {
      //   text: "舆情",
      //   icon: "order",
      // },
      {
        text: "门店动销",
        icon: "order",
      },
      {
        text: "库房车辆管理报表",
        icon: "order",
      },
      // {
      //   text: "工作台",
      //   icon: "grid",
      // },
      {
        text: "地图",
        icon: "order",
      },
      {
        text: "大车地图",
        icon: "order",
      },
      {
        text: "报表",
        icon: "order",
      },
      {
        text: "市场规划",
        icon: "order",
      },
      {
        text: "经销图表",
        icon: "order",
      },
      {
        text: "积分规则",
        icon: "order",
      },
    ];
  }
  if (userStore.userInfo?.Role_grade === "经销商") {
    return [
      {
        text: "门店动销",
        icon: "order",
      },
      {
        text: "地图",
        icon: "order",
      },
      {
        text: "工作台",
        icon: "grid",
      },
      {
        text: "报表",
        icon: "order",
      },
      {
        text: "市场规划",
        icon: "order",
      },
      {
        text: "经销图表",
        icon: "order",
      },
    ];
  }
  if (userStore.userInfo?.Role_grade === "大区总") {
    return [
      {
        text: "相册",
        icon: "home",
      },
      {
        text: "门店动销",
        icon: "order",
      },
      {
        text: "地图",
        icon: "order",
      },
      {
        text: "工作台",
        icon: "grid",
      },
      {
        text: "报表",
        icon: "order",
      },
      {
        text: "经销图表",
        icon: "order",
      },
    ];
  }
  if (userStore.userInfo?.Role_grade === "省区总") {
    return [
      {
        text: "相册",
        icon: "home",
      },
      {
        text: "门店动销",
        icon: "order",
      },
      {
        text: "地图",
        icon: "order",
      },
      {
        text: "工作台",
        icon: "grid",
      },
      {
        text: "报表",
        icon: "order",
      },
      {
        text: "经销图表",
        icon: "order",
      },
    ];
  }
  if (userStore.userInfo?.Role_grade === "公司运营") {
    return [
      {
        text: "相册",
        icon: "home",
      },
      {
        text: "舆情",
        icon: "order",
      },
      {
        text: "门店动销",
        icon: "order",
      },
      {
        text: "工作台",
        icon: "grid",
      },
      {
        text: "地图",
        icon: "order",
      },
      {
        text: "大车地图",
        icon: "order",
      },
      {
        text: "报表",
        icon: "order",
      },
      {
        text: "市场规划",
        icon: "order",
      },
      {
        text: "经销图表",
        icon: "order",
      },
    ];
  }
  if (userStore.userInfo?.Role_grade === "it信息部") {
    return [
      {
        text: "相册",
        icon: "home",
      },
      {
        text: "舆情",
        icon: "order",
      },
      {
        text: "门店动销",
        icon: "order",
      },
      {
        text: "工作台",
        icon: "grid",
      },
      {
        text: "地图",
        icon: "order",
      },
      {
        text: "大车地图",
        icon: "order",
      },
      {
        text: "报表",
        icon: "order",
      },
      {
        text: "市场规划",
        icon: "order",
      },
      {
        text: "经销图表",
        icon: "order",
      },
    ];
  }
});

const lodignLogin = ref(false);

const tabIndex = ref(0);
const clickTab = (index) => {
  tabIndex.value = index;
};

onMounted(async () => {
  const userInfo = sessionStorage.getItem("userInfo");
  if (userInfo) {
    userStore.userInfo = JSON.parse(userInfo);
    console.log(
      "userStore.userInfo?.Role_grade",
      userStore.userInfo?.Role_grade
    );
    // 大区总、省区总直接进入工作台
    if (
      userStore.userInfo?.Role_grade === "大区总" ||
      userStore.userInfo?.Role_grade === "省区总"
    ) {
      tabIndex.value = 3;
    }
    // if (userStore.userInfo?.Role_grade === "决策层") {
    //   uni.reLaunch({
    //     url: "/pages/workbench/PhotoCheck/index",
    //   });
    // }
    return;
  }

  if (manifest["h5"].type === "development" || manifest["h5"].type === "test") {
    lodignLogin.value = true;
    userStore.userInfo = {
      userName: "test1",
      userCode: "xiefei",
      // 渠道运营
      id: "c3ea4c34-25a5-481c-8457-5296fcb4a90f",
      // id: "1997d5ef-5f56-4b80-80c5-712f674f085e",
      // id: "d943fad3-0d56-48fb-9bb2-110e7ac5cf6f",
      jxqId: ["f57bf852-596b-4cfb-9ad0-a8d0d84009bd"],
      fz_area_id: "f57bf852-596b-4cfb-9ad0-a8d0d84009bd",
      Role_grade: "系统管理员", //经销商、决策层、大区总、省区总，系统管理员
      Role_name: "系统管理员", //库长、合伙人、决策层
    };
    setTimeout(() => {
      lodignLogin.value = false;
    }, 1000);

    // const { data } = await getToken("17637500760");
    // if (data.result.msg === "当前用户被禁止登录，请联系管理员！") {
    //   message.error(data.result.msg);
    //   uni.reLaunch({
    //     url: "/pages/Login/WebLogin",
    //   });
    //   return;
    // }
    // uni.setStorageSync("access_token", data.result.access_token_value);
    if (userStore.userInfo.Role_grade === "系统管理员") {
      console.log("系统管理员", userStore.userInfo);
      // 跳转到人员选择页面
      uni.reLaunch({
        url: "/pages/Login/UserList",
      });
      return;
    }
    sessionStorage.setItem("userInfo", JSON.stringify(userStore.userInfo));
    // 大区总、省区总直接进入工作台
    if (
      userStore.userInfo?.Role_grade === "大区总" ||
      userStore.userInfo?.Role_grade === "省区总"
    ) {
      tabIndex.value = 3;
    }
    if (userStore.userInfo?.Role_grade === "决策层") {
      uni.reLaunch({
        url: "/pages/workbench/PhotoCheck/index",
      });
    }
    return;
  }
  // 获取查询参数字符串
  var queryString = window.location.search;
  if (queryString) {
    const params = new URLSearchParams(queryString);
    const code = params.get("code");
    console.log("code", code);
    if (code) {
      await getUserInfo(code);
      return;
    }
  }
  // 没有用户信息，直接退回登录界面
  if (userStore.userInfo === null) {
    uni.getSystemInfo({
      success: function (res) {
        // 判断设备平台
        if (res.platform === "windows" || res.platform === "mac") {
          uni.reLaunch({
            url: "/pages/Login/WebLogin",
          });
        } else {
          uni.reLaunch({
            url: "/pages/Login/Login",
          });
        }
      },
      fail: function (err) {
        console.error("获取系统信息失败", err);
      },
    });
  }
});

// 扫码之后用户登录
async function getUserInfo(code) {
  try {
    lodignLogin.value = true;
    // 先获取用户编码
    const res1 = await user_login(code);
    const userCode = res1.data.result.userid;

    // 获取后台用户信息
    const res2 = await get_user_info_login(userCode);
    if (res2.data.result.length === 0) {
      message.error("用户无权查看，请联系管理员！");
      uni.getSystemInfo({
        success: function (res) {
          // 判断设备平台
          if (res.platform === "windows" || res.platform === "mac") {
            uni.reLaunch({
              url: "/pages/Login/WebLogin",
            });
          } else {
            uni.reLaunch({
              url: "/pages/Login/Login",
            });
          }
        },
        fail: function (err) {
          console.error("获取系统信息失败", err);
        },
      });
      return;
    }

    // 获取token
    // const { data } = await getToken("17637500760");
    // if (data.result.msg === "当前用户被禁止登录，请联系管理员！") {
    //   message.error(data.result.msg);
    //   uni.getSystemInfo({
    //     success: function (res) {
    //       // 判断设备平台
    //       if (res.platform === "windows" || res.platform === "mac") {
    //         uni.reLaunch({
    //           url: "/pages/Login/WebLogin",
    //         });
    //       } else {
    //         uni.reLaunch({
    //           url: "/pages/Login/Login",
    //         });
    //       }
    //     },
    //     fail: function (err) {
    //       console.error("获取系统信息失败", err);
    //     },
    //   });
    //   return;
    // }
    // uni.setStorageSync("access_token", data.result.access_token_value);

    const userInfo = res2.data.result[0];
    userStore.userInfo = {
      userName: userInfo.Master_data_person_name,
      fz_area_id: userInfo.Master_data_person_area_name,
      id: userInfo.id,
      jxqId: [],
      Role_grade: userInfo.Role_grade,
      userCode: userInfo.Master_data_person_code,
      Role_name: userInfo.Role_name,
    };
    if (userInfo.Role_grade === "系统管理员") {
      console.log("系统管理员", userStore.userInfo);
      // 跳转到人员选择页面
      uni.reLaunch({
        url: "/pages/Login/UserList",
      });
      return;
    }
    if (userInfo.Role_grade === "经销商") {
      userStore.userInfo.jxqId = [userInfo.Master_data_person_area_name];
      console.log("经销商", userStore.userInfo);
    }
    if (userInfo.Role_grade === "大区总") {
      //获取大区下的所有经销区
      const res3 = await getRegionUnderAreaIds(
        userInfo.Master_data_person_area_name
      );
      const jxqIdList = res3.data.result.map((item) => item.id) || [];
      userStore.userInfo.jxqId = jxqIdList;
      console.log("大区总", userStore.userInfo);
    }
    if (userInfo.Role_grade === "省区总") {
      //获取大区下的所有经销区
      const res3 = await getProvinceUnderAreaIds(
        userInfo.Master_data_person_area_name
      );
      const jxqIdList = res3.data.result.map((item) => item.id) || [];
      userStore.userInfo.jxqId = jxqIdList;
      console.log("省区总", userStore.userInfo);
    }
    // 大区总、省区总直接进入工作台
    if (
      userStore.userInfo?.Role_grade === "大区总" ||
      userStore.userInfo?.Role_grade === "省区总"
    ) {
      tabIndex.value = 3;
    }
    if (userStore.userInfo?.Role_grade === "决策层") {
      uni.reLaunch({
        url: "/pages/workbench/PhotoCheck/index",
      });
    }

    sessionStorage.setItem("userInfo", JSON.stringify(userStore.userInfo));
  } catch (err) {
    console.error(err);
    message.error("用户信息获取失败！");
    uni.reLaunch({
      url: "/pages/Login/WebLogin",
    });
  } finally {
    lodignLogin.value = false;
  }
}
</script>

<style scoped>
.loading {
  height: 200px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 36rpx;
  font-weight: bold;
  color: #999;
}
</style>
