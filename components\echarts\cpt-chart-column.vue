<template>
  <div id="cpt-chart-column" style="height: 400px; width: 100%"></div>
</template>

<script setup>
import * as echarts from "echarts";
import { nextTick } from "vue";

const props = defineProps(["data"]);
const attribute = props.data.attribute;
const xyData = JSON.parse(props.data.cptDataForm.dataText);
let columnColor = attribute.barColor;
if (attribute.gradualColor) {
  columnColor = new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: attribute.barColor1 },
    { offset: 0.5, color: attribute.barColor2 },
    { offset: 1, color: attribute.barColor3 },
  ]);
}
nextTick(() => {
  // 基于准备好的dom，初始化echarts实例
  var myChart = echarts.init(document.getElementById("cpt-chart-column"));
  // 绘制图表
  myChart.setOption({
    color: columnColor,
    title: {
      text: attribute.chartTitle,
      textStyle: {
        color: attribute.titleTextColor,
      },
      left: attribute.titleLeft,
      top: attribute.titleTop,
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        // 坐标轴指示器，坐标轴触发有效
        type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
      },
    },
    grid: {
      x: 10,
      y: 30,
      x2: 10,
      y2: 10,
      containLabel: true,
    },
    xAxis: {
      show: attribute.xAxisShow,
      type: "category",
      data: xyData.xData.split(","),
      axisLabel: {
        color: attribute.xLabelColor,
        rotate: attribute.xFontRotate, //倾斜角度-180~180
      },
      axisLine: {
        show: attribute.xLineShow,
        lineStyle: {
          color: attribute.xLineColor,
        },
      },
      axisTick: {
        //x轴刻度线
        show: attribute.xTickShow,
      },
    },
    yAxis: {
      show: attribute.yAxisShow,
      type: "value",
      axisLabel: {
        color: attribute.yLabelColor,
      },
      axisLine: {
        show: attribute.yLineShow,
        lineStyle: {
          color: attribute.yLineColor,
        },
      },
      axisTick: {
        //y轴刻度线
        show: attribute.yTickShow,
      },
      splitLine: {
        //网格线
        show: attribute.yGridLineShow,
      },
    },
    series: [
      {
        data: xyData.yData.split(","),
        type: attribute.barType, //pictorialBar || bar
        showBackground: attribute.barBgShow,
        symbol: attribute.barPath,
        backgroundStyle: {
          color: "rgba(180, 180, 180, 0.2)",
        },
        barWidth: attribute.barWidth,
        itemStyle: {
          borderRadius: attribute.barBorderRadius,
        },
        label: {
          show: attribute.barLabelShow, //开启显示
          position: "top", //在上方显示
          color: attribute.barLabelColor,
          fontSize: attribute.barLabelSize,
        },
      },
    ],
  });
});
</script>

<style></style>
