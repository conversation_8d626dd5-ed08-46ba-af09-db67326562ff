<template>
  <!-- 图例 -->
  <div class="legend">
    <div v-show="store.checkedStore || store.checkedOpenStore">
      <div v-if="store.selectObj.productClass || store.selectObj.product">
        <div v-for="item in legendMessageProStore" :key="item.id">
          <span>-</span>
          <img
            :src="item.img"
            alt=""
            style="height: 16px; width: 16px"
            @click="changeStore(item)"
          />
          <span>-</span>
          <span
            style="font-size: 12px"
            :style="{ color: item.id == store.pickOn ? 'red' : 'black' }"
            @click="changeStore(item)"
            >{{ item.name }}</span
          >
        </div>
      </div>
      <div v-else>
        <div
          style="display: flex; align-items: center"
          v-for="item in legendMessageStore"
          :key="item.id"
        >
          <span>-</span>
          <img
            :src="item.img"
            alt=""
            style="height: 16px; width: 16px"
            @click="changeStore(item)"
          />
          <span>-</span>
          <span
            style="font-size: 12px; color: black"
            :style="{ color: item.id == store.pickOn ? 'red' : 'black' }"
            @click="changeStore(item)"
            >{{ item.name }}</span
          >
        </div>
      </div>
    </div>
    <div v-show="store.checkedCar">
      <div
        v-for="item in legendMessageCar"
        :key="item.id"
        style="margin-top: 5px"
      >
        <span>-</span>
        <img
          :src="item.img"
          alt=""
          style="height: 24px"
          @click="changeStore(item)"
        />
        <span>-</span>
        <span
          style="font-size: 12px"
          :style="{ color: item.id == store.pickOn ? 'red' : 'black' }"
          @click="changeStore(item)"
          >{{ item.name }}</span
        >
      </div>
      <div style="padding-top: 10px">
        <a-select
        style="width: 180px;"
          v-model:value="carSearchValue"
          placeholder="请输入车牌号或姓名"
          :options="carListOption"
          showSearch
          @change="carSearchChange"
        ></a-select>
      </div>
    </div>
    <div v-show="store.checkedTypeStore">
      <div v-for="item in legendMessageTypeStore" :key="item.id">
        <span>-</span>
        <img
          :src="item.img"
          alt=""
          style="height: 16px; width: 16px"
          @click="changeTypeStore(item)"
        />
        <span>-</span>
        <span
          :style="{ color: item.id == store.pickOn ? 'red' : 'black' }"
          @click="changeTypeStore(item)"
          >{{ item.name }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup>
import {
  legendMessageStore,
  legendMessageProStore,
  legendMessageCar,
  legendMessageTypeStore,
} from "../../data-uitls/legend.js";
import { useMapStore } from "../../../../store/map/index.js";
import { computed, ref } from "vue";
import { processTypeStoreStyle } from "../../data-uitls/index.js";

const emit = defineEmits(["getAllStores", "changeMapCenter"]);
const props = defineProps({
  storeMarker: {},
  carMarker: {},
});

let all_stores;
let all_stores_type = [];
const store = useMapStore();
const changeStore = item => {
  if (!all_stores) {
    all_stores = store.all_stores.geometries;
  }

  let changeStore = ref([]);

  if (item.id === "1") {
    store.pickOn = 1;
    all_stores.forEach((val, index) => {
      if (val.rank === 2) {
        changeStore.value.push({ ...val });
      }
    });
    // emit("getAllStores", changeStore.value);
  } else if (item.id === "2") {
    store.pickOn = 2;
    all_stores.forEach((val, index) => {
      if (val.rank === 1) {
        changeStore.value.push({ ...val });
      }
    });
  } else if (item.id === "4") {
    store.pickOn = 4;
    all_stores.forEach((val, index) => {
      if (val.rank === 4) {
        changeStore.value.push({ ...val });
      }
    });
  } else if (item.id === "3") {
    store.pickOn = 3;
    all_stores.forEach((val, index) => {
      if (val.rank === 3) {
        changeStore.value.push({ ...val });
      }
    });
  } else if (item.id === "5") {
    store.pickOn = 5;
    all_stores.forEach((val, index) => {
      if (val.rank === 5) {
        changeStore.value.push({ ...val });
      }
    });
  } else if (item.id === "all") {
    store.pickOn = "all";
    changeStore.value = all_stores;
  }
  props.storeMarker.setGeometries(changeStore.value);
};

const changeTypeStore = item => {
  store.pickOn = item.id;
  let filerStorePoints = [];

  all_stores_type = store.filterStoreList;
  const { points } = processTypeStoreStyle(all_stores_type);

  if (item.name === "全部") {
    filerStorePoints = points;
  } else {
    filerStorePoints = points.filter(it => it.styleId === item.name);
  }

  props.storeMarker.setGeometries(filerStorePoints);
};

const carSearchValue = ref(null);
const carListOption = computed(() => {
  return store.carList.map(item => {
    return {
      value: item.userName + "-" + item.carNumber,
      label: item.userName + "-" + item.carNumber,
      ...item,
    };
  });
});

function carSearchChange(value, option) {
  emit("changeMapCenter", option.lat_tx, option.lng_tx);
}
</script>

<style lang="scss" scoped>
.legend {
  position: absolute;
  top: 450rpx;
  right: 10px;
  padding: 10px;
  border-radius: 5px;
  z-index: 3;
  display: flex;
  flex-direction: column;
  gap: 8px;
}
</style>
