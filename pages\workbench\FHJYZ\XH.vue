<template>
  <div class="table-container">
    <!-- 筛选条件区域 -->
    <div class="filter-container">

<!--      <el-date-picker-->
<!--          v-model="filterDate"-->
<!--          type="date"-->
<!--          placeholder="选择日期"-->
<!--          @change="handleDateFilter">-->
<!--      </el-date-picker>-->
      <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleDateFilter">
      </el-date-picker>
      <el-input
          v-model="jxq"
          placeholder="请输入经销库房"
          @click="dqclick('jxq')"
          style="flex: 2;width: 200px;margin-left: 20px;">
      </el-input>
      <el-input
          v-model="hhr"
          placeholder="请输入合伙人"
          @click="dqclick('hhr')"
          style="flex: 2;width: 200px;margin-left: 20px;">
      </el-input>
      <el-input
          v-model="fhjyz"
          placeholder="请输入返货经营者"
          @click="dqclick('fhjyz')"
          style="flex: 2;width: 200px;margin-left: 20px;">
      </el-input>



      <el-button
          type="info"
          @click="resetFilters">
        重置筛选
      </el-button>
    </div>

    <!-- 大区筛选弹出框 -->
    <el-dialog
        title="选择大区"
        :visible.sync="showRegionDialog"
        width="30%"
        append-to-body>
      <el-checkbox-group v-model="selectedRegions">
        <el-checkbox
            v-for="region in regions"
            :key="region.value"
            :label="region.value">
          {{ region.label }}
        </el-checkbox>
      </el-checkbox-group>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showRegionDialog = false">取消</el-button>
        <el-button type="primary" @click="handleRegionFilter">确定</el-button>
      </span>
    </el-dialog>

    <!-- 表格区域 -->
    <el-table
        :data="filteredTableData"
        style="width: 100%"
        border
        stripe
        @row-click="handleRowClick">

      <el-table-column
          prop="time"
          label="日期"
          width="120">
      </el-table-column>

      <el-table-column
          prop="code"
          label="编号"
          width="120">
      </el-table-column>

      <el-table-column
          prop="type"
          label="业务类型"
          width="150">
      </el-table-column>

      <el-table-column
          prop="Sales_shop_name"
          label="客户"
          width="180"
      >
      </el-table-column>


      <el-table-column
          prop="fhjyz"
          label="返货经营者"
          width="120"
          align="right">
      </el-table-column>

      <el-table-column
          prop="Sales_son_good_name"
          label="产品名称"
          width="120"
          align="right">
      </el-table-column>

      <el-table-column
          prop="num"
          label="数量"
          width="120"
          align="right">
      </el-table-column>

      <el-table-column
          prop="Sales_unit_two"
          label="单位"
          width="150"
          align="right">
      </el-table-column>

      <el-table-column
          prop="S_unit_two_prce"
          label="销货单价"
          width="150"
          align="right">
      </el-table-column>

      <el-table-column
          prop="Sales_son_total_prices"
          label="销货总金额"
          width="150"
          align="right">
      </el-table-column>


    </el-table>
  </div>
  <Modal
      v-if="showModal"
      :show="showModal"
      title="请选择"
      @close="showModal = false"
      @confirm="handleConfirm"
  >
    <MultiSelectListOne
        :items="allItems"
        :items2="allItems2"
        @update:selected-items="update($event)"
        @update:close="close($event)"
        @update:confirm="confirm($event)"
        v-model:selected-items="tempSelectedItems"
        :items-per-page="pageSize"
        :current-page="currentPage"
        :total-pages="totalPages"
        :single-select="true"
    />
  </Modal>
</template>

<script>
import { GetAllDqNew,GetAllSqNew } from "/api/Map/index.js";
import { get_rzkf,get_all_fhjyz,get_all_hhr } from "/api/workbench/trainmastersigning/index.js";
import { get_fhjyzxhsj } from "../../../api/workbench/JXYL/index.js";
import Modal from "@/pages/workbench/mod/Modal.vue";
import MultiSelectListOne from "@/pages/workbench/mod/MultiSelectListOne.vue";
import {ref} from "vue";
import dayjs from "dayjs";
export default {
  components: {
    Modal,
    MultiSelectListOne
  },
  data() {
    const today = new Date();
    const formattedDate = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')} 00:00:00`;
    const formattedDate2 = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')} 23:59:59`;
    return {
      dateRange: [formattedDate,formattedDate2],
      tableData: [], // 初始为空数组，将在created钩子中填充数据
      filterDate1: formattedDate,
      filterDate2: formattedDate2, // 单日期筛选值
      dq: null, // 大区
      sq: null, // 省区
      jxq: null, // 经销区
      hhr: null, // 省区
      fhjyz: null, // 经销区
      hhrid: '', // 省区
      fhjyzid: '', // 经销区
      dqid: '', // 大区
      sqid: '', // 省区
      jxqid: '', // 经销区
      selectedRegions: [], // 选中大区
      allItems: [],
      allItems2: [],
      pageType: 1,
      currentPage: 1,
      pageSize: 999,
      totalPages: 1,
      showModal: false,
      showRegionDialog: false, // 是否显示大区筛选弹窗
      regions: [ // 大区选项
        { label: '华东', value: '华东' },
        { label: '华北', value: '华北' },
        { label: '华南', value: '华南' },
        { label: '西南', value: '西南' },
        { label: '西北', value: '西北' },
        { label: '东北', value: '东北' }
      ],
      activeFilters: { // 当前生效的筛选条件
        date: null,
        regions: []
      },
      tempSelectedItems: [],
      loading: false // 新增加载状态
    }
  },

  // 新增的生命周期钩子
  created() {
    this.fetchJxylData();
  },

  computed: {
    // 筛选后的表格数据
    filteredTableData() {
      return this.tableData;
    }
  },

  methods: {
    // 新增的获取数据方法
    async fetchJxylData() {
      try {
        this.loading = true;
        const response = await get_fhjyzxhsj(this.filterDate1,this.filterDate2,this.jxqid,this.hhrid,this.fhjyzid);
        // 假设API返回的数据结构是{ data: [...] }
        this.tableData = response.data.result
      } catch (error) {
        console.error('获取经销一览数据失败:', error);
        // 可以根据需要添加错误处理，如显示提示消息
        this.$message.error('获取数据失败，请稍后重试');
      } finally {
        this.loading = false;
      }
    },

    // 日期筛选
    async handleDateFilter(val) {

      const [startDate, endDate] = val;
      // 在这里处理你的日期范围过滤逻辑
      console.log('开始日期:', startDate);
      console.log('结束日期:', endDate);

      const year = startDate.getFullYear();
      const month = String(startDate.getMonth() + 1).padStart(2, '0');
      const day = String(startDate.getDate()).padStart(2, '0');
      const formattedDate = `${year}-${month}-${day} 00:00:00`;

      const year2 = endDate.getFullYear();
      const month2 = String(endDate.getMonth() + 1).padStart(2, '0');
      const day2 = String(endDate.getDate()).padStart(2, '0');
      const formattedDate2 = `${year2}-${month2}-${day2} 23:59:59`;

      // this.activeFilters.date = formattedDate;
      this.filterDate1= formattedDate;
      this.filterDate2= formattedDate2;
      try {
        this.loading = true;
        const response = await get_fhjyzxhsj(this.filterDate1,this.filterDate2,this.jxqid,this.hhrid,this.fhjyzid);
        // 假设API返回的数据结构是{ data: [...] }
        console.log('response.data.result',response.data.result)
        this.tableData = response.data.result
      } catch (error) {
        console.error('获取经销一览数据失败:', error);
        // 可以根据需要添加错误处理，如显示提示消息
        this.$message.error('获取数据失败，请稍后重试');
      } finally {
        this.loading = false;
      }

    },

    // 大区筛选
    handleRegionFilter() {
      this.activeFilters.regions = [...this.selectedRegions];
      this.showModal = false;
    },
    async dqclick(value){
      console.log('this.showRegionDialog',this.showRegionDialog)
      this.pageType = value
      if(value == 'jxq'){
        let res = await get_rzkf();
        this.allItems = [];
        for (var i = 0; i < res.data.result.length; i++) {
          this.allItems.push({
            value: res.data.result[i].id,
            label: res.data.result[i].DistributionOrganizationName,
            label2: res.data.result[i].DistributionOrganizationName2,
            label3: res.data.result[i].DistributionOrganizationName3,
            radio: 0,
          });
        }
        this.allItems2 = [];
        this.allItems2.push({
          value: 1,
          label: "名称",
          label2: "所属省区",
          label3: "所属大区",
          radio: 0,
        });
        console.log("ssss");
      }else if(value == 'hhr'){
        let res = await get_all_hhr();
        this.allItems = [];
        for (var i = 0; i < res.data.result.length; i++) {
          this.allItems.push({
            value: res.data.result[i].id,
            label: res.data.result[i].Master_data_person_name,
            label2: res.data.result[i].DistributionOrganizationName,
            label3: res.data.result[i].Master_data_person_name_crm_really,
            radio: 0,
          });
        }
        this.allItems2 = [];
        this.allItems2.push({
          value: 1,
          label: "名称",
          label2: "所属库房",
          label3: "显示名称",
          radio: 0,
        });
        console.log("ssss");
      }else if(value == 'fhjyz'){
        let res = await get_all_fhjyz();
        this.allItems = [];
        for (var i = 0; i < res.data.result.length; i++) {
          this.allItems.push({
            value: res.data.result[i].id,
            label: res.data.result[i].Master_data_person_name,
            label2: res.data.result[i].DistributionOrganizationName,
            label3: res.data.result[i].Master_data_person_name_crm_really,
            radio: 0,
          });
        }
        this.allItems2 = [];
        this.allItems2.push({
          value: 1,
          label: "名称",
          label2: "所属库房",
          label3: "显示名称",
          radio: 0,
        });
        console.log("ssss");
      }



      this.showModal = false;
      this.$nextTick(() => {
        this.showModal = true;
      });
    },
    // 重置筛选
    resetFilters() {
      this.filterDate1 = null;
      this.filterDate2 = null;
      this.dq = null;
      this.sq = null;
      this.jxq = null;
      this.jxqid = null;
      this.hhrid = null;
      this.fhjyzid = null;
      this.hhr = null;
      this.fhjyz = null;

      this.selectedRegions = [];
      this.activeFilters = {
        date: null,
        regions: []
      };
    },

    handleEdit(index, row) {
      console.log(index, row);
      // 这里可以添加编辑逻辑
    },

    handleDelete(index, row) {
      console.log(index, row);
      // 这里可以添加删除逻辑
    },

    handleRowClick(row, column, event) {
      console.log('行点击事件', row);
    },
    handleConfirm(row) {
      console.log('行点击事件', row);
    },
    async confirm(item) {
      console.log('行点击事件', item);

      if(this.pageType == 'dq'){
        this.dq = item.label
        this.dqid = item.value
      }else if(this.pageType == 'sq'){
        this.sq = item.label
        this.sqid = item.value
      }else if(this.pageType == 'jxq'){
        this.jxq = item.label
        this.jxqid = item.value
      } else if(this.pageType == 'hhr'){
        this.hhr = item.label
        this.hhrid = item.value
      }else if(this.pageType == 'fhjyz'){
        this.fhjyz = item.label
        this.fhjyzid = item.value
      }
      this.showModal = false;
      const response = await get_fhjyzxhsj(this.filterDate1,this.filterDate2,this.jxqid,this.hhrid,this.fhjyzid);
      console.log('response.data.result',response.data.result)
      this.tableData = response.data.result
    },
    //
    // const confirm = (item) => {
    //   console.log("item", item);
    //   formData[info.value] = item.label;
    //   if (info.value == "rzkf") {
    //     rzkfid.value = item.value;
    //     formData["sssq"] = item.label2;
    //     formData["ssdq"] = item.label3;
    //   } else if (info.value == "wlfcmc") {
    //     // 存储物流分仓ID，以便后续使用
    //     formData["wlfcId"] = item.value;
    //     formData["wlfcbm"] = `WLFC${dayjs().format("YYYYMMDDHHmmss")}`;
    //   }
    //
    //   showModal.value = false;
    // };
  }
}
</script>

<style scoped>
.table-container {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.filter-container > * {
  margin-right: 10px;
}
</style>