<template>
  <div class="list-container">
    <!-- Search Box -->
    <div class="search-container">
      <u-input
          v-model="searchQuery"
          placeholder="搜索..."
          class="search-input"

      ></u-input>
      <button class="add-btn" @click="seach()">搜索</button>
    </div>

    <div>
      <button @click="handleAdd()">新增</button>
    </div>
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-indicator">
      <i class="fas fa-spinner fa-spin"></i> 数据加载中...
    </div>

    <!-- 数据列表 -->
    <div v-else class="card-list">
      <article v-for="item in filteredList" :key="item.id" class="list-item" @mouseenter="hoverItem = item.id"
               @click="toDetail(item)"
               @mouseleave="hoverItem = null">
        <div class="item-header">
          <h2 class="item-title">{{ item.sign_status == "0"?'签约中': item.sign_status == "1"?'已签约': item.sign_status == "2"?'解约中': item.sign_status == "3"?'已解约': ''}}</h2>
          <span class="item-meta">{{ item.approval_status }}</span>

        </div>
        <div class="item-header">

          <h2 class="item-title">{{ item.rzrymc }}</h2>

        </div>
        <div class="item-actions" :class="{ 'visible': hoverItem === item.id }">
          <button @click.stop="handleJY(item)" v-if="item.sign_status == '1'">
            <i class="fas fa-edit"></i> 解约
          </button>
        </div>
      </article>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import { onShow } from "@dcloudio/uni-app";
import { useUserInfo } from "/store/user/userInfo";
import { getTrainmasterListApi ,getTrainmasterListApiinfo} from "/api/workbench/trainmastersigning";
import {post_JY} from "../../../api/workbench/organization";
const userStore = useUserInfo();

const dataList = ref([]);
const filteredList = ref([]);
const loading = ref(false);
const hoverItem = ref(null);
const searchQuery = ref('');
const info = ref('');
async function getTrainmasterList() {
  const jxqListStr = userStore.userInfo.jxqId.join(",");

  const result = jxqListStr
      .split(",")
      .map(item => `"${item}"`)
      .join(",");

  const { data } = await getTrainmasterListApi(result,userStore.userInfo.id,1);
  dataList.value = data.result;
  filteredList.value = data.result;
}

function handleSearch() {
  if (!searchQuery.value) {
    filteredList.value = dataList.value;
    return;
  }

  const query = searchQuery.value.toLowerCase();
  filteredList.value = dataList.value.filter(item =>
      item.rzrymc.toLowerCase().includes(query)
  );
}
async function seach() {
  const jxqListStr = userStore.userInfo.jxqId.join(",");

  const result = jxqListStr
      .split(",")
      .map(item => `"${item}"`)
      .join(",");

  const { data } = await getTrainmasterListApiinfo(result,userStore.userInfo.id,1,searchQuery.value);
  dataList.value = data.result;
  filteredList.value = data.result;
}

function toDetail(item) {
  uni.navigateTo({
    url: `/pages/workbench/trainmastersigning/Detail?id=${item.id}`,
  });
}

function handleAdd() {
  uni.navigateTo({
    url: `/pages/workbench/trainmastersigning/addTrain?type=1`,
  });
}
function handleJY(item) {
  uni.showModal({
    title: "温馨提示",
    content: "您确定要解约该车长",

    success:async function(res) {

      if (res.confirm) {
        let res = await post_JY(item.id,userStore.userInfo.id)
        if (res.data.code == '200') {
          uni.showToast({
            title: "已发起解约流程",
            icon: "none",
            duration: 2000,
          });
          getTrainmasterList();
        }

      } else if (res.cancel) {
        console.log('否');
      }
    },
  });
}

onShow(() => {
  getTrainmasterList();
});
</script>

<style scoped>
/* 新增搜索框和按钮的样式 */
.search-container {
  display: flex;
  align-items: center;
  gap: 10px; /* 设置元素之间的间距 */
  margin-bottom: 1.5rem;
}

.search-input {
  flex: 1; /* 让输入框占据剩余空间 */
  min-width: 0; /* 防止内容溢出 */
}

.add-btn {
  padding: 0.4rem 0.8rem; /* 缩小按钮尺寸 */
  font-size: 0.9rem; /* 减小字体大小 */
  white-space: nowrap; /* 防止按钮文字换行 */
}
.list-container {
  max-width: 1000px;
  margin: 2rem auto;
  padding: 0 1rem;
}




.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #7f8c8d;
}

.card-list {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: repeat(auto-fill, minmax(1000px, 1fr));
}

.list-item {
  background: #fff;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.list-item:hover {
  transform: translateY(-4px);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.item-title {
  font-size: 1.25rem;
  color: #2c3e50;
}

.item-meta {
  font-size: 0.875rem;
  color: #7f8c8d;
}

.item-content {
  color: #34495e;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.item-actions {
  opacity: 0;
  transition: opacity 0.2s ease;
  display: flex;
  gap: 0.5rem;
}

.item-actions.visible {
  opacity: 1;
}

button {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  background: #3498db;
  color: white;
  cursor: pointer;
  transition: background 0.2s ease;
}

button:hover {
  background: #2980b9;
}

.loading-indicator {
  text-align: center;
  padding: 2rem;
  color: #7f8c8d;
}

.org-name {
  margin-right: 200px;
}
</style>