<template>
  <div class="container">
    <div class="backGround"></div>
    <a-config-provider :locale="zhCN">
      <a-date-picker
        format="YYYY-MM"
        v-model:value="month"
        picker="month"
        @change="choose_month"
      />
    </a-config-provider>

    <!-- <div class="detail progress">
      <a-progress type="dashboard" :percent="10">
        <template #format="percent">
          <span
            style="
              font-size: 15px;
              display: block;
              width: 80px;
              margin-left: 20px;
            "
            >{{ title1 }}</span
          >
        </template>
      </a-progress>
      <a-progress type="dashboard" :percent="0">
        <template #format="percent">
          <span
            style="
              font-size: 15px;
              display: block;
              width: 80px;
              margin-left: 20px;
            "
            >{{ title2 }}</span
          >
        </template>
      </a-progress>
    </div> -->
    <div
      class="chart-container1"
      :id="'chart' + index"
      v-for="(item, index) in chart_count"
      :key="index"
    ></div>
  </div>
</template>

<script setup>
import { ref, nextTick } from "vue";
import * as echarts from "echarts";
import { onLoad } from "@dcloudio/uni-app";
import {
  getChartsList,
  getNumberDataName,
  dynamic_get_chart_date,
} from "/api/FormOther/index.js";
import zhCN from "ant-design-vue/es/locale/zh_CN";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
import {
  setBarOptions,
  setLineOptions,
  setPieOptions,
} from "./utils/customCharts";
dayjs.locale("zh-cn");
import { useUserInfo } from "../../store/user/userInfo";

let option;
let showEcharts = ref("");
let chart_count = ref([]); // 图表数据
let chart_data_list = ref([]);
let month = ref();
let title1 = ref("");
let title2 = ref("");
const userStore = useUserInfo();
let jxqId;

// 销售额排序
// const saleSort = (dataForm) => {
//   dataForm.sort((a, b) => {
//     return (
//       a.sale_money_one +
//       a.sale_money_two -
//       (b.sale_money_one + b.sale_money_two)
//     );
//   });

//   return dataForm;
// };

// 获取需要展示图表数据

const getUserId = () => {
  const userInfo = JSON.parse(sessionStorage.getItem("userInfo"));

  jxqId = userInfo?.Role_grade == "决策层" ? [] : userInfo.jxqId;
};

const get_Chart_List = async (report_name) => {
  let res = await getChartsList(report_name, jxqId);
  chart_count.value = res.data.result;
  // 显示顺序排序
  chart_count.value = chart_count.value.sort((a, b) => {
    return a.p_index - b.p_index;
  });
};

const initChart = (year, month) => {
  // 动态创建Echart图表
  get_Chart_List(showEcharts.value).then(() => {
    chart_count.value.forEach(async (item, index) => {
      if (item.chart_type == "bar") {
        let chartDom = document.getElementById("chart" + index);
        let myChart = echarts.init(chartDom);
        buildBarChart(myChart, item, year, month, jxqId);
      } else if (item.chart_type == "line") {
        let chartDom = document.getElementById("chart" + index);
        let myChart = echarts.init(chartDom);
        buildLineChart(myChart, item, year, month, jxqId);
      } else if (item.chart_type == "pie") {
        let chartDom = document.getElementById("chart" + index);
        let myChart = echarts.init(chartDom);
        buildPieChart(myChart, item, year, month, jxqId);
      }
    });
  });

  // get_Chart_List(showEcharts.value).then(async () => {
  //   if (chart_count.value.chart_type == "bar") {
  //     for (let [index, item] of chart_count.value.entries()) {
  //       chartDom = document.getElementById("chart" + index);
  //       myChart = echarts.init(chartDom);
  //       console.log(chartDom);

  //       // 动态发送请求获取数据
  //       let res2 = await dynamic_get_chart_date("product_month", "2024", "5");
  //       chart_data_list.value = res2.data.result;

  //       // 自定义x轴
  //       let x = {};

  //       // 自定义y轴
  //       let y = {
  //         data: dataForm.map((item) => {
  //           return item.name;
  //         }),
  //       };

  //       let s = [];

  //       // 定义数据公共样式，接收数据后动态修改
  //       let sStyle = {
  //         name: "产品1",
  //         type: item.chart_type,
  //         stack: "total",
  //         label: {
  //           show: true,
  //           textStyle: {
  //             fontSize: "10px",
  //           },
  //         },
  //         emphasis: {
  //           focus: "series",
  //         },
  //         data: [],
  //       };

  //       dataForm = saleSort(dataForm);
  //       sStyle.data = dataForm.map((item) => {
  //         return item.sale_money_one;
  //       });
  //       s.push(sStyle);

  //       sStyle.name = "产品2";
  //       sStyle.data = dataForm.map((item) => {
  //         return item.sale_money_two;
  //       });

  //       s.push(sStyle);

  //       option = setBarOptions(item.chart_name, x, y, s);
  //       myChart.setOption(option);
  //     }
  //   }
  // });
};

let date = new Date();
let nowYear = date.getFullYear();
let nowMonth = date.getMonth() + 1;
// 构建堆叠柱状图表
const buildBarChart = async (
  myChart,
  val,
  year = nowYear,
  month = nowMonth,
  areaId
) => {
  // 动态发送请求获取数据
  let res2 = await dynamic_get_chart_date(
    val.data_interface,
    year,
    month,
    areaId
  );
  chart_data_list.value = res2.data.result[0];
  let x;
  let y;

  if (chart_data_list.value[0]) {
    // 自定义x轴
    if (chart_data_list.value[0].xAxis) {
      x = {
        type: "category",
        data: chart_data_list.value[0].xAxis.split(","),
      };

      y = {
        type: "value",
      };
    } else {
      // 自定义y轴
      y = {
        type: "category",
        data: chart_data_list.value[0].yAxis.split(","),
      };
      x = {
        type: "value",
      };
    }
  } else {
    x = {
      data: [],
    };
    y = {
      data: [],
    };
  }

  let s = [];

  let dataZoom;
  chart_data_list.value.forEach((item) => {
    let sStyle = {
      name: item.name,
      type: val.chart_type,
      stack: "total",
      data: item.datas.split(","),
      label: {
        show: true,
        textStyle: {
          fontSize: "10px",
        },
      },
      emphasis: {
        focus: "series",
      },
    };

    s.push(sStyle);
  });

  // 控制x,y轴缩放
  if (val?.Zoom_Axis == "x") {
    console.log(11);
    dataZoom = {
      xAxisIndex: 0,
    };
  } else if (val?.Zoom_Axis == "y") {
    dataZoom = {
      yAxisIndex: 0,
    };
  }
  myChart.clear();
  option = setBarOptions(val.chart_name, x, y, s, dataZoom);
  myChart.setOption(option);
};

// 构建折线图表
const buildLineChart = async (
  myChart,
  val,
  year = nowYear,
  month = nowMonth,
  jxqId
) => {
  let res2 = await dynamic_get_chart_date(
    val.data_interface,
    year,
    month,
    jxqId
  );
  chart_data_list.value = res2.data.result[0];
  let x;
  let y;
  // 自定义x轴
  if (chart_data_list.value[0]) {
    x = {
      data: chart_data_list.value[0].xAxis.split(","),
    };

    y = {
      data: chart_data_list.value[0].yAxis.split(","),
    };
  } else {
    x = {
      data: [],
    };
    y = {
      data: [],
    };
  }

  // 自定义y轴

  let s = [];

  chart_data_list.value.forEach((item, index) => {
    let sStyle = {
      name: item.name,
      type: val.chart_type,
      stack: "total",
      data: item.datas.split(","),
      label: {
        show: true,
        textStyle: {
          fontSize: "10px",
        },
      },
      emphasis: {
        focus: "series",
      },
    };

    s.push(sStyle);
  });

  myChart.clear();
  option = setLineOptions(val.chart_name, x, y, s);
  myChart.setOption(option);
};

// 构建饼状图表
const buildPieChart = async (
  myChart,
  val,
  year = nowYear,
  month = nowMonth,
  jxqId
) => {
  let res2 = await dynamic_get_chart_date(
    val.data_interface,
    year,
    month,
    jxqId
  );
  chart_data_list.value = res2.data.result[0];

  // 自定义y轴

  let s = [];

  chart_data_list.value = chart_data_list.value.map((o) => {
    return {
      value: o.val,
      name: o.name,
    };
  });
  let sStyle = {
    type: val.chart_type,
    radius: "50%",
    data: [],
    emphasis: {
      itemStyle: {
        shadowBlur: 10,
        shadowOffsetX: 0,
        shadowColor: "rgba(0, 0, 0, 0.5)",
      },
    },
  };

  chart_data_list.value.forEach((item) => {
    sStyle.data.push(item);
    s.push(sStyle);
  });

  myChart.clear();
  option = setPieOptions(val.chart_name, s);
  myChart.setOption(option);
};

// 更换日期
const choose_month = async (val, dataStr) => {
  // console.log(val.$y);
  // console.log(dataStr);

  let dataArr = dataStr.replace(/\:/g, "-").split("-");
  let year = dataArr[0] ? dataArr[0] : "2024";
  let month = dataArr[1] ? dataArr[1] : "5";
  initChart(year, month);
};

const getNumberData = async (title) => {
  let res = await getNumberDataName(title);
  // console.log(res);
  title1.value = res.data.result[0].number_data_name1;
  title2.value = res.data.result[0].number_data_name2;
};

onLoad((option) => {
  uni.setNavigationBarTitle({
    title: `${option.title}`,
  });
  showEcharts.value = option.title;
  getNumberData(option.title);
  getUserId();
});

nextTick(() => {
  initChart();
  // charts.value.initChart();
});
</script>

<style lang="scss" scoped>
body {
  font-size: 1px;
}

.backGround {
  position: absolute;
  width: 100%;
  height: 150px;
  background: linear-gradient(to right, rgb(107, 168, 255), rgb(36, 127, 255));
  top: 0;
  left: 0;
  z-index: 0;
  border-bottom-left-radius: 5%;
  border-bottom-right-radius: 5%;
}
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #f4f4f4;
  // height: 100vh;
  width: 100%;
  position: relative;
  padding-top: 50px;
  overflow: auto;
}
.detail {
  height: 25%;
  width: 95%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  margin: 5px 0 20px 0;
  padding: 10px;
  border-radius: 8px;
  z-index: 1;
}

.change_date {
  width: 50px;
  height: 10px;
  z-index: 999;
  font-size: 15px;
  position: absolute;
  top: 0;
  left: 0;
}

.detail-list {
  height: 5%;
  line-height: 30px;
  font-size: 0.9375rem;
}

.chart-container1 {
  width: 95%;
  padding: 10px;
  height: 800px;
  background-color: #fff;
  margin: 0 0 10px 0;
  border-radius: 8px;
  z-index: 5;
}
.chart-container2 {
  width: 95%;
  padding: 10px;
  height: 800px;
  background-color: #fff;
  margin: 0 0 10px 0;
  border-radius: 8px;
}
.progress {
  display: flex;
  flex-direction: row;
}

:deep(.ant-progress) {
  width: 50%;
}

:deep(.ant-progress-inner) {
  position: relative;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

:deep(.ant-picker) {
  position: absolute;
  top: 13px;
  right: 10px;
}
</style>
