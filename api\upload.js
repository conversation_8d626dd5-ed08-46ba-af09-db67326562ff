// 获取 BASE_URL
let BASE_URL = "";
import manifest from "@/manifest.json";

if (manifest["h5"].type === "development") {
  BASE_URL = "https://crm2reportdev.xcspzg.com/basic-api";
}

if (manifest["h5"].type === "test") {
  BASE_URL = "https://crm2reporttest.xcspzg.com/basic-api";
}

if (manifest["h5"].type === "production") {
  BASE_URL = "https://crmreportprd.xcspzg.com/basic-api";
}

export function uploadFile(data) {
  let urlAfter = BASE_URL + "/file/upload";

  return new Promise((resolve, reject) => {
    // 从 FormData 中获取文件
    let file = null;
    let filePath = '';

    // 处理不同的数据格式
    if (data instanceof FormData) {
      file = data.get('file');
    } else if (data.file) {
      file = data.file;
    } else {
      file = data;
    }

    // 获取文件路径
    if (file) {
      // 对于 H5 环境，文件可能有不同的路径属性
      filePath = file.path || file.tempFilePath || file.url;

      // 如果是 File 对象（H5环境），需要特殊处理
      if (file instanceof File) {
        // 在 H5 环境中，我们需要使用 file 对象本身
        filePath = file;
      }
    }

    if (!filePath) {
      reject({
        code: -1,
        message: '无法获取文件路径'
      });
      return;
    }

    const uploadConfig = {
      url: urlAfter,
      name: "file",
      header: {
        Authorization: "Bearer " + "1",
      },
			formData: {
        file_rel_id: "id_test",
        file_rel_Object: "object_test",
        file_max_num: 1,
      },
      success: (res) => {
        try {
          // 解析返回的数据
          const result = typeof res.data === 'string' ? JSON.parse(res.data) : res.data;

          if (result.code === 200) {
            resolve({
              data: result.data,
              code: result.code,
              message: result.message
            });
          } else {
            reject({
              code: result.code,
              message: result.message || '上传失败'
            });
          }
        } catch (error) {
          console.error('解析上传结果失败:', error);
          reject({
            code: -1,
            message: '解析上传结果失败'
          });
        }
      },
      fail: error => {
        console.error('上传失败:', error);
        reject({
          code: -1,
          message: error.errMsg || '上传失败'
        });
      },
    };

    // 根据环境和文件类型设置不同的配置
    // #ifdef H5
    if (file instanceof File) {
      uploadConfig.file = file;
    } else {
      uploadConfig.filePath = filePath;
    }
    // #endif

    // #ifndef H5
    uploadConfig.filePath = filePath;
    uploadConfig.fileType = "image";
    // #endif

    uni.uploadFile(uploadConfig);
  });
}

