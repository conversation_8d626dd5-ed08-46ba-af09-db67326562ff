<template>
  <div class="detail-wrap">
    <!-- 顶部信息卡片 -->
    <div class="info-card">
      <h3>{{ data.warehouse_name }}</h3>
      <p>纬度：{{ data.latitude }}　经度：{{ data.longitude }}</p>
      <p>所属经销区：{{ data.Organization_Name }}</p>
      <p>位置文本：{{ data.specificLocation }}</p>
      <p>状态：{{ approveStatusMap[data.approve_status] }}</p>
    </div>

    <!-- 地图容器 -->
    <div id="detailMap" class="map"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from "vue";
import { useRoute } from "vue-router";
import { getMapKey } from "@/api/Map";
import { onLoad } from "@dcloudio/uni-app";

const approveStatusMap = {
  0: "审批中",
  1: "已通过",
  2: "未通过",
  3: "已关闭",
};
const route = useRoute();
const data = ref({});
const mapKey = ref("");
let map, marker;

const getKey = async () => {
  const res = await getMapKey();
  mapKey.value = res.data.result[0].mapKeyValue;
};

const initMap = () => {
  // @ts-ignore
  const center = new TMap.LatLng(
    Number(data.value.latitude),
    Number(data.value.longitude)
  );
  // @ts-ignore
  map = new TMap.Map("detailMap", {
    center,
    zoom: 13,
    viewMode: "2D",
  });
  // @ts-ignore
  marker = new TMap.MultiMarker({
    map,
    styles: {
      pin: new TMap.MarkerStyle({
        width: 30,
        height: 30,
        anchor: { x: 15, y: 30 },
        src: "/static/icon/map/warehouse-fill.png",
      }),
    },
    geometries: [
      {
        id: "current",
        position: center,
        styleId: "pin",
      },
    ],
  });
};

onLoad(async (options) => {
  data.value = JSON.parse(decodeURIComponent(options.item || "{}"));
  await getKey();
  nextTick(initMap);
});
</script>

<style scoped>
.detail-wrap {
  position: relative;
  height: 100vh;
}
.info-card {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 999;
  background: rgba(255, 255, 255, 0.9);
  padding: 10px 14px;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}
.map {
  width: 100%;
  height: 100%;
}
</style>
