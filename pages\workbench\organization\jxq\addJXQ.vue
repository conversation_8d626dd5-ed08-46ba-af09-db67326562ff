<template>
  <div class="container">
    <a-card title="新建经销区" class="main-card">
      <a-form
        :model="formData"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
        ref="formRef"
      >
        <!-- 基本信息区域 -->
        <a-divider orientation="left">基本信息</a-divider>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="经销区名称" name="jxqName">
              <a-input
                v-model:value="formData.jxqName"
                placeholder="请输入经销区名称"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="经销区编码" name="jxqCode">
              <a-input
                v-model:value="formData.jxqCode"
                placeholder="系统自动生成"
                readonly
                style="background-color: #f5f5f5; cursor: not-allowed;"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 关联信息区域 -->
        <a-divider orientation="left">关联信息</a-divider>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="城市经理" name="csjl">
              <a-input
                v-model:value="formData.csjl"
                placeholder="请选择城市经理"
                readonly
                @click="csjlclick"
                style="cursor: pointer;"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="所属省区" name="sq">
              <a-input
                v-model:value="formData.sq"
                placeholder="请选择所属省区"
                readonly
                @click="csjlclick2"
                style="cursor: pointer;"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 关联行政区县区域 -->
        <a-divider orientation="left">关联行政区县</a-divider>
        <div class="related-areas-section">
          <div class="areas-header">
            <span class="areas-title">已关联的行政区县</span>
            <a-button type="primary" @click="handleXZQX" size="small">
              <template #icon>
                <plus-outlined />
              </template>
              添加行政区县
            </a-button>
          </div>

          <div v-if="formData.xzqxList && formData.xzqxList.length > 0" class="areas-list">
            <div
              v-for="item in formData.xzqxList"
              :key="item.id"
              class="area-item"
            >
              <a-card size="small" class="area-card">
                <div class="area-content">
                  <div class="area-info">
                    <a-tag color="blue">{{ item.xzqx }}</a-tag>
                  </div>
                  <a-button
                    type="text"
                    danger
                    size="small"
                    @click="handleDelete(item.id)"
                  >
                    <template #icon>
                      <delete-outlined />
                    </template>
                    删除
                  </a-button>
                </div>
              </a-card>
            </div>
          </div>

          <div v-else class="no-areas">
            <a-empty
              :image="simpleImage"
              description="暂无关联的行政区县"
              style="margin: 16px 0;"
            >
              <a-button type="primary" @click="handleXZQX">
                <template #icon>
                  <plus-outlined />
                </template>
                添加行政区县
              </a-button>
            </a-empty>
          </div>
        </div>

        <!-- 提交按钮 -->
        <a-form-item :wrapper-col="{ span: 24 }" style="text-align: center; margin-top: 32px">
          <a-space size="large">
            <a-button size="large" @click="handleCancel">
              取消
            </a-button>
            <a-button
              type="primary"
              size="large"
              :loading="submitting"
              @click="handleSubmit"
              style="width: 200px"
            >
              {{ submitting ? '提交中...' : '确认创建' }}
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>
  </div>
  <!-- 城市经理选择弹窗 -->
  <CustomModal
    :show="showModal"
    title="请选择城市经理"
    :width="600"
    @close="showModal = false"
  >
    <MultiSelectListOne
      :items="allItems"
      :items2="allItems2"
      @update:confirm="confirm"
      :items-per-page="10"
      :single-select="true"
    />
  </CustomModal>

  <!-- 省区选择弹窗 -->
  <CustomModal
    :show="showModal2"
    title="请选择省区"
    :width="600"
    @close="showModal2 = false"
  >
    <MultiSelectListOne
      :items="allItems3"
      :items2="allItems4"
      @update:confirm="confirm2"
      :items-per-page="10"
      :single-select="true"
    />
  </CustomModal>

  <!-- 行政区域选择弹窗 -->
  <CustomModal
    :show="showModal3"
    title="请选择行政区域"
    :width="700"
    @close="showModal3 = false"
  >
    <MultiSelectListMore
      :items="allItems5"
      :items2="allItems6"
      @update:close="showModal3 = false"
      @update:confirm="confirm3"
      :items-per-page="10"
    />
  </CustomModal>
</template>

<script setup>
import { ref } from 'vue'
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons-vue'
import { Empty } from 'ant-design-vue'
import CustomModal from '@/pages/workbench/partnerSign/components/CustomModal.vue'
import MultiSelectListOne from '@/pages/workbench/partnerSign/components/MultiSelectListOne.vue'
import MultiSelectListMore from '@/pages/workbench/partnerSign/components/MultiSelectListMore.vue'
import {
  get_csjl,
  get_province,
  get_xzqx_user,
  add_jxq
} from "/api/workbench/organization/index.js"
import { onShow } from "@dcloudio/uni-app"
import { useUserInfo } from "/store/user/userInfo"

const store = useUserInfo()
const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE
const formRef = ref(null)

// 表单数据
const formData = ref({
  jxqName: '',
  jxqCode: '',
  csjl: '',
  csjlid: null,
  sq: '',
  sqid: null,
  xzqxList: []
})

const submitting = ref(false)

// 生成经销区编码
const generateJxqCode = () => {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const hour = String(now.getHours()).padStart(2, '0')
  const minute = String(now.getMinutes()).padStart(2, '0')
  const second = String(now.getSeconds()).padStart(2, '0')

  return `JXQ${year}${month}${day}${hour}${minute}${second}`
}

// 页面初始化
onShow(async () => {
  // 自动生成经销区编码
  formData.value.jxqCode = generateJxqCode()

  await loadCityManagers()
  await loadProvinces()
  await loadAdministrativeAreas()
})

// 加载城市经理数据
async function loadCityManagers() {
  try {
    let res = await get_csjl(store.userInfo.id)
    allItems.value = []
    for (var i = 0; i < res.data.result.length; i++) {
      allItems.value.push({
        value: res.data.result[i].id,
        label: res.data.result[i].Master_data_person_name,
        label2: res.data.result[i].Master_data_person_code,
        label3: res.data.result[i].Role_name,
        radio: 0
      })
    }
  } catch (error) {
    console.error('加载城市经理数据失败:', error)
  }
}

// 加载省区数据
async function loadProvinces() {
  try {
    let res2 = await get_province(store.userInfo.id)
    allItems3.value = []
    for (var i = 0; i < res2.data.result.length; i++) {
      allItems3.value.push({
        value: res2.data.result[i].id,
        label: res2.data.result[i].Organization_Name,
        label2: res2.data.result[i].Organization_Code,
        label3: res2.data.result[i].Master_data_person_name,
        radio: 0
      })
    }
  } catch (error) {
    console.error('加载省区数据失败:', error)
  }
}

// 加载行政区域数据
async function loadAdministrativeAreas() {
  try {
    let res3 = await get_xzqx_user(store.userInfo.id)
    allItems5.value = []
    for (var i = 0; i < res3.data.result.length; i++) {
      if(res3.data.result[i].name){
        allItems5.value.push({
          value: res3.data.result[i].id,
          label: res3.data.result[i].name,
          radio: 0
        })
      }
    }
  } catch (error) {
    console.error('加载行政区域数据失败:', error)
  }
}


// 取消操作
const handleCancel = () => {
  uni.showModal({
    title: "温馨提示",
    content: "确定放弃当前编辑内容吗？",
    success: function(res) {
      if (res.confirm) {
        uni.navigateBack()
      }
    }
  })
}

// 删除行政区县
const handleDelete = (id) => {
  formData.value.xzqxList = formData.value.xzqxList.filter(item => item.id !== id)
}
// 城市经理选择相关
const showModal = ref(false)
const allItems = ref([])
const allItems2 = ref([{
  value: 1,
  label: '名称',
  label2: '编码',
  label3: '角色',
  radio: 0
}])
const csjlclick = () => {
  showModal.value = true
}

const confirm = (item) => {
  formData.value.csjl = item.label
  formData.value.csjlid = item.value
  showModal.value = false
}
	
// 省区选择相关
const showModal2 = ref(false)
const allItems3 = ref([])
const allItems4 = ref([{
  value: 1,
  label: '省区名称',
  label2: '省区编码',
  label3: '负责人',
  radio: 0
}])
const csjlclick2 = () => {
  showModal2.value = true
}

const confirm2 = (item) => {
  formData.value.sq = item.label
  formData.value.sqid = item.value
  showModal2.value = false
}
// 行政区域选择相关
const showModal3 = ref(false)
const allItems5 = ref([])
const allItems6 = ref([{
  value: 1,
  label: '行政区县名称',
  radio: 0
}])
const handleXZQX = () => {
  showModal3.value = true
}

const confirm3 = (items) => {
  // 判断传入的是单个对象还是数组
  const processArray = Array.isArray(items) ? items : [items]

  processArray.forEach(item => {
    const index = formData.value.xzqxList.findIndex(x => x.id === item.value)
    if (index === -1) { // 如果不存在
      formData.value.xzqxList.push({
        id: item.value,
        xzqx: item.label
      })
    } else {
      uni.showToast({
        title: `${item.label} 已存在，请勿重复添加`,
        icon: "none",
        duration: 2000,
      })
    }
  })

  showModal3.value = false
}

// 提交表单
const handleSubmit = async () => {
  // 表单验证
  if (!formData.value.jxqName) {
    uni.showToast({
      title: "请输入经销区名称",
      icon: "none",
      duration: 2000,
    })
    return
  }

  if (!formData.value.jxqCode) {
    uni.showToast({
      title: "请输入经销区编码",
      icon: "none",
      duration: 2000,
    })
    return
  }

  if (!formData.value.csjl) {
    uni.showToast({
      title: "请选择城市经理",
      icon: "none",
      duration: 2000,
    })
    return
  }

  if (!formData.value.sq) {
    uni.showToast({
      title: "请选择所属省区",
      icon: "none",
      duration: 2000,
    })
    return
  }

  submitting.value = true
  try {
    let data = {
      jxqName: formData.value.jxqName,
      jxqCode: formData.value.jxqCode,
      csjl: formData.value.csjlid,
      sq: formData.value.sqid,
      xzqxList: formData.value.xzqxList
    }

    let res = await add_jxq(data)
    if (res.data.code == '200') {
      uni.showToast({
        title: "新增成功",
        icon: "success",
        duration: 2000,
      })

      // 延迟返回上一页
      setTimeout(() => {
        uni.navigateBack()
      }, 1000)
    } else {
      uni.showToast({
        title: res.data.message || "新增失败",
        icon: "none",
        duration: 2000,
      })
    }
  } catch (error) {
    console.error('提交失败:', error)
    uni.showToast({
      title: "创建失败，请重试",
      icon: "none",
      duration: 2000,
    })
  } finally {
    submitting.value = false
  }
}
</script>



<style lang="scss" scoped>
.container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.main-card {
  max-width: 1200px;
  margin: 0 auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 8px;

  :deep(.ant-card-head) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px 8px 0 0;

    .ant-card-head-title {
      color: white;
      font-size: 18px;
      font-weight: 600;
    }
  }

  :deep(.ant-card-body) {
    padding: 32px;
  }
}

// 分割线样式
:deep(.ant-divider) {
  margin: 24px 0 16px 0;

  .ant-divider-inner-text {
    font-size: 16px;
    font-weight: 600;
    color: #1890ff;
  }
}

// 表单项样式
:deep(.ant-form-item) {
  margin-bottom: 16px;

  .ant-form-item-label {
    > label {
      font-weight: 500;
      color: #262626;
    }
  }

  .ant-input {
    border-radius: 6px;

    &:focus, &:hover {
      border-color: #40a9ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }

    &[readonly] {
      background-color: #f5f5f5;
      cursor: pointer;
    }
  }
}

// 关联行政区县区域样式
.related-areas-section {
  .areas-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .areas-title {
      font-size: 16px;
      font-weight: 500;
      color: #262626;
    }
  }

  .areas-list {
    display: grid;
    gap: 12px;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));

    .area-item {
      .area-card {
        border-radius: 6px;
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        :deep(.ant-card-body) {
          padding: 12px 16px;
        }
      }

      .area-content {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .area-info {
          flex: 1;
        }
      }
    }
  }

  .no-areas {
    text-align: center;
    padding: 24px;
    background-color: #fafafa;
    border-radius: 6px;
    border: 1px dashed #d9d9d9;
  }
}

// 按钮样式
:deep(.ant-btn-primary) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 6px;
  font-weight: 500;

  &:hover, &:focus {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
  }
}

:deep(.ant-btn) {
  border-radius: 6px;
  font-weight: 500;
}

// 标签样式
:deep(.ant-tag) {
  border-radius: 4px;
  font-weight: 500;
  padding: 4px 8px;
}

// 响应式设计
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }

  .main-card {
    :deep(.ant-card-body) {
      padding: 16px;
    }
  }

  :deep(.ant-col) {
    &.ant-col-12 {
      width: 100% !important;
    }
  }

  :deep(.ant-form-item) {
    .ant-form-item-label {
      text-align: left !important;
    }
  }

  .related-areas-section {
    .areas-header {
      flex-direction: column;
      align-items: stretch;
      gap: 12px;

      .areas-title {
        text-align: center;
      }
    }

    .areas-list {
      grid-template-columns: 1fr;
    }
  }
}
</style>