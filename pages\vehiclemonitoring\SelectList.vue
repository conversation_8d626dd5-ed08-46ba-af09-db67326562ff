<template>
  <div style="position: absolute; z-index: 3">
    <a-float-button
      style="top: 20px; left: 20px"
      v-show="_floatButton"
      @click="handleClick"
    >
      <template #icon>
        <RightCircleTwoTone />
      </template>
    </a-float-button>
  </div>
  <div class="select-container" ref="_selectList">
    <div class="select-item">
      <a-button type="primary" @click="updateData" :loading="loading">
        刷新数据
      </a-button>
      <a-button style="margin-left: 10px" @click="cancelSelect">收起</a-button>
    </div>
    <div class="select-item">
      <span>车辆状态：</span>
      <a-select
        v-model:value="catType"
        style="width: 120px"
        :options="catTypeOption"
        placeholder="请选择"
        @select="select"
      ></a-select>
    </div>
    <div class="select-item">
      <span>计划日期：</span>
      <a-date-picker
        style="width: 120px"
        v-model:value="dateValue"
        @change="changeDate"
      />
    </div>
    <div class="select-item">
      <div>
        <span>车辆：</span>
        <a-switch v-model:checked="checkedCar" @change="changeCar" />
      </div>
      <div style="margin-left: 20px">
        <span>分仓：</span>
        <a-switch v-model:checked="checkedFC" @change="changeFC" />
      </div>
    </div>
    <div class="select-item">车辆数量：{{ props.carList.length }}</div>
    <div class="select-item">分仓数量：{{ props.fcPointList.length }}</div>
    <div class="select-item">搜索车辆：</div>
    <a-select
      v-model:value="carSearchValue"
      placeholder="请输入车牌号或姓名"
      :options="carListOption"
      showSearch
      @change="carSearchChange"
    ></a-select>
  </div>
</template>

<script setup>
import { RightCircleTwoTone } from "@ant-design/icons-vue";
import { computed, ref } from "vue";
import { catTypeOption } from "./data";
import dayjs from "dayjs";
import { message } from "ant-design-vue";

const emits = defineEmits([
  "selectChange",
  "changeMapCenter",
  "setLayerVisible",
  "drawFCMarkers",
]);
const props = defineProps(["carList", "fcPointList"]);

const _floatButton = ref(true);
const _selectList = ref(null);
const handleClick = () => {
  _selectList.value.style.left = "20" + "px";
  _selectList.value.style.opacity = 1;
  _floatButton.value = false;
};

const cancelSelect = () => {
  _selectList.value.style.left = "-280" + "px";
  _selectList.value.style.opacity = 0;
  _floatButton.value = true;
};

const catType = ref("全部");
const select = value => {
  emits("selectChange", value);
};

const checkedCar = ref(true);
const checkedFC = ref(true);

const changeCar = checked => {
  emits("setLayerVisible", "car", checked);
};
const changeFC = checked => {
  emits("setLayerVisible", "fc", checked);
};
const currentDate = dayjs();
const dateValue = ref(currentDate);

const changeDate = date => {
  const dateFormat = dayjs(date).format("YYYY-MM-DD");
  emits("drawFCMarkers", dateFormat);
};

const loading = ref(false);
const updateData = () => {
  loading.value = true;
  emits("selectChange", catType.value);
  emits("drawFCMarkers", dayjs(dateValue.value).format("YYYY-MM-DD"));
  setTimeout(() => {
    loading.value = false;
    message.success("刷新成功");
  }, 600);
};

const carSearchValue = ref(null);
const carListOption = computed(() => {
  return props.carList.map(item => {
    return {
      value: item.Small_boss_car_carrier_driver + "-" + item.Tms_car_number,
      label: item.Small_boss_car_carrier_driver + "-" + item.Tms_car_number,
      ...item,
    };
  });
});

function carSearchChange(value, option) {
  emits("changeMapCenter", option.lat_tx, option.lng_tx);
}
</script>

<style lang="scss" scoped>
.select-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
  position: absolute;
  top: 20px;
  left: -300px;
  padding: 20px;
  background-color: #fff;
  border-radius: 5px;
  z-index: 2;
  transition: 0.3s;
  .select-item {
    display: flex;
    align-items: center;
  }
}
</style>
