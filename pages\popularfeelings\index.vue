<template>
  <a-config-provider :locale="zhCN">
    <div class="container-box">
      <div class="title">
        <div class="select-list">
          <span>发布时间范围：</span>
          <a-select
            ref="select"
            v-model:value="selectValue"
            style="width: 70%"
            @change="selectType"
          >
            <a-select-option value="7天">7天</a-select-option>
            <a-select-option value="当天">当天</a-select-option>
            <a-select-option value="30天">30天</a-select-option>
            <a-select-option value="自定义"> 自定义 </a-select-option>
          </a-select>
        </div>
        <div class="select-list" v-if="selectValue === '自定义'">
          <span>自定义时间：</span>
          <a-range-picker
            v-model:value="selectDate"
            @change="selectDateChange"
            style="width: 70%"
          />
        </div>
        <div class="select-list">
          <span>关键词筛选：</span>
          <a-select
            v-model:value="keyWords"
            style="width: 70%"
            :options="keywordsOption"
            showArrow
            allowClear
            @change="selectKeyWordChange"
          ></a-select>
        </div>
      </div>
      <div style="padding: 20rpx">
        <scroll-view
          :scroll-top="scrollTop"
          scroll-y="true"
          @scroll="scroll"
          @scrolltolower="scrolltolower"
          class="scroll-Y"
        >
          <div class="word-cloud" id="myEchart">1</div>
          <div class="word-list">
            <div class="text-list" v-for="item in dataList">
              <a
                :href="item.url"
                target="_blank"
                style="font-size: 40rpx"
                v-html="item.title"
              >
              </a>
              <span v-html="item.abs"> </span>
              <span>
                发布时间：
                {{ item.publish_date_f }}
              </span>
            </div>
          </div>
        </scroll-view>

        <div style="text-align: center">
          <u-loadmore :status="status" />
        </div>
      </div>
      <!-- 回到顶部按钮 -->
      <div class="go-top" @click="goTop" v-if="oldScrollTop > 0">
        <u-icon name="arrow-upward"></u-icon>
      </div>
    </div>
  </a-config-provider>
</template>

<script setup>
import { nextTick, reactive, ref } from "vue";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
import zhCN from "ant-design-vue/es/locale/zh_CN";
dayjs.locale("zh-cn");

import * as echarts from "echarts";
import "echarts-wordcloud";
import {
  getAllKeywords,
  getPopList,
  getWordCloudList,
} from "../../api/popularfeelings/inedx";

const scrollTop = ref(0);
const oldScrollTop = ref(0);

const scroll = e => {
  oldScrollTop.value = e.detail.scrollTop;
};

const scrolltolower = async () => {
  dataParams.page += 1;
  await getPopularFeels();
  initEchart();
};

const goTop = () => {
  scrollTop.value = oldScrollTop.value;
  nextTick(() => {
    scrollTop.value = 0;
  });
};

const selectValue = ref("7天");

const selectDate = ref([]);
const status = ref("loading");

const keyWords = ref([]);

const dataList = ref([]);
const optionData = ref([]);
const keywordsOption = ref([]);

let myChart;
async function initEchart() {
  const option = {
    series: [
      {
        type: "wordCloud",
        shape: "circle",
        keepAspect: false,
        // maskImage: maskImage,
        left: "center",
        top: "center",
        width: "100%",
        height: "100%",
        right: null,
        bottom: null,
        sizeRange: [12, 60],
        rotationRange: [-90, 90],
        rotationStep: 45,
        gridSize: 8,
        drawOutOfBound: false,
        layoutAnimation: true,
        textStyle: {
          fontFamily: "sans-serif",
          fontWeight: "bold",
          color: function () {
            return (
              "rgb(" +
              [
                Math.round(Math.random() * 160),
                Math.round(Math.random() * 160),
                Math.round(Math.random() * 160),
              ].join(",") +
              ")"
            );
          },
        },
        emphasis: {
          // focus: 'self',
          textStyle: {
            textShadowBlur: 3,
            textShadowColor: "#333",
          },
        },
        //data属性中的value值却大，权重就却大，展示字体就却大
        data: optionData.value,
      },
    ],
  };

  option && myChart.setOption(option);
}

// 默认请求参数
const dataParams = reactive({
  page: 1,
  pageSize: 50,
  keyWord: "全部",
  startTime: dayjs().subtract(7, "day").format("YYYY-MM-DD 00:00:00"),
  endTime: dayjs().format("YYYY-MM-DD 23:59:59"),
});

// 获取舆情列表
async function getPopularFeels() {
  status.value = "loading";
  const { data } = await getPopList(dataParams);
  dataList.value = dataList.value.concat(data.result[0]);

  // 处理关键词出现次数

  const wordsRes = await getWordCloudList(dataParams);
  optionData.value = wordsRes.data.result;

  setTimeout(() => {
    status.value =
      data.result[0].length < dataParams.pageSize ? "nomore" : "loadmore";
  }, 600);
}

function selectType(value) {
  if (value === "自定义") return;

  dataList.value = [];
  dataParams.page = 1;
  if (value === "7天") {
    dataParams.startTime = dayjs()
      .subtract(7, "day")
      .format("YYYY-MM-DD 00:00:00");
    dataParams.endTime = dayjs().format("YYYY-MM-DD 23:59:59");
  }
  if (value === "当天") {
    dataParams.startTime = dayjs().format("YYYY-MM-DD 00:00:00");
    dataParams.endTime = dayjs().format("YYYY-MM-DD 23:59:59");
  }
  if (value === "30天") {
    dataParams.startTime = dayjs()
      .subtract(30, "day")
      .format("YYYY-MM-DD 00:00:00");
    dataParams.endTime = dayjs().format("YYYY-MM-DD 23:59:59");
  }
  getPopularFeels();
}

async function selectDateChange(value) {
  dataList.value = [];
  dataParams.page = 1;
  dataParams.startTime = dayjs(value[0]).format("YYYY-MM-DD 00:00:00");
  dataParams.endTime = dayjs(value[1]).format("YYYY-MM-DD 23:59:59");
  await getPopularFeels();
  initEchart();
}

// 获取所有的关键字
async function getAllKeyWord() {
  const { data } = await getAllKeywords();
  keywordsOption.value = data.result.map(item => {
    return {
      value: item.keywordName,
    };
  });
  keywordsOption.value.unshift({
    value: "全部",
  });
}
getAllKeyWord();

async function selectKeyWordChange(value) {
  dataList.value = [];
  dataParams.page = 1;

  if (value) {
    dataParams.keyWord = value;
  } else {
    dataParams.keyWord = "全部";
  }

  await getPopularFeels();
  initEchart();
}

nextTick(async () => {
  const echartDom = document.getElementById("myEchart");
  myChart = echarts.init(echartDom);
  await getPopularFeels();
  initEchart();

  //随着屏幕大小调节图表
  window.addEventListener("resize", () => {
    myChart.resize();
  });
});
</script>

<style lang="scss" scoped>
.container-box {
  height: calc(100vh - 120rpx);
  width: 100vw;
  overflow-y: scroll;
  position: relative;
  .go-top {
    position: absolute;
    bottom: 20px;
    right: 20px;
    height: 50px;
    width: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    background: #fff;
    box-shadow: 0px 1px 31px rgba(0, 0, 0, 0.2);
    cursor: pointer;
  }
  .title {
    position: fixed;
    top: 0;
    width: 100%;
    padding-left: 40rpx;
    padding: 40rpx;
    border-bottom: 1px solid rgba(217, 217, 217);
    display: grid;
    gap: 20rpx;
    grid-template-columns: repeat(3, minmax(0, 1fr));
    background: #fff;
    z-index: 1;
    .select-list {
      display: flex;
      align-items: center;
    }
  }

  .word-cloud {
    margin-top: 200rpx;
    height: 400px;
    border-radius: 20rpx;
    border: 1px solid rgba(217, 217, 217);
    margin-bottom: 40rpx;
  }
  .word-list {
    // margin-top: 160rpx;
    display: flex;
    flex-direction: column;
    gap: 40rpx;
    padding: 0 20rpx;
    .text-list {
      display: flex;
      flex-direction: column;
      gap: 10rpx;
      color: #474747;
    }
  }
}

.scroll-Y {
  height: 100vh;
}

/* 媒体查询 */
@media (max-width: 1000px) {
  .container-box {
    .title {
      display: grid;
      grid-template-columns: repeat(1, minmax(0, 1fr));
    }
    .word-cloud {
      margin-top: 200rpx;
    }
  }
}
/* 媒体查询 */
@media (max-width: 700px) {
  .container-box {
    .title {
      display: grid;
      grid-template-columns: repeat(1, minmax(0, 1fr));
    }
    .word-cloud {
      margin-top: 300rpx;
    }
  }
}
/* 媒体查询 */
@media (max-width: 450px) {
  .container-box {
    .title {
      display: grid;
      grid-template-columns: repeat(1, minmax(0, 1fr));
    }
    .word-cloud {
      margin-top: 380rpx;
    }
  }
}
</style>
