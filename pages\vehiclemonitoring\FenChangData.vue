<template>
  <a-table :columns="columns" :data-source="dataSource" size="small">
    <template #title>
      <div style="text-align: right">
        <a-button type="primary" @click="exportData">导出Excel</a-button>
      </div>
    </template>
  </a-table>
</template>

<script setup>
import { onMounted, ref } from "vue";
import { getFCArrival30 } from "../../api/vehiclemonitoring";
import { exportToExcel } from "../Map/data-uitls/dataModal";

const props = defineProps({
  branchCode: {
    type: String,
    default: "",
  },
});

onMounted(() => {
  getBranchArrivalData();
});

const columns = ref([
  {
    title: "日期",
    dataIndex: "arrive_date",
    key: "arrive_date",
    align: "center",
  },
  {
    title: "到货筐数",
    dataIndex: "number_of_baskets",
    key: "number_of_baskets",
    align: "center",
  },
  {
    title: "到货金额",
    dataIndex: "arrival_amount",
    key: "arrival_amount",
    align: "center",
  },
]);
const dataSource = ref([]);

// 获取该分仓近30批到货数据
async function getBranchArrivalData() {
  const { data } = await getFCArrival30(props.branchCode);

  dataSource.value = data.result;
}

// 导出数据
const exportData = () => {
  exportToExcel(
    ["分仓近30批到货"],
    [["日期", "到货筐数", "到货金额"]],
    [
      dataSource.value.map(item => {
        return {
          arrive_date: item.arrive_date,
          number_of_baskets: item.number_of_baskets,
          arrival_amount: item.arrival_amount,
        };
      }),
    ]
  );
};
</script>

<style></style>
