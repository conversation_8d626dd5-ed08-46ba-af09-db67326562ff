<template>
  <div class="container">
    <a-card title="库长管理" class="main-card">
      <!-- 搜索和操作区域 -->
      <div class="header-actions">
        <div class="search-section">
          <a-input-search
            v-model:value="searchQuery"
            placeholder="搜索库长姓名..."
            enter-button="搜索"
            size="large"
            allow-clear
            @search="handleSearch"
            @change="handleSearchChange"
            style="width: 300px"
          />
        </div>
        <div class="action-section">
          <a-button type="primary" size="large" @click="handleAdd">
            <template #icon>
              <plus-outlined />
            </template>
            新增库长
          </a-button>
        </div>
      </div>

      <!-- 数据表格 -->
      <a-table
        :columns="columns"
        :data-source="filteredList"
        :loading="loading"
        :pagination="paginationConfig"
        row-key="id"
        @change="handleTableChange"
        class="data-table"
      >
        <!-- 签约状态列 -->
        <template #signStatus="{ record }">
          <a-tag :color="getStatusColor(record.sign_status)">
            {{ getStatusText(record.sign_status) }}
          </a-tag>
        </template>

        <!-- 审批状态列 -->
        <template #approvalStatus="{ record }">
          <a-tag color="blue">
            {{ record.approval_status }}
          </a-tag>
        </template>

        <!-- 操作列 -->
        <template #action="{ record }">
          <a-space>
            <a-button type="link" @click="toDetail(record)">
              查看详情
            </a-button>
          </a-space>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script setup>
import { ref, computed, onUnmounted } from "vue";
import { PlusOutlined } from "@ant-design/icons-vue";
import { onShow } from "@dcloudio/uni-app";
import { useUserInfo } from "/store/user/userInfo";
import {
  getTrainmasterListApi,
  getTrainmasterListApiinfo,
  getTrainmasterListApi_dq,
  getTrainmasterListApiinfo_dq,
} from "/api/workbench/trainmastersigning";

const userStore = useUserInfo();

const dataList = ref([]);
const filteredList = ref([]);
const loading = ref(false);
const searchQuery = ref("");
const isSearching = ref(false); // 标记是否正在搜索状态
let searchTimer = null; // 搜索防抖定时器

// 表格列配置
const columns = [
  {
    title: "库长姓名",
    dataIndex: "rzrymc",
    key: "rzrymc",
    width: 150,
    ellipsis: true,
  },
  {
    title: "签约状态",
    dataIndex: "sign_status",
    key: "sign_status",
    width: 120,
    slots: { customRender: "signStatus" },
  },
  {
    title: "审批状态",
    dataIndex: "approval_status",
    key: "approval_status",
    width: 120,
    slots: { customRender: "approvalStatus" },
  },
  {
    title: "入职库房",
    dataIndex: "rzkf",
    key: "rzkf",
    width: 180,
    ellipsis: true,
  },
  {
    title: "操作",
    key: "action",
    width: 150,
    slots: { customRender: "action" },
  },
];

// 分页状态
const currentPage = ref(1);
const pageSize = ref(10);

// 分页配置
const paginationConfig = computed(() => ({
  current: currentPage.value,
  pageSize: pageSize.value,
  total: filteredList.value.length,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
  pageSizeOptions: ["10", "20", "50", "100"],
  locale: {
    items_per_page: "条/页",
    jump_to: "跳至",
    jump_to_confirm: "确定",
    page: "页",
    prev_page: "上一页",
    next_page: "下一页",
    prev_5: "向前 5 页",
    next_5: "向后 5 页",
    prev_3: "向前 3 页",
    next_3: "向后 3 页",
  },
}));

// 获取状态颜色
const getStatusColor = (status) => {
  const colorMap = {
    0: "processing", // 签约中
    1: "success", // 已签约
  };
  return colorMap[status] || "default";
};

// 获取状态文本
const getStatusText = (status) => {
  const textMap = {
    0: "签约中",
    1: "已签约",
  };
  return textMap[status] || "未知状态";
};
async function getTrainmasterList() {
  loading.value = true;
  try {
    const jxqListStr = userStore.userInfo.jxqId.join(",");
    const result = jxqListStr
      .split(",")
      .map((item) => `"${item}"`)
      .join(",");
    if (
      userStore.userInfo.Role_grade == "大区总"
    ) {
      const { data } = await getTrainmasterListApi_dq(
        result,
        userStore.userInfo.id,
        2
      );
      dataList.value = data.result;
      filteredList.value = data.result;
    } else {
      const { data } = await getTrainmasterListApi(
        result,
        userStore.userInfo.id,
        2
      );
      dataList.value = data.result;
      filteredList.value = data.result;
    }

    isSearching.value = false;
    currentPage.value = 1; // 重置分页
  } catch (error) {
    console.error("获取库长列表失败:", error);
    uni.showToast({
      title: "获取数据失败",
      icon: "none",
      duration: 2000,
    });
  } finally {
    loading.value = false;
  }
}

function handleSearch(value) {
  searchQuery.value = value;
  currentPage.value = 1; // 重置分页

  if (!value || value.trim() === "") {
    // 清空搜索，恢复原始数据
    if (isSearching.value) {
      // 如果之前是搜索状态，重新获取完整数据
      getTrainmasterList();
    } else {
      // 如果不是搜索状态，直接恢复本地数据
      filteredList.value = dataList.value;
    }
    return;
  }

  searchWithKeyword(value.trim());
}

// 处理搜索框内容变化（包括清空操作）
function handleSearchChange(e) {
  const value = e.target.value;

  // 清除之前的定时器
  if (searchTimer) {
    clearTimeout(searchTimer);
  }

  if (!value || value.trim() === "") {
    // 搜索框被清空，立即恢复原始数据
    handleSearch("");
  } else {
    // 添加防抖，500ms后执行搜索
    searchTimer = setTimeout(() => {
      handleSearch(value);
    }, 500);
  }
}

async function searchWithKeyword(keyword) {
  loading.value = true;
  isSearching.value = true;
  try {
    const jxqListStr = userStore.userInfo.jxqId.join(",");
    const result = jxqListStr
      .split(",")
      .map((item) => `"${item}"`)
      .join(",");

    if (
      userStore.userInfo.Role_grade == "大区总" 
    ) {
      const { data } = await getTrainmasterListApiinfo_dq(
        result,
        userStore.userInfo.id,
        2,
        keyword
      );

      if (data.result && data.result.length > 0) {
        filteredList.value = data.result;
      } else {
        filteredList.value = [];
        uni.showToast({
          title: "未找到相关数据",
          icon: "none",
          duration: 2000,
        });
      }
    } else {
      const { data } = await getTrainmasterListApiinfo(
        result,
        userStore.userInfo.id,
        2,
        keyword
      );

      if (data.result && data.result.length > 0) {
        filteredList.value = data.result;
      } else {
        filteredList.value = [];
        uni.showToast({
          title: "未找到相关数据",
          icon: "none",
          duration: 2000,
        });
      }
    }
  } catch (error) {
    console.error("搜索失败:", error);
    uni.showToast({
      title: "搜索失败",
      icon: "none",
      duration: 2000,
    });
    // 搜索失败时恢复原始数据
    filteredList.value = dataList.value;
    isSearching.value = false;
  } finally {
    loading.value = false;
  }
}

// 表格变化处理
function handleTableChange(pagination, filters, sorter) {
  console.log("表格变化:", { pagination, filters, sorter });

  // 更新分页状态
  if (pagination) {
    currentPage.value = pagination.current;
    pageSize.value = pagination.pageSize;
  }
}

function toDetail(item) {
  uni.navigateTo({
    url: `/pages/workbench/trainmastersigning/Detail?id=${item.id}`,
  });
}

function handleAdd() {
  uni.navigateTo({
    url: `/pages/workbench/trainmastersigning/addTrain?type=${2}`,
  });
}

onShow(() => {
  getTrainmasterList();
});

// 组件卸载时清理定时器
onUnmounted(() => {
  if (searchTimer) {
    clearTimeout(searchTimer);
    searchTimer = null;
  }
});
</script>

<style lang="scss" scoped>
.container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.main-card {
  max-width: 1400px;
  margin: 0 auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 8px;

  :deep(.ant-card-head) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px 8px 0 0;

    .ant-card-head-title {
      color: white;
      font-size: 18px;
      font-weight: 600;
    }
  }

  :deep(.ant-card-body) {
    padding: 32px;
  }
}

// 头部操作区域
.header-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 16px;

  .search-section {
    flex: 1;
    min-width: 300px;
  }

  .action-section {
    flex-shrink: 0;
  }
}

// 数据表格样式
.data-table {
  :deep(.ant-table) {
    background: #ffffff;
    border-radius: 8px;
    overflow: hidden;

    .ant-table-thead > tr > th {
      background: #fafafa;
      font-weight: 600;
      color: #262626;
      border-bottom: 2px solid #f0f0f0;
    }

    .ant-table-tbody > tr {
      transition: all 0.2s;

      &:hover {
        background-color: #f5f5f5;
      }

      > td {
        border-bottom: 1px solid #f0f0f0;
        padding: 16px;
      }
    }
  }

  // 标签样式
  :deep(.ant-tag) {
    border-radius: 4px;
    font-weight: 500;
    padding: 4px 8px;
  }

  // 按钮样式
  :deep(.ant-btn-link) {
    padding: 0;
    height: auto;

    &.ant-btn-dangerous {
      color: #ff4d4f;

      &:hover {
        color: #ff7875;
      }
    }
  }
}

// 搜索框样式
:deep(.ant-input-search) {
  .ant-input {
    border-radius: 6px;

    &:focus {
      border-color: #40a9ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }

  .ant-btn {
    border-radius: 0 6px 6px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;

    &:hover {
      background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
      border-color: #5a6fd8;
    }
  }
}

// 主要按钮样式
:deep(.ant-btn-primary) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 6px;
  font-weight: 500;

  &:hover,
  &:focus {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
  }
}

// 分页样式
:deep(.ant-pagination) {
  margin-top: 24px;
  text-align: center;

  .ant-pagination-item {
    border-radius: 4px;

    &.ant-pagination-item-active {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-color: #667eea;

      a {
        color: white;
      }
    }
  }

  .ant-pagination-prev,
  .ant-pagination-next {
    border-radius: 4px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }

  .main-card {
    :deep(.ant-card-body) {
      padding: 16px;
    }
  }

  .header-actions {
    flex-direction: column;
    align-items: stretch;

    .search-section {
      min-width: auto;
    }

    .action-section {
      align-self: center;
    }
  }

  .data-table {
    :deep(.ant-table) {
      .ant-table-tbody > tr > td {
        padding: 12px 8px;
        font-size: 14px;
      }
    }
  }
}

@media (max-width: 480px) {
  .header-actions {
    .search-section {
      :deep(.ant-input-search) {
        .ant-input {
          font-size: 14px;
        }
      }
    }
  }
}
</style>
