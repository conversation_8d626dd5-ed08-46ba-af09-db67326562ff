<template>
  <div class="price-value-change">
    <!-- 表单容器 -->
    <div class="form-container">
      <a-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        layout="vertical"
        :scroll-to-first-error="true"
      >
        <!-- 模块1: 产品选择 -->
        <a-card class="form-section" :bordered="false">
          <template #title>
            <div class="section-title">
              <AppstoreOutlined class="title-icon" />
              <span class="title-text">产品选择</span>
            </div>
          </template>

          <a-form-item label="选择产品" name="productId" :required="true">
            <a-select
              v-model:value="formData.productId"
              placeholder="请选择产品"
              size="large"
              :options="productOptions"
              @change="handleProductChange"
              show-search
            />
          </a-form-item>
          <div
            v-if="formData.productId"
            style="display: flex; margin-top: 15px"
          >
            <span>产品活动价格：</span>
            <a-input style="width: 200px" v-model:value="productPrice" />
          </div>
        </a-card>

        <!-- 模块2: 渠道范围选择 -->
        <a-card class="form-section" :bordered="false">
          <template #title>
            <div class="section-title">
              <ClusterOutlined class="title-icon" />
              <span class="title-text">渠道范围</span>
            </div>
          </template>

          <!-- 渠道类型选择 -->
          <a-form-item label="选择类型" name="channelType" :required="true">
            <a-radio-group
              v-model:value="formData.channelType"
              @change="handleChannelTypeChange()"
            >
              <a-space direction="vertical">
                <a-radio
                  v-for="item in channelTypes"
                  :key="item.value"
                  :value="item.value"
                >
                  {{ item.label }}
                </a-radio>
              </a-space>
            </a-radio-group>
          </a-form-item>

          <!-- 具体选择区域 -->
          <a-form-item
            v-if="formData.channelType && formData.channelType !== 'company'"
            :label="getChannelLabel()"
            :name="formData.channelType"
            :required="true"
          >
            <a-select
              v-model:value="formData[formData.channelType]"
              :placeholder="`请选择${getChannelLabel()}`"
              size="large"
              mode="multiple"
              :options="channelOptions"
              show-search
              :filter-option="filterOption"
              @change="handleChannelChange"
            />
          </a-form-item>
        </a-card>

        <!-- 模块3: 生效时间设置 -->
        <a-card class="form-section" :bordered="false">
          <template #title>
            <div class="section-title">
              <CalendarOutlined class="title-icon" />
              <span class="title-text">活动时间范围</span>
            </div>
          </template>
          <a-form-item label="活动名称" name="eventName" :required="true">
            <a-input
              v-model:value="formData.eventName"
              placeholder="请输入活动名称"
              size="large"
            >
            </a-input>
          </a-form-item>

          <a-form-item
            label="活动开始时间"
            name="eventStartTime"
            :required="true"
          >
            <a-date-picker
              v-model:value="formData.eventStartTime"
              placeholder="请选择活动开始时间"
              size="large"
              :disabled-date="disabledDate"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
          </a-form-item>

          <a-form-item
            label="活动结束时间"
            name="eventEndTime"
            :required="true"
          >
            <a-date-picker
              v-model:value="formData.eventEndTime"
              placeholder="请选择活动结束时间"
              size="large"
              :disabled-date="disabledDate"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
          </a-form-item>
        </a-card>

        <!-- 模块4: 价值链填写 -->
        <a-card class="form-section" :bordered="false">
          <template #title>
            <div class="section-title">
              <DollarOutlined class="title-icon" />
              <span class="title-text">价值链设置</span>
            </div>
          </template>

          <a-row :gutter="[16, 16]">
            <a-col :xs="24" :sm="12" :md="8">
              <a-form-item
                label="公司价值链"
                name="companyValueChain"
                :required="true"
              >
                <a-input-number
                  v-model:value="formData.companyValueChain"
                  placeholder="请输入公司价值链"
                  size="large"
                  :min="0"
                  :precision="2"
                  :step="0.01"
                  style="width: 100%"
                  @change="calculateManagerPoint"
                />
              </a-form-item>
            </a-col>

            <a-col :xs="24" :sm="12" :md="8">
              <a-form-item
                label="车长默认提点"
                name="driverDefaultPoint"
                :required="true"
              >
                <a-input-number
                  v-model:value="formData.driverDefaultPoint"
                  placeholder="请输入车长默认提点"
                  size="large"
                  :min="0"
                  :precision="2"
                  :step="0.01"
                  style="width: 100%"
                  @change="calculateManagerPoint"
                />
              </a-form-item>
            </a-col>

            <a-col :xs="24" :sm="12" :md="8">
              <a-form-item
                label="库长默认提点"
                name="warehouseDefaultPoint"
                :required="true"
              >
                <a-input-number
                  v-model:value="formData.warehouseDefaultPoint"
                  placeholder="请输入库长默认提点"
                  size="large"
                  :min="0"
                  :precision="2"
                  :step="0.01"
                  style="width: 100%"
                  @change="calculateManagerPoint"
                />
              </a-form-item>
            </a-col>

            <a-col :xs="24" :sm="12" :md="8">
              <a-form-item label="区长提点" name="managerPoint">
                <a-input
                  v-model:value="formData.managerPoint"
                  placeholder="自动计算"
                  size="large"
                  readonly
                  :style="{ background: '#f5f5f5' }"
                >
                  <template #prefix>
                    <CalculatorOutlined />
                  </template>
                </a-input>
              </a-form-item>
            </a-col>
          </a-row>
        </a-card>

        <!-- 提交按钮 -->
        <a-card class="submit-section" :bordered="false">
          <a-space style="width: 100%" direction="vertical">
            <a-button
              type="primary"
              size="large"
              :loading="submitLoading"
              @click="handleSubmit"
              style="
                width: 100%;
                height: 50px;
                font-size: 16px;
                font-weight: 600;
              "
            >
              <template #icon>
                <SendOutlined />
              </template>
              提交申请
            </a-button>

            <a-button size="large" @click="handleReset" style="width: 100%">
              <template #icon>
                <ReloadOutlined />
              </template>
              重置表单
            </a-button>
          </a-space>
        </a-card>
      </a-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from "vue";
import { message } from "ant-design-vue";
import dayjs from "dayjs";
import {
  AppstoreOutlined,
  ClusterOutlined,
  DollarOutlined,
  CalendarOutlined,
  SendOutlined,
  ReloadOutlined,
  CalculatorOutlined,
} from "@ant-design/icons-vue";
import {
  getProductListApi,
  getChannelListApi,
} from "/api/workbench/PriceValueChange/index.js";
import { submitProductActivityApi } from "/api/workbench/productEvent";

// 响应式数据
const formRef = ref(null);
const submitLoading = ref(false);

// 表单数据
const formData = reactive({
  productId: undefined,
  productName: "",
  channelType: "",
  region: [],
  province: [],
  area: [],
  driver: [],
  eventName: null,
  eventStartTime: undefined,
  eventEndTime: undefined,
  companyValueChain: undefined,
  driverDefaultPoint: undefined,
  warehouseDefaultPoint: undefined,
  managerPoint: "",
});

// 渠道类型选项
const channelTypes = [
  { label: "全公司", value: "company" },
  { label: "多选大区", value: "region" },
  { label: "多选省区", value: "province" },
  { label: "多选经销区", value: "area" },
];

// 产品选项
const productOptions = ref([]);

// 渠道选项数据
const channelOptions = ref([]);

//产品活动价格
const productPrice = ref(0);

const product_id = ref("");
const area_range = ref("");
// 获取产品数据
const getProductList = async () => {
  const { data } = await getProductListApi();

  productOptions.value = data.result.map(item => ({
    label: item.name,
    value: item.name,
    ...item,
  }));
};

// 禁用日期函数 - 禁用今天及以前的日期
const disabledDate = current => {
  // 禁用今天及以前的日期
  return current && current <= dayjs().startOf("day");
};

// 表单验证规则
const rules = {
  productId: [{ required: true, message: "请选择产品", trigger: "change" }],
  channelType: [
    { required: true, message: "请选择渠道类型", trigger: "change" },
  ],
  regions: [{ required: true, message: "请选择大区", trigger: "change" }],
  provinces: [{ required: true, message: "请选择省区", trigger: "change" }],
  dealers: [{ required: true, message: "请选择经销区", trigger: "change" }],
  drivers: [{ required: true, message: "请选择车长", trigger: "change" }],
  eventName: [{ required: true, message: "请输入活动名称", trigger: "change" }],
  eventStartTime: [
    { required: true, message: "请选择活动开始时间", trigger: "change" },
  ],
  eventEndTime: [
    { required: true, message: "请选择活动结束时间", trigger: "change" },
  ],
  companyValueChain: [
    { required: true, message: "请输入公司价值链", trigger: "blur" },
  ],
  driverDefaultPoint: [
    { required: true, message: "请输入车长默认提点", trigger: "blur" },
  ],
  warehouseDefaultPoint: [
    { required: true, message: "请输入库长默认提点", trigger: "blur" },
  ],
};

const getChannelLabel = () => {
  const typeLabels = {
    region: "大区",
    province: "省区",
    area: "经销区",
    driver: "车长",
  };
  return typeLabels[formData.channelType] || "";
};

const filterOption = (input, option) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

const handleProductChange = value => {
  const product = productOptions.value.find(p => p.value === value);
  product_id.value = product.id;
  productPrice.value = product.price ?? 0;
};

const handleChannelChange = value => {
  if (formData.channelType === "company") {
    area_range.value = "";
  }
  if (formData.channelType === "region") {
    const fil = channelOptions.value
      .filter(item => {
        return value.includes(item.value);
      })
      .map(item => {
        return item.id;
      });
    area_range.value = fil.join(",");
    console.log("region", area_range.value);
  }
  if (formData.channelType === "province") {
    const fil = channelOptions.value
      .filter(item => {
        return value.includes(item.value);
      })
      .map(item => {
        return item.id;
      });
    area_range.value = fil.join(",");
    console.log("province", area_range.value);
  }
  if (formData.channelType === "area") {
    console.log("area", value);
    const fil = channelOptions.value
      .filter(item => {
        return value.includes(item.value);
      })
      .map(item => {
        return item.id;
      });
    area_range.value = fil.join(",");
    console.log("area", area_range.value);
  }
  if (formData.channelType === "driver") {
    const fil = channelOptions.value
      .filter(item => {
        return value.includes(item.value);
      })
      .map(item => {
        return item.id;
      });
    area_range.value = fil.join(",");
    console.log("driver", area_range.value);
  }
};

const handleChannelTypeChange = async () => {
  // 清除其他类型的选项
  formData.region = [];
  formData.province = [];
  formData.area = [];
  formData.driver = [];

  const type = formData.channelType;
  if (type === "company") {
    return (channelOptions.value = []);
  }
  const { data } = await getChannelListApi(type);

  channelOptions.value = data.result.map(item => ({
    label: item.name,
    value: item.name,
    ...item,
  }));
};

const calculateManagerPoint = () => {
  const company = formData.companyValueChain || 0;
  const driver = formData.driverDefaultPoint || 0;
  const warehouse = formData.warehouseDefaultPoint || 0;

  const result = company - driver - warehouse;
  formData.managerPoint = result >= 0 ? result.toFixed(2) : "0.00";
};

const handleSubmit = async () => {
  try {
    await formRef.value.validate();

    submitLoading.value = true;

    const userInfo = JSON.parse(sessionStorage.getItem("userInfo"));

    // 提交参数
    const params = {
      activity_name: formData.eventName, //活动名称
      product_id: product_id.value,
      area_range: area_range.value,
      type: formData.channelType, // 生效范围，type分为（company/province/region/area）其中公司不需要传id
      price: productPrice.value, // 活动价格
      value_chain: formData.companyValueChain, //  价值链
      driver_rate: formData.driverDefaultPoint, // 车长默认提点
      warehouse_manage_rate: formData.warehouseDefaultPoint, //库长默认提点
      start_date: formData.eventStartTime, // 生效时间
      end_date: formData.eventEndTime, // 结束时间
      submit_staff: userInfo.id, // 提交人
    };
    console.log("🚀 ~ handleSubmit ~ params:", params);

    await submitProductActivityApi(params);
    message.success("提交成功！");
    uni.navigateBack();
  } catch (error) {
    console.error("提交失败", error);
  } finally {
    submitLoading.value = false;
  }
};

const handleReset = () => {
  formRef.value.resetFields();
  Object.assign(formData, {
    productId: undefined,
    productName: "",
    channelType: "",
    regions: [],
    provinces: [],
    dealers: [],
    drivers: [],
    effectiveTime: undefined,
    companyValueChain: undefined,
    driverDefaultPoint: undefined,
    warehouseDefaultPoint: undefined,
    managerPoint: "",
  });
  message.info("表单已重置");
};

onMounted(() => {
  // 初始化数据
  getProductList();
});
</script>

<style lang="scss" scoped>
.price-value-change {
  min-height: 100vh;
  background: #f0f2f5;

  .page-header {
    position: sticky;
    top: 0;
    z-index: 100;
    background: #ffffff;
    border-bottom: 1px solid #e8e8e8;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    .header-content {
      padding: 16px 20px;
      max-width: 1200px;
      margin: 0 auto;

      .page-title {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        color: #262626;
      }
    }
  }

  .form-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 24px 20px;

    .form-section {
      margin-bottom: 24px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

      .section-title {
        display: flex;
        align-items: center;

        .title-icon {
          margin-right: 8px;
          font-size: 16px;
          color: #1890ff;
        }

        .title-text {
          font-size: 16px;
          font-weight: 600;
          color: #262626;
        }
      }

      :deep(.ant-card-head) {
        border-bottom: 1px solid #f0f0f0;
      }

      :deep(.ant-card-body) {
        padding: 24px;
      }
    }

    .submit-section {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

      :deep(.ant-card-body) {
        padding: 24px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .price-value-change {
    .page-header {
      .header-content {
        padding: 12px 16px;

        .page-title {
          font-size: 18px;
        }
      }
    }

    .form-container {
      padding: 16px;

      .form-section {
        margin-bottom: 16px;

        :deep(.ant-card-body) {
          padding: 16px;
        }
      }

      .submit-section {
        :deep(.ant-card-body) {
          padding: 16px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .price-value-change {
    .page-header {
      .header-content {
        padding: 10px 12px;

        .page-title {
          font-size: 16px;
        }
      }
    }

    .form-container {
      padding: 12px;

      .form-section {
        margin-bottom: 12px;

        :deep(.ant-card-body) {
          padding: 12px;
        }
      }

      .submit-section {
        :deep(.ant-card-body) {
          padding: 12px;
        }
      }
    }
  }
}

// 自定义样式
:deep(.ant-form-item-label) {
  font-weight: 500;
}

:deep(.ant-radio-group) {
  width: 100%;

  .ant-radio-wrapper {
    display: block;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

:deep(.ant-select-multiple) {
  .ant-select-selector {
    min-height: 40px;
  }
}

:deep(.ant-input-number) {
  .ant-input-number-input {
    height: 40px;
  }
}
</style>
