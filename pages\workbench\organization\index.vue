<template>
	<view class="container">
		<view class="button-list">
			<view class="button-item" v-for="(button, index) in buttons" :key="index">
				<!-- 左侧图片区域 -->
				<view class="image-container">
					<text class="art-text">{{ button.title }}</text>
					<!-- 遮罩层 -->
					<view class="corner-mask"></view>
				</view>

				<!-- 中间描述 -->
				<view class="item-description">
					<view><text class="desc-text">{{ button.description }}</text></view>
					<view><text class="desc-text2">{{ button.description2 }}</text></view>
				</view>

				<!-- 右侧操作按钮 -->
				<button class="action-button" @click="handleButtonClick(button.label)">
					{{ button.label }}
				</button>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		ref
	} from 'vue';
	import {
		onLoad
	} from '@dcloudio/uni-app'


	import { useUserInfo } from "/store/user/userInfo";
	export default {
		setup() {
			const buttons = ref([{
					number: 1,
					label: '省区关联经销区',
					description: '省区关联经销区xxx',
					description2: '省区关联经销区',
					title: '关联经销'
				},
				{
					number: 2,
					label: '省区关联省区总',
					description: '请将空框返货到工厂',
					description2: '物流司机装车确认后资金到账',
					title: '关联省区总'
				},
				{
					number: 3,
					label: '经销区域新建修改关闭',
					description: '请将质量问题的货和筐返回到工厂',
					description2: '核验通过后资金到账，非质量问题不返货',
					title: '经销区域'
				},
				{
					number: 4,
					label: '经销区域关联行政区县',
					description: '请将货和筐返到工厂',
					description2: '审核通过后货款和筐押金到账',
					title: '关联行政区县'
				},
				{
					number: 5,
					label: '经销区域关联城市经理',
					description: '请将货和筐返到工厂',
					description2: '审核通过后货款和筐押金到账',
					title: '关联城市经理'
				},
			]);
			
		
			const store = useUserInfo();
			onLoad(async () => {
				
			});
			const handleButtonClick = (buttonNumber) => {
				uni.removeStorageSync("productInfoList")
				// uni.showToast({
				// 	title: `你点击了按钮 ${buttonNumber}`,
				// 	icon: 'none',
				// });
				console.log(buttonNumber);
				if(buttonNumber == '省区关联经销区'){
					uni.navigateTo({
						url: `/pages/workbench/organization/sqgljxq/index`,
					});
				}else if(buttonNumber == '返筐到工厂'){
					uni.navigateTo({
						url: `/pages/WorkbenchOther1/FHNavigation/FK/OrderDetails`,
					});
				}else if(buttonNumber == '质返到工厂'){
					uni.navigateTo({
						url: `/pages/WorkbenchOther1/FHNavigation/ZF/OrderDetails`,
					});
				}else if(buttonNumber == '返货到工厂'){
					uni.navigateTo({
						url: `/pages/WorkbenchOther1/FHNavigation/FH/OrderDetails`,
					});
				
				}
				
			};

			return {
				buttons,
				handleButtonClick,
			};
		},
	};
</script>

<style>
	.container {
		padding: 20rpx;
	}

	/* 按钮列表布局 */
	.button-list {
		display: flex;
		flex-direction: column;
		gap: 30rpx;
	}

	/* 单个按钮项 */
	.button-item {
		display: flex;
		align-items: center;
		padding: 20rpx;
		background: linear-gradient(45deg, #FEFAEF, #FEFAEF);
		border-radius: 16rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	}

	/* 图片容器 */
	.image-container {
		position: relative;
		width: 120rpx;
		height: 120rpx;
		background-color: #FFEAC3;
		border-radius: 16rpx;
		margin-right: 20rpx;
		flex-shrink: 0;
		overflow: hidden;
		clip-path: polygon(90% 0, 0 500%, 0 0%, 0 0%, 0% 0);
	}

	/* 斜线遮罩 */
	.diagonal-mask {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: linear-gradient(135deg, transparent 50%, #a2e6f0 50%);
		clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%, 0 50%, 50% 0);
	}

	/* 艺术字样式 */
	.art-text {
		font-size: 20px;
		font-weight: bold;
		background: linear-gradient(45deg, #000000, #000000);
		-webkit-background-clip: text;
		color: transparent;
		/* position: absolute;
		top: 50%;
		left: 50%; */
		transform: translate(-50%, -50%);
		z-index: 1;
		/* 确保文字在遮罩层上方 */
	}

	/* 新增：右下角透明遮罩 */
	.corner-mask {
		position: absolute;
		bottom: 0;
		right: 0;
		width: 0;
		height: 0;
		border-style: solid;
		border-width: 0 0 40rpx 40rpx;
		/* 调整数值控制透明三角区域大小 */
		border-color: transparent transparent rgba(0, 0, 0, 0) transparent;
		/* 完全透明 */
		transform: rotate(0deg);
	}

	/* 中间描述区域 */
	.item-description {
		flex: 1;
		min-width: 0;
		/* 防止文本溢出 */
	}

	.desc-text {
		font-size: 28rpx;
		color: #171717;
		line-height: 1.5;
	}

	.desc-text2 {
		font-size: 20rpx;
		color: #666;
		line-height: 1.5;
	}

	/* 右侧操作按钮 */
	.action-button {
		flex-shrink: 0;
		background-color: #FF112B;
		color: white;
		padding: 12rpx 24rpx;
		border-radius: 40rpx;
		font-size: 28rpx;
		margin-left: 20rpx;
	}
</style>