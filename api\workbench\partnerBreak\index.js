import request from "../../index";

// 获取合伙企业列表
export function getPartnerListApi(page, page_size,user) {
  return request({
    url: `/boss/get_qy_list_new?page=${page}&page_size=${page_size}&user=${user}`,
    method: "get",
  });
}

// 获取下属列表
export function getSubordinateListApi(id) {
  return request({
    url: `/boss/get_subordinate_list?id=${id}`,
    method: "get",
  });
}

// 企业解约
export function terminatePartnerApi(data) {
  return request({
    url: `/partnerBreak/terminate`,
    method: "post",
    data
  });
}

// 获取库长列表
export function getManageListApi(page, page_size, user) {
  return request({
    url: `/boss/get_shop_manage_list?page=${page}&page_size=${page_size}&user=${user}`,
    method: "get",
  });
}

// 获取库长下属
export function getManageSubordinateListApi(id) {
  return request({
    url: `/boss/get_manage_subordinate_list?id=${id}`,
    method: "get",
  });
}

// 库长解约
export function terminateShopManageApi(data) {
  return request({
    url: `/shopmanageBreak/terminate`,
    method: "post",
    data,
  });
}
