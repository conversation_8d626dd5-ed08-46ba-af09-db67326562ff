import { Value } from "sass";

// 车仓监控图例
export const legendData = [
  {
    img: "/static/icon/vehiclemonitoring/free_car.png",
    name: "空闲",
  },
  {
    img: "/static/icon/vehiclemonitoring/ready.png",
    name: "发车",
  },
  {
    img: "/static/icon/vehiclemonitoring/is_return.png",
    name: "已返厂",
  },
  {
    img: "/static/icon/vehiclemonitoring/is_scheduling.png",
    name: "已排车",
  },
  {
    img: "/static/icon/vehiclemonitoring/return.png",
    name: "返厂中",
  },
  {
    img: "/static/icon/vehiclemonitoring/unload.png",
    name: "卸货",
  },
  {
    img: "/static/icon/vehiclemonitoring/transportation.png",
    name: "运输中",
  },
  {
    img: "/static/icon/vehiclemonitoring/malfunction.png",
    name: "故障",
  },
  {
    img: "/static/icon/vehiclemonitoring/waiting.png",
    name: "待装车",
  },
];
export const CarStatus = {
  空闲: "/static/icon/vehiclemonitoring/free_car.png",
  发车: "/static/icon/vehiclemonitoring/ready.png",
  已返厂: "/static/icon/vehiclemonitoring/is_return.png",
  已排车: "/static/icon/vehiclemonitoring/is_scheduling.png",
  返厂中: "/static/icon/vehiclemonitoring/return.png",
  卸货: "/static/icon/vehiclemonitoring/unload.png",
  运输中: "/static/icon/vehiclemonitoring/transportation.png",
  故障: "/static/icon/vehiclemonitoring/malfunction.png",
  待装车: "/static/icon/vehiclemonitoring/waiting.png",
};

export const catTypeOption = [
  {
    value: "全部",
    label: "全部",
  },
  {
    value: "空闲",
    label: "空闲",
  },
  {
    value: "发车",
    label: "发车",
  },
  {
    value: "已返厂",
    label: "已返厂",
  },
  {
    value: "已排车",
    label: "已排车",
  },
  {
    value: "返厂中",
    label: "返厂中",
  },
  {
    value: "卸货",
    label: "卸货",
  },
  {
    value: "运输中",
    label: "运输中",
  },
  {
    value: "故障",
    label: "故障",
  },
  {
    value: "待装车",
    label: "待装车",
  },
];
