import request from "../../index";

// 获取车长签约详情
export function getTrainmasterDetailApi(id) {
  return request({
    url: "/trainmaster/getDetail",
    method: "get",
    data: {
      id,
    },
  });
}

// 提交签约审批记录
export function summitTrainmasterApi(data) {
  return request({
    url: "/trainmaster/submitApproval",
    method: "post",
    data,
  });
}
export function summitTrainmasterApikzjy(data) {
  return request({
    url: "/trainmaster/submitApproval_kzjy",
    method: "post",
    data,
  });
}


// 获取大区总管理区域车长签约列表
export function getTrainmasterListApi(jxq_list,id,type) {
  return request({
    url: "/trainmaster/getDataList",
    method: "get",
    data: {
      jxq_list,
      id,
      type
    },
  });
}
// 获取大区总管理区域车长签约列表
export function getTrainmasterListApi_dq(jxq_list,id,type) {
  return request({
    url: "/trainmaster/getDataList_dq",
    method: "get",
    data: {
      jxq_list,
      id,
      type
    },
  });
}
export function getTrainmasterListApiinfo(jxq_list,id,type,info) {
  return request({
    url: "/trainmaster/getDataListinfo",
    method: "get",
    data: {
      jxq_list,
      id,
      type,
      info
    },
  });
}
export function getTrainmasterListApiinfo_dq(jxq_list,id,type,info) {
  return request({
    url: "/trainmaster/getDataListinfo_dq",
    method: "get",
    data: {
      jxq_list,
      id,
      type,
      info
    },
  });
}
// 获取大区总管理区域车长签约列表
export function getTrainmasterListApikzjy(jxq_list,id,type) {
  return request({
    url: "/trainmaster/getDataList_kzjy",
    method: "get",
    data: {
      jxq_list,
      id,
      type
    },
  });
}
export function getTrainmasterListApiinfokzjy(jxq_list,id,type,info) {
  return request({
    url: "/trainmaster/getDataListinfo_kzjy",
    method: "get",
    data: {
      jxq_list,
      id,
      type,
      info
    },
  });
}
// 省区内人员清单
export function getTrainmasterListApiSQ(jxq_list,id,type) {
  return request({
    url: "/trainmaster/getDataList_person",
    method: "get",
    data: {
      jxq_list,
      id,
      type
    },
  });
}
export function getTrainmasterListApiinfoSQ(jxq_list,id,type,info) {
  return request({
    url: "/trainmaster/getDataListinfo_person",
    method: "get",
    data: {
      jxq_list,
      id,
      type,
      info
    },
  });
}


export function getTrainmasterListApiDQ(jxq_list,id,type) {
  return request({
    url: "/trainmaster/getDataList_person_dq",
    method: "get",
    data: {
      jxq_list,
      id,
      type
    },
  });
}
export function getTrainmasterListApiinfoDQ(jxq_list,id,type,info) {
  return request({
    url: "/trainmaster/getDataListinfo_person_dq",
    method: "get",
    data: {
      jxq_list,
      id,
      type,
      info
    },
  });
}

export function getTrainmasterListApiUser(id,user) {
  return request({
    url: "/trainmaster/getDataList_personList",
    method: "get",
    data: {

      id,
      user
    },
  });
}
// 获取入职库房
export function get_rzkf() {
  return request({
    url: "/boss/get_rzkf",
    method: "get",

  });
}

// 获取该大区下的入职库房
export function get_rzkfDq(id) {
  return request({
    url: `/boss/get_rzkf_dq?id=${id}`,
    method: "get",
  });
}

export function get_all_hhr() {
  return request({
    url: "/boss/get_all_hhr",
    method: "get",

  });
}
export function get_all_fhjyz() {
  return request({
    url: "/boss/get_all_fhjyz",
    method: "get",

  });
}


export function get_jyqy(id) {
  return request({
    url: "/boss/get_jyqy",
    method: "get",
    data: {
      id,
    },
  });
}
export function get_jyqy2(id) {
  return request({
    url: "/boss/get_jyqy2",
    method: "get",
    data: {
      id,
    },
  });
}

export function add_qy(data) {
  return request({
    url: "/boss/add_qy",
    method: "post",
    data: data,
  });
}

export function get_kzmc(id) {
  return request({
    url: "/boss/get_kzmc",
    method: "get",
    data: {
      id,
    },
  });
}