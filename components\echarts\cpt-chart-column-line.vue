<template>
  <div id="column-line" style="height: 400px; width: 100%"></div>
</template>

<script setup>
import * as echarts from "echarts";
import { nextTick } from "vue";

const props = defineProps(["data"]);
const xyData = JSON.parse(props.data.cptDataForm.dataText);
const attribute = props.data.attribute;

nextTick(() => {
  // 基于准备好的dom，初始化echarts实例
  var myChart = echarts.init(document.getElementById("column-line"));
  // 绘制图表
  myChart.setOption({
    title: {
      text: attribute.chartTitle,
      textStyle: {
        color: "#fff",
      },
      left: "center",
    },
    grid: {
      top: "20%",
      left: "3%",
      right: "3%",
      bottom: "%",
      containLabel: true,
    },
    textStyle: {
      color: "#fff",
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    legend: {
      textStyle: {
        color: "#fff",
      },
      top: "7%",
    },
    xAxis: [
      {
        type: "category",
        data: xyData.xData.split(","),
      },
    ],
    yAxis: [
      {
        type: "value",
        splitLine: {
          //网格线
          show: attribute.yGridLineShow,
        },
      },
      {
        type: "value",
        axisLabel: {
          formatter: "{value} %",
        },
        splitLine: {
          //网格线
          show: attribute.yGridLineShow,
        },
      },
    ],
    series: [
      {
        name: "月净销售额",
        type: "bar",
        stack: "Ad",
        emphasis: {
          focus: "series",
        },
        tooltip: {
          valueFormatter: function (value) {
            return value + " 元";
          },
        },
        itemStyle: {
          color: attribute.barColor,
        },
        data: xyData.yData1.split(","),
      },
      {
        name: "月净订货额",
        type: "bar",
        stack: "Ad",
        emphasis: {
          focus: "series",
        },
        tooltip: {
          valueFormatter: function (value) {
            return value + " 元";
          },
        },
        itemStyle: {
          color: attribute.barColor1,
        },
        data: xyData.yData2.split(","),
      },
      {
        name: "xxxx",
        type: "line",
        yAxisIndex: 1,
        tooltip: {
          valueFormatter: function (value) {
            return value + " %";
          },
        },
        itemStyle: {
          color: attribute.lineColor,
        },
        data: xyData.yData3.split(","),
      },
    ],
  });
});
</script>

<style></style>
