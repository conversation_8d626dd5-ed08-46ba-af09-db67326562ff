<template>
  <div>
    <div id="container"></div>
    <div
      class="selectTime"
      @click.stop="getTrack(imei, '2024-01-26 00:00:00', '2024-01-26 23:59:59')"
    >
      <a-button @click="back" style="margin-right: 20px">返回</a-button>
      <a-date-picker
        v-model:value="selectTime"
        :format="dateFormat"
        @change="changeTime"
        :allowClear="false"
      />
    </div>
  </div>
  <a-modal
    v-model:open="store.selectObj.modalShow"
    :footer="null"
    title="门店信息"
    width="80%"
    :bodyStyle="Style"
  >
    <CarStoreInfoModal
      v-if="store.selectObj.modalShow"
      :startTime="startTime"
      :endTime="endTime"
    />
  </a-modal>
</template>

<script setup>
import { nextTick, ref } from "vue";
import { get_cars_tracks, get_user_info, get_sings_stores } from "@/api/map";
import { onLoad, onReady } from "@dcloudio/uni-app";
import CarStoreInfoModal from "../components/modal/CarStoreInfoModal.vue";
import { useMapStore } from "../../../store/map";
import dayjs from "dayjs";
import { message } from "ant-design-vue";
let map;
let center;
const store = useMapStore();
const imei = ref();
const userId = ref();
const userInfo = ref();
const tracksList = ref([]);
const selectTime = ref(dayjs());
const dateFormat = "YYYY-MM-DD";
const startTime = ref(dayjs().format("YYYY-MM-DD 00:00:00"));
const endTime = ref(dayjs().format("YYYY-MM-DD 23:59:59"));

onLoad((optins) => {
  imei.value = optins.imei;
  userId.value = optins.userid;
});

function changeTime(e) {
  startTime.value = dayjs(e.$d).format("YYYY-MM-DD 00:00:00");
  endTime.value = dayjs(e.$d).format("YYYY-MM-DD 23:59:59");
  map.destroy();
  initMap();
}

async function initMap() {
  center = new TMap.LatLng(43.623133, 122.25545); // 设置中心点坐标
  // 初始化地图
  map = new TMap.Map("container", {
    center: center,
    zoom: 11,
  });
  tracksList.value = await getTrack(imei.value, startTime.value, endTime.value);
  await getUserInfo();
  await getAllSignStores();
  drawTracks();
}

async function getUserInfo() {
  const res = await get_user_info(userId.value);
  userInfo.value = res.data.result[0];
  //   console.log("userInfo", res);
}

// 标记门店
async function getAllSignStores() {
  const res = await get_sings_stores(
    userId.value,
    startTime.value,
    endTime.value
  );
  const arr = res.data.result;
  //过滤后的门店销货记录
  const uniqueArr = Array.from(
    arr.reduce((map, obj) => map.set(obj.Store_name, obj), new Map()).values()
  );
  //stopMessage停车数据
  const stopMessage = await carStopMessage(tracksList.value);
  // 过滤出速度为2个点以上的
  const stopFilter = stopMessage.filter((item) => item.length > 2);
  let points = [];

  uniqueArr.forEach((item) => {
    const shopTime = dayjs(item.Sales_bills_finish_Date);
    let stopContent = "";
    stopFilter.forEach((it) => {
      const starTime = dayjs(it[0].create_time);
      const stopTime = dayjs(it[it.length - 1].create_time);
      if (shopTime.isBefore(stopTime) && shopTime.isAfter(starTime)) {
        //计算停留时长
        const stopTime12 = dayjs(it[it.length - 1].create_time).diff(
          dayjs(it[0].create_time),
          "minutes"
        );
        stopContent = `${stopTime12}分钟`;
        // console.log(stopContent);
      }
    });
    points.push({
      position: new TMap.LatLng(item.Store_Latitude, item.Store_longitude),
      id: item.Store_name,
      styleId: "store",
    });
    new TMap.InfoWindow({
      map: map,
      enableCustom: true,
      position: new TMap.LatLng(item.Store_Latitude, item.Store_longitude),
      content: `<div style="padding: 10px; background-color: #fff;">${item.Store_name} | ${item.sales_money}元 | ${stopContent}</div>`, //设置信息框内容
      offset: {
        x: 0,
        y: -35,
      },
    });
  });

  let markerCar = new TMap.MultiMarker({
    id: "marksers-store",
    map: map,
    zIndex: 8,
    styles: {
      store: new TMap.MarkerStyle({
        width: 30, // 样式宽
        height: 30, // 样式高
        src: "../../static/icon/map/storeTrack.png",
      }),
    },
    geometries: points,
  });

  markerCar.on("click", (e) => {
    try {
      const filterStore = arr.filter(
        (item) => item.Store_name === e.geometry.id
      )[0];
      console.log(filterStore, "filterStore");

      //打开门店销弹窗
      store.selectObj.modalShow = true;

      sessionStorage.setItem("storeId", filterStore.id);
    } catch (err) {}
  });
}

// 处理停车数据
async function carStopMessage(arr) {
  let result = [];
  let tempArr = [];

  for (let i = 0; i < arr.length; i++) {
    if (arr[i].speed === 0) {
      tempArr.push(arr[i]);
    } else {
      if (tempArr.length > 0) {
        result.push(tempArr);
        tempArr = [];
      }
    }
  }

  if (tempArr.length > 0) {
    result.push(tempArr);
  }
  return result;
}

// 画出轨迹
async function drawTracks() {
  const paths = tracksList.value.map((item) => {
    return new TMap.LatLng(item.lat_tx, item.lng_tx);
  });
  if (paths.length === 0) {
    message.info("没有找到当日轨迹数据");
    return;
  }
  map.setCenter(paths[paths.length - 1]);
  map.setZoom(14);
  let polylineLayer = new TMap.MultiPolyline({
    id: "polyline-layer", //图层唯一标识
    map: map, //设置折线图层显示到哪个地图实例中
    //折线样式定义
    styles: {
      style_blue: new TMap.PolylineStyle({
        color: "#3777FF", //线填充色
        width: 6, //折线宽度
        borderWidth: 5, //边线宽度
        borderColor: "#FFF", //边线颜色
        lineCap: "butt", //线端头方式
        showArrow: true,
      }),
    },
    geometries: [
      {
        //第1条线
        id: "pl_1", //折线唯一标识，删除时使用
        styleId: "style_blue", //绑定样式名
        paths: paths,
      },
    ],
  });

  let marker = new TMap.MultiMarker({
    id: "markers",
    map: map,
    styles: {
      start: new TMap.MarkerStyle({
        width: 30, // 样式宽
        height: 30, // 样式高
        src: "../../static/icon/map/start.png",
      }),
      end: new TMap.MarkerStyle({
        width: 20, // 样式宽
        height: 30, // 样式高
        src: "../../static/icon/map/che3.png",
        rotate:
          tracksList.value[tracksList.value.length - 1].gps_direction_value,
      }),
    },
    geometries: [
      {
        id: "startMarker",
        position: new TMap.LatLng(
          tracksList.value[0].lat_tx,
          tracksList.value[0].lng_tx
        ),
        styleId: "start",
        rank: 0,
      },
      {
        id: "startMarker",
        position: new TMap.LatLng(
          tracksList.value[tracksList.value.length - 1].lat_tx,
          tracksList.value[tracksList.value.length - 1].lng_tx
        ),
        styleId: "end",
        rank: 1,
      },
    ],
  });
  let infoWindowStart = new TMap.InfoWindow({
    map: map,
    enableCustom: true,
    position: new TMap.LatLng(
      tracksList.value[0].lat_tx,
      tracksList.value[0].lng_tx
    ),
    content: `<div style="padding: 10px; background-color: #fff;">起点时间：${dayjs(
      tracksList.value[0].create_time
    ).format("HH:mm:ss")}</div>`,
    offset: {
      x: 0,
      y: -30,
    },
  });
  let infoWindowEnd = new TMap.InfoWindow({
    map: map,
    enableCustom: true,
    position: new TMap.LatLng(
      tracksList.value[tracksList.value.length - 1].lat_tx,
      tracksList.value[tracksList.value.length - 1].lng_tx
    ),
    content: `<div style="padding: 10px; background-color: #fff; display:flex; flex-direction:column; gap:10px;">
        <span>${userInfo.value.Master_data_person_name} | ${
      userInfo.value.Licence_number
    } | ${userInfo.value.Small_boss_home_sales_sell}元</span>
        <div style="text-align: center;">终点时间：${dayjs(
          tracksList.value[tracksList.value.length - 1].create_time
        ).format("HH:mm:ss")}</div>
        </div>`, //设置信息框内容
    offset: {
      x: 0,
      y: -35,
    },
  });
}

// 获取车辆轨迹
async function getTrack(imei, startTime, endTime) {
  const res = await get_cars_tracks(imei, startTime, endTime);
  return res.data.result;
}

const back = () => {
  // uni.reLaunch({ url: "/pages/Map/MapForm" });
  uni.navigateBack();
};
nextTick(() => {
  initMap();
});
</script>

<style lang="scss" scoped>
#container {
  height: 100vh;
  width: 100vw;
}
.selectTime {
  position: absolute;
  top: 30px;
  left: 30px;
  z-index: 9999999;
}
.infoWindows {
  padding: 10px;
  background-color: #fff;
}
</style>
