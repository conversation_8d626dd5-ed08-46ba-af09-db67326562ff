<template>
  <view class="content">
    <view class="toolbar">
      <view class="refresh-btn" @click="refreshData">
        <sync-outlined class="icon" />
        刷新
      </view>
    </view>

    <view class="table">
      <view class="table-header">
        <view class="th">合伙人姓名</view>
        <view class="th">签约状态</view>
        <view class="th">入职状态</view>
        <view class="th">操作</view>
      </view>
      <view v-if="loading" class="loading">加载中...</view>
      <view v-else-if="partnerList.length === 0" class="empty">暂无数据</view>
      <view v-else class="table-body">
        <view
          v-for="(item, index) in partnerList"
          :key="item.id"
          class="table-row"
        >
          <view class="td">{{ item.Master_data_person_name }}</view>
          <view class="td">{{ getSignStatusText(item.sign_status) }}</view>
          <view class="td">{{ item.Master_data_person_onboard_status }}</view>
          <view class="td">
            <view class="btn-view" @click="handleViewClick(item)">查看</view>
          </view>
        </view>
      </view>
    </view>

    <view class="pagination">
      <a-config-provider :locale="zhCN">
        <a-pagination
          :current="page"
          :pageSize="page_size"
          :total="total"
          :showSizeChanger="true"
          :pageSizeOptions="pageSizeOptions"
          @change="handlePageChange"
        />
      </a-config-provider>
    </view>
  </view>

  <!-- 加载弹窗 -->
  <view class="modal" v-if="showLoadingModal">
    <view class="modal-content loading-modal">
      <view class="loading-icon"></view>
      <view class="loading-text">加载中...</view>
    </view>
  </view>

  <!-- 下属列表弹窗 -->
  <view class="modal" v-if="showSubordinateModal">
    <view class="modal-content">
      <view class="modal-header">
        <view class="modal-title">下属列表</view>
        <view class="modal-close" @click="closeSubordinateModal">×</view>
      </view>
      <view class="modal-body">
        <!-- 警告提示 -->
        <a-alert
          v-if="hasActiveSubordinates"
          :message="activeSubordinatesWarning"
          type="warning"
          show-icon
          closable
          class="warning-alert"
        />

        <view class="table sub-table">
          <view class="table-header">
            <view class="th">下属名称</view>
            <view class="th">下属类型</view>
            <view class="th">签约状态</view>
          </view>
          <view v-if="subordinateLoading" class="loading">加载中...</view>
          <view v-else-if="subordinateList.length === 0" class="empty"
            >暂无数据</view
          >
          <view v-else class="table-body">
            <view
              v-for="(item, index) in subordinateList"
              :key="index"
              class="table-row"
            >
              <view class="td">{{ item.Master_data_person_name }}</view>
              <view
                class="td role-type-cell"
                :style="{ backgroundColor: getRoleTypeColor(item.Role_name) }"
              >
                {{ item.Role_name }}
              </view>
              <view
                class="td sign-status-cell"
                :style="{ backgroundColor: getSignStatusColor(item.Master_data_person_onboard_status) }"
              >
                {{ item.Master_data_person_onboard_status }}
              </view>
            </view>
          </view>
        </view>
      </view>
      <view class="modal-footer">
        <view class="btn btn-cancel" @click="closeSubordinateModal">取消</view>

        <!-- 当按钮可用时显示带气泡确认的解约按钮 -->
        <a-popover
          v-if="canTerminate"
          v-model:open="popoverVisible"
          title="提示"
          trigger="click"
          placement="top"
        >
          <template #content>
            <view class="popover-content">
              <view class="popover-message">是否确认解约？</view>
              <view class="popover-buttons">
                <view class="popover-btn cancel" @click="popoverVisible = false">取消</view>
                <view class="popover-btn confirm" @click="confirmTerminate">确认</view>
              </view>
            </view>
          </template>
          <view class="btn btn-terminate">解约</view>
        </a-popover>

        <!-- 当按钮禁用时显示带提示的禁用按钮 -->
        <a-tooltip
          v-else
          :title="terminateButtonTitle"
          placement="top"
        >
          <view class="btn btn-terminate disabled">
            解约
          </view>
        </a-tooltip>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from "vue";
import {
  getPartnerListApi,
  getSubordinateListApi,
  terminatePartnerApi,
} from "@/api/workbench/partnerBreak/index";
import { message, Pagination as APagination, Alert as AAlert, Tooltip as ATooltip } from "ant-design-vue";
import { SyncOutlined } from "@ant-design/icons-vue";
import zhCN from "ant-design-vue/es/locale/zh_CN";
import { useUserInfo } from "@/store/user/userInfo";

// 用户信息store
const userStore = useUserInfo();

// 数据定义
const partnerList = ref([]);
const loading = ref(true);
const page = ref(1);
const page_size = ref(10);
const total = ref(0);
const pageSizeOptions = ref(["10", "20", "50", "100"]);


// 弹窗相关
const showLoadingModal = ref(false);
const showSubordinateModal = ref(false);
const subordinateList = ref([]);
const subordinateLoading = ref(false);
const currentPartner = ref(null);
const canTerminate = ref(false);
const terminateButtonTitle = ref("");
const popoverVisible = ref(false);

// 获取合伙企业列表
const fetchPartnerList = async () => {
  loading.value = true;

  // 检查用户ID是否存在
  if (!userStore.userInfo || !userStore.userInfo.id) {
    message.error("用户信息不完整，无法获取合伙企业列表");
    console.error("用户ID缺失:", userStore.userInfo);
    loading.value = false;
    return;
  }

  try {
    const res = await getPartnerListApi(page.value, page_size.value, userStore.userInfo.id);
    if (res && res.data) {
      partnerList.value = res.data.result;
      total.value = res.data.result[0].total || 0;
    }
  } catch (error) {
    console.error("获取合伙企业列表失败:", error);
    message.error("获取合伙企业列表失败");
  } finally {
    loading.value = false;
  }
};

// 签约状态映射
const signStatusMap = {
  '0': '签约中',
  '1': '正式',
  '2': '解约中',
  '3': '已解约'
};

// 获取签约状态文本
const getSignStatusText = (status) => {
  return signStatusMap[status] || "未知状态";
};

// 获取下属类型背景颜色
const getRoleTypeColor = (roleType) => {
  const colorMap = {
    '车长': '#e6f7ff', // 浅蓝色
    '库长': '#f6ffed', // 浅绿色
    '库管': '#fff7e6', // 浅橙色
    '财务库管': '#f9f0ff' // 浅紫色
  };
  return colorMap[roleType] || '#f5f5f5';
};

// 获取签约状态背景颜色
const getSignStatusColor = (status) => {
  const colorMap = {
    '正式': '#f6ffed', // 绿色系
    '停用': '#f5f5f5', // 灰色系
    '离职': '#fff2f0', // 红色系
    '停职留薪': '#fffbe6' // 黄色系
  };
  return colorMap[status] || '#f5f5f5';
};

// 检查是否存在正式签约的下属
const hasActiveSubordinates = ref(false);
const activeSubordinatesWarning = ref('');

// 点击查看按钮
const handleViewClick = (item) => {
  currentPartner.value = item;

  // 检查当前合伙人的签约状态，预设解约按钮状态
  checkPartnerTerminateStatus(item);

  showLoadingModal.value = true;

  // 模拟请求下属列表
  setTimeout(() => {
    showLoadingModal.value = false;
    showSubordinateModal.value = true;
    getSubordinateList(item.id);
  }, 1000);
};

// 检查合伙人解约状态
const checkPartnerTerminateStatus = (partner) => {
  // 如果签约状态为"解约中"（状态值"2"）或"已解约"（状态值"3"）
  if (partner.sign_status === "2" || partner.sign_status === "3") {
    canTerminate.value = false;
    terminateButtonTitle.value = "该合伙人已处于解约状态，无法重复操作";
  } else if (partner.sign_status === "0" || partner.sign_status === "1") {
    // 签约状态为"签约中"（状态值"0"）或"正式"（状态值"1"）时允许发起解约
    // 但还需要检查下属状态，这个在getSubordinateList后的checkTerminateStatus中处理
    canTerminate.value = true;
    terminateButtonTitle.value = "";
  } else {
    canTerminate.value = false;
    terminateButtonTitle.value = "未知签约状态，无法操作";
  }
};

// 获取下属列表
const getSubordinateList = async (partnerId) => {
  subordinateLoading.value = true;
  subordinateList.value = [];

  try {
    const res = await getSubordinateListApi(partnerId);
    if (res && res.data) {
      subordinateList.value = res.data.result;
      checkTerminateStatus();
    }
  } catch (error) {
    console.error("获取下属列表失败:", error);
  } finally {
    subordinateLoading.value = false;
  }
};

// 检查是否可以解约
const checkTerminateStatus = () => {
  // 首先检查合伙人本身的签约状态
  if (!currentPartner.value) return;

  const partnerSignStatus = currentPartner.value.sign_status;

  // 如果合伙人已处于解约状态，直接禁用
  if (partnerSignStatus === "2" || partnerSignStatus === "3") {
    canTerminate.value = false;
    terminateButtonTitle.value = "该合伙人已处于解约状态，无法重复操作";
    hasActiveSubordinates.value = false;
    activeSubordinatesWarning.value = '';
    return;
  }

  // 如果合伙人状态允许解约，再检查下属状态
  if (partnerSignStatus === "0" || partnerSignStatus === "1") {
    // 检查是否存在正式签约的下属
    const activeSubordinates = subordinateList.value.filter(
      (item) => item.Master_data_person_onboard_status === "正式"
    );

    hasActiveSubordinates.value = activeSubordinates.length > 0;

    if (hasActiveSubordinates.value) {
      activeSubordinatesWarning.value = `存在未解约的下属，请先处理相关签约状态（共${activeSubordinates.length}人）`;
      canTerminate.value = false; // 存在正式签约下属时禁用解约按钮
      terminateButtonTitle.value = "存在正式签约的下属，无法解约";
    } else {
      activeSubordinatesWarning.value = '';
      canTerminate.value = true; // 没有正式签约下属时允许解约
      terminateButtonTitle.value = "";
    }
  } else {
    // 未知状态
    canTerminate.value = false;
    terminateButtonTitle.value = "未知签约状态，无法操作";
    hasActiveSubordinates.value = false;
    activeSubordinatesWarning.value = '';
  }
};

// 关闭下属列表弹窗
const closeSubordinateModal = () => {
  showSubordinateModal.value = false;
  subordinateList.value = [];
  currentPartner.value = null;
};

// 处理解约操作
const handleTerminate = async () => {
  if (!canTerminate.value) return;

  // 检查用户信息是否存在
  if (!userStore.userInfo || !userStore.userInfo.id || !userStore.userInfo.userName) {
    message.error("用户信息不完整，无法执行解约操作");
    console.error("用户信息缺失:", userStore.userInfo);
    return;
  }

  // 构造完整的参数对象
  const terminateParams = {
    id: currentPartner.value.id,                    // 被解约的合伙企业ID
    promoter: userStore.userInfo.id,                // 发起解约操作的用户ID
    promoter_name: userStore.userInfo.userName      // 发起解约操作的用户姓名
  };

  try {
    await terminatePartnerApi(terminateParams);
    message.success("解约成功");
    closeSubordinateModal();
    fetchPartnerList(); // 刷新列表
  } catch (error) {
    console.error("解约失败:", error);
    message.error("解约失败");
  }
};

// 确认解约
const confirmTerminate = () => {
  popoverVisible.value = false;
  handleTerminate();
};

// 处理页面变化
const handlePageChange = (newPage, newPageSize) => {
  page.value = newPage || 1;
  page_size.value = newPageSize;
  fetchPartnerList();
};

// 刷新数据
const refreshData = () => {
  page.value = 1;
  fetchPartnerList();
  message.success("数据已刷新");
};

// 页面加载时获取数据
onMounted(() => {
  fetchPartnerList();
});
</script>

<style scoped>
.header {
  padding: 20px;
  background-color: #fff;
  margin-bottom: 20px;
}

.header-title {
  font-size: 18px;
  font-weight: bold;
}

.content {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
}

.toolbar {
  margin-bottom: 16px;
  display: flex;
  justify-content: flex-end;
}

.refresh-btn {
  display: inline-flex;
  align-items: center;
  padding: 6px 15px;
  background-color: #f0f2f5;
  color: #606266;
  border-radius: 4px;
  cursor: pointer;
}

.refresh-btn:hover {
  background-color: #e6e8eb;
}

.refresh-btn .icon {
  margin-right: 5px;
}

.table {
  width: 100%;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.table-header {
  display: flex;
  background-color: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
}

.th {
  flex: 1;
  padding: 12px;
  text-align: center;
  font-weight: bold;
  color: #606266;
}

.table-body {
  max-height: 500px;
  overflow-y: auto;
}

.table-row {
  display: flex;
  border-bottom: 1px solid #ebeef5;
}

.table-row:last-child {
  border-bottom: none;
}

.td {
  flex: 1;
  padding: 12px;
  text-align: center;
  color: #606266;
}

.btn-view {
  display: inline-block;
  padding: 5px 15px;
  background-color: #409eff;
  color: #fff;
  border-radius: 4px;
  cursor: pointer;
}

.btn-view:hover {
  background-color: #66b1ff;
}

.loading,
.empty {
  padding: 20px;
  text-align: center;
  color: #909399;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

/* 弹窗样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100;
}

.modal-content {
  background-color: #fff;
  border-radius: 4px;
  width: 80%;
  max-width: 800px;
  max-height: 80vh;
  overflow: auto;
}

.loading-modal {
  width: 200px;
  height: 150px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.loading-icon {
  width: 50px;
  height: 50px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #409eff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  margin-top: 10px;
  color: #606266;
}

.modal-header {
  padding: 20px;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.modal-close {
  font-size: 20px;
  color: #909399;
  cursor: pointer;
}

.modal-body {
  max-height: 500px;
  overflow-y: auto;
  padding: 20px;
}

.modal-footer {
  padding: 10px 20px 20px;
  text-align: right;
  border-top: 1px solid #ebeef5;
}

.btn {
  display: inline-block;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
  margin-left: 10px;
}

.btn-cancel {
  background-color: #f0f2f5;
  color: #606266;
}

.btn-terminate {
  background-color: #f56c6c;
  color: #fff;
}

.btn-terminate.disabled {
  background-color: #d9d9d9;
  color: #999;
  cursor: not-allowed;
  opacity: 0.6;
}

.btn-terminate.disabled:hover {
  background-color: #d9d9d9;
  color: #999;
}

.sub-table {
  margin-bottom: 20px;
}

/* 警告提示样式 */
.warning-alert {
  margin-bottom: 16px;
}

/* 下属类型单元格样式 */
.role-type-cell {
  border-radius: 4px;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

/* 签约状态单元格样式 */
.sign-status-cell {
  border-radius: 4px;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

/* 表格行悬停效果 */
.table-row:hover .role-type-cell,
.table-row:hover .sign-status-cell {
  opacity: 0.8;
}

/* 气泡确认框样式 */
.popover-content {
  padding: 5px;
}

.popover-message {
  margin-bottom: 10px;
  text-align: center;
}

.popover-buttons {
  display: flex;
  justify-content: flex-end;
}

.popover-btn {
  padding: 5px 10px;
  border-radius: 2px;
  margin-left: 8px;
  cursor: pointer;
}

.popover-btn.cancel {
  background-color: #f0f2f5;
  color: #606266;
}

.popover-btn.confirm {
  background-color: #f56c6c;
  color: #fff;
}
</style>
