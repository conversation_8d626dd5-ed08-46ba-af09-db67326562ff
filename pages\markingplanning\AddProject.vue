<template>
  <div class="add-box">
    <a-form
      :model="formState"
      name="basic"
      :label-col="{ span: 8 }"
      :wrapper-col="{ span: 16 }"
      @finish="onFinish"
    >
      <template v-if="userStore.userInfo.Role_grade === '决策层'">
        <a-form-item
          label="经销区域"
          name="selectArea"
          :rules="[{ required: true, message: '请选择经销区域！' }]"
        >
          <a-select
            v-model:value="formState.selectArea"
            :options="areaOptions"
            showSearch
            @change="selectAreaChange"
          />
        </a-form-item>
      </template>
      <template v-if="userStore.userInfo.Role_grade === '决策层'">
        <a-form-item
          label="库长"
          name="principal"
          :rules="[{ required: true, message: '请选择库长!' }]"
        >
          <a-select
            v-model:value="formState.principal"
            showSearch
            :options="userOptions"
            @change="selectUserChange"
          />
        </a-form-item>
      </template>
      <a-form-item
        label="执行人"
        name="executor"
        :rules="[{ required: true, message: '请选择执行人!' }]"
      >
        <a-select
          v-model:value="formState.executor"
          showSearch
          :options="executorOptions"
        />
      </a-form-item>

      <a-form-item
        label="门店负责人"
        name="storePrincipal"
        :rules="[{ required: true, message: '请选择门店负责人!' }]"
      >
        <a-select
          v-model:value="formState.storePrincipal"
          showSearch
          :options="storePrincipalOptions"
        />
      </a-form-item>

      <a-form-item
        label="时间范围"
        name="timeHorizon"
        :rules="[{ required: true, message: '请选择时间范围!' }]"
      >
        <a-range-picker
          v-model:value="formState.timeHorizon"
          @change="getAllEditArea"
        />
      </a-form-item>

      <div class="text-hint">
        <span>绘制：鼠标左键点击及移动即可绘制图形 </span>
        <span>结束绘制：鼠标左键双击即可结束绘制折线、多边形会自动闭合</span>
        <span>中断：绘制过程中按下esc键可中断该过程</span>
      </div>

      <a-form-item :wrapper-col="{ offset: 0, span: 24 }">
        <a-button @click="reset('plan')"> 规划重置 </a-button>
        <a-button style="margin-left: 10px" @click="reset('all')">
          全部重置
        </a-button>
        <a-button
          style="margin-left: 10px"
          type="primary"
          html-type="submit"
          :loading="buttonLoading"
        >
          提交规划
        </a-button>
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup>
import { onMounted, reactive, ref, h } from "vue";
import { message, Modal } from "ant-design-vue";
import { useUserInfo } from "/store/user/userInfo";
import { transtionCopy } from "/utils/map";
import {
  getNationalAreas,
  getStoreNotPerson,
  createPlanRecord,
  getIndatePlanMessage,
  getThisAreaPersons,
  getUserCrmId,
} from "/api/workingplanning";
import { getAllAreas_sell } from "/api/map/index";

import dayjs from "dayjs";
import { useMarking } from "/store/markingplanning/index";
import { getAreaStreets } from "../../api/workingplanning";

onMounted(async () => {
  if (userStore.userInfo.Role_grade === "决策层") {
    await getAreaOptions();
  }
  if (userStore.userInfo.Role_grade === "经销商") {
    await jxsDisposeArea();
  }
});
const props = defineProps(["messageMap"]);
const emits = defineEmits([
  "drawArea",
  "drawStreet",
  "drawStore",
  "resetPlan",
  "drawHistoryArea",
]);

const userStore = useUserInfo();
const useMarkingStore = useMarking();
const formState = reactive({
  selectArea: null,
  timeHorizon: null,
  principal: null,
  executor: null,
  storePrincipal: null,
});
const buttonLoading = ref(false);

const areaOptions = ref([]);
const userOptions = ref([]);
const executorOptions = ref([]);

const storePrincipalOptions = ref([]);
async function getAreaOptions() {
  // 决策层先获取所有的经销区列表
  const { data } = await getNationalAreas();
  const options = data.result.map(item => {
    return {
      label: item.DistributionOrganizationName,
      value: item.DistributionOrganizationName,
      id: item.Distribution_territory,
      code: item.id,
    };
  });
  areaOptions.value = Array.from(new Set(options.map(item => item.id))).map(
    id => options.find(item => item.id === id)
  );
}

async function selectAreaChange(select, option) {
  // 切换区域先清除地图
  emits("resetPlan", "all");

  const { data } = await getAllAreas_sell(option.id);
  // 处理区域信息
  await disposeAreaMessage(data);
  // 处理门店信息
  await disposeStoreMessage(data);
  // 处理负责人信息
  await disposeUsers(option);

  // 处理街道信息
  await disposeStreetMessage(option);

  await getAllEditArea();
}

// 经销商角色逻辑
async function jxsDisposeArea() {
  const { data } = await getAllAreas_sell(userStore.userInfo.fz_area_id);

  // 处理区域信息
  await disposeAreaMessage(data);

  // 处理执行人选项
  const { data: userData } = await getThisAreaPersons(
    userStore.userInfo.fz_area_id
  );
  executorOptions.value = userData.result
    .filter(
      item =>
        item.Role_grade === "分销商" && item.Master_data_person_name_crm_really
    )
    .map(item => {
      return {
        label: item.Master_data_person_name_crm_really,
        value: item.Master_data_person_name_crm_really,
        id: item.id,
      };
    });

  storePrincipalOptions.value = executorOptions.value;

  // 处理门店信息
  await disposeStoreMessage(data);

  // 处理街道信息
  if (data.result.length > 0) {
    const option = {
      code: data.result[0].id,
    };
    await disposeStreetMessage(option);
  }
}

function selectUserChange(value) {
  formState.executor = value;
}

// 处理区域信息
async function disposeAreaMessage(data) {
  let geometries = [];
  let center;
  data.result.forEach(item => {
    center = new TMap.LatLng(item.Resident_latitude, item.Station_longitude);
    JSON.parse(item.Region_profile).forEach(_item => {
      const outlinePath = transtionCopy(_item).map(
        _item_ => new TMap.LatLng(_item_.latitude, _item_.longitude)
      );
      const path = {
        styleId: "bgColor",
        paths: outlinePath,
      };
      geometries.push(path);
    });
  });
  emits("drawArea", geometries, center);
}

// 处理门店信息
async function disposeStoreMessage(data) {
  let storeGeometries = [];
  emits("drawStore", storeGeometries, "clear"); //先传空清空上一次的
  data.result.forEach(async item => {
    const address = item.Provincial_name + item.City_name + item.District_name;
    const { data: storeData } = await getStoreNotPerson(address);
    useMarkingStore.storeList = storeData.result;
    storeData.result
      .filter(
        _item =>
          _item.Store_Latitude !== null &&
          _item.Store_Latitude !== "" &&
          _item.Store_monthly_sales === "0"
      )
      .forEach(_item => {
        storeGeometries.push({
          position: new TMap.LatLng(
            _item.Store_Latitude,
            _item.Store_longitude
          ),
          id: _item.id,
          styleId: judgeColor(_item),
        });
      });
    emits("drawStore", storeGeometries, "add");
  });
}

// 处理负责人信息
async function disposeUsers(option) {
  formState.principal = null;
  const { data: userData } = await getThisAreaPersons(option.id);
  userOptions.value = userData.result
    .filter(
      item =>
        item.Role_grade === "经销商" && item.Master_data_person_name_crm_really
    )
    .map(item => {
      return {
        label: item.Master_data_person_name_crm_really,
        value: item.Master_data_person_name_crm_really,
        id: item.id,
      };
    });

  executorOptions.value = userData.result.map(item => {
    return {
      label: item.Master_data_person_name_crm_really,
      value: item.Master_data_person_name_crm_really,
      id: item.id,
    };
  });

  storePrincipalOptions.value = userData.result
    .filter(
      item =>
        item.Role_grade === "分销商" && item.Master_data_person_name_crm_really
    )
    .map(item => {
      return {
        label: item.Master_data_person_name_crm_really,
        value: item.Master_data_person_name_crm_really,
        id: item.id,
      };
    });
}

// 处理街道信息
async function disposeStreetMessage(option) {
  // 获取该经销区的街道数据
  const { data } = await getAreaStreets(option.code);
  let geometries = [];
  data.result.forEach(item => {
    geometries.push({
      id: "label", // 点图形数据的标志信息
      styleId: "label", // 样式id
      position: new TMap.LatLng(item.latitude, item.longitude), // 标注点位置
      content: `${item.place_name}${
        item.population ? "|" + item.population : ""
      }`, // 标注文本
    });
  });

  emits("drawStreet", geometries);
}

// 判断门店标记点颜色
function judgeColor(store) {
  if (!store.Master_data_person_name_crm_really) {
    return "marker5"; // 未开门店蓝色
  }
  if (store.Master_data_person_name_crm_really) {
    return "marker2"; // 已开门店浅绿色
  }

  // 超期未谈深蓝色
  if (store.endDate) {
    const currentDate = dayjs();
    const endDate = dayjs(store.endDate);
    if (currentDate > endDate) {
      return "marker6";
    }
  }

  if (!store.is_stork) {
    return "marker4"; // 无意向红色
  }

  if (store.is_stork && store.is_drop_cabinet) {
    return "marker3"; // 有意向有柜体深绿色
  }
  if (store.Last_visittime) {
    return "marker1"; // 已拜访黄色
  }
}

async function getAllEditArea() {
  if (!formState.timeHorizon) return;
  const startDate = dayjs(formState.timeHorizon[0]).format("YYYY-MM-DD");
  const endDate = dayjs(formState.timeHorizon[1]).format("YYYY-MM-DD");

  let areaId;
  if (userStore.userInfo.Role_grade === "经销商") {
    areaId = userStore.userInfo.fz_area_id;
  }

  if (userStore.userInfo.Role_grade === "决策层") {
    areaId = areaOptions.value.find(
      item => item.value === formState.selectArea
    )?.id;
  }

  const dataParams = {
    startDate,
    endDate,
    areaId,
  };
  const { data } = await getIndatePlanMessage(dataParams);
  let geometries = [];
  let styles = {};
  let centers = [];
  data.result.forEach(item => {
    const paths = JSON.parse(item.planningArea).map(
      _item => new TMap.LatLng(_item[0], _item[1])
    );
    styles[item.planCode] = new TMap.PolygonStyle({
      color: getRandomColorWithOpacity(),
      showBorder: true,
      borderColor: "rgba(0,0,0,0.8)",
      borderWidth: 1,
    });
    geometries.push({
      styleId: item.planCode,
      paths: paths,
    });
    centers.push({
      id: "label", //点图形数据的标志信息
      styleId: "label", //样式id
      position: TMap.geometry.computeCentroid(paths), //标注点位置
      content: item.Master_data_person_name, //标注文本
      properties: {
        //标注点的属性数据
        title: "label",
      },
    });
  });
  emits("drawHistoryArea", geometries, styles, centers);
  function getRandomColorWithOpacity(opacity = 0.3) {
    const r = Math.floor(Math.random() * 256);
    const g = Math.floor(Math.random() * 256);
    const b = Math.floor(Math.random() * 256);
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  }
}

async function reset(type) {
  if (type === "all") {
    formState.selectArea = null;
    formState.timeHorizon = null;
    formState.executor = null;
    if (userStore.userInfo.Role_grade === "决策层") {
      formState.principal = null;
    }
    emits("resetPlan", "all");
  }
  if (type === "plan") {
    if (formState.selectArea) emits("resetPlan", "plan");
  }
}

async function onFinish(event) {
  console.log(
    "props.messageMap.userStoreList",
    props.messageMap.clickStoreList
  );

  if (
    props.messageMap.editAreaPoints.length === 0 &&
    props.messageMap.clickStoreList.length === 0
  ) {
    return message.error("请先绘制规划区域！");
  }

  const points = props.messageMap.editAreaPoints.map(item => {
    return [item.lat, item.lng];
  });

  let planAreaPoints;
  planAreaPoints =
    props.messageMap.editAreaPoints.length > 0 ? JSON.stringify(points) : null;

  let stores;
  if (props.messageMap.clickStoreList.length === 0) {
    stores = props.messageMap.inEditAreaStore.map(item => {
      return {
        storeid: item,
      };
    });
  }
  if (props.messageMap.clickStoreList.length > 0) {
    stores = props.messageMap.clickStoreList.map(item => {
      return {
        storeid: item.id,
      };
    });
  }

  let dataParams = {
    code: "SCGH" + dayjs().format("YYYYMMDDHHmmss"), //编码
    principal: "", // 负责人
    executor: getPrincipal(event.executor), // 执行人
    storePrincipal: getStorePrincipal(event.storePrincipal), //门店负责人
    startDate: dayjs(event.timeHorizon[0]).format("YYYY-MM-DD"), // 开始日期
    endDate: dayjs(event.timeHorizon[1]).format("YYYY-MM-DD"), // 结束日期
    planAreaPoints: planAreaPoints, // 规划区域
    createByUserId: userStore.userInfo.id, // 创建人ID
    storeid_list: stores, // 门店id列表
    areaId: getAreaId(), // 所在经销区ID
  };
  Modal.confirm({
    title: "确认规划片区?",
    content: h("div", {}, [
      h("p", `负责人: ${event.principal}`),
      h(
        "p",
        `规划日期段: ${dayjs(event.timeHorizon[0]).format(
          "YYYY-MM-DD"
        )}-${dayjs(event.timeHorizon[1]).format("YYYY-MM-DD")}`
      ),
      h(
        "p",
        `涉及门店数量: ${
          props.messageMap.clickStoreList.length > 0
            ? props.messageMap.clickStoreList.length
            : props.messageMap.inEditAreaStore.length
        }`
      ),
    ]),
    onOk: async () => {
      buttonLoading.value = true;
      if (userStore.userInfo.Role_grade === "决策层") {
        dataParams.principal = getPrincipal(event.principal);
      }
      if (userStore.userInfo.Role_grade === "经销商") {
        // 获取用户在crm系统的id
        const { data } = await getUserCrmId(
          userStore.userInfo.fz_area_id,
          userStore.userInfo.id
        );

        dataParams.principal = data.result[0]?.id;
      }

      await createPlanRecord(dataParams);
      setTimeout(() => {
        formState.selectArea = null;
        formState.timeHorizon = null;
        formState.storePrincipal = null;
        if (userStore.userInfo.Role_grade === "决策层") {
          formState.principal = null;
        }
        formState.executor = null;
        emits("resetPlan", "all");
        buttonLoading.value = false;
        message.success("规划成功");
      }, 500);
    },
  });
}

// 获取选择的经销区ID
function getAreaId() {
  if (userStore.userInfo.Role_grade === "经销商") {
    return userStore.userInfo.jxqId[0];
  }
  if (userStore.userInfo.Role_grade === "决策层") {
    const areaMes = areaOptions.value.find(
      item => item.value === formState.selectArea
    );
    return areaMes.id;
  }
}

function getPrincipal(value) {
  const principal = executorOptions.value.find(item => item.value === value);
  return principal?.id;
}

function getStorePrincipal(value) {
  const principal = storePrincipalOptions.value.find(
    item => item.value === value
  );
  return principal?.id;
}
</script>

<style lang="scss" scoped>
.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}
.add-box {
  display: flex;
  flex-direction: column;
  width: 300px;
  .text-hint {
    display: flex;
    flex-direction: column;
    gap: 5px;
    color: green;
    margin-bottom: 5px;
  }
}
</style>
