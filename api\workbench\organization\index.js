import request from "../../index";

// 获取渠道建设汇总数据-库长
export function get_province(id) {
  return request({
    url: `/boss/get_province?id=${id}`,
    method: "get",
   
  });
}
export function get_Distribution(sq,id) {
	return request({
		method:'get',
		url:`/boss/get_Distribution?sq=${sq}&id=${id}`,
	});
}

// 省区关联经销区
export function Province_Distribution(data) {
  return request({
    url: "/boss/Province_Distribution",
    method: "post",
    data,
  });
}

export function get_user_sqz(id) {
	return request({
		method:'get',
		url:`/boss/get_user_sqz?sq=${id}`,
	});
}

// 省区关联省区总
export function Province_Person(data) {
  return request({
    url: "/boss/Province_Person",
    method: "post",
    data,
  });
}

export function delete_sq(id) {
	return request({
		method:'get',
		url:`/boss/delete_sq?sq=${id}`,
	});
}

export function deleted_jxq_xzqx(id) {
	return request({
		method:'get',
		url:`/boss/deleted_jxq_xzqx?id=${id}`,
	});
}

export function change_jxq_name(data) {
	return request({
		method:'post',
		url: `/change/outlet_name`,
		data,
	});
}

export function post_JY(id,user) {
	return request({
		method:'get',
		url:`/boss/post_JY?id=${id}&user=${user}`,
	});
}


export function get_Distribution_list(id) {
	return request({
		method:'get',
		url:`/boss/get_Distribution_list?id=${id}`,
	});
}

export function get_csjl(id) {
	return request({
		method:'get',
		url:`/boss/get_csjl?id=${id}`,
	});
}

export function get_xzqx() {
	return request({
		method:'get',
		url:`/boss/get_xzqx`,
	});
}



export function get_xzqx_user(user) {
	return request({
		method:'get',
		url:`/boss/get_xzqx_user?user=${user}`,
	});
}
export function add_jxq(data) {
	return request({
		url: "/boss/add_jxq",
		method: "post",
		data,
	});
}
export function jxq_xzqy(data) {
	return request({
		url: "/boss/jxq_xzqy",
		method: "post",
		data,
	});
}

export function jxq_csjl(data) {
	return request({
		url: "/boss/jxq_csjl",
		method: "post",
		data,
	});
}