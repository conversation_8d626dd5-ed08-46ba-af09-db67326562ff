{
  "easycom": {
    "custom": {
      "^u--(.*)": "uview-plus/components/u-$1/u-$1.vue",
      "^up-(.*)": "uview-plus/components/u-$1/u-$1.vue",
      "^u-([^-].*)": "uview-plus/components/u-$1/u-$1.vue"
    }
  },
  "pages": [
    //首页
    {
      "path": "pages/Index/index",
      "style": {
        "navigationBarTitleText": "",
        "enablePullDownRefresh": false,
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/popularfeelings/index",
      "style": {
        "navigationBarTitleText": "",
        "enablePullDownRefresh": false,
        "navigationStyle": "custom"
      }
    },
    //门店动销
    {
      "path": "pages/shopxh/index",
      "style": {
        "navigationBarTitleText": "门店动销"
      }
    },
    // 报表
    {
      "path": "pages/FormOther/index",
      "style": {
        "navigationBarTitleText": "报表"
      }
    },
    {
      "path": "pages/vehiclemonitoring/index",
      "style": {
        "navigationBarTitleText": "大车地图",
        "enablePullDownRefresh": false,
        "navigationStyle": "custom"
      }
    },
    // 地图（tab栏）
    {
      "path": "pages/Map/index",
      "style": {
        "navigationBarTitleText": "",
        "enablePullDownRefresh": false,
        "navigationStyle": "custom"
      }
    },
    // 登录
    {
      "path": "pages/Login/Login",
      "style": {
        "navigationBarTitleText": "",
        "enablePullDownRefresh": false,
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/Login/UserList",
      "style": {
        "navigationBarTitleText": "",
        "enablePullDownRefresh": false,
        "navigationStyle": "custom"
      }
    },
    // 车辆路线详情
    {
      "path": "pages/Map/components/MapTrack",
      "style": {
        "navigationBarTitleText": "",
        "enablePullDownRefresh": false,
        "navigationStyle": "custom"
      }
    },
    // 详情
    {
      "path": "pages/FormOther/detail2",
      "style": {
        "navigationBarTitleText": "详情"
      }
    },
    // 扫码登录页
    {
      "path": "pages/Login/WebLogin",
      "style": {
        "navigationBarTitleText": "",
        "enablePullDownRefresh": false,
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/shopxh/Detail",
      "style": {
        "navigationBarTitleText": "销退明细"
      }
    },
    {
      "path": "pages/workbench/storeCarManage/index",
      "style": {
        "navigationBarTitleText": "库房车辆管理报表",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/workbench/trainmastersigning/index",
      "style": {
        "navigationBarTitleText": "车长签约列表",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/workbench/trainmastersigning/KZList",
      "style": {
        "navigationBarTitleText": "库长签约列表",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/workbench/trainmastersigning/KGList",
      "style": {
        "navigationBarTitleText": "库管签约列表",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/workbench/trainmastersigning/SQUserList",
      "style": {
        "navigationBarTitleText": "省区内人员清单",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/workbench/trainmastersigning/DQUserList",
      "style": {
        "navigationBarTitleText": "大区内人员清单",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/workbench/partnerSign/index",
      "style": {
        "navigationBarTitleText": "区长签约",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/workbench/partnerBreak/index",
      "style": {
        "navigationBarTitleText": "区长解约",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/workbench/shopmanageBreak/index",
      "style": {
        "navigationBarTitleText": "库长解约",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/workbench/logisticsList/index",
      "style": {
        "navigationBarTitleText": "物流分仓列表",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/workbench/logisticsList/detail",
      "style": {
        "navigationBarTitleText": "物流分仓详情",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/workbench/trainmastersigning/UserList",
      "style": {
        "navigationBarTitleText": "人员清单",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/workbench/trainmastersigning/KZJYList",
      "style": {
        "navigationBarTitleText": "库长解约审批列表",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/workbench/trainmastersigning/addTrain",
      "style": {
        "navigationBarTitleText": "新增库长签约",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/workbench/trainmastersigning/Detail",
      "style": {
        "navigationBarTitleText": "签约详情",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/workbench/trainmastersigning/DetailSP",
      "style": {
        "navigationBarTitleText": "解约审批",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/workbench/logisticsWarehouseBuild/index",
      "style": {
        "navigationBarTitleText": "物流分仓建立",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/workbench/channelbuilding/index",
      "style": {
        "navigationBarTitleText": "终端渠道建设",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/workbench/organization/index",
      "style": {
        "navigationBarTitleText": "渠道运营组织",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/workbench/organization/sqgljxq/index",
      "style": {
        "navigationBarTitleText": "省区关联经销区",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/workbench/organization/sqgljxq/addSq",
      "style": {
        "navigationBarTitleText": "省区关联经销区新增",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/workbench/organization/jxq/index",
      "style": {
        "navigationBarTitleText": "经销区管理及新增",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/workbench/organization/jxq/addJXQ",
      "style": {
        "navigationBarTitleText": "新增经销区",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/workbench/FHSP/index",
      "style": {
        "navigationBarTitleText": "返货审批",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/workbench/FHSP/CheckDetail",
      "style": {
        "navigationBarTitleText": "返货审批详情",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/workbench/JXTB/index",
      "style": {
        "navigationBarTitleText": "经销图表",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/workbench/JFGZ/index",
      "style": {
        "navigationBarTitleText": "积分规则",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/workbench/JFGZ/addJF",
      "style": {
        "navigationBarTitleText": "新增积分规则",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/workbench/QfType/index",
      "style": {
        "navigationBarTitleText": "清分类型设置",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/workbench/QfType/add",
      "style": {
        "navigationBarTitleText": "新增清分类型设置",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/workbench/FHJYZ/HD",
      "style": {
        "navigationBarTitleText": "回调数据表",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/workbench/FHJYZ/FH",
      "style": {
        "navigationBarTitleText": "返货数据表",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/workbench/FHJYZ/XH",
      "style": {
        "navigationBarTitleText": "销货数据表",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/workbench/FHJYZ/KC",
      "style": {
        "navigationBarTitleText": "返货经营者库存数据表",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/workbench/TCDH/TCDHKC",
      "style": {
        "navigationBarTitleText": "统筹到货库存数据表",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/FormOther/ZZJG",
      "style": {
        "navigationBarTitleText": "组织架构"
      }
    },
    {
      "path": "pages/FormOther/MRGZQK",
      "style": {
        "navigationBarTitleText": "每日工作情况"
      }
    },
    {
      "path": "pages/workbench/PhotoCheck/index",
      "style": {
        "navigationBarTitleText": "照片图册",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/workbench/PriceValueChange/index",
      "style": {
        "navigationBarTitleText": "价格价值变化",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/workbench/PriceValueChange/add",
      "style": {
        "navigationBarTitleText": "价格价值变化",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/workbench/productEvent/index",
      "style": {
        "navigationBarTitleText": "产品活动申请列表",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/workbench/productEvent/add",
      "style": {
        "navigationBarTitleText": "新增产品活动申请",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/workbench/ExchangeRules/index",
      "style": {
        "navigationBarTitleText": "清分卷兑换规则",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/workbench/ExchangeRules/add",
      "style": {
        "navigationBarTitleText": "新增兑换规则",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/workbench/ExchangeRules/detail",
      "style": {
        "navigationBarTitleText": "兑换规则详情",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/workbench/OrderSwitch/index",
      "style": {
        "navigationBarTitleText": "产品订返开关",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/workbench/OrderSwitch/Detail",
      "style": {
        "navigationBarTitleText": "产品订返开关",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/workbench/SpecialChannel/index",
      "style": {
        "navigationBarTitleText": "特渠费用发放",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/workbench/SpecialChannel/Detail",
      "style": {
        "navigationBarTitleText": "特渠费用发放",
        "enablePullDownRefresh": false
      }
    }
  ],
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "uni-app",
    "navigationBarBackgroundColor": "#F8F8F8",
    "backgroundColor": "#F8F8F8"
  },
  "uniIdRouter": {}
}