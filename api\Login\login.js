import request from "../index";

//用户登录
export function user_login(code) {
  return request({
    method: "get",
    url: "/user/login",
    data: {
      code,
    },
  });
}

//根据编码获取用户信息
export function get_user_info_login(userCode) {
  return request({
    method: "get",
    url: "/user/getUserInfo",
    data: {
      userCode,
    },
  });
}

//获取大区总大区下的所有经销区ID
export function getRegionUnderAreaIds(areaId) {
  return request({
    method: "get",
    url: "/national/getRegionUnderAreaIds",
    data: {
      areaId,
    },
  });
}

//获取省区总省区下的所有经销区ID
export function getProvinceUnderAreaIds(areaId) {
  return request({
    method: "get",
    url: "/national/getProvinceUnderAreaIds",
    data: {
      areaId,
    },
  });
}

// 用户获取token
export function getToken(user_code) {
  return request({
    method: "post",
    url: "/user/getToken",
    data: {
      user_code: user_code,
    },
    header: {
      Authorization: "",
    },
  });
}

// 获取用户工作台功能模块
export function getWorkbenchFunction(userId) {
  return request({
    method: "get",
    url: "/user/getWorkbenchFunction",
    data: {
      userId: userId,
    },
  });
}

// 获取所有的用户列表
export function getAllUserList(data) {
  return request({
    method: "get",
    url: "/user/getAllUsers",
    data: data,
  });
}
