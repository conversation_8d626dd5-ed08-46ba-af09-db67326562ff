import request from "../../index";

// 获取所有经销区
export function getAllAreaApi() {
  return request({
    url: `/get/all_area`,
    method: "get",
  });
}

// 提交清分类型
export function addQfApi(data) {
  return request({
    url: `/add/qf_type`,
    method: "post",
    data
  });
}

// 获取清分类型列表
export function getQfListApi(limit,keyword) {
  return request({
    url: `/get/qf_list?limit=${limit}&keywords=${keyword}`,
    method: "get",
  });
}

// 修改清分类型
export function editQfApi(data) {
  return request({
    url: `/edit/qf_type`,
    method: "post",
    data
  });
}
