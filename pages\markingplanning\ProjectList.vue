<template>
  <div class="list-box">
    <div style="display: flex">
      <span>显示已开门店：</span>
      <a-switch v-model:checked="openViewStore" @change="changeView" />
    </div>
    <a-tabs v-model:activeKey="activeKey" @change="tabChange">
      <a-tab-pane key="1" tab="正在执行">
        <a-input-search
          v-model:value="dataParams.key_words"
          enter-button
          placeholder="请输入负责人名称"
          allowClear
          @search="onSearch"
        />
        <div
          class="list-container"
          ref="scrollContainer1"
          @scroll="handleScroll1"
        >
          <div
            :class="item.active ? 'list-item bg-active' : 'list-item'"
            v-for="(item, index) in planList"
            @click="choosePlan(index)"
          >
            <span>编&nbsp;&nbsp;&nbsp;&nbsp;号：</span>
            <span>{{ item.planCode }}</span>
            <span>日&nbsp;&nbsp;&nbsp;&nbsp;期：</span>
            <span>{{ item.startDate + "至" + item.endDate }}</span>
            <span>负责人：</span>
            <span>{{ item.Master_data_person_name_f }}</span>
            <span>执行人：</span>
            <span>{{ item.Master_data_person_name_z }}</span>
            <span>门店负责人：</span>
            <span>{{ item.Master_data_person_name_s }}</span>
            <span>门店数：</span>
            <span>{{ item.storeCount }}</span>
            <span>进度：</span>
            <a-progress
              style="grid-column: span 4 / span 4; padding-right: 5px"
              :percent="getPercent(item)"
              size="small"
              status="active"
            />
            <a-button
              v-show="item.active"
              type="primary"
              style="grid-column: span 6 / span 6"
              danger
              @click.stop="deletedPlan(item)"
            >
              作废
            </a-button>
          </div>
          <u-loadmore :status="loading" />
        </div>
      </a-tab-pane>
      <a-tab-pane key="2" tab="超期规划">
        <a-input-search
          v-model:value="dataParams.key_words"
          placeholder="请输入负责人名称"
          enter-button
          allowClear
          @search="onSearch"
        />
        <div
          class="list-container"
          ref="scrollContainer"
          @scroll="handleScroll"
        >
          <div class="list-item" v-for="(item, index) in planListPast">
            <span>编&nbsp;&nbsp;&nbsp;&nbsp;号：</span>
            <span>{{ item.planCode }}</span>
            <span>日&nbsp;&nbsp;&nbsp;&nbsp;期：</span>
            <span>{{ item.startDate + "至" + item.endDate }}</span>
            <span>负责人：</span>
            <span>{{ item.Master_data_person_name_f }}</span>
            <span>执行人：</span>
            <span>{{ item.Master_data_person_name_z }}</span>
            <span>门店负责人：</span>
            <span>{{ item.Master_data_person_name_s }}</span>
            <span>门店数：</span>
            <span>{{ item.storeCount }}</span>
            <span>进度：</span>
            <a-progress
              style="grid-column: span 4 / span 4; padding-right: 5px"
              :percent="getPercent(item)"
              size="small"
              status="active"
            />
          </div>
          <u-loadmore :status="loading" />
        </div>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script setup>
import { onMounted, reactive, ref } from "vue";
import {
  deletedPlanArea,
  getPlanList,
  getPlanListPast,
  getPlanStoreList,
} from "/api/workingplanning";
import { useUserInfo } from "/store/user/userInfo";
import { useMarking } from "/store/markingplanning/index";
import { message } from "ant-design-vue";
import { transtionCopy } from "/utils/map";
import { getAllAreas_sell } from "/api/map/index";

const emits = defineEmits(["drawPlanArea", "changeViewStore", "deletedPlan"]);

const userStore = useUserInfo();
const useMarkingStore = useMarking();
const planList = ref([]);
const planListPast = ref([]);
const openViewStore = ref(false);

const activeKey = ref("1");

function tabChange() {
  console.log("activeKey", activeKey.value);
  planList.value = [];
  planListPast.value = [];
  dataParams.page = 1;
  dataParams.key_words = "";
  getAllPlans();
}
function changeView(value) {
  console.log("changeView", value);
  if (value) {
    const geometriesStore = useMarkingStore.inAreaEditStore
      .filter(item => item.is_negotiation)
      .map(item => {
        return {
          position: new TMap.LatLng(item.Store_Latitude, item.Store_longitude),
          styleId: judgeColor(item),
          id: item.id,
        };
      });
    emits("changeViewStore", geometriesStore);
  } else {
    const geometriesStore = useMarkingStore.inAreaEditStore.map(item => {
      return {
        position: new TMap.LatLng(item.Store_Latitude, item.Store_longitude),
        styleId: judgeColor(item),
        id: item.id,
      };
    });
    emits("changeViewStore", geometriesStore);
  }
}

const dataParams = reactive({
  page: 1,
  page_size: 10,
  createUserId: userStore.userInfo.id,
  key_words: "",
});

const currentDataLength = ref(0);
// 获取本人规划的所有规划列表
async function getAllPlans() {
  // 执行列表
  if (activeKey.value === "1") {
    loading.value = "loading";
    const { data } = await getPlanList(dataParams);

    const resultData = data.result.map(item => {
      return {
        ...item,
        active: false,
      };
    });
    planList.value = planList.value.concat(resultData);
    currentDataLength.value = data.result.length;
    if (currentDataLength.value < dataParams.page_size) {
      loading.value = "nomore";
    } else {
      loading.value = "loadmore";
    }
  }
  // 超期列表
  if (activeKey.value === "2") {
    loading.value = true;
    const { data } = await getPlanListPast(dataParams);
    const resultData = data.result;
    planListPast.value = planListPast.value.concat(resultData);
    currentDataLength.value = data.result.length;
    if (currentDataLength.value < dataParams.page_size) {
      loading.value = "nomore";
    } else {
      loading.value = "loadmore";
    }
  }
}

async function choosePlan(index) {
  planList.value.forEach(item => {
    item.active = false;
  });
  planList.value[index].active = true;

  let center;
  let geometries = [];

  const thisPlan = planList.value[index];
  const planningArea = JSON.parse(thisPlan.planningArea);
  if (thisPlan.planningArea) {
    const paths = planningArea.map(item => new TMap.LatLng(item[0], item[1]));
    center = TMap.geometry.computeCentroid(paths); //规划区域中心点

    // 自定义区域
    geometries = [
      {
        styleId: "bgColor", //绑定样式名
        paths: paths, //多边形轮廓
      },
    ];
  }

  // 规划区域
  let geometries1 = [];
  const { data } = await getAllAreas_sell(thisPlan.inAreaId);
  data.result.forEach(item => {
    JSON.parse(item.Region_profile).forEach(_item => {
      const outlinePath = transtionCopy(_item).map(
        _item_ => new TMap.LatLng(_item_.latitude, _item_.longitude)
      );
      const path = {
        styleId: "bgColor",
        paths: outlinePath,
      };
      geometries1.push(path);
    });
  });

  // 自定义区域门店信息
  const { data: storeData } = await getPlanStoreList(thisPlan.id);
  useMarkingStore.inAreaEditStore = storeData.result;
  const geometriesStore = storeData.result.map(item => {
    return {
      position: new TMap.LatLng(item.Store_Latitude, item.Store_longitude),
      styleId: judgeColor(item),
      id: item.id,
    };
  });
  // 门店规划中心用这个
  if (!thisPlan.planningArea) {
    center = new TMap.LatLng(
      storeData.result[0].Store_Latitude,
      storeData.result[0].Store_longitude
    ); //规划区域中心点
  }
  emits("drawPlanArea", geometries, geometries1, geometriesStore, center);
}

// 判断门店标记点颜色
function judgeColor(store) {
  return store.is_negotiation ? "marker1" : "marker2";
}

function getPercent(item) {
  const num = Number((item.hasCount / item.storeCount) * 100).toFixed(1);
  return parseFloat(num);
}

// 删除
async function deletedPlan(item) {
  if (item.hasCount > 0) return message.warn("有预谈不允许作废");
  await deletedPlanArea(item.id);
  emits("deletedPlan");
  message.success("删除成功");
  planList.value = [];
  dataParams.page = 1;
  getAllPlans();
}

async function onSearch() {
  getAllPlans();
}

const scrollContainer = ref(null);
const loading = ref("loading");
const handleScroll = () => {
  if (scrollContainer.value) {
    const container = scrollContainer.value;
    if (
      container.scrollHeight - (container.scrollTop + container.clientHeight) <
      1
    ) {
      console.log("滚动到底部");
      if (currentDataLength.value < dataParams.page_size) {
        loading.value = "nomore";
      } else {
        dataParams.page += 1;
        getAllPlans();
        // 在这里可以执行滚动到底部后的操作，比如加载更多数据等
      }
    }
  }
};

const scrollContainer1 = ref(null);
const handleScroll1 = () => {
  if (scrollContainer.value) {
    const container = scrollContainer.value;
    if (
      container.scrollHeight - (container.scrollTop + container.clientHeight) <
      1
    ) {
      console.log("滚动到底部");
      if (currentDataLength.value < dataParams.page_size) {
        loading.value = "nomore";
      } else {
        dataParams.page += 1;
        getAllPlans();
        // 在这里可以执行滚动到底部后的操作，比如加载更多数据等
      }
    }
  }
};

onMounted(() => {
  getAllPlans();
});
</script>

<style lang="scss" scoped>
.list-box {
  width: 300px;
  padding: 8px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  .list-container {
    height: 550px;
    overflow-y: scroll;
    .list-item {
      margin-top: 15px;
      display: grid;
      grid-template-columns: repeat(6, minmax(0, 1fr));
      gap: 5px;
      padding: 5px;
      border: 1px solid rgba(217, 217, 217);
      border-radius: 5px;
      span:nth-child(odd) {
        grid-column: span 2 / span 2;
        text-align: right;
      }
      span:nth-child(even) {
        grid-column: span 4 / span 4;
      }
      &:hover {
        cursor: pointer;
        background-color: rgba(221, 227, 233, 0.5);
      }
    }
    .bg-active {
      background-color: rgba(211, 227, 253);
    }
  }
}
</style>
