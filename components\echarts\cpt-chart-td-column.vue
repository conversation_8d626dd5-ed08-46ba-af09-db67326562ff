<template>
  <div id="cpt-chart-td-column" style="height: 400px; width: 100%"></div>
</template>

<script setup>
import * as echarts from "echarts";
import { nextTick } from "vue";

const props = defineProps(["data"]);
const attribute = props.data.attribute;
const xyData = JSON.parse(props.data.cptDataForm.dataText);
let columnColor = attribute.barColor;
if (attribute.gradualColor) {
  columnColor = new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: attribute.barColor1 },
    { offset: 0.5, color: attribute.barColor2 },
    { offset: 1, color: attribute.barColor3 },
  ]);
}
nextTick(() => {
  // 基于准备好的dom，初始化echarts实例
  var myChart = echarts.init(document.getElementById("cpt-chart-td-column"));
  // 绘制图表
  myChart.setOption({
    color: columnColor,
    title: {
      text: attribute.chartTitle,
      textStyle: {
        color: attribute.titleTextColor,
      },
      left: attribute.titleLeft,
      top: attribute.titleTop,
    },
    tooltip: {},
    grid: {
      x: 10,
      y: 30,
      x2: 10,
      y2: 10,
      containLabel: true,
    },
    xAxis: {
      show: attribute.xAxisShow,
      type: "category",
      data: xyData.xData.split(","),
      axisLabel: {
        color: attribute.xLabelColor,
        rotate: attribute.xFontRotate, //倾斜角度-180~180
      },
      axisLine: {
        show: attribute.xLineShow,
        lineStyle: {
          color: attribute.xLineColor,
        },
      },
      axisTick: {
        //x轴刻度线
        show: attribute.xTickShow,
      },
    },
    yAxis: {
      show: attribute.yAxisShow,
      type: "value",
      axisLabel: {
        color: attribute.yLabelColor,
      },
      axisLine: {
        show: attribute.yLineShow,
        lineStyle: {
          color: attribute.yLineColor,
        },
      },
      axisTick: {
        //y轴刻度线
        show: attribute.yTickShow,
      },
      splitLine: {
        //网格线
        show: attribute.yGridLineShow,
      },
    },
    series: [
      {
        name: "新增用户数量",
        type: "bar", //pictorialBar || bar
        showBackground: attribute.barBgShow,
        stack: "account",
        barWidth: attribute.barWidth,
        data: xyData.yData.split(","),
      },
      {
        name: "邀请新用户数量",
        type: "bar",
        stack: "account",
        barWidth: attribute.barWidth,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "#ffae88" },
            { offset: 1, color: "#ff7388" },
          ]),
        },
        data: xyData.yData2.split(","),
      },
      {
        z: 3,
        type: "pictorialBar",
        symbolPosition: "end",
        symbol: "diamond",
        symbolOffset: [0, "-50%"],
        symbolSize: [attribute.barWidth, 10],
        symbolRotate: 0,
        itemStyle: {
          borderWidth: 0,
          color: "#10e6ff",
        },
        data: xyData.yData3.split(","),
      },
      {
        z: 3,
        type: "pictorialBar",
        symbolPosition: "end",
        symbol: "diamond",
        symbolOffset: [0, "-50%"],
        symbolSize: [attribute.barWidth, 10],
        itemStyle: {
          borderWidth: 0,
          color: "#ffcf90",
        },
        data: xyData.yData4.split(","),
      },
    ],
  });
});
</script>

<style></style>
