<template>
  <div class="container">
    <a-card title="区长签约信息" class="main-card">
      <a-form
        :model="formData"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
        ref="formRef"
      >
        <!-- 基本信息区域 -->
        <a-divider orientation="left">基本信息</a-divider>
        <a-row :gutter="24">
          <a-col :span="12" v-for="item in basicFields" :key="item.key">
            <a-form-item :label="item.label" :name="item.key">
              <a-input
                v-model:value="formData[item.key]"
                :placeholder="getPlaceholder(item.key, item.label)"
                @click="labelclick(item.key)"
                :disabled="isFieldDisabled(item.key)"
                :maxlength="getMaxLength(item.key)"
                :show-count="item.key === 'yyzzjc'"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 法人信息区域 -->
        <a-divider orientation="left">法人信息</a-divider>
        <a-row :gutter="24">
          <a-col :span="12" v-for="item in legalFields" :key="item.key">
            <a-form-item :label="item.label" :name="item.key">
              <a-input
                v-model:value="formData[item.key]"
                :placeholder="getPlaceholder(item.key, item.label)"
                :disabled="isFieldDisabled(item.key)"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 委托执行人信息区域 -->
        <a-divider orientation="left">委托执行人信息</a-divider>
        <a-row :gutter="24">
          <a-col :span="12" v-for="item in executorFields" :key="item.key">
            <a-form-item :label="item.label" :name="item.key">
              <a-input
                v-model:value="formData[item.key]"
                :placeholder="getPlaceholder(item.key, item.label)"
                :disabled="isFieldDisabled(item.key)"
              />
            </a-form-item>
          </a-col>
          <!-- 性别字段 -->
          <a-col :span="12">
            <a-form-item label="性别" name="sex">
              <a-radio-group v-model:value="formData.sex">
                <a-radio value="男">男</a-radio>
                <a-radio value="女">女</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 银行信息区域 -->
        <a-divider orientation="left">银行信息</a-divider>
        <a-row :gutter="24">
          <a-col :span="12" v-for="item in bankFields" :key="item.key">
            <a-form-item :label="item.label" :name="item.key">
              <a-input
                v-model:value="formData[item.key]"
                :placeholder="getPlaceholder(item.key, item.label)"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 保证金信息 -->
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="保证金金额" name="bzjje">
              <a-input
                v-model:value="formData.bzjje"
                placeholder="请输入保证金金额"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 保证金打款截图上传 -->
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="保证金打款截图" name="bzjdkzt">
              <a-upload
                :file-list="formData.bzjdkzt ? [{ uid: '1', url: formData.bzjdkzt, status: 'done' }] : []"
                list-type="picture-card"
                :before-upload="beforeUpload"
                @change="(info) => handleSingleUploadChange(info, 'bzjdkzt')"
                :max-count="1"
                accept="image/*"
              >
                <div v-if="!formData.bzjdkzt">
                  <plus-outlined />
                  <div style="margin-top: 8px">上传截图</div>
                </div>
              </a-upload>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 区域信息 -->
        <a-divider orientation="left">区域信息</a-divider>
        <a-row :gutter="24">
          <a-col :span="12" v-for="item in regionFields.filter(field => field.key !== 'kfnfgctx')" :key="item.key">
            <a-form-item :label="item.label" :name="item.key">
              <a-input
                v-model:value="formData[item.key]"
                :placeholder="getPlaceholder(item.key, item.label)"
                :disabled="isFieldDisabled(item.key)"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 库房能否挂车同行 - 开关样式 -->
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="库房能否挂车通行" name="kfnfgctx">
              <a-switch
                v-model:checked="formData.kfnfgctx"
                checked-children="是"
                un-checked-children="否"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 签约类型选择 -->
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="签约类型" name="qylx">
              <a-radio-group v-model:value="formData.qylx">
                <a-radio value="签约">签约</a-radio>
                <a-radio value="续约">续约</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if="formData.qylx === '续约'">
            <a-form-item label="续约选项" name="xyxz">
              <a-select
                v-model:value="formData.xyxz"
                placeholder="请选择续约选项"
                :options="renewalOptions.map(item => ({ label: item.label, value: item.value }))"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 图片上传区域 -->
        <a-divider orientation="left">证件照片</a-divider>

        <!-- 营业执照照片上传 -->
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="营业执照照片" name="yyzztp">
              <a-upload
                :file-list="formData.yyzztp ? [{ uid: '1', url: formData.yyzztp, status: 'done' }] : []"
                list-type="picture-card"
                :before-upload="beforeUpload"
                @change="(info) => handleSingleUploadChange(info, 'yyzztp')"
                :max-count="1"
                accept="image/*"
              >
                <div v-if="!formData.yyzztp">
                  <plus-outlined />
                  <div style="margin-top: 8px">上传营业执照</div>
                </div>
              </a-upload>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 法人身份证照片上传 -->
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="法人身份证正面照片" name="sfztp_zm">
              <a-upload
                :file-list="formData.sfztp_zm ? [{ uid: '1', url: formData.sfztp_zm, status: 'done' }] : []"
                list-type="picture-card"
                :before-upload="beforeUpload"
                @change="(info) => handleSingleUploadChange(info, 'sfztp_zm')"
                :max-count="1"
                accept="image/*"
              >
                <div v-if="!formData.sfztp_zm">
                  <plus-outlined />
                  <div style="margin-top: 8px">上传正面</div>
                </div>
              </a-upload>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="法人身份证反面照片" name="sfztp_fm">
              <a-upload
                :file-list="formData.sfztp_fm ? [{ uid: '1', url: formData.sfztp_fm, status: 'done' }] : []"
                list-type="picture-card"
                :before-upload="beforeUpload"
                @change="(info) => handleSingleUploadChange(info, 'sfztp_fm')"
                :max-count="1"
                accept="image/*"
              >
                <div v-if="!formData.sfztp_fm">
                  <plus-outlined />
                  <div style="margin-top: 8px">上传反面</div>
                </div>
              </a-upload>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 银行卡照片上传 -->
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="银行卡正面照片" name="yhktp_zm">
              <a-upload
                :file-list="formData.yhktp_zm ? [{ uid: '1', url: formData.yhktp_zm, status: 'done' }] : []"
                list-type="picture-card"
                :before-upload="beforeUpload"
                @change="(info) => handleSingleUploadChange(info, 'yhktp_zm')"
                :max-count="1"
                accept="image/*"
              >
                <div v-if="!formData.yhktp_zm">
                  <plus-outlined />
                  <div style="margin-top: 8px">上传正面</div>
                </div>
              </a-upload>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="银行卡反面照片" name="yhktp_fm">
              <a-upload
                :file-list="formData.yhktp_fm ? [{ uid: '1', url: formData.yhktp_fm, status: 'done' }] : []"
                list-type="picture-card"
                :before-upload="beforeUpload"
                @change="(info) => handleSingleUploadChange(info, 'yhktp_fm')"
                :max-count="1"
                accept="image/*"
              >
                <div v-if="!formData.yhktp_fm">
                  <plus-outlined />
                  <div style="margin-top: 8px">上传反面</div>
                </div>
              </a-upload>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 提交按钮 -->
        <a-form-item :wrapper-col="{ span: 24 }" style="text-align: center; margin-top: 32px">
          <a-button
            type="primary"
            size="large"
            :loading="buttonLoading"
            @click="submit"
            style="width: 200px"
          >
            提交申请
          </a-button>
        </a-form-item>
      </a-form>
    </a-card>
  </div>
  <CustomModal
    :show="showModal"
    title="请选择"
    :width="600"
    @close="showModal = false"
  >
    <MultiSelectListOne
      :items="allItems"
      :items2="allItems2"
      @update:confirm="confirm"
      :items-per-page="pageSize"
      :current-page="currentPage"
      :single-select="true"
    />
  </CustomModal>
</template>

<script setup>
import { ref, reactive, watch } from "vue";
import { PlusOutlined } from '@ant-design/icons-vue';
import { useUserInfo } from "@/store/user/userInfo";
import {
  get_wlfc,
  sign_wlfc,
} from "/api/workbench/logisticsWarehouseBuild/index.js";
import { uploadFile } from "/api/upload.js";
import CustomModal from "./components/CustomModal.vue";
import MultiSelectListOne from "./components/MultiSelectListOne.vue";
import dayjs from "dayjs";

const userStore = useUserInfo();

const formRef = ref(null);
const formData = reactive({
  wlfcmc: null, //物流分仓
  jxqy: null, //经销区域
  yyzzjc: null, //营业执照简称
  jxkfmc: null, //经销库房名称
  yfyyzzmc: null, //营业执照名称
  yfyyzzh: null, //营业执照号
  yfjyz: null, //法定代表人
  frsfz: null, //法人身份证号
  yflxdh: null, //法人电话
  yfjycs: null, //营业执照经营场所
  rzrymc: null, //委托执行人系统名称
  rzryjs: "区长", //委托执行人角色，默认值为"区长"
  wtzxrxm: null, //委托执行人姓名
  sex: null, //性别
  sfzh: null, //身份证号
  lxdh: null, //联系电话
  khyh: null, //开户银行
  zhmc: null, //账户名称
  yxkh: null, //银行账号
  bzjje: null, //保证金金额
  bzjdkzt: null, //保证金打款截图
  qylx: "签约", //签约类型，默认为"签约"
  xyxz: null, //续约选择（当签约类型为续约时显示）
  ssdq: null, //所属大区
  sssq: null, //所属省区
  kfwz: null, //库房位置
  tzcls: null, //投资车辆数
  kfnfgctx: false, //库房能否挂车同行
  yyzztp: null, //营业执照照片
  sfztp_zm: null, //法人身份证正面照片
  sfztp_fm: null, //法人身份证反面照片
  yhktp_zm: null, //银行卡正面照片
  yhktp_fm: null, //银行卡反面照片
});

// 存储物流分仓的相关信息
const outletName = ref("");

// 监听jc和jxqy变化，自动生成jxkfmc
watch(
  [() => formData.yyzzjc, () => formData.jxqy],
  ([jcVal, jxqyVal]) => {
    if (jcVal && jxqyVal) {
      formData.jxkfmc = `${jcVal}(${jxqyVal})`;
    } else {
      formData.jxkfmc = null;
    }
  },
  { immediate: true }
);

const buttonLoading = ref(false);

// 字段禁用状态控制
const isFieldDisabled = (key) => {
  const disabledFields = ['jxqy', 'jxkfmc', 'rzryjs', 'ssdq', 'sssq', 'kfwz'];
  return disabledFields.includes(key);
};

// 获取placeholder文本
const getPlaceholder = (key, label) => {
  switch (key) {
    case 'rzrymc':
      return '请输入区域简称和身份证姓名';
    case 'wtzxrxm':
      return '请输入身份证姓名';
    default:
      return `请输入${label}`;
  }
};

// 获取字段最大长度
const getMaxLength = (key) => {
  if (key === 'yyzzjc') {
    return 6; // 营业执照简称限制4-6个字符
  }
  return null;
};

const validateForm = () => {
  for (let item of formObject) {
    // 跳过开关类型字段的验证
    if (item.key === 'kfnfgctx') {
      continue;
    }

    if (formData[item.key] == null || formData[item.key] == "") {
      uni.showToast({
        title: `请输入${item.label}`,
        icon: "none",
      });
      return false;
    }
  }

  // 验证营业执照简称长度
  if (formData.yyzzjc && (formData.yyzzjc.length < 4 || formData.yyzzjc.length > 6)) {
    uni.showToast({
      title: "营业执照简称应为4-6个字符",
      icon: "none",
    });
    return false;
  }

  // 验证图片上传（检查字符串字段是否为空）
  if (!formData.bzjdkzt) {
    uni.showToast({
      title: "请上传保证金打款截图",
      icon: "none",
    });
    return false;
  }

  if (!formData.yyzztp) {
    uni.showToast({
      title: "请上传营业执照照片",
      icon: "none",
    });
    return false;
  }

  if (!formData.sfztp_zm) {
    uni.showToast({
      title: "请上传法人身份证正面照片",
      icon: "none",
    });
    return false;
  }

  if (!formData.sfztp_fm) {
    uni.showToast({
      title: "请上传法人身份证反面照片",
      icon: "none",
    });
    return false;
  }

  if (!formData.yhktp_zm) {
    uni.showToast({
      title: "请上传银行卡正面照片",
      icon: "none",
    });
    return false;
  }

  if (!formData.yhktp_fm) {
    uni.showToast({
      title: "请上传银行卡反面照片",
      icon: "none",
    });
    return false;
  }

  return true;
};



// 提交逻辑
async function submit() {
  buttonLoading.value = true;

  if (!validateForm()) {
    buttonLoading.value = false;
    return;
  }

  const dataParams = {
    ...formData,
    type: 4,
    user: userStore.userInfo.id,
    userName: userStore.userInfo.userName,
  };

  console.log(dataParams, "dataParams");
  try {
    await sign_wlfc(dataParams);
    uni.showToast({
      title: "提交成功",
      duration: 1000,
      icon: "success",
    });
    setTimeout(() => {
      uni.navigateBack();
    }, 1100);
  } catch (error) {
    buttonLoading.value = false;
    console.error("提交失败", error);
    uni.showToast({
      title: "提交失败",
      icon: "none",
      duration: 2000,
    });
  }
}
// 字段分组配置
const basicFields = [
  { label: "物流分仓", key: "wlfcmc" },
  { label: "经销区域", key: "jxqy" },
  { label: "营业执照简称", key: "yyzzjc" },
  { label: "经销库房名称", key: "jxkfmc" }
];

const legalFields = [
  { label: "营业执照名称", key: "yfyyzzmc" },
  { label: "营业执照号", key: "yfyyzzh" },
  { label: "法定代表人", key: "yfjyz" },
  { label: "法人身份证号", key: "frsfz" },
  { label: "法人电话", key: "yflxdh" },
  { label: "营业执照经营场所", key: "yfjycs" }
];

const executorFields = [
  { label: "委托执行人系统名称", key: "rzrymc" },
  { label: "委托执行人角色", key: "rzryjs" },
  { label: "委托执行人姓名", key: "wtzxrxm" },
  { label: "身份证号", key: "sfzh" },
  { label: "联系电话", key: "lxdh" }
];

const bankFields = [
  { label: "开户银行", key: "khyh" },
  { label: "账户名称", key: "zhmc" },
  { label: "银行账号", key: "yxkh" }
];

const regionFields = [
  { label: "所属大区", key: "ssdq" },
  { label: "所属省区", key: "sssq" },
  { label: "库房位置", key: "kfwz" },
  { label: "投资车辆数", key: "tzcls" },
  { label: "库房能否挂车通行", key: "kfnfgctx" }
];

// 保持原有的formObject用于验证
const formObject = [
  ...basicFields,
  ...legalFields,
  ...executorFields,
  ...bankFields,
  { label: "保证金金额", key: "bzjje" },
  ...regionFields
];

const info = ref();
async function labelclick(data) {
  console.log(data);
  info.value = data;
  if (data == "wlfcmc") {
    wlfcclick();
  }
}


const wlfcclick = async () => {
  showModal.value = true;
  allItems.value = [];
  allItems2.value = [
    {
      value: 1,
      label: "分仓名称",
      label2: "具体位置",
      radio: 0,
    },
  ];

  await loadWlfcData(1); // 初始加载第一页数据
};

const currentPage = ref(1);
const pageSize = ref(999);
const totalPages = ref(1);

// 加载物流分仓数据
const loadWlfcData = async (page) => {
  try {
    const res = await get_wlfc(page, pageSize.value);

    if (res.data && res.data.result) {
      allItems.value = res.data.result.map((item) => ({
        value: item.id,
        label: item.entrepot_name, // 分仓名称
        label2: item.locationText, // 具体位置
        outlet_name: item.Organization_Name, // 经销区名称
        region_name: item.region_Name, // 大区名称
        province_name: item.province_Name, // 省区名称
        entrepot_code: item.entrepot_code,
        radio: 0,
      }));

      // 设置总页数
      if (res.data.total_pages) {
        totalPages.value = res.data.total_pages;
      }
    }
  } catch (error) {
    console.error("获取物流分仓数据失败", error);
    uni.showToast({
      title: "获取数据失败",
      icon: "none",
      duration: 2000,
    });
  }
};

const showModal = ref(false);
const allItems = ref([
  {
    value: 1,
    label: "选项1",
    radio: 0,
  },
]);
const allItems2 = ref([
  {
    value: 1,
    label: "名称",
    label2: "编码",
    label3: "角色",
    radio: 0,
  },
]);



// 续约相关
const renewalOptions = ref([
  { value: 'option1', label: '续约选项1' },
  { value: 'option2', label: '续约选项2' },
  { value: 'option3', label: '续约选项3' }
]);

// 文件上传前验证
const beforeUpload = (file) => {
  // 检查文件类型
  const isImage = file.type.startsWith('image/');
  if (!isImage) {
    uni.showToast({
      title: '只能上传图片文件',
      icon: 'none',
      duration: 2000
    });
    return false;
  }

  // 检查文件大小（限制为5MB）
  const isLt5M = file.size / 1024 / 1024 < 5;
  if (!isLt5M) {
    uni.showToast({
      title: '图片大小不能超过5MB',
      icon: 'none',
      duration: 2000
    });
    return false;
  }

  return false; // 阻止默认上传行为，使用自定义上传
};

// 单文件上传处理函数
const handleSingleUploadChange = async (info, fieldName) => {
  const { fileList } = info;

  // 如果文件列表为空，清空字段
  if (fileList.length === 0) {
    formData[fieldName] = null;
    return;
  }

  // 获取最新的文件
  const file = fileList[fileList.length - 1];

  // 如果文件已经有 url，直接使用
  if (file.url) {
    formData[fieldName] = file.url;
    return;
  }

  // 如果是新上传的文件，调用 uploadFile 接口
  if (file.originFileObj) {
    try {
      // 显示上传提示
      uni.showLoading({
        title: '正在上传...'
      });

      // 创建 FormData 对象
      const uploadFormData = new FormData();
      uploadFormData.append('file', file.originFileObj);

      // 调用上传接口
      const response = await uploadFile(uploadFormData);

      // 隐藏加载提示
      uni.hideLoading();

      // 获取文件URL
      const fileUrl = response.data?.url ||
                     response.data?.file_url ||
                     response.data?.path ||
                     response.data?.fileUrl ||
                     response.data?.filePath ||
                     response.url;

      if (!fileUrl) {
        throw new Error('上传接口未返回文件URL');
      }

      // 直接将URL赋值给字段
      formData[fieldName] = fileUrl;

      // 显示上传成功提示
      uni.showToast({
        title: '上传成功',
        icon: 'success',
        duration: 1000
      });

    } catch (error) {
      console.error('文件上传失败:', error);

      // 隐藏加载提示
      uni.hideLoading();

      const errorMessage = error.message || error.errMsg || '文件上传失败';
      uni.showToast({
        title: errorMessage,
        icon: 'none',
        duration: 2000
      });

      // 上传失败时清空字段
      formData[fieldName] = null;
    }
  }
};
const confirm = (item) => {
  console.log("item", item);
  formData[info.value] = item.label;
  if (info.value == "wlfcmc") {
    // 存储物流分仓ID，以便后续使用
    formData["wlfcId"] = item.value;
    formData["wlfcbm"] = item.entrepot_code;
    formData["sssq"] = item.province_name;
    formData["ssdq"] = item.region_name;
    formData["kfwz"] = item.label2; // 库房位置使用具体位置
    // 自动填入经销区域
    formData["jxqy"] = item.outlet_name || "";
    // 保存outlet_name用于经销库房名称的生成
    outletName.value = item.outlet_name || "";
  }

  showModal.value = false;
};
</script>

<style lang="scss" scoped>
.container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.main-card {
  max-width: 1200px;
  margin: 0 auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 8px;

  :deep(.ant-card-head) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px 8px 0 0;

    .ant-card-head-title {
      color: white;
      font-size: 18px;
      font-weight: 600;
    }
  }

  :deep(.ant-card-body) {
    padding: 32px;
  }
}

// 分割线样式
:deep(.ant-divider) {
  margin: 24px 0 16px 0;

  .ant-divider-inner-text {
    font-size: 16px;
    font-weight: 600;
    color: #1890ff;
  }
}

// 表单项样式
:deep(.ant-form-item) {
  margin-bottom: 16px;

  .ant-form-item-label {
    > label {
      font-weight: 500;
      color: #262626;
    }
  }

  .ant-input {
    border-radius: 6px;

    &:focus, &:hover {
      border-color: #40a9ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }

    &:disabled {
      background-color: #f5f5f5;
      color: #8c8c8c;
    }
  }
}

// 上传组件样式
:deep(.ant-upload) {
  .ant-upload-select {
    border-radius: 6px;
    border: 1px dashed #d9d9d9;
    transition: all 0.3s;

    &:hover {
      border-color: #40a9ff;
    }
  }

  .ant-upload-list-picture-card {
    .ant-upload-list-item {
      border-radius: 6px;
    }
  }
}

// 单选按钮组样式
:deep(.ant-radio-group) {
  .ant-radio-wrapper {
    margin-right: 16px;

    .ant-radio-inner {
      border-color: #d9d9d9;
    }

    &.ant-radio-wrapper-checked {
      .ant-radio-inner {
        border-color: #1890ff;
        background-color: #1890ff;
      }
    }
  }
}

// 开关样式
:deep(.ant-switch) {
  background-color: #f5f5f5;

  &.ant-switch-checked {
    background-color: #1890ff;
  }

  .ant-switch-inner {
    font-size: 12px;
    color: #fff;
  }
}

// 按钮样式
:deep(.ant-btn-primary) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 6px;
  font-weight: 500;

  &:hover, &:focus {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
  }
}

// 续约选项弹窗样式
.renewal-options {
  .renewal-option {
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
      background-color: #f5f5f5;
    }

    &:last-child {
      border-bottom: none;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }

  .main-card {
    :deep(.ant-card-body) {
      padding: 16px;
    }
  }

  :deep(.ant-col) {
    &.ant-col-12 {
      width: 100% !important;
    }
  }

  :deep(.ant-form-item) {
    .ant-form-item-label {
      text-align: left !important;
    }
  }
}
</style>
