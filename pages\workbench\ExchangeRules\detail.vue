<template>
  <a-card class="exchange-detail" title="兑换规则详情">
    <div class="rule-header">
      <div class="rule-title">
        <span>规则序号：</span>{{ ruleInfo.serial_number }}
        <a-tag :color="statusColor(ruleInfo.status)" style="margin-left: 12px"
          >{{ statusText(ruleInfo.status) }}
        </a-tag>
      </div>
      <div class="rule-title">
        <span class="label">产品：</span>{{ ruleInfo.product }}
      </div>
      <div class="rule-meta">
        <span>
          <span class="label">有效期：</span>
          {{ ruleInfo.validity_date }}
        </span>
        <span
          ><span class="label">最大兑换值：</span
          >{{ ruleInfo.max_exchange_value }} 积分</span
        >
        <span
          ><span class="label">剩余积分：</span
          >{{ ruleInfo.remaining_points }} 积分</span
        >
      </div>
      <div class="rule-meta">
        <span><span class="label">生效范围：</span>{{ ruleInfo.scope }}</span>
        <span
          ><span class="label">创建时间：</span>{{ ruleInfo.create_time }}</span
        >
      </div>
    </div>

    <!-- 兑换列表 -->
    <div class="exchange-list" v-if="!isMobile">
      <table class="custom-table">
        <thead>
          <tr>
            <th v-for="col in columns" :key="col.key">{{ col.title }}</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(item, idx) in exchangeList" :key="item.id">
            <td>{{ idx + 1 }}</td>
            <td>{{ item.time }}</td>
            <td>{{ item.amount }}</td>
            <td>{{ item.user }}</td>
            <td>
              <span :class="['status-badge', statusColorClass(item.status)]">{{
                statusText(item.status)
              }}</span>
            </td>
            <td>{{ item.used }}</td>
            <td>{{ item.left }}</td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 移动端卡片 -->
    <div class="exchange-cards" v-else>
      <a-card
        v-for="item in exchangeList"
        :key="item.id"
        class="exchange-card"
        title="兑换记录"
      >
        <div class="card-row">
          <span class="label">序号：</span>{{ item.id }}
        </div>
        <div class="card-row">
          <span class="label">兑换时间：</span>{{ item.time }}
        </div>
        <div class="card-row">
          <span class="label">兑换积分额：</span>{{ item.amount }}
        </div>
        <div class="card-row">
          <span class="label">兑换人：</span>{{ item.user }}
        </div>
        <div class="card-row">
          <span class="label">状态：</span
          ><span :class="['status-badge', statusColorClass(item.status)]">{{
            statusText(item.status)
          }}</span>
        </div>
        <div class="card-row">
          <span class="label">已使用额：</span>{{ item.used }}
        </div>
        <div class="card-row">
          <span class="label">剩余额：</span>{{ item.left }}
        </div>
      </a-card>
    </div>
  </a-card>
</template>

<script setup>
import { reactive, ref, onMounted } from "vue";

// 表格列定义
const columns = [
  { title: "序号", key: "index" },
  { title: "兑换时间", key: "time" },
  { title: "兑换积分额", key: "amount" },
  { title: "兑换人", key: "user" },
  { title: "状态", key: "status" },
  { title: "已使用额", key: "used" },
  { title: "剩余额", key: "left" },
];

// 规则信息
const ruleInfo = reactive({
  serial_number: "",
  product: "",
  validity_date: "",
  max_exchange_value: "",
  remaining_points: "",
  scope: "",
  create_time: "",
  status: "",
});

// mock 兑换列表
const exchangeList = ref([
  {
    id: 1,
    time: "2024-06-10 10:00",
    amount: 500,
    user: "张三",
    status: 0,
    used: 200,
    left: 300,
  },
  {
    id: 2,
    time: "2024-06-11 11:30",
    amount: 1000,
    user: "李四",
    status: 1,
    used: 1000,
    left: 0,
  },
  {
    id: 3,
    time: "2024-06-12 09:20",
    amount: 800,
    user: "王五",
    status: 2,
    used: 800,
    left: 0,
  },
]);

// 状态文本映射
function statusText(status) {
  switch (status) {
    case 0:
      return "待生效";
    case 1:
      return "已生效";
    case 2:
      return "已用完";
    case "pending":
      return "待生效";
    case "effective":
      return "生效中";
    case "expired":
      return "已失效";
    default:
      return "-";
  }
}
function statusColor(status) {
  switch (status) {
    case "pending":
      return "processing";
    case "effective":
      return "success";
    case "expired":
      return "error";
    default:
      return "default";
  }
}
function statusColorClass(status) {
  switch (status) {
    case 0:
    case "pending":
      return "badge-pending";
    case 1:
    case "effective":
      return "badge-effective";
    case 2:
    case "expired":
      return "badge-expired";
    default:
      return "badge-default";
  }
}
// 判断是否移动端
const isMobile = ref(false);
function checkIsMobile() {
  isMobile.value = window.innerWidth <= 768;
}
onMounted(() => {
  checkIsMobile();
  window.addEventListener("resize", checkIsMobile);

  // 获取本地存储的规则详情
  let detail = {};
  try {
    detail = uni.getStorageSync("exchangeRuleDetail") || {};
  } catch (e) {
    detail = {};
  }
  ruleInfo.serial_number = detail.serial_number || "";
  ruleInfo.product = detail.product || "";
  ruleInfo.validity_date = detail.validity_date || "";
  ruleInfo.max_exchange_value = detail.max_exchange_value || "";
  ruleInfo.remaining_points = detail.remaining_points || "";
  ruleInfo.scope =
    detail.scope_of_effectiveness ||
    detail.scope_of_effectiveness_region ||
    detail.scope_of_effectiveness_area ||
    "";
  ruleInfo.create_time = detail.create_time || "";
  ruleInfo.status = detail.status || "";
});
</script>

<style scoped>
.exchange-detail {
  max-width: 1200px;
  margin: 0 auto;
}
.rule-header {
  margin-bottom: 20px;
}
.rule-title {
  font-size: 20px;
  font-weight: bold;
  display: flex;
  align-items: center;
}
.rule-desc {
  color: #666;
}
.rule-meta span {
  margin-right: 24px;
  color: #999;
  font-size: 14px;
}
.custom-table {
  width: 100%;
  border-collapse: collapse;
  background: #fff;
  margin-top: 12px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}
.custom-table th,
.custom-table td {
  border: 1px solid #eee;
  padding: 10px 8px;
  text-align: center;
  font-size: 15px;
}
.custom-table th {
  background: #f7f8fa;
  font-weight: 600;
}
.status-badge {
  display: inline-block;
  padding: 2px 10px;
  border-radius: 12px;
  font-size: 13px;
  font-weight: 500;
  color: #fff;
}
.badge-pending {
  background: #faad14;
}
.badge-effective {
  background: #52c41a;
}
.badge-expired {
  background: #f5222d;
}
.badge-default {
  background: #bfbfbf;
}
.exchange-cards {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.exchange-card {
  margin-bottom: 12px;
}
.card-row {
  display: flex;
  font-size: 15px;
  margin-bottom: 4px;
}
.label {
  color: #888;
  min-width: 80px;
}
</style>
