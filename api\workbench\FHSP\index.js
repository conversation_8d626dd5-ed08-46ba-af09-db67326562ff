import request from "../../index";

// 获取返货单列表
export function postReturnGoodsBillListFK(data) {
  return request({
    method: 'POST',
    url: '/boss/get_return_lists_fk',
    data,
  })
}
// 获取返货单列表
export function postReturnGoodsBillListFKYS(data) {
  return request({
    method: 'POST',
    url: '/boss/get_return_lists_fk_ys',
    data,
  })
}

// 获取返货单列表
export function postReturnGoodsBillListFKSS(data) {
  return request({
    method: 'POST',
    url: '/boss/get_return_lists_fk_ss',
    data,
  })
}
// 获取返货单列表
export function postReturnGoodsBillListFKYSSS(data) {
  return request({
    method: 'POST',
    url: '/boss/get_return_lists_fk_ysss',
    data,
  })
}

// 获取返货单列表
export function get_return_lists_addFK(data) {
  return request({
    method: 'POST',
    url: '/boss/get_return_lists_add_fk',
    data,
  })
}

export function postGetReturnBasicInfo(main_id) {
  return request({
    method: 'POST',
    url: '/boss/get_return_basic_info',
    data: { main_id },
  })
}
export function postGetReturnGoodInfo(main_id) {
  return request({
    method: 'POST',
    url: '/boss/get_return_goods_info',
    data: { main_id },
  })
}

export function getApproveInfo(main_id) {
  return request({
    method: 'GET',
    url: `/get/return_approval?return_id=${main_id}`,
  })
}

export function get_fhbz(id) {
  return request({
    method: 'GET',
    url: `/boss/get_fhbz?id=${id}`,
  })
}

export function sumit_fhsp(data) {
  return request({
    method: 'post',
    url: '/boss/sumit_fhsp',
    data,
  })
}

export function sumit_fhsp_dq(data) {
  return request({
    method: 'post',
    url: '/boss/sumit_fhdq_new',
    data,
  })
}

export function postReturnGoodsBillListFKDQ(data) {
  return request({
    method: 'POST',
    url: '/boss/get_return_lists_fk_dq',
    data,
  })
}

export function postReturnGoodsBillListFKDQYS(data) {
  return request({
    method: 'POST',
    url: '/boss/get_return_lists_fk_dqys',
    data,
  })
}
export function postReturnGoodsBillListFKDQSS(data) {
  return request({
    method: 'POST',
    url: '/boss/get_return_lists_fk_dqss',
    data,
  })
}

export function postReturnGoodsBillListFKDQYSSS(data) {
  return request({
    method: 'POST',
    url: '/boss/get_return_lists_fk_dqysss',
    data,
  })
}