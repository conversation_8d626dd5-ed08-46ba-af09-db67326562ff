import request from "../../index";

// 获取所有大区
export function getAllRegionApi() {
  return request({
    url: `/get/all_region_qd`,
    method: "get",
  });
}

export function getAllRegionApi_dqz(userId) {
  return request({
    method: "get",
    url: `/get/all_region_qd_dqz?userId=${userId}`,
  });
}

// 获取所有省区
export function getAllProvincesApi() {
  return request({
    url: `/get/all_provinces_new`,
    method: "get",
  });
}

export function getAllProvincesApi_sqz(userId) {
  return request({
    method: "get",
    url: `/get/all_provinces_sqz_new_cl?userId=${userId}`,
  });
}

//获取大区下省区
export function getProvinceApi_dqz(userId) {
  return request({
    method: "get",
    url: `/boss/get_province_user?userId=${userId}`,
  });
}

// 获取所有经销区
export function getAllJxqApi() {
  return request({
    url: `/get/all_jxq_new`,
    method: "get",
  });
}

export function getAllJxqApiCS(userId) {
  return request({
    url: `/get/all_jxq_new_csjl?userId=${userId}`,
    method: "get",
  });
}

// 获取大区总下经销区
export function getJxqApi_dqz(userId) {
  return request({
    method: "get",
    url: `/boss/get_jxq_user?userId=${userId}`,
  });
}

// 获取人员列表
export function getPersonListApi(data) {
  return request({
    url: `/store/getAllUsers_jxq`,
    method: "post",
    data
  });
}

//
export function getDealerListApi(data) {
  return request({
    url: `/boss/getDealer_dq`,
    method: "post",
    data,
  });
}

// 获取未出车明细
export function getUnusedCarListApi(data) {
  return request({
    url: `/boss/get_info_wcc`,
    method: "post",
    data,
  });
}

// 获取所有管理车辆列表
export function getAllManageCarListApi(data) {
  return request({
    url: `/boss/get_clgl_info_all`,
    method: "post",
    data,
  });
}

// 获取管理车辆列表
export function getManageCarListApi(data) {
  return request({
    url: `/boss/get_clgl_info`,
    method: "post",
    data,
  });
}

// 获取人员管理车辆列表
export function getUserManageCarListApi(data) {
  return request({
    url: `/boss/get_clgl_info_user`,
    method: "post",
    data,
  });
}

// 获取车辆管理列表
export function getCarManageTableApi(data) {
  return request({
    url: `/boss/get_info_clglqk`,
    method: "post",
    data,
  });
}