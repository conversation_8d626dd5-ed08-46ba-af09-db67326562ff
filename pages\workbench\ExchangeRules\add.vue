<template>
  <div class="exchange-rules-container">
    <a-config-provider :locale="locale">
      <a-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        :label-col="{ span: isMobile ? 24 : 6 }"
        :wrapper-col="{ span: isMobile ? 24 : 18 }"
        class="exchange-form"
        :class="{ 'mobile-form': isMobile }"
      >
        <!-- 有效期选择 -->
        <a-form-item label="有效期" name="validMonth" required>
          <a-select
            v-model:value="formData.validMonth"
            placeholder="选择有效期月份"
            style="width: 100%"
            :options="monthOptions"
            @change="onMonthChange"
          />
        </a-form-item>

        <!-- 剩余可设置积分值 -->
        <a-form-item label="剩余可设置积分" v-if="formData.validMonth">
          <div class="remaining-points">
            <a-statistic
              :value="remainingPoints"
              suffix="积分"
              :value-style="{
                color: remainingPoints > 0 ? '#3f8600' : '#cf1322',
              }"
            />
            <div class="points-detail">
              <span class="detail-text"
                >本月最大可分配: {{ maxMonthlyPoints }}积分</span
              >
              <span class="detail-text">已分配: {{ allocatedPoints }}积分</span>
            </div>
          </div>
        </a-form-item>

        <!-- 产品选择 -->
        <a-form-item label="选择产品" name="productId" required>
          <a-select
            v-model:value="formData.productId"
            placeholder="请选择产品"
            style="width: 100%"
            :options="productOptions"
            show-search
            :filter-option="filterProductOption"
          />
        </a-form-item>

        <!-- 最大兑换值 -->
        <a-form-item label="最大兑换值" name="maxExchangeValue" required>
          <a-input-number
            v-model:value="formData.maxExchangeValue"
            :min="1"
            :max="remainingPoints"
            placeholder="请输入最大兑换值"
            style="width: 100%"
            :formatter="
              value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
            "
            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
          >
            <template #addonAfter>积分</template>
          </a-input-number>
        </a-form-item>

        <!-- 生效范围 -->
        <a-form-item label="生效范围" name="effectiveScope" required>
          <div class="scope-selection">
            <a-radio-group
              v-model:value="formData.scopeType"
              @change="onScopeTypeChange"
            >
              <a-radio
                value="company"
                v-if="userInfo?.Role_name === '渠道运营'"
              >
                全公司
              </a-radio>
              <a-radio value="region"> 指定大区 </a-radio>
              <a-radio value="dealer" v-if="userInfo?.Role_name !== '渠道运营'">
                指定经销区
              </a-radio>
            </a-radio-group>

            <div class="scope-content" v-if="formData.scopeType">
              <!-- 大区选择 -->
              <a-select
                v-if="formData.scopeType === 'region'"
                v-model:value="formData.effectiveScope"
                :options="regionOptions"
                placeholder="请选择大区"
                style="width: 100%; margin-top: 12px"
                mode="multiple"
                :max-tag-count="isMobile ? 1 : 3"
                show-search
                :filter-option="filterRegionOption"
              />

              <!-- 经销区选择 -->
              <a-select
                v-if="formData.scopeType === 'dealer'"
                v-model:value="formData.effectiveScope"
                :options="dealerOptions"
                placeholder="请选择经销区"
                style="width: 100%; margin-top: 12px"
                mode="multiple"
                :max-tag-count="isMobile ? 1 : 3"
                show-search
                :filter-option="filterDealerOption"
              />
            </div>
          </div>
        </a-form-item>
      </a-form>

      <!-- 操作按钮 -->
      <div class="form-actions">
        <a-space
          :size="16"
          :direction="isMobile ? 'vertical' : 'horizontal'"
          style="width: 100%"
        >
          <a-button
            type="primary"
            @click="handleSubmit"
            :loading="submitting"
            block
          >
            保存规则
          </a-button>
          <a-button @click="handleReset" block>重置</a-button>
          <a-button @click="handleCancel" block>取消</a-button>
        </a-space>
      </div>
    </a-config-provider>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from "vue";
import { message, Modal } from "ant-design-vue";
import dayjs from "dayjs";
import zhCN from "ant-design-vue/es/locale/zh_CN";
import "dayjs/locale/zh-cn";
import { getAllProductsApi } from "/api/workbench/ExchangeRules";
import {
  getAllDealersApi,
  getAllRegionsApi,
  getMonthIntegralApi,
  submitExchangeRuleApi,
} from "../../../api/workbench/ExchangeRules";

// 设置 dayjs 为中文
dayjs.locale("zh-cn");

// 中文语言包
const locale = zhCN;

const userInfo = ref();

// 移动端检测
const isMobile = computed(() => {
  if (typeof window !== "undefined") {
    return window.innerWidth <= 768;
  }
  return false;
});

// 表单引用
const formRef = ref();

// 表单数据
const formData = reactive({
  validMonth: dayjs().add(1, "month").format("YYYY-MM"), // 默认选择下个月
  productId: null,
  maxExchangeValue: null,
  scopeType: "company",
  effectiveScope: [],
});

// 提交状态
const submitting = ref(false);

// 积分相关数据
const maxMonthlyPoints = ref(100000); // 月度最大积分值
const allocatedPoints = ref(0); // 已分配积分

// 剩余可设置积分
const remainingPoints = computed(() => {
  return maxMonthlyPoints.value - allocatedPoints.value;
});

// 产品选项
const productOptions = ref([]);

// 大区选项
const regionOptions = ref([]);

// 经销区选项
const dealerOptions = ref([]);

// 有效期选项
const monthOptions = ref([
  {
    label: dayjs().format("YYYY-MM"),
    value: dayjs().format("YYYY-MM"),
  },
  {
    label: dayjs().add(1, "month").format("YYYY-MM"),
    value: dayjs().add(1, "month").format("YYYY-MM"),
  },
]);

// 获取所有可选产品
const getAllProducts = async () => {
  try {
    const { data } = await getAllProductsApi();
    productOptions.value = data.result.map(item => ({
      label: item.Small_boss_product_name,
      // value: item.id,
      value: item.Small_boss_product_name,
    }));
  } catch (error) {
    productOptions.value = [];
    console.error("获取所有可选产品失败", error);
  }
};

//获取所有可选大区
const getAllRegions = async () => {
  try {
    const { data } = await getAllRegionsApi();
    regionOptions.value = data.result.map(item => ({
      label: item.DistributionOrganizationName,
      // value: item.id,
      value: item.DistributionOrganizationName,
      id: item.id,
    }));

    //如果是大区总，则只显示自己负责的大区
    if (userInfo.value.Role_name === "大区总") {
      regionOptions.value = regionOptions.value.filter(
        item => item.id === userInfo.value.fz_area_id
      );
    }
  } catch (error) {
    regionOptions.value = [];
    console.error("获取所有可选大区失败", error);
  }
};

//获取所有可选经销区
const getAllDealers = async () => {
  try {
    const dealers = userInfo.value.jxqId.join(",");
    const { data } = await getAllDealersApi(dealers);
    dealerOptions.value = data.result.map(item => ({
      label: item.DistributionOrganizationName,
      // value: item.id,
      value: item.DistributionOrganizationName,
    }));
  } catch (error) {
    dealerOptions.value = [];
    console.error("获取所有可选经销区失败", error);
  }
};

// 表单验证规则
const rules = {
  validMonth: [
    { required: true, message: "请选择有效期月份", trigger: "change" },
  ],
  productId: [{ required: true, message: "请选择产品", trigger: "change" }],
  maxExchangeValue: [
    { required: true, message: "请输入最大兑换值", trigger: "blur" },
    { type: "number", min: 1, message: "最大兑换值不能小于1", trigger: "blur" },
  ],
  effectiveScope: [
    { required: true, message: "请选择生效范围", trigger: "change" },
  ],
};

// 月份变化事件
const onMonthChange = async monthString => {
  console.log("🚀 ~ monthString:", monthString);
  if (monthString) {
    // 查询该月份已分配的积分
    await fetchAllocatedPoints(monthString);
  }
};

// 生效范围类型变化
const onScopeTypeChange = () => {
  if (formData.scopeType === "company") {
    formData.effectiveScope = ["全公司"];
  }
  if (formData.scopeType === "region") {
    formData.effectiveScope = [];
  }
  if (formData.scopeType === "dealer") {
    formData.effectiveScope = [];
  }
};

// 产品搜索过滤
const filterProductOption = (input, option) => {
  return option.label.toLowerCase().includes(input.toLowerCase());
};

// 大区搜索过滤
const filterRegionOption = (input, option) => {
  return option.label.toLowerCase().includes(input.toLowerCase());
};

// 经销区搜索过滤
const filterDealerOption = (input, option) => {
  return option.label.toLowerCase().includes(input.toLowerCase());
};

// 获取已分配积分
const fetchAllocatedPoints = async month => {
  try {
    const params = {
      grade: userInfo.value.Role_name === "大区总" ? "大区" : "公司",
      regional_name: regionOptions.value[0].label,
      year: month.split("-")[0],
      month: month.split("-")[1],
    };
    const { data } = await getMonthIntegralApi(params);

    allocatedPoints.value = data.result[1][0].total_max_score ?? 0;
    maxMonthlyPoints.value = data.result[0][0].total_monthly_ticket_max ?? 0;
  } catch (error) {
    allocatedPoints.value = 0;
    maxMonthlyPoints.value = 0;
    console.error("获取已分配积分失败", error);
  }
};

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validateFields();

    if (formData.maxExchangeValue > remainingPoints.value) {
      message.error("最大兑换值不能超过剩余可设置积分");
      return;
    }

    submitting.value = true;

    // 构建提交数据
    const submitData = {
      serial_number: "GZ-" + new Date().getTime(), //序号
      validity_date: formData.validMonth, //有效期
      remaining_points: remainingPoints.value, //剩余可设置积分
      product: formData.productId, //产品
      max_exchange_value: formData.maxExchangeValue, //最大兑换值
      scope_of_effectiveness: "", //生效范围
      scope_of_effectiveness_region: "", //生效范围(大区)
      scope_of_effectiveness_area: "", //生效范围(经销区)
      create_user_name: userInfo.value.id, //创建人
    };

    if (formData.scopeType === "company") {
      submitData.scope_of_effectiveness = formData.effectiveScope.join(",");
    }
    if (formData.scopeType === "region") {
      submitData.scope_of_effectiveness_region =
        formData.effectiveScope.join(",");
    }
    if (formData.scopeType === "dealer") {
      submitData.scope_of_effectiveness_area =
        formData.effectiveScope.join(",");
    }

    console.log("submitData", submitData);
    await submitExchangeRuleApi(submitData);
    message.success("清分券兑换规则创建成功");

    uni.navigateBack();
  } catch (error) {
    console.log("error", error);
    if (error.errorFields) {
      message.error("请检查表单数据");
    }
  } finally {
    submitting.value = false;
  }
};

// 重置表单
const handleReset = () => {
  formRef.value.resetFields();
  Object.assign(formData, {
    validMonth: dayjs().add(1, "month").format("YYYY-MM"), // 默认选择下个月
    productId: null,
    maxExchangeValue: null,
    scopeType: "company",
    effectiveScope: [],
  });
  allocatedPoints.value = 0;
};

// 取消操作
const handleCancel = () => {
  if (
    Object.values(formData).some(
      value =>
        value &&
        value !== "company" &&
        (!Array.isArray(value) || value.length > 0)
    )
  ) {
    Modal.confirm({
      title: "确认取消",
      content: "当前有未保存的数据，确认要取消吗？",
      onOk() {
        uni.navigateBack();
      },
    });
  } else {
    uni.navigateBack();
  }
};

// 初始化数据加载
const initializeData = async () => {
  //   如果用户角色是大区总，修改生效范围默认显示选择类型
  if (userInfo.value?.Role_name === "大区总") {
    formData.scopeType = "region";
  }

  // 加载默认月份的积分数据
  await fetchAllocatedPoints(formData.validMonth);
};

// 组件挂载
onMounted(async () => {
  userInfo.value = JSON.parse(sessionStorage.getItem("userInfo"));
  await getAllRegions();
  await initializeData();
  getAllProducts();

  // 只有大区总进来的时候需要获取经销区列表
  if (userInfo.value?.Role_name === "大区总") {
    getAllDealers();
  }
});
</script>

<style scoped>
.exchange-rules-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 16px;
}

.exchange-form {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
}

.mobile-form {
  padding: 16px;
}

.remaining-points {
  padding: 16px;
  background: #f6f8fa;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
}

.points-detail {
  margin-top: 8px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-text {
  font-size: 12px;
  color: #666;
}

.scope-selection {
  width: 100%;
}

.scope-content {
  width: 100%;
}

.form-actions {
  background: white;
  padding: 16px 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .exchange-rules-container {
    padding: 8px;
  }

  .exchange-form {
    padding: 16px;
    margin-bottom: 8px;
  }

  .form-actions {
    padding: 12px 16px;
  }

  .points-detail {
    flex-direction: column;
  }

  .remaining-points {
    padding: 12px;
  }
}

/* antd组件样式重写 */
:deep(.ant-form-item-label) {
  font-weight: 500;
}

:deep(.ant-select-multiple .ant-select-selection-item) {
  background: #e6f7ff;
  border: 1px solid #91d5ff;
}

:deep(.ant-input-number) {
  width: 100%;
}

/* 响应式字体 */
@media (max-width: 480px) {
  :deep(.ant-form-item-label > label) {
    font-size: 14px;
  }
}
</style>
