<template>
  <view class="list-container">
    <view style="padding: 10px">
      <u-button
        type="primary"
        style="border-radius: 6px; font-size: 20px"
        @click="toAdd"
      >
        新增
      </u-button>
    </view>
    <view class="search-container">
      <u-search
        placeholder="请输入关键字..."
        v-model="keyword"
        @search="keywordSearch"
        @clear="keywordClear"
        @custom="keywordSearch"
      ></u-search>
    </view>

    <view class="card-list">
      <article
        v-for="item in filteredList"
        :key="item.id"
        class="list-item"
        @mouseenter="hoverItem = item.id"
        @click="toDetail(item)"
        @mouseleave="hoverItem = null"
      >
        <div class="item-header">
          <div class="item-meta" v-if="item.initiator">
            {{ item.initiator }}发起
          </div>
          <div class="item-meta" v-if="!item.initiator"></div>
          <text class="item-meta">{{ item.approve_state }}</text>
        </div>
        <div class="item-header">
          <h2 class="item-title" style="width: 50%">
            清分类型：
            <text class="item-meta" style="font-size: 1.05rem">
              {{ item.clean_type }}
            </text>
          </h2>
          <h2 class="item-title" style="width: 50%">
            经销区：
            <text class="item-meta" style="font-size: 1.05rem">
              {{ item.area_name }}
            </text>
          </h2>
        </div>
        <div class="item-header">
          <h2 class="item-title" style="width: 50%">
            开始时间：
            <text class="item-meta" style="font-size: 1.05rem">
              {{ item.start_time }}
            </text>
          </h2>
          <h2 class="item-title" style="width: 50%">
            结束时间：
            <text class="item-meta" style="font-size: 1.05rem">
              {{ item.end_time }}
            </text>
          </h2>
        </div>
      </article>
    </view>
    <!-- 加载提示 -->
    <view v-if="isLoading" style="text-align: center; padding: 10px"
      >正在加载更多数据...</view
    >
    <view
      v-if="limitValue == '加载完成'"
      style="text-align: center; padding: 10px"
      >没有更多数据了</view
    >
  </view>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { onShow, onLoad, onReachBottom } from "@dcloudio/uni-app";
import { getQfListApi } from "/api/workbench/qfType/index.js";
import { message } from "ant-design-vue";
import dayjs from "dayjs";

const filteredList = ref([]);
const hasMore = ref(true);
const isLoading = ref(false);

const keyword = ref(null);
const limit = ref(0);
const limitValue = ref("加载更多");

const keywordSearch = async (e) => {
  console.log("搜索", e);

  if (e) {
    keyword.value = e;
  } else {
    keyword.value = null;
  }
  limit.value = 0;
  filteredList.value = [];
  limitValue.value = "加载更多";
  isLoading.value = true;
  await getList();
};
const keywordClear = async () => {
  console.log("清空");
  keyword.value = null;
  isLoading.value = true;
  await getList();
};

//获取列表数据
const getList = async () => {
  const res = await getQfListApi(limit.value, keyword.value);
  console.log(res);
  if (res.data.code == 200) {
    if (res.data.result[0].length < 10) {
      limitValue.value = "加载完成";
    }
    if (res.data.result[0] && res.data.result[0].length > 0) {
      filteredList.value = res.data.result[0].map((item) => ({
        ...item,
        start_time: item.start_time
          ? dayjs(item.start_time)
              .subtract(8, "h")
              .format("YYYY-MM-DD HH:mm:ss")
          : "",
        end_time: item.end_time
          ? dayjs(item.end_time).subtract(8, "h").format("YYYY-MM-DD HH:mm:ss")
          : "",
        approve_time: item.approve_time
          ? dayjs(item.approve_time)
              .subtract(8, "h")
              .format("YYYY-MM-DD HH:mm:ss")
          : "",
      }));
      console.log("列表数据", filteredList.value);
    }
    isLoading.value = false;
  } else {
    message.error("获取列表信息失败");
    isLoading.value = false;
  }
};

onShow(async () => {
  try {
    console.log("加载");
    isLoading.value = true;
    limit.value = 0;
    filteredList.value = [];
    limitValue.value = "加载更多";
    await getList();
  } catch (error) {
    isLoading.value = false;
    message.error("获取列表信息失败");
    console.error(error);
  }
});

// 加载更多数据
const getDatalist = async () => {
  try {
    isLoading.value = true;
    const res = await getQfListApi(limit.value, keyword.value);
    // console.log('数据',res.data.result);
    if (res.data.code == 200) {
      if (res.data.result[0] && res.data.result[0].length > 0) {
        if (res.data.result[0].length < 10) {
          limitValue.value = "加载完成";
        }
        const lists = res.data.result[0].map((item) => ({
          ...item,
          start_time: item.start_time
            ? dayjs(item.start_time)
                .subtract(8, "h")
                .format("YYYY-MM-DD HH:mm:ss")
            : "",
          end_time: item.end_time
            ? dayjs(item.end_time)
                .subtract(8, "h")
                .format("YYYY-MM-DD HH:mm:ss")
            : "",
          approve_time: item.approve_time
            ? dayjs(item.approve_time)
                .subtract(8, "h")
                .format("YYYY-MM-DD HH:mm:ss")
            : "",
        }));

        filteredList.value = filteredList.value.concat(lists);
        console.log("列表数据", filteredList.value);
      }
      isLoading.value = false;
    } else {
      isLoading.value = false;
      message.error("获取列表信息失败");
    }
  } catch (error) {
    isLoading.value = false;
    message.error("获取列表信息失败");
    console.error(error);
  }
};

// 页面加载时首次请求数据
onMounted(() => {
  // loadMoreData();
});
// 页面触底时自动触发
onReachBottom(() => {
  console.log("limitValue.value", limitValue.value);
  if (limitValue.value != "加载完成") {
    limit.value = Number(limit.value) + 10;
    getDatalist();
  }
});

// 进入新增页面
const toAdd = () => {
  uni.redirectTo({
    url: `/pages/workbench/QfType/add`,
  });
};

// 进入详情页
const toDetail = (item) => {
  const data = JSON.stringify(item);
  uni.redirectTo({
    url: `/pages/workbench/QfType/add?type=detail&data=${data}`,
  });
};
</script>

<style lang="scss" scoped>
.list-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 1rem;
  // height: calc(100vh - 4rem);
  box-sizing: border-box !important;
  // overflow: hidden;
  display: flex;
  flex-direction: column;
  .search-container {
    padding: 1rem;
    display: flex;
    align-items: center;
  }

  .card-list {
    display: grid;
    gap: 1.5rem;
    grid-template-columns: repeat(auto-fill, minmax(500px, 1fr));
    flex: 1;
    overflow-y: scroll;
    padding: 20px 10px;
    .list-item {
      height: max-content;
      background: #fff;
      border-radius: 12px;
      padding: 1.5rem;
      box-shadow: 0 1px 14px rgba(0, 0, 0, 0.2);
      transition: transform 0.2s ease;

      .item-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
        .item-title {
          font-size: 1.05rem;
          color: #2c3e50;
        }
        .item-meta {
          font-size: 0.875rem;
          color: #7f8c8d;
        }
      }
    }

    .list-item:hover {
      transform: translateY(-4px);
    }
  }
}
</style>
