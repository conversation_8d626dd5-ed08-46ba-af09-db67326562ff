<template>
	<div class="list-container">
		<div>
			<!-- <button @click="handleAdd()">新增</button> -->
		</div>
		<!-- 加载状态 -->
		<div v-if="loading" class="loading-indicator">
			<i class="fas fa-spinner fa-spin"></i> 数据加载中...
		</div>

		<!-- 数据列表 -->
		<div v-else class="card-list">
			<article v-for="item in items" :key="item.id" class="list-item" @mouseenter="hoverItem = item.id"
				@mouseleave="hoverItem = null">
				<div class="item-header">
					<h2 class="item-title">{{ item.Organization_Name }}</h2>
					<span class="item-meta">{{ item.Organization_Code }}</span>
				</div>

				<!-- <p class="item-content">{{ truncateText(item.content, 100) }}</p> -->
				<p class="item-content">组织级别：{{ item.Organization_level }}</p>
				<p class="item-content">负责人：{{ item.Master_data_person_name }}</p>
				<p class="item-content">关联经销区：</p>
				<article v-for="item1 in item.list" :key="item1.id" class="list-item">
					<!-- <div class="item-header">
						<h2 class="item-title">{{ item1.Organization_Name }}</h2>
						<span class="item-meta">{{ item1.Organization_Code }}</span>
					</div> -->

					<!-- <p class="item-content">{{ truncateText(item.content, 100) }}</p> -->
					<p class="item-content">
						<span class="org-name">{{ item1.Organization_Name }}</span>
						<span class="person-name">负责人：{{ item1.Master_data_person_name }}</span>
					</p>


				</article>
				<div class="item-actions" :class="{ 'visible': hoverItem === item.id }">
<!--					<button @click="handleEditjxq(item)">-->
<!--						<i class="fas fa-edit"></i> 关联经销区-->
<!--					</button>-->
<!--					<button @click="handleEditsqz(item)">-->
<!--						<i class="fas fa-edit"></i> 关联省区总-->
<!--					</button>-->
<!--					<button @click="handleDelete(item.id)">-->
<!--						<i class="fas fa-trash"></i> 删除-->
<!--					</button>-->
				</div>
			</article>
		</div>
		<u-picker :show="userListShow" :columns="userListPerson" title="选择经销区进行关联" @confirm="confirmUser"
			@cancel="cancelUser"></u-picker>
		<u-picker :show="sqzListShow" :columns="sqzListPerson" title="选择省区总" @confirm="confirmsqz"
			@cancel="cancelsqz"></u-picker>
	</div>
</template>

<script setup>
	import {
		ref,
		computed
	} from 'vue'
	import {
		get_province,
		get_Distribution,
		Province_Distribution,
		get_user_sqz,
		Province_Person,
		delete_sq
	} from "/api/workbench/organization/index.js";
	import {
		onShow
	} from "@dcloudio/uni-app";
	import {
		Item
	} from 'ant-design-vue/es/menu';
	import { useUserInfo } from "/store/user/userInfo";
	const store = useUserInfo();
	// 响应式数据
	const items = ref([{
			id: 1,
			title: 'Vue 3 新特性解析',
			content: 'Composition API、性能优化、TypeScript支持等核心改进...',
			timestamp: new Date('2025-03-20')
		},
		// 其他数据项...
	])

	const loading = ref(false)
	const hoverItem = ref(null)

	// 计算属性
	const sortedItems = computed(() => {
		return [...items.value].sort((a, b) => b.timestamp - a.timestamp)
	})

	// 方法定义
	onShow(() => {
		getTrainmasterList();
	});

	async function getTrainmasterList() {
		console.log('store',store.userInfo.id);
		
		let res = await get_province(store.userInfo.id);
		console.log('res', res);
		items.value = res.data.result;

	}


	const handleConfirm = (selected) => {
		selectedOptions.value = selected;
	};
	const truncateText = (text, maxLength) =>
		text.length > maxLength ? text.substring(0, maxLength) + '...' : text

	const handleAdd = () => {
		uni.navigateTo({
			url: `/pages/workbench/organization/sqgljxq/addSq`,
		});
	}
	const userListShow = ref(false);
	const userList = ref([]);
	const userListPerson = ref([]);
	const sq = ref();
	const jxq = ref();
	const handleEditjxq = async (item) => {
		console.log('item', item);
		sq.value = item.id
		userListShow.value = true
		let res = await get_Distribution(item.id,store.userInfo.id);
		console.log('res', res);
		userList.value = res.data.result
		userListPerson.value = [
			res.data.result.map(
				item => item.Organization_Name
			),
		];
	}
	const handleDelete =async (id) => {
		// items.value = items.value.filter(item => item.id !== id)
		uni.showModal({
			title: "温馨提示",
			content: "您确定要删除该省区，删除后会解绑该省区下的经销区和省区总",

			success:async function(res) {

				if (res.confirm) {
					let res = await delete_sq(id)
					if (res.data.code == '200') {
						uni.showToast({
							title: "删除成功",
							icon: "none",
							duration: 2000,
						});
						getTrainmasterList();
					}

				} else if (res.cancel) {
					console.log('否');
				}
			},
		});
	}

	const confirmUser = async value => {
		console.log(value);
		console.log(value.value[0]);
		let info = userList.value.filter(item => {
			return item.Organization_Name == value.value[0]
		})
		console.log(info);
		jxq.value = info[0].id
		let data = {
			sq: sq.value,
			jxq: jxq.value
		}
		let res = await Province_Distribution(data)
		if (res.data.code == '200') {
			uni.showToast({
				title: "关联成功",
				icon: "none",
				duration: 2000,
			});
			getTrainmasterList();
		}
		userListShow.value = false;
	}
	const cancelUser = () => {
		userListShow.value = false;

	};

	const sqzListShow = ref(false);
	const sqzList = ref([]);
	const sqzListPerson = ref([]);

	const sqz = ref();
	const handleEditsqz = async (item) => {
		console.log('item', item);
		sq.value = item.id
		sqzListShow.value = true
		let res = await get_user_sqz(item.id);
		console.log('res', res);
		sqzList.value = res.data.result
		sqzListPerson.value = [
			res.data.result.map(
				item => item.Master_data_person_name
			),
		];
	}

	const confirmsqz = async value => {
		console.log(value);
		console.log(value.value[0]);
		let info = sqzList.value.filter(item => {
			return item.Master_data_person_name == value.value[0]
		})
		console.log(info);
		if(info[0].aPerson_Name != null){
			uni.showModal({
				title: "温馨提示",
				content: "该省区总已关联"+info[0].aOrganization_Name,
				success:async function(res) {
					return
				},
			});
		}else{
			sqz.value = info[0].id
			let data = {
				sq: sq.value,
				sqz: sqz.value
			}
			let res = await Province_Person(data)
			if (res.data.code == '200') {
				uni.showToast({
					title: "关联成功",
					icon: "none",
					duration: 2000,
				});
				getTrainmasterList();
			}
		}
		
		sqzListShow.value = false;
	}
	const cancelsqz = () => {
		sqzListShow.value = false;

	};
</script>

<style scoped>
	.list-container {
		max-width: 1000px;
		margin: 2rem auto;
		padding: 0 1rem;
	}

	.card-list {
		display: grid;
		gap: 1.5rem;
		grid-template-columns: repeat(auto-fill, minmax(1000px, 1fr));
	}

	.list-item {
		background: #fff;
		border-radius: 12px;
		padding: 1.5rem;
		box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
		transition: transform 0.2s ease;
	}

	.list-item:hover {
		transform: translateY(-4px);
	}

	.item-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 1rem;
	}

	.item-title {
		font-size: 1.25rem;
		color: #2c3e50;
	}

	.item-meta {
		font-size: 0.875rem;
		color: #7f8c8d;
	}

	.item-content {
		color: #34495e;
		line-height: 1.6;
		margin-bottom: 1.5rem;
	}

	.item-actions {
		opacity: 0;
		transition: opacity 0.2s ease;
		display: flex;
		gap: 0.5rem;
	}

	.item-actions.visible {
		opacity: 1;
	}

	button {
		padding: 0.5rem 1rem;
		border: none;
		border-radius: 6px;
		background: #3498db;
		color: white;
		cursor: pointer;
		transition: background 0.2s ease;
	}

	button:hover {
		background: #2980b9;
	}

	.loading-indicator {
		text-align: center;
		padding: 2rem;
		color: #7f8c8d;
	}

	.org-name {
		margin-right: 200px;
		/* 调整这个值来控制间距 */
	}
</style>