import dayjs from "dayjs";

//处理门店样式
export const processStoreStyle = (storeList) => {
  const points = storeList.map((item) => {
    if (item.Key_development === "1") {
      // 重点开拓（深蓝色）
      return {
        position: new TMap.LatLng(item.Store_Latitude, item.Store_longitude),
        id: item.id,
        styleId: "marker2",
        rank: 1,
      };
    } else if (Number(item.Store_monthly_sales > 5000)) {
      // 净销售额超过5000的用深绿色显示
      return {
        position: new TMap.LatLng(item.Store_Latitude, item.Store_longitude),
        id: item.id,
        styleId: "marker5",
        rank: 5,
      };
    } else if (
      item.Store_contribution !== null &&
      item.Store_contribution > 0 &&
      item.Cyclist_name !== null
    ) {
      // 绑定分销商贡献值>0 （绿色）
      return {
        position: new TMap.LatLng(item.Store_Latitude, item.Store_longitude),
        id: item.id,
        styleId: "marker4",
        rank: 4,
      };
    } else if (
      item.Store_contribution !== null &&
      item.Store_contribution < 0 &&
      item.Cyclist_name !== null
    ) {
      // 绑定分销商贡献值<0 （红色）
      return {
        position: new TMap.LatLng(item.Store_Latitude, item.Store_longitude),
        id: item.id,
        styleId: "marker3",
        rank: 3,
      };
    } else {
      // 其他（浅蓝色色）
      return {
        position: new TMap.LatLng(item.Store_Latitude, item.Store_longitude),
        id: item.id,
        styleId: "marker1",
        rank: 2,
      };
    }
  });
  const styles = {
    marker1: new TMap.MarkerStyle({
      width: 15, // 样式宽
      height: 15, // 样式高
      src: "../../static/icon/map/store11.png",
    }),
    marker2: new TMap.MarkerStyle({
      width: 15, // 样式宽
      height: 15, // 样式高
      src: "../../static/icon/map/store22.png",
    }),
    marker3: new TMap.MarkerStyle({
      width: 15, // 样式宽
      height: 15, // 样式高
      src: "../../static/icon/map/store33.png",
    }),
    marker4: new TMap.MarkerStyle({
      width: 15, // 样式宽
      height: 15, // 样式高
      src: "../../static/icon/map/store44.png",
    }),
    marker5: new TMap.MarkerStyle({
      width: 15, // 样式宽
      height: 15, // 样式高
      src: "../../static/icon/map/store55.png",
    }),
  };
  return { points, styles };
};

export const processTypeStoreStyle = (storeList) => {
  const points = storeList.map((item) => {
    return {
      position: new TMap.LatLng(item.Store_Latitude, item.Store_longitude),
      id: item.id,
      styleId:
        item.Channel_type !== "" &&
        item.Channel_type !== null &&
        item.Channel_type !== "None"
          ? item.Channel_type
          : "其他",
    };
  });

  return { points };
};

// 点击产品后处理门店样式
export const processProStoreStyle = (storeList) => {
  const points = storeList.map((item) => {
    if (
      Number(item.product_return_money) / Number(item.product_sale_money) >
      0.2
    ) {
      // 返货率大于20%
      return {
        position: new TMap.LatLng(item.Store_Latitude, item.Store_longitude),
        id: item.id,
        styleId: "marker4",
        rank: 5,
      };
    } else if (
      Number(item.product_sale_money) - Number(item.product_return_money) >
      1000
    ) {
      // 净销售额大于1000
      return {
        position: new TMap.LatLng(item.Store_Latitude, item.Store_longitude),
        id: item.id,
        styleId: "marker3",
        rank: 4,
      };
    } else if (Number(item.product_sale_money) > 0) {
      // 已卖货门店(橘黄)
      return {
        position: new TMap.LatLng(item.Store_Latitude, item.Store_longitude),
        id: item.id,
        styleId: "marker2",
        rank: 3,
      };
    } else if (item.Cyclist_name) {
      // 已开门店（浅绿色）
      return {
        position: new TMap.LatLng(item.Store_Latitude, item.Store_longitude),
        id: item.id,
        styleId: "marker1",
        rank: 2,
      };
    } else {
      // 其他（浅蓝色色）
      return {
        position: new TMap.LatLng(item.Store_Latitude, item.Store_longitude),
        id: item.id,
        styleId: "marker5",
        rank: 1,
      };
    }
  });
  const styles = {
    marker1: new TMap.MarkerStyle({
      width: 15, // 样式宽
      height: 15, // 样式高
      src: "../../static/icon/map/store44.png",
    }),
    marker2: new TMap.MarkerStyle({
      width: 15, // 样式宽
      height: 15, // 样式高
      src: "../../static/icon/map/store88.png",
    }),
    marker3: new TMap.MarkerStyle({
      width: 15, // 样式宽
      height: 15, // 样式高
      src: "../../static/icon/map/store99.png",
    }),
    marker4: new TMap.MarkerStyle({
      width: 15, // 样式宽
      height: 15, // 样式高
      src: "../../static/icon/map/store33.png",
    }),
    marker5: new TMap.MarkerStyle({
      width: 15, // 样式宽
      height: 15, // 样式高
      src: "../../static/icon/map/store11.png",
    }),
  };
  return { points, styles };
};
// 处理区域轮廓
export const processAreaStyle = (path, map) => {
  let polygon = new TMap.MultiPolygon({
    id: "polygon-layer", // 图层id
    map: map, // 显示多边形图层的底图
    styles: {
      // 多边形的相关样式
      polygon: new TMap.PolygonStyle({
        color: "rgba(94, 176, 230,0.5)", // 面填充色
        showBorder: false, // 是否显示拔起面的边线
        borderColor: "#00FFFF", // 边线颜色
      }),
    },
    geometries: [
      {
        id: "polygon", // 多边形图形数据的标志信息
        styleId: "polygon", // 样式id
        paths: path, // 多边形的位置信息
        properties: {
          // 多边形的属性数据
          title: "polygon",
        },
      },
    ],
  });
};

// 处理车辆信息
export const processVehicleStyle = (vehicleList) => {
  let cars = [];
  let styles = {};

  vehicleList.forEach((element) => {
    cars.push({
      id: element.imei,
      userName: element.userName ?? element.imei,
      lat: element.lat_tx,
      long: element.lng_tx,
      direction: element.gps_direction_value,
      speed: element.speed,
      acc_state: element.acc_state,
      device_record_time: element.device_record_time,
    });
  });

  const points = cars.map((item) => {
    return {
      position: new TMap.LatLng(item.lat, item.long),
      id: item.id,
      styleId: item.id,
      content: item.userName,
    };
  });

  // 获取所有的车辆样式
  cars.forEach((item) => {
    styles[item.id] = new TMap.MarkerStyle({
      width: 32, // 样式宽
      height: 30, // 样式高
      src: getChePng(item.speed, item.acc_state, item.device_record_time),
      offset: { x: 0, y: -25 },
      color: "#FFFFFF",
      strokeColor: "#000000",
      size: 18,
      rotate: Number(item.direction),
    });
  });

  function getChePng(speed, status, upTime) {
    const currentTime = dayjs();
    const currentDate = dayjs().format("YYYY-MM-DD");
    const endTime = dayjs(upTime);
    const endDate = dayjs(upTime).format("YYYY-MM-DD");
    if (endDate !== currentDate) {
      return "../../static/icon/map/che1.png";
    }
    if (status === 0 && endDate === currentDate) {
      return "../../static/icon/map/che4.png";
    }
    if (status === 1 && speed === 0) {
      return "../../static/icon/map/che3.png";
    }
    return "../../static/icon/map/che2.png";
  }
  return { points, styles };
};

export const getRecent24Months = () => {
  let currentDate = new Date();
  console.log(currentDate.getMonth() + 1);
  const months = [];

  for (let i = 0; i < 24; i++) {
    let year = currentDate.getFullYear();
    let month = currentDate.getMonth() + 1; // 月份从 0 开始，需要加 1

    if (month < 10) {
      month = "0" + month; // 如果月份小于 10，在前面补 0
    }

    // console.log(month);
    months.push(`${year}-${month}`); // 将年份和月份拼接成 YYYYMM 格式并添加到数组头部
    // console.log(months);
    currentDate = new Date(currentDate.setMonth(currentDate.getMonth() - 1)); // 获取上一个月的日期
  }
  // months.shift();

  const result = months.map((item) => {
    return {
      label: item,
      value: item,
    };
  });
  result.unshift({
    label: "近30天",
    value: "近30天",
  });
  result.push({
    label: "自定义时间",
    value: "自定义时间",
  });

  return result;
};

// 门店信息商品销货表格列名
export const storeGoodsInfoColumns = [
  {
    title: "商品名称",
    dataIndex: "name",
    key: "name",
  },
  {
    title: "商品销售额(元)",
    dataIndex: "sales",
    key: "sales",
  },
];

// 门店信息商品今日销售明细表格列名
export const storeGoodsInfoColumnsDetails = [
  {
    title: "商品名称",
    dataIndex: "name",
    key: "name",
  },
  {
    title: "商品销售额(元)",
    dataIndex: "sales",
    key: "sales",
  },
];

// 门店信息商品今日返货明细表格列名
export const storeGoodsInfoColumnsReturnDetails = [
  {
    title: "商品名称",
    dataIndex: "name",
    key: "name",
  },
  {
    title: "商品销售额(元)",
    dataIndex: "sales",
    key: "sales",
  },
];

// 门店信息商品返货表格列名
export const storeGoodsReturnInfoColumns = [
  {
    title: "商品名称",
    dataIndex: "name",
    key: "name",
  },
  {
    title: "商品返货额(元)",
    dataIndex: "sales",
    key: "sales",
  },
];

// 门店信息销售额表格列名
export const storeSalesroomColumns = [
  {
    title: "日期",
    dataIndex: "dateTime",
    key: "dateTime",
  },
  {
    title: "销货(元)",
    dataIndex: "sales",
    key: "sales",
  },
  {
    title: "返货(元)",
    dataIndex: "salesReturn",
    key: "salesReturn",
  },
  {
    title: "净值(元)",
    dataIndex: "salesMain",
    key: "salesMain",
  },
];

// 门店信息操作历史表格列名
export const storeEditHistoryColumns = [
  {
    title: "操作日期",
    dataIndex: "Edit_date",
    key: "Edit_date",
  },
  {
    title: "操作人",
    dataIndex: "Edit_by",
    key: "Edit_by",
  },
  {
    title: "更新属性",
    dataIndex: "Update_attribute",
    key: "Update_attribute",
  },
  {
    title: "更新前",
    dataIndex: "Update_beforce",
    key: "Update_beforce",
  },
  {
    title: "更新后",
    dataIndex: "Update_after",
    key: "Update_after",
  },
];

// 门店物料库存
export const storeMMIColumns = [
  {
    title: "编码",
    dataIndex: "Material_code",
    key: "Material_code",
  },
  {
    title: "物料名称",
    dataIndex: "Material_name",
    key: "Material_name",
  },
  {
    title: "数量",
    dataIndex: "Material_num",
    key: "Material_num",
  },
];

// 物料盘点明细
export const storeMMIDetailColumns = [
  {
    title: "物料名称",
    dataIndex: "Material_name",
    key: "Material_name",
  },
  {
    title: "物料单价",
    dataIndex: "Material_price",
    key: "Material_name",
  },
  {
    title: "物料数量",
    dataIndex: "Material_num",
    key: "Material_num",
  },
  {
    title: "物料金额",
    dataIndex: "Material_money",
    key: "Material_money",
  },
];

// 物料盘点明细
export const storeBasketDetailColumns = [
  {
    title: "产品名称",
    dataIndex: "Product_name",
    key: "Product_name",
  },
  {
    title: "数量",
    dataIndex: "Number",
    key: "Number",
  },
  {
    title: "单位",
    dataIndex: "Unit",
    key: "Unit",
  },
];

export const visitSituationColumns = [
  {
    title: "门店名称",
    dataIndex: "store_name",
    key: "store_name",
  },
  {
    title: "拜访人员",
    dataIndex: "visitor_name",
    key: "visitor_name",
  },
  {
    title: "计划日期",
    dataIndex: "plan_date",
    key: "plan_date",
  },
  {
    title: "是否已拜访",
    dataIndex: "has_visited",
    key: "has_visited",
  },
];

// 物料投放
export const materialDeliveryColumns = [
  {
    title: "投放门店",
    dataIndex: "Store_name",
    key: "Store_name",
  },
  {
    title: "物料名称",
    dataIndex: "Material",
    key: "Material",
  },
  {
    title: "投放数量",
    dataIndex: "Number",
    key: "Number",
  },
  {
    title: "投放时间",
    dataIndex: "Pull_date",
    key: "Pull_date",
  },
  {
    title: "投放人",
    dataIndex: "Operator",
    key: "Operator",
  },
  {
    title: "领料照片",
    dataIndex: "Voucher_photo_ll",
    key: "Voucher_photo_ll",
  },
  {
    title: "投放照片",
    dataIndex: "Voucher_photo",
    key: "Voucher_photo",
  },
];
// 物料撤回
export const materialWithdrawColumns = [
  {
    title: "撤回门店",
    dataIndex: "Store_name",
    key: "Store_name",
  },
  {
    title: "物料名称",
    dataIndex: "Material",
    key: "Material",
  },
  {
    title: "撤回数量",
    dataIndex: "Number",
    key: "Number",
  },
  {
    title: "撤回时间",
    dataIndex: "Back_date",
    key: "Back_date",
  },
  {
    title: "撤回人",
    dataIndex: "Operator",
    key: "Operator",
  },
  {
    title: "撤回照片",
    dataIndex: "Voucher_photo_ll",
    key: "Voucher_photo_ll",
  },
  {
    title: "回库照片",
    dataIndex: "Voucher_photo",
    key: "Voucher_photo",
  },
];
// 随机颜色
export const randomColor = () => {
  const r = Math.floor(Math.random() * 256);
  const g = Math.floor(Math.random() * 256);
  const b = Math.floor(Math.random() * 256);
  return `rgba(${r},${g},${b},${0.2})`;
};

// 线路选项
export const cabinetList = [
  {
    value: "全部门店",
    label: "全部门店",
  },
  {
    value: "无规划",
    label: "无规划",
  },
  {
    value: "线路一",
    label: "线路一",
  },
  {
    value: "线路二",
    label: "线路二",
  },
  {
    value: "线路三",
    label: "线路三",
  },
  {
    value: "线路四",
    label: "线路四",
  },
  {
    value: "线路五",
    label: "线路五",
  },
  {
    value: "线路六",
    label: "线路六",
  },
  {
    value: "线路七",
    label: "线路七",
  },
];
