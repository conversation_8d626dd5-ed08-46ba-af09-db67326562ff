import request from "../index";

//获取动销门店列表
export function getSalesListRecent() {
  return request({
    method: "get",
    url: `/crm/getSalesListRecent`,
  });
}

//获取动销门店列表-经销商,省区总，大区总
export function getSalesListRecentSell(jxqIDList) {
  return request({
    method: "post",
    url: `/crm/getSalesListRecent_sell`,
    data: {
      jxqIDList,
    },
  });
}

//获取动销明细详细数据
export function getSalesDetail(id) {
  return request({
    method: "get",
    url: `/crm/getSalesDetail?id=${id}`,
  });
}
