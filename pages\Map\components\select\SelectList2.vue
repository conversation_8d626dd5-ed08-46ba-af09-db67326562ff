<template>
  <a-float-button
    v-show="_floatButton"
    style="top: 20px; left: 20px"
    @click="handleClick"
  >
    <template #icon>
      <RightCircleTwoTone />
    </template>
  </a-float-button>
  <div class="select-container" ref="_selectlist">
    <!-- 显隐 -->
    <div>
      <a-button @click="cancelSelect">收起</a-button>
      <!-- <a-button @click="reset" style="margin-left: 20px" type="primary">
        重置选项
      </a-button> -->
    </div>
    <!-- 地图图层 -->
    <div class="map-coverage">
      <div style="font-weight: 700">地图控件</div>
      <div class="switch-item">
        <span style="text-align: right">区域</span>
        <a-switch
          style="width: 80rpx"
          v-model:checked="store.checkedArea"
          @change="changeChecked"
        />
        <span style="text-align: right">车辆</span>
        <a-switch
          style="width: 80rpx"
          v-model:checked="store.checkedCar"
          @change="changeChecked"
        />
      </div>
      <div class="switch-item">
        <span style="text-align: right">门店</span>
        <a-switch
          v-model:checked="store.checkedStore"
          @change="changeChecked_store('门店')"
          style="width: 80rpx"
        />
        <span style="text-align: right" v-if="userInfo.Role_grade === '决策层'">
          区域规划
        </span>
        <a-switch
          v-if="userInfo.Role_grade === '决策层'"
          v-model:checked="store.checkedAllArea"
          @change="changeChecked"
          style="width: 80rpx"
        />
      </div>
      <div class="switch-item">
        <span style="text-align: right">已开门店</span>
        <a-switch
          v-model:checked="store.checkedOpenStore"
          @change="changeChecked_store('已开门店')"
          style="width: 80rpx"
        />
        <span style="text-align: right">类型门店</span>
        <a-switch
          v-model:checked="store.checkedTypeStore"
          @change="changeChecked_store('类型门店')"
          style="width: 80rpx"
        />
      </div>
      <div class="switch-item">
        <span style="text-align: right">显示街镇</span>
        <a-switch
          v-model:checked="store.checkedOpenStreet"
          @change="changeChecked"
          style="width: 80rpx"
        />
      </div>
    </div>
    <!-- 选项 -->
    <div class="select-box">
      <!-- 大区/省区 -->
      <div>
        <div style="width: 49%; float: left">
          <span style="font-weight: 700">大区</span>
          <a-select
              v-model:value="store.selectObj.dqName"
              showSearch
              style="width: 100%; margin: 5px 0"
              :options="store.dqList"
              @select="dqSelect"
              placeholder="大区"
              :disabled="userInfo.Role_grade === '经销商' || userInfo.Role_grade === '省区总'"
          ></a-select>
        </div>
        <div style="width: 49%; float: right">
          <span style="font-weight: 700">省区</span>
          <a-select
              v-model:value="store.selectObj.sqName"
              showSearch
              style="width: 100%; margin-top: 5px"
              :options="store.sqList"
              @select="sqSelect"
              placeholder="省区"
              :disabled="userInfo.Role_grade === '经销商'"
          ></a-select>
        </div>
      </div>
      <!-- 大区/省区 -->
      <div>
        <div style="width: 49%; float: left">
          <span style="font-weight: 700">经销区</span>
          <a-select
              v-model:value="store.selectObj.jxqName"
              showSearch
              style="width: 100%; margin-top: 5px"
              :options="store.jxqList"
              @select="jxqSelect"
              placeholder="经销区"

          ></a-select>
        </div>
        <div style="width: 49%; float: right">
          <span style="font-weight: 700">行政区域</span>
          <a-select
              v-model:value="store.selectObj.xzqyName"
              showSearch
              style="width: 100%; margin-top: 5px"
              :options="store.xzqyList"
              @select="xzqySelect"
              placeholder="行政区域"

          ></a-select>
        </div>
      </div>
      <!-- 库长/车长 -->
      <div>
        <div style="width: 49%; float: left">
          <span style="font-weight: 700">库长</span>
          <a-select
            v-model:value="store.selectObj.jxsName"
            showSearch
            style="width: 100%; margin: 5px 0"
            :options="store.jxsUserList"
            @select="jxsSelect"
            placeholder="库长"
            :disabled="userInfo.Role_grade === '经销商'"
          ></a-select>
        </div>
        <div style="width: 49%; float: right">
          <span style="font-weight: 700">车长</span>
          <a-select
            v-model:value="store.selectObj.fxsName"
            showSearch
            style="width: 100%; margin-top: 5px"
            :options="store.fxsUserList"
            @select="fxsSelect"
            placeholder="车长"
            :disabled="
              store.selectObj.jxsName === null ||
              store.selectObj.jxsName === '全部'
            "
          ></a-select>
        </div>
      </div>
      <div v-if="userInfo?.Role_grade === '决策层'">
        <span style="font-weight: 700">省市区</span>
        <a-cascader
          v-model:value="store.selectObj.province"
          showSearch
          style="width: 100%"
          :options="provinceData"
          placeholder="省市区"
          @change="provincesChange"
          :allowClear="true"
        />
      </div>
      <a-select
        v-model:value="store.selectObj.optionValue"
        :options="timeOptions"
        @select="timeSelect"
      ></a-select>
      <div>
        <div style="width: 49%; float: left">
          <div style="font-weight: 700">产品类别</div>
          <a-select
            v-model:value="choose_product_category"
            showSearch
            style="width: 100%; margin: 5px 0"
            :options="store.product_list_category"
            @select="productCategorySelect"
            placeholder="产品类别"
          ></a-select>
        </div>
        <div style="width: 49%; float: right">
          <div style="font-weight: 700">产品</div>
          <a-select
            v-model:value="choose_product"
            showSearch
            style="width: 100%; margin: 5px 0"
            :options="store.product_list"
            @select="productSelect"
            placeholder="产品"
          ></a-select>
        </div>
      </div>
      <a-range-picker
        v-if="customTime"
        v-model:value="store.selectObj.dateSection"
        @change="changeDate"
      />
    </div>
    <a-button
      style="width: 100%; margin: 5px 0"
      type="primary"
      :loading="loading"
      @click="refreshData"
    >
      提交选项
    </a-button>
    <div class="area-name">{{ store.selectObj.areaName }}</div>
    <DataView
      :dataView="dataView"
      :show_data_list="show_data_list"
      @openDataModal="openDataModal"
    />
  </div>
  <DataModal
    v-if="showModal"
    v-model:showModal="showModal"
    :modalMessage="modalMessage"
    :choose_product_category_code="choose_product_category_code"
    :choose_product_code="choose_product_code"
    :product_group="product_group"
    @timeSelect="changeTime"
  />
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { RightCircleTwoTone } from "@ant-design/icons-vue";
import { useMapStore } from "../../../../store/map";
import DataModal from "../modal/DataModal";
import { getRecent24Months } from "../../data-uitls";
import { provinceData } from "../../data-uitls/provinces";
import DataView from "./DataView";
import { getDataView, totalAmountLoaded } from "../../../../api/map/data";
import dayjs from "dayjs";
import { useUserInfo } from "../../../../store/user/userInfo";

const store = useMapStore();
const userStore = useUserInfo();
const emits = defineEmits([
  "changeChecked",
  "chooseJxs",
  "chooseFxs",
  "chooseArea",
  "chooseProType",
  "choosePro",
  "chooseDq",
  "chooseSq",
  "chooseJxq",
  "chooseXzqx",
]);
const userInfo = userStore.userInfo;

const _selectlist = ref(null);
const _floatButton = ref(true);
let show_data_list = ref(true);
let choose_product = ref("全部"); // 产品
let choose_product_code = ref(""); // 产品编码
let choose_product_category = ref("全部"); // 产品类别
let choose_product_category_code = ref(""); // 产品类别编码
const product_group = ref(""); // 产品组编码
const reset = () => {};

let commitFunction = []; // 设置一个变量接收函数

const changeChecked = () => {
  // emits("changeChecked");
};

const loading = ref(false);
function refreshData() {
  emits("changeChecked");
  if (commitFunction.length !== 0) {
    loading.value = true;
    const lastFunction = commitFunction[commitFunction.length - 1]; // 执行最后一个选择函数
    lastFunction();
  }
}

const changeChecked_store = type => {
  if (type === "门店") {
    store.checkedOpenStore = false;
    store.checkedTypeStore = false;
    // emits("changeChecked");
  }
  if (type === "已开门店") {
    store.checkedStore = false;
    store.checkedTypeStore = false;
    // emits("changeChecked");
  }
  if (type === "类型门店") {
    store.checkedStore = false;
    store.checkedOpenStore = false;
    // emits("changeChecked");
  }
};

function getDqIdJxqId(dqList, jxqList) {
  let region = "";
  let area = "";
  dqList.forEach(item => {
    if (store.selectObj.dqName === "全部") {
      region = "";
    } else if (item.label === store.selectObj.dqName) {
      region = item.dqId;
    }
  });
  jxqList.forEach(item => {
    if (store.selectObj.jxqName === "全部") {
      area = "";
    } else if (item.label === store.selectObj.jxqName) {
      area = item.jxqId;
    }
  });
  if (userStore.userInfo.Role_grade === "经销商") {
    area = userStore.userInfo.fz_area_id;
  }

  if (
    userStore.userInfo.Role_grade === "大区总" ||
    userStore.userInfo.Role_grade === "省区总"
  ) {
    region = userStore.userInfo.fz_area_id;
  }

  return {
    region, //大区ID
    area, //经销区
  };
}
// 选择大区
const dqSelect = (e, v) => {
  emits("chooseDq", e, v);
  if (v.label === "全部") {
    store.selectObj.dqId = "";
  } else {
    store.selectObj.dqId = v.id;
  }
  commitFunction.push(() =>
      getData(
          store.selectObj.jxsId,
          store.selectObj.fxsId,
          thirtyDaysAgo,
          today,
          choose_product_code.value,
          choose_product_category_code.value
      )
  );
  show_data_list.value = true;
};
// 选择大区
const sqSelect = (e, v) => {
  emits("chooseSq", e, v);
  if (v.label === "全部") {
    store.selectObj.sqId = "";
  } else {
    store.selectObj.sqId = v.id;
  }
  commitFunction.push(() =>
      getData(
          store.selectObj.jxsId,
          store.selectObj.fxsId,
          thirtyDaysAgo,
          today,
          choose_product_code.value,
          choose_product_category_code.value
      )
  );
  show_data_list.value = true;
};
// 选择经销区
const jxqSelect = (e, v) => {
  emits("chooseJxq", e, v);
  if (v.label === "全部") {
    store.selectObj.jxqId = "";
  } else {
    store.selectObj.jxqId = v.id;
  }
  commitFunction.push(() =>
      getData(
          store.selectObj.jxsId,
          store.selectObj.fxsId,
          thirtyDaysAgo,
          today,
          choose_product_code.value,
          choose_product_category_code.value
      )
  );

  show_data_list.value = true;
};

const xzqySelect = (e, v) => {
  emits("chooseXzqx", e, v);
  if (v.label === "全部") {
    store.selectObj.xzqxId = "";
  } else {
    store.selectObj.xzqxId = v.id;
  }
  commitFunction.push(() =>
      getData(
          store.selectObj.jxsId,
          store.selectObj.fxsId,
          thirtyDaysAgo,
          today,
          choose_product_code.value,
          choose_product_category_code.value
      )
  );

  show_data_list.value = true;
};

// 选择库长
const jxsSelect = (e, v) => {
  emits("chooseJxs", e, v);
  if (v.label === "全部") {
    store.selectObj.jxsId = "";
    store.selectObj.fxsId = "";
  } else {
    store.selectObj.jxsId = v.jxsId;
  }
  if (store.selectObj.optionValue === "自定义时间") {
    commitFunction.push(() =>
      getData(
        store.selectObj.jxsId,
        store.selectObj.fxsId,
        date1.value,
        date2.value,
        choose_product_code.value,
        choose_product_category_code.value
      )
    );
  } else {
    commitFunction.push(() => timeSelect(store.selectObj.optionValue));
  }
  show_data_list.value = true;
};

const fxsSelect = (e, v) => {
  emits("chooseFxs", e, v);
  if (v.label === "全部") {
    store.selectObj.fxsId = "";
  } else {
    store.selectObj.fxsId = v.id;
  }
  if (store.selectObj.optionValue === "自定义时间") {
    commitFunction.push(() =>
      getData(
        store.selectObj.jxsId,
        store.selectObj.fxsId,
        date1.value,
        date2.value,
        choose_product_code.value,
        choose_product_category_code.value
      )
    );
  } else {
    commitFunction.push(() => timeSelect(store.selectObj.optionValue));
  }
};

const provincesChange = (e, v) => {
  console.log("改变", e);
  if ((e && e[0] == "") || !e) {
    commitFunction.push(() =>
      getData(
        store.selectObj.jxsId,
        store.selectObj.fxsId,
        date1.value,
        date2.value,
        choose_product_code.value,
        choose_product_category_code.value
      )
    );

    show_data_list.value = true;
  } else {
    show_data_list.value = false;
  }
  emits("chooseArea", e, v);
};

// 选择产品
const productSelect = (e, v) => {
  emits("choosePro", e, v);
  choose_product.value = v.label;
  choose_product_code.value = v.code;
  store.selectObj.product = choose_product_code.value;
  commitFunction.push(() =>
    getData(
      store.selectObj.jxsId,
      store.selectObj.fxsId,
      thirtyDaysAgo,
      today,
      choose_product_code.value,
      choose_product_category_code.value
    )
  );
};

// 选择产品类别
const productCategorySelect = (e, v) => {
  emits("chooseProType", e, v);
  console.log("选择产品类别", e, v);
  choose_product_category.value = v.label;
  choose_product_category_code.value = v.code;
  product_group.value = v.groupId;
  choose_product.value = "全部";
  choose_product_code.value = "";
  store.selectObj.productClass = choose_product_category_code.value;
  store.selectObj.product = choose_product_code.value;
  store.selectObj.productGroup = product_group.value;
  store.getAllProduct(choose_product_category.value);
  commitFunction.push(() =>
    getData(
      store.selectObj.jxsId,
      store.selectObj.fxsId,
      thirtyDaysAgo,
      today,
      choose_product_code.value,
      choose_product_category_code.value,
      product_group.value
    )
  );
};

const showModal = ref(false);
const modalMessage = ref({
  title: null,
});

async function openDataModal(name) {
  modalMessage.value.title = name;
  store.selectObj.start_date = null;
  store.selectObj.end_date = null;
  showModal.value = true;
}

const timeOptions = getRecent24Months();
const customTime = computed(() => {
  return store.selectObj.optionValue === "自定义时间";
});

function changeTime(e) {
  commitFunction.push(() => timeSelect(e));
}
async function timeSelect(e) {
  store.selectObj.dateSection = null;
  if (e === "自定义时间") {
    date1.value = dayjs().subtract(30, "day").format("YYYY-MM-DD");
    date2.value = dayjs().format("YYYY-MM-DD");
  } else if (e === "近30天") {
    dataView.value = null;
    const today = dayjs().format("YYYY-MM-DD");
    const thirtyDaysAgo = dayjs().subtract(30, "day").format("YYYY-MM-DD");
    console.log("11123123");

    getData(
      store.selectObj.jxsId,
      store.selectObj.fxsId,
      thirtyDaysAgo,
      today,
      choose_product_code.value,
      choose_product_category_code.value
    );
  } else {
    dataView.value = null;
    const startDate = e + "-01";
    const endDate = dayjs(startDate).add(30, "day").format("YYYY-MM-DD");
    getData(
      store.selectObj.jxsId,
      store.selectObj.fxsId,
      startDate,
      endDate,
      choose_product_code.value,
      choose_product_category_code.value
    );
  }
}

const date1 = ref("");
const date2 = ref("");

async function changeDate(e) {
  date1.value = dayjs(e[0]).format("YYYY-MM-DD");
  date2.value = dayjs(e[1]).format("YYYY-MM-DD");
  getData(
    store.selectObj.jxsId,
    store.selectObj.fxsId,
    date1.value,
    date2.value,
    choose_product.value,
    choose_product_category.value
  );
}

const dataView = ref(null);

async function getData(
  dealer,
  retailer,
  start_date,
  end_date,
  product,
  product_class,
  product_group
) {
  let id = getDqIdJxqId(store.dqList, store.jxqList);
  const params = {
    dealer, //经销商ID
    retailer,
    start_date,
    end_date,
    product,
    product_class,
    product_group,
    ...id,
  };
  const res = await getDataView(params);

  const res2 = await totalAmountLoaded(params);

  dataView.value = res.data.result[0];
  dataView.value.total_normal_delivery =
    res2.data.result[0].total_normal_delivery;
  dataView.value.total_normal_return = Number(
    res2.data.result[0].total_normal_return
  ).toFixed(2);
  dataView.value.ratio = res2.data.result[0].ratio * 100;
  loading.value = false;
}

const today = dayjs().format("YYYY-MM-DD");
const thirtyDaysAgo = dayjs().subtract(30, "day").format("YYYY-MM-DD");
setTimeout(() => {
  if (userStore.userInfo.Role_grade === "决策层") {
    getData(
      "",
      "",
      thirtyDaysAgo,
      today,
      choose_product_code.value,
      choose_product_category_code.value
    );
  }
  if (userStore.userInfo.Role_grade === "经销商") {
    getData(
      // userStore.userInfo.id,
      "",
      "",
      thirtyDaysAgo,
      today,
      choose_product_code.value,
      choose_product_category_code.value
    );
  }
  if (
    userStore.userInfo.Role_grade === "大区总" ||
    userStore.userInfo.Role_grade === "省区总"
  ) {
    getData(
      "",
      "",
      thirtyDaysAgo,
      today,
      choose_product_code.value,
      choose_product_category_code.value
    );
  }
}, 500);

const handleClick = () => {
  _selectlist.value.style.left = "20" + "px";
  _selectlist.value.style.opacity = 1;
  _floatButton.value = false;
};
const cancelSelect = () => {
  _selectlist.value.style.left = "-280" + "px";
  _selectlist.value.style.opacity = 0;
  _floatButton.value = true;
};

onMounted(() => {
  store.getProductCategory();
});
</script>

<style lang="scss" scoped>
.select-container {
  position: absolute;
  padding: 20px;
  max-height: calc(100vh - 120px);
  background: #fff;
  border-radius: 10px;
  top: 20px;
  left: -280px;
  z-index: 2;
  opacity: 0;
  transition: 0.3s;
  overflow-y: auto;

  .select-box {
    margin-top: 15px;
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .area-name {
    font-weight: 700;
    padding: 10px;
    margin-top: 10px;
    background-color: #d3e3fd;
    text-align: center;
  }
  .map-coverage {
    margin-top: 10px;
    display: flex;
    flex-direction: column;
    gap: 5px;
    .switch-item {
      display: grid;
      grid-template-columns: repeat(4, minmax(0, 1fr));
      gap: 15rpx;
    }
  }
}
</style>
