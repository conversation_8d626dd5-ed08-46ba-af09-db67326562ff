import request from "../index";

// 获取销售指标分类
export function getTargetClassify() {
  return request({
    method: "get",
    url: "/get/target_classify",
  });
}

// 获取指标列表
export function getTargetList() {
  return request({
    method: "get",
    url: "/get/target_list",
  });
}

// 获取数字数据名称
export function getNumberDataName(classify) {
  return request({
    method: "get",
    url: `/get/number_data_name?target_list_name=${classify}`,
  });
}

// 动态获取报表图表数据
export function dynamic_get_chart_date(date_url, year, month, area_id) {
  return request({
    method: "post",
    url: `/from_other/${date_url}`,
    data: {
      year,
      month,
      area_id,
    },
  });
}

// 获取图表列表
export function getChartsList(report_name, area_id) {
  return request({
    method: "get",
    url: `/get/charts_list?report_name=${report_name}&area_id=${area_id}`,
  });
}
