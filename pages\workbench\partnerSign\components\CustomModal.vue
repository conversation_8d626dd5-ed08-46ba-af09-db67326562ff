<template>
  <a-modal
    :open="show"
    :title="title"
    :width="width"
    :footer="null"
    @cancel="handleClose"
    :mask-closable="closeOnOverlay"
    :destroy-on-close="true"
  >
    <div class="modal-content">
      <slot></slot>
    </div>

    <template #footer v-if="showFooter">
      <div class="modal-footer">
        <a-button @click="handleClose">
          取消
        </a-button>
        <a-button type="primary" @click="handleConfirm">
          确定
        </a-button>
      </div>
    </template>
  </a-modal>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '提示'
  },
  width: {
    type: [String, Number],
    default: 600
  },
  showFooter: {
    type: Boolean,
    default: false
  },
  closeOnOverlay: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['close', 'confirm'])

const handleClose = () => {
  emit('close')
}

const handleConfirm = () => {
  emit('confirm')
}

const handleOverlayClick = () => {
  if (props.closeOnOverlay) {
    handleClose()
  }
}
</script>

<style lang="scss" scoped>
.modal-content {
  max-height: 60vh;
  overflow-y: auto;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 16px;
}

// 自定义Ant Design Modal样式
:deep(.ant-modal) {
  .ant-modal-header {
    border-bottom: 1px solid #f0f0f0;
    padding: 16px 24px;

    .ant-modal-title {
      font-size: 16px;
      font-weight: 600;
      color: #262626;
    }
  }

  .ant-modal-body {
    padding: 24px;
  }

  .ant-modal-close {
    top: 16px;
    right: 16px;

    .ant-modal-close-x {
      width: 22px;
      height: 22px;
      line-height: 22px;
      font-size: 16px;
      color: #8c8c8c;

      &:hover {
        color: #262626;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  :deep(.ant-modal) {
    margin: 0 16px;
    max-width: calc(100vw - 32px);

    .ant-modal-header {
      padding: 12px 16px;

      .ant-modal-title {
        font-size: 14px;
      }
    }

    .ant-modal-body {
      padding: 16px;
    }
  }
}
</style>
