<template>
  <view class="container">
    <img src="../../static/logo.png" alt="" style="height: 300rpx" />
    <view style="font-weight: 700; font-size: 50rpx"> 现春BI报表平台 </view>
    <view style="width: 300rpx">
      <u-button
        :text="text"
        :loading="loading"
        loadingText="登录中..."
        @click="loginForm"
        color="#009900"
      ></u-button>
    </view>
    <view class="version">
      <p style="font-size: 32rpx; color: #999999">版本号：2025-07-23</p>
    </view>
  </view>
</template>

<script setup>
import { ref } from "vue";

const text = ref("进入应用");
const loading = ref(false);

const loginForm = async () => {
  loading.value = true;
  try {
    uni.reLaunch({
      url: "/pages/Index/index",
    });
  } catch (err) {
    uni.showToast({
      title: "登录失败",
      icon: "none",
      duration: 2000,
    });
    loading.value = false;
  }
};
</script>

<style lang="scss" scoped>
.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  justify-content: center;
  align-items: center;

  .title-png {
    height: 150rpx;
    width: 150rpx;
    border-radius: 50%;
    font-size: 48rpx;
    font-weight: 700;
    line-height: 150rpx;
    text-align: center;
    background-color: aqua;
  }

  button[type="primary"] {
    background-color: #009900;
  }

  .login-from {
    display: flex;
    flex-direction: column;
    gap: 20rpx;
  }

  .version {
    display: flex;
    align-items: center;
    flex-direction: column;
    gap: 15rpx;
    // justify-content: center;
    position: fixed;
    bottom: 0;
    margin-bottom: 50rpx;
  }
}
</style>
