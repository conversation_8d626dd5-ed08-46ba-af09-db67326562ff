<template>
  <div class="container">
    <a-card title="经销商区域管理" class="main-card">
      <!-- 搜索和操作区域 -->
      <div class="header-actions">
        <div class="search-section">
          <a-input-search
            v-model:value="searchQuery"
            placeholder="搜索经销区名称..."
            enter-button="搜索"
            size="large"
            allow-clear
            @search="handleSearch"
            @change="handleSearchChange"
            style="width: 300px"
          />
        </div>
        <div class="action-section">
          <a-button
            type="primary"
            size="large"
            @click="handleAdd"
          >
            <template #icon>
              <plus-outlined />
            </template>
            新增经销区
          </a-button>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <a-spin size="large" tip="数据加载中...">
          <div class="loading-content"></div>
        </a-spin>
      </div>

      <!-- 数据列表 -->
      <div v-else class="data-list">
        <a-row :gutter="[16, 16]">
          <a-col
            v-for="item in filteredItems"
            :key="item.id"
            :xs="24"
            :sm="24"
            :md="12"
            :lg="12"
            :xl="8"
          >
            <a-card
              class="item-card"
              :hoverable="true"
              size="small"
            >
              <template #title>
                <div class="card-title">
                  <!-- 编辑状态 -->
                  <div v-if="editingItemId === item.id" class="title-edit-container">
                    <a-input
                      ref="editInput"
                      v-model:value="editingName"
                      class="title-edit-input"
                      :maxlength="50"
                      @blur="handleEditBlur"
                      @keyup.esc="handleEditCancel"
                      :loading="isUpdating"
                    />
                  </div>
                  <!-- 正常显示状态 -->
                  <div v-else class="title-display-container">
                    <span class="title-text">{{ item.Organization_Name }}</span>
                  </div>
                  <a-tag color="blue">{{ item.Organization_Code }}</a-tag>
                </div>
              </template>

              <template #extra>
                <a-dropdown :trigger="['click']">
                  <a-button type="text" size="small">
                    <template #icon>
                      <more-outlined />
                    </template>
                  </a-button>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item key="1" @click="handleXZQX(item)">
                        <link-outlined />
                        关联行政区县
                      </a-menu-item>
                      <a-menu-item key="2" @click="csjlclick(item)">
                        <user-outlined />
                        关联城市经理
                      </a-menu-item>
                      <a-menu-item key="3" @click="handleEditName(item)">
                        <edit-outlined />
                        修改经销区名称
                      </a-menu-item>
                      <a-menu-divider />
                      <a-menu-item key="4" @click="handleDelete(item.id)" class="danger-item">
                        <delete-outlined />
                        删除
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </template>

              <div class="card-content">
                <div class="info-item">
                  <span class="label">组织级别：</span>
                  <span class="value">{{ item.Organization_level }}</span>
                </div>
                <div class="info-item">
                  <span class="label">负责人：</span>
                  <span class="value">{{ item.Master_data_person_name || '暂无' }}</span>
                </div>

                <!-- 关联行政区县 -->
                <div class="related-areas">
                  <div class="areas-title">关联行政区县：</div>
                  <div v-if="item.list && item.list.length > 0" class="areas-list">
                    <div
                      v-for="area in item.list"
                      :key="area.id"
                      class="area-item"
                    >
                      <a-tag
                        color="green"
                        closable
                        @close="(e) => handleGLConfirm(e, area.id)"
                        class="area-tag"
                      >
                        {{ area.name }}
                      </a-tag>
                    </div>
                  </div>
                  <div v-else class="no-areas">
                    <a-empty
                      :image="simpleImage"
                      description="暂无关联区县"
                      style="margin: 8px 0;"
                    />
                  </div>
                </div>
              </div>
            </a-card>
          </a-col>
        </a-row>
      </div>
    </a-card>
  </div>

  <!-- 城市经理选择弹窗 -->
  <CustomModal
    :show="showModal"
    title="请选择城市经理"
    :width="600"
    @close="showModal = false"
  >
    <MultiSelectListOne
      :items="allItems"
      :items2="allItems2"
      @update:confirm="confirm"
      :items-per-page="10"
      :single-select="true"
    />
  </CustomModal>

  <!-- 行政区县选择弹窗 -->
  <CustomModal
    :show="showModal3"
    title="请选择行政区县"
    :width="700"
    @close="showModal3 = false"
  >
    <MultiSelectListMore
      :items="allItems5"
      :items2="allItems6"
      @update:close="showModal3 = false"
      @update:confirm="confirm3"
      :items-per-page="10"
    />
  </CustomModal>
</template>

<script setup>
import { ref, computed, nextTick } from 'vue'
import {
  PlusOutlined,
  MoreOutlined,
  LinkOutlined,
  UserOutlined,
  DeleteOutlined,
  EditOutlined
} from '@ant-design/icons-vue'
import { Empty } from 'ant-design-vue'
import {
  get_Distribution_list,
  jxq_xzqy,
  get_csjl,
  jxq_csjl,
  deleted_jxq_xzqx,
  get_xzqx_user,
  delete_sq,
  change_jxq_name
} from "/api/workbench/organization/index.js"
import CustomModal from '@/pages/workbench/partnerSign/components/CustomModal.vue'
import MultiSelectListOne from '@/pages/workbench/partnerSign/components/MultiSelectListOne.vue'
import MultiSelectListMore from '@/pages/workbench/partnerSign/components/MultiSelectListMore.vue'
import { onShow } from "@dcloudio/uni-app"
import { useUserInfo } from "/store/user/userInfo"

const store = useUserInfo()
const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE

// 响应式数据
const items = ref([])
const loading = ref(false)
const searchQuery = ref('')

// 编辑状态相关
const editingItemId = ref(null)
const editingName = ref('')
const originalName = ref('')
const isUpdating = ref(false)

// 搜索相关
let searchTimer = null

// 计算属性 - 过滤后的数据
const filteredItems = computed(() => {
  if (!searchQuery.value.trim()) {
    return items.value
  }

  return items.value.filter(item =>
    item.Organization_Name?.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
    item.Organization_Code?.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
    item.Master_data_person_name?.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

// 搜索功能
const handleSearch = (value) => {
  searchQuery.value = value
}

const handleSearchChange = (e) => {
  const value = e.target.value

  // 清除之前的定时器
  if (searchTimer) {
    clearTimeout(searchTimer)
  }

  // 设置新的定时器，实现防抖
  searchTimer = setTimeout(() => {
    searchQuery.value = value
  }, 300)
}

// 页面初始化
onShow(async() => {
  try {
    // 确保用户信息已加载
    await ensureUserInfo()
    await getTrainmasterList()
    await loadCityManagers()
    await loadAdministrativeAreas()
  } catch (error) {
    console.error('页面初始化失败:', error)
    uni.showToast({
      title: "页面初始化失败",
      icon: "none",
      duration: 2000,
    })
  }
})

// 确保用户信息已加载
const ensureUserInfo = async () => {
  if (!store.userInfo || !store.userInfo.id) {
    const userInfo = sessionStorage.getItem("userInfo")
    if (userInfo) {
      try {
        store.setUserInfo(JSON.parse(userInfo))
      } catch (error) {
        console.error('解析用户信息失败:', error)
        // 如果解析失败，跳转到登录页
        uni.reLaunch({
          url: "/pages/Login/WebLogin"
        })
        throw new Error('用户信息无效')
      }
    } else {
      // 没有用户信息，跳转到登录页
      uni.reLaunch({
        url: "/pages/Login/WebLogin"
      })
      throw new Error('用户未登录')
    }
  }
}

// 获取经销区列表
async function getTrainmasterList() {
  try {
    loading.value = true
    let res = await get_Distribution_list(store.userInfo.id)
    console.log('res', res)
    items.value = res.data.result || []
  } catch (error) {
    console.error('获取经销区列表失败:', error)
    uni.showToast({
      title: "获取数据失败",
      icon: "none",
      duration: 2000,
    })
  } finally {
    loading.value = false
  }
}

// 加载城市经理数据
async function loadCityManagers() {
  try {
    let res = await get_csjl(store.userInfo.id)
    allItems.value = []
    for (var i = 0; i < res.data.result.length; i++) {
      allItems.value.push({
        value: res.data.result[i].id,
        label: res.data.result[i].Master_data_person_name,
        label2: res.data.result[i].Master_data_person_code,
        label3: res.data.result[i].Role_name,
        radio: 0
      })
    }
  } catch (error) {
    console.error('加载城市经理数据失败:', error)
  }
}

// 加载行政区域数据
async function loadAdministrativeAreas() {
  try {
    let res3 = await get_xzqx_user(store.userInfo.id)
    allItems5.value = []
    for (var i = 0; i < res3.data.result.length; i++) {
      if(res3.data.result[i].name){
        allItems5.value.push({
          value: res3.data.result[i].id,
          label: res3.data.result[i].name,
          radio: 0
        })
      }
    }
  } catch (error) {
    console.error('加载行政区域数据失败:', error)
  }
}

// 新增经销区
const handleAdd = () => {
  uni.navigateTo({
    url: `/pages/workbench/organization/jxq/addJXQ`,
  })
}

// 开始编辑经销区名称
const handleEditName = (item) => {
  // 确保同时只能编辑一个
  if (editingItemId.value !== null) {
    handleEditCancel()
  }

  editingItemId.value = item.id
  editingName.value = item.Organization_Name
  originalName.value = item.Organization_Name

  // 下一帧聚焦并选中文本
  nextTick(() => {
    const input = document.querySelector('.title-edit-input input')
    if (input) {
      input.focus()
      input.select()
    }
  })
}

// 取消编辑
const handleEditCancel = () => {
  editingItemId.value = null
  editingName.value = ''
  originalName.value = ''
  isUpdating.value = false
}

// 处理输入框失焦
const handleEditBlur = () => {
  // 如果内容没有变化，直接退出编辑模式
  if (editingName.value.trim() === originalName.value.trim()) {
    handleEditCancel()
    return
  }

  // 验证输入内容
  if (!editingName.value.trim()) {
    uni.showToast({
      title: "经销区名称不能为空",
      icon: "none",
      duration: 2000,
    })
    // 恢复原始值
    editingName.value = originalName.value
    return
  }

  // 显示确认气泡
  uni.showModal({
    title: "确认修改",
    content: "确定要修改经销区名称吗？",
    success: async function(res) {
      if (res.confirm) {
        await handleConfirmEdit()
      } else {
        // 用户取消，恢复原始值
        editingName.value = originalName.value
        handleEditCancel()
      }
    }
  })
}

// 确认修改
const handleConfirmEdit = async () => {
  if (isUpdating.value) return

  try {
    isUpdating.value = true

    const data = {
      id: editingItemId.value,
      Organization_Name: editingName.value.trim()
    }

    const response = await change_jxq_name(data)

    if (response.data.code === 200) {
      uni.showToast({
        title: "修改成功",
        icon: "success",
        duration: 2000,
      })

      // 刷新数据
      await getTrainmasterList()
      handleEditCancel()
    } else {
      uni.showToast({
        title: response.data.message || "修改失败",
        icon: "none",
        duration: 2000,
      })
      // 恢复原始值
      editingName.value = originalName.value
    }
  } catch (error) {
    console.error('修改经销区名称失败:', error)
    uni.showToast({
      title: "修改失败，请重试",
      icon: "none",
      duration: 2000,
    })
    // 恢复原始值
    editingName.value = originalName.value
  } finally {
    isUpdating.value = false
  }
}
// 删除经销区
const handleDelete = async (id) => {
  uni.showModal({
    title: "温馨提示",
    content: "您确定要删除该经销区吗？删除后会解绑该经销区下的相关数据",
    success: async function(res) {
      if (res.confirm) {
        try {
          let response = await delete_sq(id)
          if (response.data.code == '200') {
            uni.showToast({
              title: "删除成功",
              icon: "success",
              duration: 2000,
            })
            await getTrainmasterList()
          } else {
            uni.showToast({
              title: response.data.message || "删除失败",
              icon: "none",
              duration: 2000,
            })
          }
        } catch (error) {
          console.error('删除失败:', error)
          uni.showToast({
            title: "删除失败",
            icon: "none",
            duration: 2000,
          })
        }
      }
    },
  })
}

// 处理取消关联行政区县的确认弹窗
const handleGLConfirm = (e, id) => {
  // 阻止默认的关闭行为
  e.preventDefault()

  uni.showModal({
    title: "温馨提示",
    content: "您确定要取消该行政区县的关联吗？",
    success: async function(res) {
      if (res.confirm) {
        // 用户点击确定，执行取消关联操作
        await handleGL(id)
      }
      // 用户点击取消，不执行任何操作，标签保持原状
    },
  })
}

// 取消关联行政区县的实际执行函数
const handleGL = async (id) => {
  try {
    let response = await deleted_jxq_xzqx(id)
    if (response.data.code == '200') {
      uni.showToast({
        title: "取消关联成功",
        icon: "success",
        duration: 2000,
      })
      await getTrainmasterList()
    } else {
      uni.showToast({
        title: response.data.message || "取消关联失败",
        icon: "none",
        duration: 2000,
      })
    }
  } catch (error) {
    console.error('取消关联失败:', error)
    uni.showToast({
      title: "取消关联失败",
      icon: "none",
      duration: 2000,
    })
  }
}

// 城市经理选择相关
const showModal = ref(false)
const allItems = ref([])
const allItems2 = ref([{
  value: 1,
  label: '名称',
  label2: '编码',
  label3: '角色',
  radio: 0
}])
const tempSelectedItem = ref(null)

// 行政区县选择相关
const showModal3 = ref(false)
const allItems5 = ref([])
const allItems6 = ref([{
  value: 1,
  label: '行政区县名称',
  radio: 0
}])
const formData = ref(null)

// 城市经理关联功能
const csjlclick = (item) => {
  tempSelectedItem.value = item
  showModal.value = true
}

const confirm = async (item) => {
  try {
    let data = {
      id: tempSelectedItem.value.id,
      csjl: item.value,
    }
    let res = await jxq_csjl(data)
    if (res.data.code == '200') {
      uni.showToast({
        title: "关联成功",
        icon: "success",
        duration: 2000,
      })
      await getTrainmasterList()
    } else {
      uni.showToast({
        title: res.data.message || "关联失败",
        icon: "none",
        duration: 2000,
      })
    }
  } catch (error) {
    console.error('关联城市经理失败:', error)
    uni.showToast({
      title: "关联失败",
      icon: "none",
      duration: 2000,
    })
  } finally {
    showModal.value = false
  }
}

// 行政区县关联功能
const handleXZQX = (item) => {
  formData.value = item
  showModal3.value = true
}


const confirm3 = async (items) => {
  // 判断传入的是单个对象还是数组
  const processArray = Array.isArray(items) ? items : [items]

  try {
    // 用于收集所有请求
    const requests = processArray.map(item => {
      let data = {
        id: formData.value.id,
        ncid: item.value,
      }
      return jxq_xzqy(data) // 返回Promise
    })

    // 并行执行所有请求
    const results = await Promise.all(requests)

    // 检查所有结果
    const allSuccess = results.every(res => res.data.code === 200)

    if (allSuccess) {
      uni.showToast({
        title: `成功关联 ${processArray.length} 个行政区县`,
        icon: "success",
        duration: 2000,
      })
      await getTrainmasterList()
    } else {
      // 如果有部分失败
      const failedCount = results.filter(res => res.data.code !== 200).length
      uni.showToast({
        title: `${processArray.length - failedCount}项成功，${failedCount}项失败`,
        icon: "none",
        duration: 2000,
      })
    }
  } catch (error) {
    console.error('批量操作出错:', error)
    uni.showToast({
      title: "关联失败",
      icon: "none",
      duration: 2000,
    })
  } finally {
    showModal3.value = false
  }
}
</script>

<style lang="scss" scoped>
.container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.main-card {
  max-width: 1400px;
  margin: 0 auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 8px;

  :deep(.ant-card-head) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px 8px 0 0;

    .ant-card-head-title {
      color: white;
      font-size: 18px;
      font-weight: 600;
    }
  }

  :deep(.ant-card-body) {
    padding: 24px;
  }
}

// 头部操作区域
.header-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 16px;

  .search-section {
    flex: 1;
    min-width: 300px;
  }

  .action-section {
    flex-shrink: 0;
  }
}

// 加载状态
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;

  .loading-content {
    width: 100px;
    height: 100px;
  }
}

// 数据列表
.data-list {
  margin-top: 16px;
}

// 卡片样式
.item-card {
  height: 100%;
  transition: all 0.3s ease;
  border-radius: 8px;
  overflow: hidden;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  }

  :deep(.ant-card-head) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-bottom: 1px solid #f0f0f0;
    min-height: 48px;
    padding: 0 16px;

    .ant-card-head-title {
      padding: 12px 0;
      font-size: 14px;
      color: white !important;
    }

    .ant-card-extra {
      padding: 12px 0;

      .ant-btn {
        color: white !important;
        border-color: rgba(255, 255, 255, 0.3) !important;

        &:hover {
          background-color: rgba(255, 255, 255, 0.1) !important;
          border-color: rgba(255, 255, 255, 0.5) !important;
        }
      }
    }
  }

  :deep(.ant-card-body) {
    padding: 16px;
  }
}

.card-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;

  .title-display-container {
    flex: 1;
    margin-right: 8px;

    .title-text {
      font-weight: 600;
      color: #fff;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .title-edit-container {
    flex: 1;
    margin-right: 8px;

    .title-edit-input {
      :deep(.ant-input) {
        font-weight: 600;
        font-size: 14px;
        border: 1px solid #40a9ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);

        &:focus {
          border-color: #40a9ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
      }
    }
  }
}

.card-content {
  .info-item {
    display: flex;
    margin-bottom: 8px;
    font-size: 14px;

    .label {
      color: #8c8c8c;
      min-width: 80px;
      flex-shrink: 0;
    }

    .value {
      color: #262626;
      flex: 1;
    }
  }

  .related-areas {
    margin-top: 16px;
    padding-top: 12px;
    border-top: 1px solid #f0f0f0;

    .areas-title {
      font-size: 14px;
      color: #8c8c8c;
      margin-bottom: 8px;
    }

    .areas-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .area-tag {
        margin: 0;
        border-radius: 4px;
        font-size: 12px;
      }
    }

    .no-areas {
      text-align: center;
      color: #bfbfbf;
    }
  }
}

// 下拉菜单样式
:deep(.ant-dropdown-menu) {
  .ant-dropdown-menu-item {
    display: flex;
    align-items: center;
    gap: 8px;

    .anticon {
      color: #1890ff;
    }

    &.danger-item {
      color: #ff4d4f;

      .anticon {
        color: #ff4d4f;
      }

      &:hover {
        background-color: #fff2f0;
      }
    }

    &:hover {
      .anticon {
        color: #40a9ff;
      }

      &.danger-item .anticon {
        color: #ff7875;
      }
    }
  }
}

// 搜索框样式
:deep(.ant-input-search) {
  .ant-input {
    border-radius: 6px;

    &:focus {
      border-color: #40a9ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }

  .ant-btn {
    border-radius: 0 6px 6px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;

    &:hover {
      background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
      border-color: #5a6fd8;
    }
  }
}

// 主要按钮样式
:deep(.ant-btn-primary) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 6px;
  font-weight: 500;

  &:hover, &:focus {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }

  .main-card {
    :deep(.ant-card-body) {
      padding: 16px;
    }
  }

  .header-actions {
    flex-direction: column;
    align-items: stretch;

    .search-section {
      min-width: auto;
    }

    .action-section {
      align-self: center;
    }
  }

  .item-card {
    :deep(.ant-card-body) {
      padding: 12px;
    }
  }

  .card-content {
    .info-item {
      font-size: 13px;

      .label {
        min-width: 70px;
      }
    }
  }
}
</style>