import request from "../../index";

// 获取产品列表
export function getFoodListApi(num,keyword) {
  return request({
    url: `/get/switch_foods?limit=${num}&keyword=${keyword}`,
    method: "get",
  });
}

// 获取经销区明细
export function getAreaApi(name,code) {
  return request({
    url: `/get/switch_areas?food_name=${name}&food_code=${code}`,
    method: "get",
  });
}

// 修改经销区订返按钮
export function changeSwitchApi(data) {
  return request({
    url: '/change/switch_data',
    method: "post",
    data
  });
}