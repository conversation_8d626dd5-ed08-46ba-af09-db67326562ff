import request from "../../index";

// 清分券兑换规则相关API

/**
 * 提交清分卷规则
 */
export function submitExchangeRuleApi(data) {
  return request({
    url: "/api/exchange-rules/submit",
    method: "post",
    data,
  });
}

/**
 * 获取所有产品
 */
export function getAllProductsApi() {
  return request({
    url: "/api/exchange-rules/products",
    method: "get",
  });
}

/**
 * 获取所有可选大区
 */
export function getAllRegionsApi() {
  return request({
    url: "/api/exchange-rules/regions",
    method: "get",
  });
}

/**
 * 获取所有可选经销区
 */
export function getAllDealersApi(dealers) {
  return request({
    url: "/api/exchange-rules/dealers",
    method: "get",
    data: {
      dealers,
    },
  });
}

/**
 * 获取搜友清分券兑换规则列表
 */
export function getExchangeRulesApi(page, keyWord, userId) {
  return request({
    url: "/api/exchange-rules",
    method: "get",
    data: {
      page: page,
      pageSize: 10,
      keyWord: keyWord,
      user_id: userId,
    },
  });
}

/**
 * 删除清分券兑换规则
 */

export function deleteExchangeRuleApi(id) {
  return request({
    url: "/api/delete/exchange-rules",
    method: "get",
    data: {
      record_id: id,
    },
  });
}

/**
 * 获取月份可设积分值
 */
export function getMonthIntegralApi(data) {
  return request({
    url: "/api/exchange-rules/month-integral",
    method: "get",
    data,
  });
}
