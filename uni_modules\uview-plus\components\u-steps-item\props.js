import defprops from '../../libs/config/props';
export default {
    props: {
        // 标题
        title: {
            type: [String, Number],
            default: defprops.stepsItem.title
        },
        // 描述文本
        desc: {
            type: [String, Number],
            default: defprops.stepsItem.desc
        },
        // 图标大小
        iconSize: {
            type: [String, Number],
            default: defprops.stepsItem.iconSize
        },
        // 当前步骤是否处于失败状态
        error: {
            type: <PERSON><PERSON>an,
            default: defprops.stepsItem.error
        }
    }
}
