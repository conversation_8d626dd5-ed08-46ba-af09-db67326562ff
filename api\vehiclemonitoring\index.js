import request from "../index";

//获取所有车辆信息
export function getAllCars() {
  return request({
    method: "get",
    url: `/tms/getAllCars`,
  });
}

// 获取车正在执行排车计划
export function getCarPlan(data) {
  return request({
    method: "get",
    url: `/tms/getCarPlan`,
    data,
  });
}

// 获取当前车辆任务走过轨迹
export function getCarTracks(data) {
  return request({
    method: "get",
    url: `/tms/getCarTrack`,
    data,
  });
}

// 获取所有的分仓列表
export function getAllFCs(data) {
  return request({
    method: "get",
    url: `/tms/getAllFCs`,
    data,
  });
}

// 获取所有的分仓列表
export function getSeparateCost(fc_list) {
  return request({
    method: "post",
    url: `/bi/getSeparateCost`,
    data: {
      fc_list,
    },
  });
}

// 查找车辆对应的每公里运费数据
export function getCarFreight(carNumber) {
  return request({
    method: "post",
    url: `/bi/getCarFreight`,
    data: {
      carNumber,
    },
  });
}

// 查询车辆近30批运费情况
export function getCarFreight30(carNumber) {
  return request({
    method: "get",
    url: `/bi/car/getRecentFreight`,
    data: {
      carNumber,
    },
  });
}
// 获取分仓的近30批到货数据
export function getFCArrival30(branchCode) {
  return request({
    method: "get",
    url: `/bi/getBranchArrivalData`,
    data: {
      branchCode,
    },
  });
}
