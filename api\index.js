import manifest from "@/manifest.json";
import { message } from "ant-design-vue";

let BASE_URL = "";

if (manifest["h5"].type === "development") {
  BASE_URL = "https://crm2reportdev.xcspzg.com/basic-api";
}

if (manifest["h5"].type === "test") {
  BASE_URL = "https://crm2reporttest.xcspzg.com/basic-api";
}

if (manifest["h5"].type === "production") {
  BASE_URL = "https://crmreportprd.xcspzg.com/basic-api";
}

const request = config => {
  config.url = BASE_URL + config.url;
  config.timeout = 50000;
  config.data = config.data || {};
  config.header = config.header || {
    Authorization: "Bearer token_42c6c4614bc346f2b7b99c44c66e296b_str",
  };
  return new Promise((resolve, reject) => {
    uni
      .request(config)
      .then(res => {
        if (res.statusCode === 401) {
          message.destroy();
          message.error("登录验证失败，请重新登录");
          uni.removeStorageSync("access_token");
          uni.getSystemInfo({
            success: function (res) {
              // 判断设备平台
              if (res.platform === "windows" || res.platform === "mac") {
                uni.reLaunch({
                  url: "/pages/Login/WebLogin",
                });
              } else {
                uni.reLaunch({
                  url: "/pages/Login/Login",
                });
              }
            },
            fail: function (err) {
              console.error("获取系统信息失败", err);
            },
          });
        }
        if (res.statusCode === 200) {
          if (res.data.code === 200) {
            resolve(res);
          } else {
            console.error(res);
            reject(res);
          }
        }
      })
      .catch(err => {
        console.error(err);
        uni.showToast({
          title: "网络错误",
          icon: "error",
          duration: 2000,
        });
        reject(err);
      });
  });
};

export default request;
