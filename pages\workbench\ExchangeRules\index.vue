<template>
  <div class="exchange-rules-page">
    <a-config-provider :locale="locale">
      <!-- 头部操作区 -->
      <div class="header-section">
        <!-- 新增按钮 -->
        <div class="add-section">
          <a-button
            type="primary"
            size="large"
            @click="handleAddRule"
            :block="isMobile"
          >
            <template #icon>
              <PlusOutlined />
            </template>
            新增规则
          </a-button>
        </div>

        <!-- 搜索框 -->
        <div class="search-section">
          <a-input-search
            v-model:value="searchKeyword"
            placeholder="搜索产品名称、序号、生效范围"
            size="large"
            @search="handleSearch"
            allow-clear
          />
        </div>
      </div>

      <!-- 列表内容 -->
      <div class="list-container">
        <!-- 状态分组Tab -->
        <div v-if="hasAnyData" class="status-tabs">
          <a-tabs v-model:activeKey="activeStatus" @change="handleStatusChange">
            <a-tab-pane key="all" :tab="getAllTabTitle()"> </a-tab-pane>
            <a-tab-pane key="pending" :tab="getPendingTabTitle()"> </a-tab-pane>
            <a-tab-pane key="effective" :tab="getEffectiveTabTitle()">
            </a-tab-pane>
            <a-tab-pane key="expired" :tab="getExpiredTabTitle()"> </a-tab-pane>
          </a-tabs>
        </div>

        <!-- 规则卡片列表 -->
        <div v-if="currentTabData.length > 0" class="rules-list">
          <a-card
            v-for="(item, index) in currentTabData"
            :key="item.id"
            class="rule-card"
            :hoverable="true"
            @click="handleCardClick(item)"
          >
            <!-- 卡片头部 -->
            <template #title>
              <div class="card-header">
                <div class="rule-title">
                  <span class="serial-number">{{ item.serial_number }}</span>
                  <a-tag
                    :color="getStatusColor(item.status)"
                    class="status-tag"
                  >
                    {{ getStatusText(item.status) }}
                  </a-tag>
                </div>
                <div
                  v-if="item.status === 'pending' || item.status === 'expired'"
                  class="card-actions"
                  @click.stop
                >
                  <a-dropdown :trigger="['click']" placement="bottomRight">
                    <a-button type="text" shape="circle" @click.prevent>
                      <template #icon>
                        <MoreOutlined />
                      </template>
                    </a-button>
                    <template #overlay>
                      <a-menu @click="handleDeleteRule(item)">
                        <a-menu-item
                          v-if="
                            item.status === 'pending' ||
                            item.status === 'expired'
                          "
                          danger
                        >
                          <DeleteOutlined />
                          删除
                        </a-menu-item>
                      </a-menu>
                    </template>
                  </a-dropdown>
                </div>
              </div>
            </template>

            <!-- 卡片内容 -->
            <div class="card-content">
              <!-- 基本信息行 -->
              <a-row :gutter="16" class="info-row">
                <a-col :span="12">
                  <div class="info-item">
                    <span class="label">产品:</span>
                    <span class="value">{{ item.product }}</span>
                  </div>
                </a-col>
                <a-col :span="12">
                  <div class="info-item">
                    <span class="label">有效期:</span>
                    <span class="value">{{ item.validity_date }}</span>
                  </div>
                </a-col>
              </a-row>

              <!-- 积分信息行 -->
              <a-row :gutter="16" class="info-row">
                <a-col :span="12">
                  <div class="info-item">
                    <span class="label">最大兑换值:</span>
                    <span class="value highlight"
                      >{{ formatNumber(item.max_exchange_value) }}积分</span
                    >
                  </div>
                </a-col>
                <a-col :span="12">
                  <div class="info-item">
                    <span class="label">剩余积分:</span>
                    <span class="value"
                      >{{ formatNumber(item.remaining_points) }}积分</span
                    >
                  </div>
                </a-col>
              </a-row>

              <!-- 生效范围 -->
              <div class="info-row">
                <div class="info-item full-width">
                  <span class="label">生效范围:</span>
                  <span class="value scope-text">{{ getScopeText(item) }}</span>
                </div>
              </div>

              <!-- 时间信息 -->
              <div class="info-row time-info">
                <span class="time-text">
                  <CalendarOutlined />
                  创建时间: {{ formatTime(item.create_time) }}
                </span>
              </div>
            </div>
          </a-card>
        </div>

        <!-- 空状态 -->
        <div v-else class="empty-state">
          <a-empty :description="getEmptyDescription()">
            <template #image>
              <FileTextOutlined style="font-size: 48px; color: #d9d9d9" />
            </template>
            <a-button
              v-if="!searchKeyword && activeStatus === 'all'"
              type="primary"
              @click="handleAddRule"
            >
              立即创建
            </a-button>
          </a-empty>
        </div>
      </div>
    </a-config-provider>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { message, Modal } from "ant-design-vue";
import {
  PlusOutlined,
  DeleteOutlined,
  MoreOutlined,
  CalendarOutlined,
  FileTextOutlined,
} from "@ant-design/icons-vue";
import zhCN from "ant-design-vue/es/locale/zh_CN";
import {
  deleteExchangeRuleApi,
  getExchangeRulesApi,
} from "../../../api/workbench/ExchangeRules";
import { onShow } from "@dcloudio/uni-app";

// 中文语言包
const locale = zhCN;

// 移动端检测
const isMobile = computed(() => {
  if (typeof window !== "undefined") {
    return window.innerWidth <= 768;
  }
  return false;
});

// 搜索关键词
const searchKeyword = ref("");

// 当前激活的状态tab
const activeStatus = ref("all");

// 模拟数据
const rulesList = ref([]);

// 获取所有的清分券兑换规则
const getAllExchangeRules = async () => {
  const userId = JSON.parse(sessionStorage.getItem("userInfo")).id;
  const { data } = await getExchangeRulesApi(1, searchKeyword.value, userId);
  rulesList.value = data.result.map(item => {
    // 获取当前日期
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth() + 1; // getMonth() 返回 0-11，需要 +1

    // 解析生效日期
    const validityDate = new Date(item.validity_date);
    const validityYear = validityDate.getFullYear();
    const validityMonth = validityDate.getMonth() + 1;

    // 判断状态
    let status = "expired";
    if (validityYear === currentYear && validityMonth === currentMonth) {
      status = "effective";
    } else if (
      validityYear === currentYear &&
      validityMonth === currentMonth + 1
    ) {
      status = "pending";
    } else if (
      validityYear === currentYear + 1 &&
      currentMonth === 12 &&
      validityMonth === 1
    ) {
      // 处理跨年的情况：当前是12月，生效月份是下一年1月
      status = "pending";
    }

    return {
      id: item.id,
      serial_number: item.serial_number, //规则序号
      validity_date: item.validity_date, //生效月份
      remaining_points: item.remaining_points, //剩余可设置积分
      product: item.product, //产品名称
      max_exchange_value: item.max_exchange_value, //最大兑换值
      scope_of_effectiveness: item.scope_of_effectiveness,
      scope_of_effectiveness_region: item.scope_of_effectiveness_region,
      scope_of_effectiveness_area: item.scope_of_effectiveness_area,
      status: status,
      create_time: item.create_time_format,
    };
  });
};

// 过滤后的列表
const filteredList = computed(() => {
  if (!searchKeyword.value) {
    return rulesList.value;
  }

  const keyword = searchKeyword.value.toLowerCase();
  return rulesList.value.filter(item => {
    return (
      item.product.toLowerCase().includes(keyword) ||
      item.serial_number.toLowerCase().includes(keyword) ||
      getScopeText(item).toLowerCase().includes(keyword)
    );
  });
});

// 各状态数量统计
const statusCounts = computed(() => {
  const counts = {
    all: filteredList.value.length,
    pending: 0,
    effective: 0,
    expired: 0,
  };

  filteredList.value.forEach(item => {
    counts[item.status]++;
  });

  return counts;
});

// 当前Tab的数据
const currentTabData = computed(() => {
  if (activeStatus.value === "all") {
    return filteredList.value;
  }
  return filteredList.value.filter(item => item.status === activeStatus.value);
});

// 是否有任何数据
const hasAnyData = computed(() => {
  return rulesList.value.length > 0;
});

// 获取原始索引（用于删除操作）
const getOriginalIndex = item => {
  return rulesList.value.findIndex(rule => rule.id === item.id);
};

// Tab标题生成方法
const getAllTabTitle = () => {
  return `全部 ${
    statusCounts.value.all > 0 ? `(${statusCounts.value.all})` : ""
  }`;
};

const getPendingTabTitle = () => {
  return `待生效 ${
    statusCounts.value.pending > 0 ? `(${statusCounts.value.pending})` : ""
  }`;
};

const getEffectiveTabTitle = () => {
  return `生效中 ${
    statusCounts.value.effective > 0 ? `(${statusCounts.value.effective})` : ""
  }`;
};

const getExpiredTabTitle = () => {
  return `已失效 ${
    statusCounts.value.expired > 0 ? `(${statusCounts.value.expired})` : ""
  }`;
};

// Tab切换处理
const handleStatusChange = key => {
  activeStatus.value = key;
  console.log("切换到状态:", key);
};

// 空状态描述
const getEmptyDescription = () => {
  if (searchKeyword.value) {
    return "未找到相关规则";
  }

  const statusNames = {
    all: "兑换规则",
    pending: "待生效的规则",
    effective: "生效中的规则",
    expired: "已失效的规则",
  };

  return `暂无${statusNames[activeStatus.value] || "数据"}`;
};

// 获取状态文本
const getStatusText = status => {
  const statusMap = {
    pending: "待生效",
    effective: "生效中",
    expired: "已失效",
  };
  return statusMap[status] || "未知";
};

// 获取状态颜色
const getStatusColor = status => {
  const colorMap = {
    pending: "processing",
    effective: "success",
    expired: "error",
  };
  return colorMap[status] || "default";
};

// 获取生效范围文本
const getScopeText = item => {
  if (item.scope_of_effectiveness) {
    return item.scope_of_effectiveness;
  }
  if (item.scope_of_effectiveness_region) {
    return item.scope_of_effectiveness_region;
  }
  if (item.scope_of_effectiveness_area) {
    return item.scope_of_effectiveness_area;
  }
  return "未设置";
};

// 格式化数字
const formatNumber = num => {
  return num?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",") || "0";
};

// 格式化时间
const formatTime = time => {
  return time || "";
};

// 新增规则
const handleAddRule = () => {
  uni.navigateTo({
    url: "/pages/workbench/ExchangeRules/add",
  });
};

// 搜索处理
const handleSearch = () => {
  // 搜索逻辑已在 computed 中处理
  console.log("搜索关键词:", searchKeyword.value);
};

// 卡片点击处理
const handleCardClick = item => {
  uni.setStorageSync('exchangeRuleDetail', item);
  uni.navigateTo({
    url: `/pages/workbench/ExchangeRules/detail?id=${item.id}`,
  });
};

// 删除规则
const handleDeleteRule = item => {
  Modal.confirm({
    title: "删除确认",
    content: `确定要删除规则 ${item.serial_number} 吗？`,
    okText: "确定",
    cancelText: "取消",
    onOk() {
      deleteExchangeRuleApi(item.id).then(() => {
        message.success("删除成功");
        getAllExchangeRules();
      });
    },
  });
};

// 页面生命周期
onShow(() => {
  getAllExchangeRules();
});
</script>

<style scoped>
.exchange-rules-page {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding: 0;
}

/* 头部操作区 */
.header-section {
  background: #fff;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.add-section {
  margin-bottom: 16px;
}

.search-section {
  width: 100%;
}

/* 列表容器 */
.list-container {
  padding: 16px;
}

/* 状态分组Tab */
.status-tabs {
  margin-bottom: 16px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
  padding: 8px 12px;
}

/* 规则卡片列表 */
.rules-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.rule-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.rule-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
}

.rule-title {
  display: flex;
  /* flex-direction: column; */
  gap: 6px;
  flex: 1;
  align-items: center;
}

.serial-number {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1.3;
}

.status-tag {
  align-self: flex-start;
}

.card-actions {
  display: flex;
  align-items: center;
  margin-left: 8px;
}

/* 卡片内容 */
.card-content {
  margin-top: 12px;
}

.info-row {
  margin-bottom: 8px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.info-item.full-width {
  width: 100%;
}

.label {
  font-size: 12px;
  color: #909399;
  line-height: 1.2;
}

.value {
  font-size: 14px;
  color: #2c3e50;
  font-weight: 500;
  line-height: 1.3;
  word-break: break-all;
}

.value.highlight {
  color: #fa8c16;
  font-weight: 600;
}

.scope-text {
  color: #606266;
  font-size: 13px;
  line-height: 1.3;
}

.time-info {
  margin-top: 6px;
  padding-top: 8px;
  border-top: 1px solid #f5f7fa;
}

.time-text {
  font-size: 12px;
  color: #c0c4cc;
  line-height: 1.2;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 空状态 */
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .header-section {
    padding: 12px;
  }

  .list-container {
    padding: 8px 12px;
  }

  .status-tabs {
    margin-bottom: 12px;
    padding: 6px 8px;
    border-radius: 8px;
  }

  .rule-card {
    border-radius: 8px;
  }

  .serial-number {
    font-size: 15px;
  }

  .value {
    font-size: 13px;
  }

  .scope-text {
    font-size: 12px;
  }

  .info-row {
    margin-bottom: 6px;
  }

  .card-content {
    margin-top: 8px;
  }

  .time-info {
    margin-top: 4px;
    padding-top: 6px;
  }
}

/* PC端适配 */
@media (min-width: 769px) {
  .exchange-rules-page {
    max-width: 1200px;
    margin: 0 auto;
  }

  .header-section {
    padding: 20px 24px;
    display: flex;
    align-items: center;
    gap: 20px;
  }

  .add-section {
    margin-bottom: 0;
    flex-shrink: 0;
  }

  .search-section {
    max-width: 400px;
  }

  .list-container {
    padding: 20px 24px;
  }

  .rules-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 16px;
  }

  .serial-number {
    font-size: 17px;
  }
}

/* 中等屏幕适配 */
@media (min-width: 1024px) {
  .exchange-rules-page {
    max-width: 1400px;
  }

  .rules-list {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 18px;
  }
}

/* 大屏幕适配 */
@media (min-width: 1280px) {
  .exchange-rules-page {
    max-width: 1600px;
  }

  .rules-list {
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
    gap: 20px;
  }
}

/* 超大屏幕适配 */
@media (min-width: 1600px) {
  .exchange-rules-page {
    max-width: 1800px;
  }

  .rules-list {
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 24px;
  }
}

/* antd 组件样式重写 */
:deep(.ant-card-head) {
  border-bottom: 1px solid #f5f7fa;
  padding: 12px 16px 8px;
}

:deep(.ant-card-body) {
  padding: 0 16px 16px;
}

:deep(.ant-empty-image) {
  margin-bottom: 16px;
}

:deep(.ant-tag) {
  border-radius: 6px;
  font-size: 12px;
}

:deep(.ant-dropdown-menu) {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

:deep(.ant-dropdown-menu-item) {
  padding: 8px 12px;
}

:deep(.ant-dropdown-menu-item-icon) {
  margin-right: 8px;
}

/* Tab组件样式 */
:deep(.ant-tabs) {
  margin: 0;
}

:deep(.ant-tabs-nav) {
  margin: 0;
}

:deep(.ant-tabs-tab) {
  padding: 8px 12px;
  margin: 0 4px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

:deep(.ant-tabs-tab:hover) {
  background-color: #f5f7fa;
}

:deep(.ant-tabs-tab-active) {
  background-color: #e6f7ff;
  border-radius: 6px;
}

:deep(.ant-tabs-tab-active .ant-tabs-tab-btn) {
  color: #1890ff;
  font-weight: 500;
}

:deep(.ant-tabs-ink-bar) {
  display: none;
}

:deep(.ant-tabs-content-holder) {
  display: none;
}

/* 移动端卡片样式调整 */
@media (max-width: 768px) {
  :deep(.ant-card-head) {
    padding: 10px 12px 6px;
  }

  :deep(.ant-card-body) {
    padding: 0 12px 12px;
  }

  /* 移动端Tab样式 */
  :deep(.ant-tabs-tab) {
    padding: 6px 8px;
    margin: 0 2px;
    font-size: 13px;
  }
}
</style>
