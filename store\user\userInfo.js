import { defineStore } from "pinia";
import { ref } from "vue";

export const useUserInfo = defineStore("useUserInfo", () => {
  // 初始化时从 sessionStorage 获取用户信息
  const initUserInfo = () => {
    try {
      const userInfo = sessionStorage.getItem("userInfo");
      return userInfo ? JSON.parse(userInfo) : null;
    } catch (error) {
      console.error("解析用户信息失败:", error);
      return null;
    }
  };

  const userInfo = ref(initUserInfo());

  // 设置用户信息并同步到 sessionStorage
  const setUserInfo = (info) => {
    userInfo.value = info;
    if (info) {
      sessionStorage.setItem("userInfo", JSON.stringify(info));
    } else {
      sessionStorage.removeItem("userInfo");
    }
  };

  // 清除用户信息
  const clearUserInfo = () => {
    userInfo.value = null;
    sessionStorage.removeItem("userInfo");
  };

  return {
    userInfo,
    setUserInfo,
    clearUserInfo
  };
});
