<template>
  <div style="height: calc(100vh - 60px); overflow-y: auto">
    <TransitionGroup tag="div" name="fade" class="list-container">
      <div class="list-item" v-for="item in dataList" :key="item.id">
        <div class="top">
          <span>门店名称</span>
          <span style="grid-column: span 3 / span 3">
            {{ item.Sales_shop_name }}
          </span>
          <span>经销区</span>
          <span style="grid-column: span 3 / span 3">
            {{ item.Sales_aifiliation_area_name }}
          </span>
          <span>销货时间</span>
          <span style="grid-column: span 3 / span 3">{{
            item.CreatedDate
          }}</span>
          <span>销货人</span>
          <span>{{ item.Sales_initiatorname }}</span>
          <span>销货金额</span>
          <span>{{ item.Sales_sales_money }}</span>
          <span>返货金额</span>
          <span>{{ item.Sales_stock_money }}</span>
          <span>净销售额</span>
          <span>{{ item.Sales_receivable_money }}</span>
          <a-button
            style="grid-column: span 4 / span 4"
            type="primary"
            @click="openDetail(item)"
          >
            查看销退明细
          </a-button>
        </div>
        <div class="bottom">
          <template v-for="item in item.imgList">
            <div class="img">
              <div class="img-alt">{{ item.alt }}</div>
              <a-image
                style="height: 10vh"
                :src="item.src"
                v-if="
                  item.src !== null && item.src !== 'None' && item.src !== ''
                "
              />
              <div
                style="
                  height: 10vh;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  color: rgba(0, 0, 0, 0.5);
                "
                v-else
              >
                暂无图片
              </div>
            </div>
          </template>
        </div>
      </div>
    </TransitionGroup>
    <div style="text-align: center">
      <u-loadmore
        :status="status"
        loadmore-text="点击加载更多"
        @click="getMoreData"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from "vue";
import { getSalesListRecent, getSalesListRecentSell } from "../../api/shopxh";
import { onHide } from "@dcloudio/uni-app";
import { useUserInfo } from "../../store/user/userInfo";

const userStore = useUserInfo();

onMounted(() => {
  setTimeout(() => {
    getShopData();
  }, 1000);
  timer = setInterval(() => {
    getShopData();
  }, 30000);
});

onUnmounted(() => {
  clearInterval(timer);
});

let timer;
const status = ref("loading");
const dataList = ref([]);
const allData = ref([]);

// 获取门店数据
const page = ref(0);
async function getShopData() {
  let res;
  if (userStore.userInfo?.Role_grade === "决策层") {
    res = await getSalesListRecent();
  }
  if (userStore.userInfo?.Role_grade === "经销商") {
    const jxqIdList = userStore.userInfo.jxqId.join(",");
    res = await getSalesListRecentSell(jxqIdList);
  }
  if (userStore.userInfo?.Role_grade === "省区总") {
    const jxqIdList = userStore.userInfo.jxqId.join(",");
    res = await getSalesListRecentSell(jxqIdList);
  }
  if (userStore.userInfo?.Role_grade === "大区总") {
    const jxqIdList = userStore.userInfo.jxqId.join(",");
    res = await getSalesListRecentSell(jxqIdList);
  }

  dataList.value = res.data.result.slice(0, page.value * 12 + 12) || [];
  allData.value = res.data.result;
  dataList.value.forEach(item => {
    const imgList = [
      {
        alt: "门头照片",
        src: item.Store_photo,
      },
      {
        alt: "销前照片",
        src: item.Sales_last_pic,
      },
      {
        alt: "销后照片",
        src: item.Sales_next_pic,
      },
      {
        alt: "销后照片",
        src: item.Sales_next_pic2,
      },
      {
        alt: "销后照片",
        src: item.Sales_next_pic3,
      },
      {
        alt: "销后照片",
        src: item.Sales_next_pic4,
      },
    ];
    item.imgList = imgList;
  });
  status.value = "loadmore";
}

async function getMoreData() {
  status.value = "loading";
  page.value += 1;
  let arr = allData.value.slice(page.value * 12, page.value * 12 + 12);
  arr.forEach(item => {
    const imgList = [
      {
        alt: "门头照片",
        src: item.Store_photo,
      },
      {
        alt: "销前照片",
        src: item.Sales_last_pic,
      },
      {
        alt: "销后照片",
        src: item.Sales_next_pic,
      },
      {
        alt: "销后照片",
        src: item.Sales_next_pic2,
      },
      {
        alt: "销后照片",
        src: item.Sales_next_pic3,
      },
      {
        alt: "销后照片",
        src: item.Sales_next_pic4,
      },
    ];
    item.imgList = imgList;
  });

  setTimeout(() => {
    dataList.value = dataList.value.concat(arr);
    if (dataList.value.length < allData.value.length) {
      status.value = "loadmore";
    } else {
      status.value = "nomore";
    }
  }, 600);
}

function openDetail(item) {
  let all = JSON.stringify(item);
  uni.navigateTo({
    url: `/pages/shopxh/Detail?id=${item.id}&all=${all}`,
  });
}
</script>

<style lang="scss" scoped>
.list-container {
  padding: 10px;
  font-size: 12px;
  display: grid;
  gap: 10px;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  .list-item {
    margin-top: 10px;
    padding: 10px;
    border-radius: 10px;
    box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.1);
    .top {
      display: grid;
      border: 1px solid rgba(0, 0, 0, 0.1);
      grid-template-columns: repeat(4, minmax(0, 1fr));
      span {
        padding: 5px;
        text-align: center;
        border: 1px solid rgba(0, 0, 0, 0.1);
        &:nth-child(odd) {
          background-color: #f5f5f5;
        }
      }
    }
    .bottom {
      margin-top: 5px;
      display: grid;
      grid-template-columns: repeat(3, minmax(0, 1fr));
      gap: 10px;
      .img {
        border: 1px solid rgba(0, 0, 0, 0.1);
        text-align: center;
        .img-alt {
          background-color: #f5f5f5;
          padding: 3px 0;
        }
      }
    }
  }
}

/* 针对屏幕宽度小于等于 600px 的设备 */
@media (max-width: 600px) {
  .list-container {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
}
/* 针对屏幕宽度大于 600px 但小于等于 1200px 的设备 */
@media (min-width: 601px) and (max-width: 1200px) {
  .list-container {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

/* 针对屏幕宽度大于 1200px 的设备 */
@media (min-width: 1201px) {
  .list-container {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}
/* 1. 声明过渡效果 */
.fade-move,
.fade-enter-active,
.fade-leave-active {
  transition: all 0.5s cubic-bezier(0.55, 0, 0.1, 1);
}

/* 2. 声明进入和离开的状态 */
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: scaleY(0.01) translate(30px, 0);
}
</style>
