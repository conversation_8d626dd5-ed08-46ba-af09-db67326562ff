<template>
  <div class="container">
    <div class="backGround"></div>
    <a-config-provider :locale="zhCN">
      <a-date-picker
        format="YYYY-MM"
        v-model:value="month"
        picker="month"
        @change="choose_month"
      />
    </a-config-provider>

    <div class="detail progress">
      <a-progress type="dashboard" :percent="75">
        <template #format="percent">
          <span
            style="
              font-size: 15px;
              display: block;
              width: 80px;
              margin-left: 20px;
            "
            >{{ title1 }}</span
          >
        </template>
      </a-progress>
      <a-progress type="dashboard" :percent="100">
        <template #format="percent">
          <span
            style="
              font-size: 15px;
              display: block;
              width: 80px;
              margin-left: 20px;
            "
            >{{ title2 }}</span
          >
        </template>
      </a-progress>
    </div>
    <div
      class="chart-container1"
      id="chart"
      v-if="showEcharts == '营收及增长'"
    ></div>
    <div
      class="chart-container1"
      id="chart5"
      v-if="showEcharts == '营收及增长'"
    ></div>
    <div
      class="chart-container1"
      id="chart2"
      v-if="showEcharts == '营收及增长'"
    ></div>
    <div
      class="chart-container1"
      id="chart6"
      v-if="showEcharts == '营收及增长'"
    ></div>
    <div
      class="chart-container1"
      id="chart3"
      v-if="showEcharts == '营收及增长'"
    ></div>
    <div
      class="chart-container2"
      id="chart4"
      v-if="showEcharts == '营收及增长'"
    ></div>
  </div>
</template>
  
  <script setup>
import { ref, nextTick } from "vue";
import * as echarts from "echarts";
import { onLoad } from "@dcloudio/uni-app";
import {
  getNumberDataName,
  get_month_sale,
  getMonthSaleDistribution,
  getProductSale,
} from "/api/FormOther/index.js";
import zhCN from "ant-design-vue/es/locale/zh_CN";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
import { setBarOptions, setLineOptions } from "./utils/customCharts";
dayjs.locale("zh-cn");

let chartDom;
let myChart;
let option;
let showEcharts = ref("");
let title1 = ref("");
let title2 = ref("");

// 销售额排序
const saleSort = (dataForm) => {
  dataForm.sort((a, b) => {
    return (
      a.sale_money_one +
      a.sale_money_two -
      (b.sale_money_one + b.sale_money_two)
    );
  });

  return dataForm;
};

const initChart1 = () => {
  chartDom = document.getElementById("chart");
  myChart = echarts.init(chartDom);

  let dataForm = [
    {
      name: "南开",
      sale_money_one: 320,
      sale_money_two: 110,
    },
    {
      name: "西青",
      sale_money_one: 314,
      sale_money_two: 80,
    },
    {
      name: "静海",
      sale_money_one: 189,
      sale_money_two: 33,
    },
    {
      name: "和平",
      sale_money_one: 220,
      sale_money_two: 100,
    },
    {
      name: "河北",
      sale_money_one: 150,
      sale_money_two: 83,
    },
    {
      name: "河西",
      sale_money_one: 250,
      sale_money_two: 75,
    },
    {
      name: "河东",
      sale_money_one: 170,
      sale_money_two: 33,
    },
  ];

  dataForm = saleSort(dataForm);

  // 自定义x轴
  let x = {};

  // 自定义y轴
  let y = {
    data: dataForm.map((item) => {
      return item.name;
    }),
  };

  let s = [
    {
      name: "产品1",
      type: "bar",
      stack: "total",
      label: {
        show: true,
        textStyle: {
          fontSize: "10px",
        },
      },
      emphasis: {
        focus: "series",
      },
      data: [],
    },
    {
      name: "产品2",
      type: "bar",
      stack: "total",
      label: {
        show: true,
        textStyle: {
          fontSize: "10px",
        },
      },
      emphasis: {
        focus: "series",
      },
      data: [],
    },
  ];

  s[0].data = dataForm.map((item) => {
    return item.sale_money_one;
  });

  s[1].data = dataForm.map((item) => {
    return item.sale_money_two;
  });

  option = setBarOptions("月度销售额-大区", x, y, s);
  myChart.setOption(option);
};

const initChart2 = (options) => {
  chartDom = document.getElementById("chart2");
  myChart = echarts.init(chartDom);
  if (options) {
    option = options;
  } else {
    option = {
      tooltip: {
        trigger: "axis",
        axisPointer: {
          // Use axis to trigger tooltip
          type: "shadow", // 'shadow' as default; can also be 'line' or 'shadow'
        },
      },
      legend: {
        top: "10%",
      },
      grid: {
        left: "0%",
        right: "4%",
        bottom: "0%",
        containLabel: true,
        height: "75%",
      },
      xAxis: {
        type: "value",
      },
      yAxis: {
        type: "category",
        data: [],
      },
      dataZoom: {
        type: "inside",
        yAxisIndex: 0, // 控制x轴
        show: true,
        start: 0,
        end: 60,
        realtime: true,
      },
      series: [],
      title: {
        text: "月度销售额-经销区",
      },
    };
  }
  // console.log(option);
  myChart.clear();
  myChart.setOption(option);
};

const initChart3 = (options) => {
  chartDom = document.getElementById("chart3");
  myChart = echarts.init(chartDom);
  if (options) {
    option = options;
  } else {
    option = {
      tooltip: {
        trigger: "axis",
        axisPointer: {
          // Use axis to trigger tooltip
          type: "shadow", // 'shadow' as default; can also be 'line' or 'shadow'
        },
      },
      legend: {
        top: "10%",
      },
      grid: {
        left: "0%",
        right: "4%",
        bottom: "0%",
        containLabel: true,
        height: "75%",
      },
      xAxis: {
        type: "value",
      },
      yAxis: {
        type: "category",
        data: ["产品1", "产品2", "产品3"],
      },
      series: [
        {
          name: "销货额",
          type: "bar",
          stack: "total",
          label: {
            show: true,
            textStyle: {
              fontSize: "10px",
            },
          },
          emphasis: {
            focus: "series",
          },
          data: [320, 120, 200],
        },
        {
          name: "返货额",
          type: "bar",
          stack: "total",
          label: {
            show: true,
            textStyle: {
              fontSize: "10px",
            },
          },
          emphasis: {
            focus: "series",
          },
          data: [120, 100, 130],
        },
      ],
      title: {
        text: "产品销售额",
      },
    };
  }
  // console.log(option);
  myChart.clear();
  myChart.setOption(option);
};

const initChart5 = () => {
  chartDom = document.getElementById("chart5");
  myChart = echarts.init(chartDom);

  option = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        // Use axis to trigger tooltip
        type: "shadow", // 'shadow' as default; can also be 'line' or 'shadow'
      },
    },
    legend: {
      top: "10%",
    },
    grid: {
      left: "0%",
      right: "4%",
      bottom: "0%",
      containLabel: true,
      height: "75%",
    },
    xAxis: {
      type: "value",
    },
    yAxis: {
      type: "category",
      data: ["南开", "西青", "静海", "和平", "河北", "河西", "河东"],
    },
    dataZoom: {
      type: "inside",
      yAxisIndex: 0, // 控制x轴
      show: true,
      start: 0,
      end: 60,
      realtime: true,
    },
    series: [
      {
        name: "产品1",
        type: "bar",
        stack: "total",
        label: {
          show: true,
          textStyle: {
            fontSize: "10px",
          },
        },
        emphasis: {
          focus: "series",
        },
        data: [320, 302, 301, 334, 390, 330, 320],
      },
      {
        name: "产品2",
        type: "bar",
        stack: "total",
        label: {
          show: true,
          textStyle: {
            fontSize: "10px",
          },
        },
        emphasis: {
          focus: "series",
        },
        data: [120, 132, 101, 134, 90, 230, 210],
      },
      {
        name: "产品3",
        type: "bar",
        stack: "total",
        label: {
          show: true,
          textStyle: {
            fontSize: "10px",
          },
        },
        emphasis: {
          focus: "series",
        },
        data: [220, 182, 191, 234, 290, 330, 310],
      },
      {
        name: "产品4",
        type: "bar",
        stack: "total",
        label: {
          show: true,
          textStyle: {
            fontSize: "10px",
          },
        },
        emphasis: {
          focus: "series",
        },
        data: [150, 212, 201, 154, 190, 330, 410],
      },
      {
        name: "产品5",
        type: "bar",
        stack: "total",
        label: {
          show: true,
          textStyle: {
            fontSize: "10px",
          },
        },
        emphasis: {
          focus: "series",
        },
        data: [820, 832, 901, 934, 1290, 1330, 100],
      },
    ],
    title: {
      text: "月度订货额-大区",
    },
  };
  myChart.setOption(option);
};

const initChart6 = () => {
  chartDom = document.getElementById("chart6");
  myChart = echarts.init(chartDom);

  option = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        // Use axis to trigger tooltip
        type: "shadow", // 'shadow' as default; can also be 'line' or 'shadow'
      },
    },
    legend: {
      top: "10%",
    },
    grid: {
      left: "0%",
      right: "4%",
      bottom: "0%",
      containLabel: true,
      height: "75%",
    },
    xAxis: {
      type: "value",
    },
    yAxis: {
      type: "category",
      data: ["南开", "西青", "静海", "和平", "河北", "河西", "河东"],
    },
    dataZoom: {
      type: "inside",
      yAxisIndex: 0, // 控制x轴
      show: true,
      start: 0,
      end: 60,
      realtime: true,
    },
    series: [
      {
        name: "产品1",
        type: "bar",
        stack: "total",
        label: {
          show: true,
          textStyle: {
            fontSize: "10px",
          },
        },
        emphasis: {
          focus: "series",
        },
        data: [320, 302, 301, 334, 390, 330, 320],
      },
      {
        name: "产品2",
        type: "bar",
        stack: "total",
        label: {
          show: true,
          textStyle: {
            fontSize: "10px",
          },
        },
        emphasis: {
          focus: "series",
        },
        data: [120, 132, 101, 134, 90, 230, 210],
      },
      {
        name: "产品3",
        type: "bar",
        stack: "total",
        label: {
          show: true,
          textStyle: {
            fontSize: "10px",
          },
        },
        emphasis: {
          focus: "series",
        },
        data: [220, 182, 191, 234, 290, 330, 310],
      },
      {
        name: "产品4",
        type: "bar",
        stack: "total",
        label: {
          show: true,
          textStyle: {
            fontSize: "10px",
          },
        },
        emphasis: {
          focus: "series",
        },
        data: [150, 212, 201, 154, 190, 330, 410],
      },
      {
        name: "产品5",
        type: "bar",
        stack: "total",
        label: {
          show: true,
          textStyle: {
            fontSize: "10px",
          },
        },
        emphasis: {
          focus: "series",
        },
        data: [820, 832, 901, 934, 1290, 1330, 1320],
      },
    ],
    title: {
      text: "月度订货额-经销区",
    },
  };
  myChart.setOption(option);
};

// 竖形堆叠图
const initChartVertical = (options) => {
  chartDom = document.getElementById("chart4");
  myChart = echarts.init(chartDom);
  if (options) {
    option = options;
  } else {
    option = {
      tooltip: {
        trigger: "axis",
        axisPointer: {
          // Use axis to trigger tooltip
          type: "shadow", // 'shadow' as default; can also be 'line' or 'shadow'
        },
      },
      legend: {
        top: "10%",
      },
      grid: {
        left: "0%",
        right: "4%",
        bottom: "0%",
        containLabel: true,
        height: "75%",
      },
      yAxis: {
        type: "value",
      },
      xAxis: {
        type: "category",
        data: [
          "1月",
          "2月",
          "3月",
          "4月",
          "5月",
          "6月",
          "7月",
          "8月",
          "9月",
          "10月",
          "11月",
          "12月",
        ],
        axisLabel: {
          interval: 0,
        },
      },
      series: [
        {
          name: "销货额",
          type: "bar",
          stack: "total",
          label: {
            show: true,
            textStyle: {
              fontSize: "10px",
            },
          },
          emphasis: {
            focus: "series",
          },
          data: [
            320, 120, 200, 400, 350, 320, 150, 130, 220, 180, 330, 328, 174,
          ],
          barWidth: "13px",
        },
        {
          name: "返货额",
          type: "bar",
          stack: "total",
          label: {
            show: true,
          },
          emphasis: {
            focus: "series",
          },
          data: [120, 100, 130, 110, 100, 125, 81, 90, 102, 50, 210, 20],
        },
      ],
      title: {
        text: "月度销售额",
      },
    };
  }
  myChart.setOption(option);
};

let sale_money = ref([]); // 月度销货额
let return_money = ref([]); // 月度返货额

// 更换日期
const choose_month = async (val, dataStr) => {
  // console.log(val.$y);
  // console.log(dataStr);

  let dataArr = dataStr.replace(/\:/g, "-").split("-");
  let year = dataArr[0] ? dataArr[0] : "2024";
  let month = dataArr[1] ? dataArr[1] : "3";
  // console.log(dataArr);
  let res;
  let options;

  //#region ----------------------------- 月度销售额start -----------------------------------------
  if (val) {
    res = await get_month_sale(val.$y);
  } else {
    res = await get_month_sale("2024");
  }

  // console.log(res.data.result);
  sale_money.value = [];
  return_money.value = [];
  res.data.result.forEach((item) => {
    sale_money.value.push(item.sale_money);
    return_money.value.push(item.return_money);
  });

  options = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        // Use axis to trigger tooltip
        type: "shadow", // 'shadow' as default; can also be 'line' or 'shadow'
      },
    },
    legend: {
      top: "10%",
    },
    grid: {
      left: "0%",
      right: "4%",
      bottom: "0%",
      containLabel: true,
      height: "75%",
    },
    yAxis: {
      type: "value",
    },
    xAxis: {
      type: "category",
      data: [
        "1月",
        "2月",
        "3月",
        "4月",
        "5月",
        "6月",
        "7月",
        "8月",
        "9月",
        "10月",
        "11月",
        "12月",
      ],
      axisLabel: {
        interval: 0,
      },
    },
    series: [
      {
        name: "销货额",
        type: "bar",
        barMinHeight: 30,
        stack: "total",
        label: {
          show: true,
          textStyle: {
            fontSize: "10px",
          },
        },
        emphasis: {
          focus: "series",
        },
        data: sale_money.value,
        barWidth: "13px",
      },
      {
        name: "返货额",
        type: "bar",
        stack: "total",
        barMinHeight: 30,
        label: {
          show: true,
          fontSize: 10,
        },
        emphasis: {
          focus: "series",
        },
        data: return_money.value,
      },
    ],
    title: {
      text: "月度销售额",
    },
  };
  initChartVertical(options);

  // #endregion ------------------------ 月度销售额end ------------------------------------------------

  //#region ------------------------- 月度销售额-经销区start -----------------------------------
  if (year && month) {
    res = await getMonthSaleDistribution(year, month);
  }
  // console.log(res.data?.result[0]);
  let area_name;
  let class_name;
  let sale_money2;
  if (res.data.result.length > 0) {
    area_name = res.data?.result[0].area_name;
    class_name = res.data?.result[0].class_name.split(",");
    sale_money2 = res.data?.result[0].sale_money.split(",");

    option = {
      tooltip: {
        trigger: "axis",
        axisPointer: {
          // Use axis to trigger tooltip
          type: "shadow", // 'shadow' as default; can also be 'line' or 'shadow'
        },
      },
      legend: {
        top: "10%",
      },
      grid: {
        left: "0%",
        right: "4%",
        bottom: "0%",
        containLabel: true,
        height: "75%",
      },
      xAxis: {
        type: "value",
      },
      yAxis: {
        type: "category",
        data: [`${area_name}`],
      },
      dataZoom: {
        type: "slider",
        xAxisIndex: 0, // 控制x轴
        show: true,
        start: 0,
        end: 30,
        realtime: true,
      },
      axisLabel: {
        interval: 0,
        formatter: function (value) {
          var ret = ""; //拼接加\n返回的类目项
          var maxLength = 4; //每项显示文字个数
          var valLength = value.length; //X轴类目项的文字个数
          var rowN = Math.ceil(valLength / maxLength); //类目项需要换行的行数
          if (rowN > 1) {
            //如果类目项的文字大于5,
            for (var i = 0; i < rowN; i++) {
              var temp = ""; //每次截取的字符串
              var start = i * maxLength; //开始截取的位置
              var end = start + maxLength; //结束截取的位置
              //这里也可以加一个是否是最后一行的判断，但是不加也没有影响，那就不加吧
              temp = value.substring(start, end) + "\n";
              ret += temp; //凭借最终的字符串
            }
            return ret;
          } else {
            return value;
          }
        },
      },
      series: [],
      title: {
        text: "月度销售额-经销区",
      },
    };

    option.series = [];
    class_name.forEach((item, index) => {
      // console.log(sale_money2[index]);
      let a = {
        name: item,
        type: "bar",
        stack: "total",
        barMinHeight: 20,
        label: {
          show: true,
          textStyle: {
            fontSize: "10px",
          },
        },
        emphasis: {
          focus: "series",
        },
        data: [`${sale_money2[index]}`],
      };
      option.series.push(a);
    });

    initChart2(option);
  }
  // #endregion -------------------------- 月度销售额end --------------------------------------

  //#region -------------------------- 产品销售额start -------------------------------------------
  let product_name = ref([]);
  let return_money_product = ref([]);
  let sale_money_product = ref([]);
  res = await getProductSale(year, month);
  if (res.data.result.length > 0) {
    res.data.result.forEach((item) => {
      product_name.value.push(item.product_name);
      return_money_product.value.push(item.return_money);
      sale_money_product.value.push(item.sale_money);
    });

    options = {
      tooltip: {
        trigger: "axis",
        axisPointer: {
          // Use axis to trigger tooltip
          type: "shadow", // 'shadow' as default; can also be 'line' or 'shadow'
        },
      },
      legend: {
        top: "10%",
      },
      grid: {
        left: "0%",
        right: "4%",
        bottom: "0%",
        containLabel: true,
        height: "75%",
      },
      xAxis: {
        type: "value",
      },
      yAxis: {
        type: "category",
        data: product_name.value,
      },
      dataZoom: {
        type: "slider",
        xAxisIndex: 0, // 控制x轴
        show: true,
        start: 0,
        end: 30,
        realtime: true,
      },
      series: [
        {
          name: "销货额",
          type: "bar",
          stack: "total",
          label: {
            show: true,
            textStyle: {
              fontSize: "10px",
            },
          },
          emphasis: {
            focus: "series",
          },
          data: sale_money_product.value,
        },
        {
          name: "返货额",
          type: "bar",
          stack: "total",
          label: {
            show: true,
            textStyle: {
              fontSize: "10px",
            },
          },
          emphasis: {
            focus: "series",
          },
          data: return_money_product.value,
        },
      ],
      title: {
        text: "产品销售额",
      },
    };
    initChart3(options);
  }
};

const getNumberData = async (title) => {
  let res = await getNumberDataName(title);
  // console.log(res);
  title1.value = res.data.result[0].number_data_name1;
  title2.value = res.data.result[0].number_data_name2;
};

onLoad((option) => {
  uni.setNavigationBarTitle({
    title: `${option.title}`,
  });
  showEcharts.value = option.title;
  getNumberData(option.title);
});

nextTick(() => {
  initChart1();
  initChart2();
  initChart3();
  initChartVertical();
  initChart5();
  initChart6();
  // charts.value.initChart();
});
</script>
  
  <style lang="scss" scoped>
body {
  font-size: 1px;
}

.backGround {
  position: absolute;
  width: 100%;
  height: 150px;
  background: linear-gradient(to right, rgb(107, 168, 255), rgb(36, 127, 255));
  top: 0;
  left: 0;
  z-index: 0;
  border-bottom-left-radius: 5%;
  border-bottom-right-radius: 5%;
}
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #f4f4f4;
  // height: 100vh;
  width: 100%;
  position: relative;
  padding-top: 50px;
  overflow: auto;
}
.detail {
  height: 25%;
  width: 95%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  margin: 5px 0 20px 0;
  padding: 10px;
  border-radius: 8px;
  z-index: 1;
}

.change_date {
  width: 50px;
  height: 10px;
  z-index: 999;
  font-size: 15px;
  position: absolute;
  top: 0;
  left: 0;
}

.detail-list {
  height: 5%;
  line-height: 30px;
  font-size: 0.9375rem;
}

.chart-container1 {
  width: 95%;
  padding: 10px;
  height: 500px;
  background-color: #fff;
  margin: 0 0 10px 0;
  border-radius: 8px;
}
.chart-container2 {
  width: 95%;
  padding: 10px;
  height: 800px;
  background-color: #fff;
  margin: 0 0 10px 0;
  border-radius: 8px;
}
.progress {
  display: flex;
  flex-direction: row;
}

:deep(.ant-progress) {
  width: 50%;
}

:deep(.ant-progress-inner) {
  position: relative;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

:deep(.ant-picker) {
  position: absolute;
  top: 13px;
  right: 10px;
}
</style>
  