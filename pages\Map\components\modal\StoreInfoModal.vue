<template>
  <div style="max-height: 850px; overflow-y: scroll">
    <div class="top-info">
      <div class="message-list">
        <div class="message-item">
          <div class="key-name">门店名称</div>
          <div class="key-value">
            {{ storeInfo?.Store_name }}
          </div>
        </div>
        <div class="col-item">
          <div class="message-item" style="width: 49%; min-width: 250px">
            <div class="key-name">当前负责人</div>
            <div class="key-value">
              {{ storeInfo?.Cyclist_name }}
            </div>
          </div>
          <div class="message-item" style="width: 49%; min-width: 250px">
            <div class="key-name">库长</div>
            <div class="key-value">
              {{ storeInfo?.jxs_name }}
            </div>
          </div>
        </div>
        <div class="col-item">
          <div class="message-item" style="width: 49%; min-width: 250px">
            <div class="key-name">柜体投放数量</div>
            <div class="key-value">
              {{ storeInfo?.Material_num ? storeInfo.Material_num : "暂无" }}
            </div>
          </div>
          <div class="message-item" style="width: 49%; min-width: 250px">
            <div class="key-name">筐库存数</div>
            <div class="key-value">暂无</div>
          </div>
        </div>
        <div class="col-item">
          <div class="message-item" style="width: 49%; min-width: 250px">
            <div class="key-name">总净销售额</div>
            <div class="key-value">
              {{
                storeInfo?.Store_all_sales
                  ? Number(storeInfo?.Store_all_sales).toFixed(2)
                  : 0
              }}
            </div>
          </div>
          <div class="message-item" style="width: 49%; min-width: 250px">
            <div class="key-name">当前净销售额</div>
            <div class="key-value">
              {{
                storeInfo?.Store_monthly_sales
                  ? Number(storeInfo?.Store_monthly_sales).toFixed(2)
                  : 0
              }}
            </div>
          </div>
        </div>
        <div class="col-item">
          <div class="message-item" style="width: 49%; min-width: 250px">
            <div class="key-name">门店联系人</div>
            <div class="key-value">
              {{ storeInfo?.Store_contact }}
            </div>
          </div>
          <div class="message-item" style="width: 49%; min-width: 250px">
            <div class="key-name">门店电话</div>
            <div class="key-value">
              {{ storeInfo?.Store_telephone }}
            </div>
          </div>
        </div>
        <div class="col-item">
          <div class="message-item" style="width: 49%; min-width: 250px">
            <div class="key-name">店面面积</div>
            <div class="key-value">
              {{ storeInfo?.Store_area }}
            </div>
          </div>
          <div class="message-item" style="width: 49%; min-width: 250px">
            <div class="key-name">陈列方式</div>
            <div class="key-value">
              {{ storeInfo?.Display_pattern }}
            </div>
          </div>
        </div>
      </div>
      <div class="img-list">
        <div class="list-item">
          <span class="item-title">门头照片</span>
          <a-image
            style="height: 70px; width: 70px; border-radius: 7px"
            v-if="storeInfo?.Store_photo && storeInfo?.Store_photo !== 'None'"
            :src="storeInfo?.Store_photo"
          />
          <div class="no-png" v-else>无</div>
        </div>
        <div class="list-item">
          <span class="item-title">盘前照片</span>
          <a-image
            style="height: 70px; width: 70px; border-radius: 7px"
            v-if="salesImgs.img1"
            :src="salesImgs.img1"
          />
          <div class="no-png" v-else>无</div>
        </div>
        <div class="list-item">
          <span class="item-title">主阵地</span>
          <a-image
            style="height: 70px; width: 70px; border-radius: 7px"
            v-if="salesImgs.img2"
            :src="salesImgs.img2"
          />
          <div class="no-png" v-else>无</div>
        </div>
        <div class="list-item">
          <span class="item-title">补强</span>
          <a-image
            style="height: 70px; width: 70px; border-radius: 7px"
            v-if="salesImgs.img3"
            :src="salesImgs.img3"
          />
          <div class="no-png" v-else>无</div>
        </div>
        <div class="list-item">
          <span class="item-title">散货</span>
          <a-image
            style="height: 70px; width: 70px; border-radius: 7px"
            v-if="salesImgs.img4"
            :src="salesImgs.img4"
          />
          <div class="no-png" v-else>无</div>
        </div>
        <div class="list-item">
          <span class="item-title">季节</span>
          <a-image
            style="height: 70px; width: 70px; border-radius: 7px"
            v-if="salesImgs.img5"
            :src="salesImgs.img5"
          />
          <div class="no-png" v-else>无</div>
        </div>
        <div class="list-item">
          <span class="item-title">促销</span>
          <a-image
            style="height: 70px; width: 70px; border-radius: 7px"
            v-if="salesImgs.img6"
            :src="salesImgs.img6"
          />
          <div class="no-png" v-else>无</div>
        </div>
        <div class="list-item">
          <span class="item-title">友商</span>
          <a-image
            style="height: 70px; width: 70px; border-radius: 7px"
            v-if="salesImgs.img7"
            :src="salesImgs.img7"
          />
          <div class="no-png" v-else>无</div>
        </div>
      </div>
    </div>

    <div style="margin: 25px 0 10px 0">
      <a-select
        v-model:value="optionValue"
        style="width: 120px"
        :options="options"
        @select="select"
      ></a-select>
    </div>
    <a-tabs v-model:activeKey="activeKey" type="card">
      <a-tab-pane key="1" tab="商品销售详情">
        <a-table
          :dataSource="storeGoodsInfo"
          :columns="storeGoodsInfoColumns"
          :pagination="{ pageSize: 5 }"
          size="small"
        />
      </a-tab-pane>
      <a-tab-pane key="2" tab="商品返货详情">
        <a-table
          :dataSource="storeGoodsReturnInfo"
          :columns="storeGoodsReturnInfoColumns"
          :pagination="{ pageSize: 5 }"
          size="small"
        />
      </a-tab-pane>
      <a-tab-pane key="3" tab="销售额">
        <a-table
          :dataSource="storeSalesroom"
          :columns="storeSalesroomColumns"
          :pagination="{ pageSize: 5 }"
          size="small"
        />
      </a-tab-pane>
      <a-tab-pane key="4" tab="物料库存">
        <a-table
          :dataSource="storeMMI"
          :columns="storeMMIColumns"
          :pagination="{ pageSize: 5 }"
          size="small"
        />
      </a-tab-pane>
      <a-tab-pane key="5" tab="盘筐明细">
        <a-table
          :dataSource="storeBasketDetail"
          :columns="storeBasketDetailColumns"
          :pagination="{ pageSize: 5 }"
          size="small"
        />
      </a-tab-pane>
      <a-tab-pane key="6" tab="物料盘点明细">
        <a-table
          :dataSource="storeMMIDetail"
          :columns="storeMMIDetailColumns"
          :pagination="{ pageSize: 5 }"
          size="small"
        />
      </a-tab-pane>
      <a-tab-pane key="7" tab="操作历史">
        <a-table
          :dataSource="storeEditHistory"
          :columns="storeEditHistoryColumns"
          :pagination="{ pageSize: 5 }"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'Update_beforce'">
              <a-image
                v-if="
                  (record.Update_attribute === '门店门头照' ||
                    record.Update_attribute === '门店内景照') &&
                  record.Update_beforce !== null &&
                  record.Update_beforce !== ''
                "
                :src="record.Update_beforce"
                style="height: 50px; width: 50px"
              ></a-image>
              <span v-else>{{ record.Update_beforce }}</span>
            </template>
            <template v-if="column.dataIndex === 'Update_after'">
              <a-image
                v-if="
                  (record.Update_attribute === '门店门头照' ||
                    record.Update_attribute === '门店内景照') &&
                  record.Update_after !== null &&
                  record.Update_after !== ''
                "
                :src="record.Update_after"
                style="height: 50px; width: 50px"
              ></a-image>
              <span span v-else>{{ record.Update_after }}</span>
            </template>
          </template>
        </a-table>
      </a-tab-pane>
      <a-tab-pane key="8" tab="拜访情况">
        <a-table
          :dataSource="visitSituation"
          :columns="visitSituationColumns"
          :pagination="{ pageSize: 5 }"
          size="small"
        >
        </a-table>
      </a-tab-pane>
      <a-tab-pane key="9" tab="物料投放">
        <a-table
          :dataSource="materialDelivery"
          :columns="materialDeliveryColumns"
          :pagination="{ pageSize: 5 }"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'Voucher_photo_ll'">
              <a-image
                :src="record.Voucher_photo_ll"
                style="height: 50px; width: 50px"
              ></a-image>
            </template>
            <template v-if="column.dataIndex === 'Voucher_photo'">
              <a-image
                :src="record.Voucher_photo"
                style="height: 50px; width: 50px"
              ></a-image>
            </template>
          </template>
        </a-table>
      </a-tab-pane>
      <a-tab-pane key="10" tab="物料撤回">
        <a-table
          :dataSource="materialWithdraw"
          :columns="materialWithdrawColumns"
          :pagination="{ pageSize: 5 }"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'Voucher_photo_ll'">
              <a-image
                :src="record.Voucher_photo_ll"
                style="height: 50px; width: 50px"
              ></a-image>
            </template>
            <template v-if="column.dataIndex === 'Voucher_photo'">
              <a-image
                :src="record.Voucher_photo"
                style="height: 50px; width: 50px"
              ></a-image>
            </template>
          </template>
        </a-table>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from "vue";

import {
  getRecent24Months,
  storeGoodsInfoColumns,
  storeGoodsReturnInfoColumns,
  storeSalesroomColumns,
  storeEditHistoryColumns,
  storeMMIColumns,
  storeMMIDetailColumns,
  storeBasketDetailColumns,
  visitSituationColumns,
  materialDeliveryColumns,
  materialWithdrawColumns,
} from "../../data-uitls/index";
import {
  carMapGetStoreInfo,
  carMapGetStoreGoodsInfo,
  carMapGetStoreGoodsInfo01,
  carMapGetStoreSsaleroom,
  carMapGetStoreEditHistory,
  getStoreMaterialDetail,
  getStoreMMI,
  getBasketDetail,
  getStoreSalesImgs,
  getMaterialDeliveryApi,
  getMaterialWithdrawApi,
} from "/api/Map/index";
import { getVisitSituationApi } from "../../../../api/map";

const currentStoreId = ref();
const storeInfo = ref();

const salesImgs = reactive({
  img1: null, //盘前
  img2: null, //主阵地
  img3: null, //补强
  img4: null, //散货
  img5: null, //季节
  img6: null, //促销
  img7: null, //友商
});

async function getSalesImgs() {
  const { data } = await getStoreSalesImgs(currentStoreId.value);
  data.result.forEach((item) => {
    salesImgs.img1 = item.Sales_last_pic ? item.Sales_last_pic : null;
    salesImgs.img2 = item.Sales_next_pic ? item.Sales_next_pic : null;
    salesImgs.img3 = item.Sales_next_pic2 ? item.Sales_next_pic2 : null;
    salesImgs.img4 = item.Sales_next_pic3 ? item.Sales_next_pic3 : null;
    salesImgs.img5 = item.Sales_next_pic4 ? item.Sales_next_pic4 : null;
    salesImgs.img6 = item.Sales_next_pic5 ? item.Sales_next_pic5 : null;
    salesImgs.img7 = item.Sales_next_pic6 ? item.Sales_next_pic6 : null;
  });
}

async function getStoreInfo() {
  const res = await carMapGetStoreInfo(currentStoreId.value);
  storeInfo.value = res.data?.result[0];
}

const activeKey = ref("1");

const optionValue = ref("近30天");
const options = getRecent24Months();

// ---------销，返start----------
const storeGoodsInfo = ref([]);
const storeGoodsReturnInfo = ref([]);
async function getStoreGoodsInfo(month, year) {
  const res = await carMapGetStoreGoodsInfo(currentStoreId.value, month, year);
  const res1 = await carMapGetStoreGoodsInfo01(
    currentStoreId.value,
    month,
    year
  );
  storeGoodsInfo.value = await disposeGoodsInfo(res.data?.result[0]);
  storeGoodsReturnInfo.value = await disposeGoodsReturnInfo(
    res1.data?.result[0]
  );
}

async function disposeGoodsInfo(arrpParams) {
  let arr = arrpParams.map((item) => {
    return {
      name: item.Sales_son_good_name,
      sales: item.Sales_son_total_prices,
    };
  });

  // 使用 reduce 方法将具有相同 name 的对象的 value 值进行合并
  let result = arr.reduce((acc, curr) => {
    let existingItem = acc.find((item) => item.name === curr.name);
    if (existingItem) {
      existingItem.sales += curr.sales;
    } else {
      acc.push({ name: curr.name, sales: curr.sales });
    }
    return acc;
  }, []);

  result = result.map((item) => {
    return {
      name: item.name,
      sales: Number(item.sales).toFixed(2),
    };
  });
  return result.sort((a, b) => b.sales - a.sales);
}

async function disposeGoodsReturnInfo(arrpParams) {
  let arr = arrpParams.map((item) => {
    return {
      name: item.XF_goods_name,
      sales: item.XF_total_prices,
    };
  });

  // 使用 reduce 方法将具有相同 name 的对象的 value 值进行合并
  let result = arr.reduce((acc, curr) => {
    let existingItem = acc.find((item) => item.name === curr.name);
    if (existingItem) {
      existingItem.sales += curr.sales;
    } else {
      acc.push({ name: curr.name, sales: curr.sales });
    }
    return acc;
  }, []);

  result = result.map((item) => {
    return {
      name: item.name,
      sales: Number(item.sales).toFixed(2),
    };
  });
  return result.sort((a, b) => b.sales - a.sales);
}
// ---------销，返end----------

// ---------销售额start----------

const storeSalesroom = ref([]);
async function getStoreSsaleroom(month, year) {
  const res = await carMapGetStoreSsaleroom(currentStoreId.value, month, year);
  storeSalesroom.value = await disposeSalesInfo(res.data?.result[0]);

  // console.log("storeSalesroom.value", storeSalesroom.value);
}

//处理销售额
async function disposeSalesInfo(arrpParams) {
  let arr2 = arrpParams.map((item) => {
    return {
      dateTime: item.finish_date,
      sales: Number(item.Sales_sales_money),
      salesReturn: Number(item.Sales_stock_money),
      salesMain: Number(item.Sales_receivable_money),
    };
  });

  let result2 = Object.values(
    arr2.reduce((acc, { dateTime, sales, salesReturn, salesMain }) => {
      if (!acc[dateTime]) {
        acc[dateTime] = { dateTime, sales: 0, salesReturn: 0, salesMain: 0 };
      }
      acc[dateTime].sales += sales;
      acc[dateTime].salesReturn += salesReturn;
      acc[dateTime].salesMain += salesMain;
      return acc;
    }, {})
  );

  result2 = result2.map((item) => {
    return {
      dateTime: item.dateTime,
      sales: Number(item.sales).toFixed(2),
      salesReturn: Number(item.salesReturn).toFixed(2),
      salesMain: Number(item.salesMain).toFixed(2),
    };
  });
  return result2.sort((a, b) => b.dateTime - a.dateTime);
}
// ---------销售额end----------

// ---------物料明细start----------
const storeMMI = ref([]);
async function getAllStoreMMI() {
  const res = await getStoreMMI(currentStoreId.value);
  storeMMI.value = res.data?.result || [];
}
// ---------物料明细end----------

// ---------物料盘点明细start----------
const storeMMIDetail = ref([]);
async function getMaterialDetail(month, year) {
  const res = await getStoreMaterialDetail(currentStoreId.value, month, year);
  // const res = await getStoreMaterialDetail("2023112117274067781", month, year); // 测试
  storeMMIDetail.value = res.data?.result[0] || [];
}
// ---------物料盘点明细end----------

// ---------盘筐明细start----------
const storeBasketDetail = ref([]);
async function getStoreBasketDetail(month, year) {
  const res = await getStoreMaterialDetail(currentStoreId.value, month, year);
  // const res = await getBasketDetail("2023112117271135413", month, year); // 测试
  storeBasketDetail.value = res.data?.result[0] || [];
}
// ---------盘筐明细end----------

// ---------拜访情况start----------
const visitSituation = ref([]);
async function getVisitSituation(month, year) {
  const { data } = await getVisitSituationApi(
    currentStoreId.value,
    month,
    year
  );

  console.log("data", data);

  visitSituation.value = data.result[0] || [];
}
// ---------拜访情况end----------

// ---------操作历史start----------
const storeEditHistory = ref([]);
async function getStoreEditHistory() {
  const res = await carMapGetStoreEditHistory(currentStoreId.value);
  storeEditHistory.value = res.data?.result;
}

// ---------操作历史end----------

// ---------物料投放/撤回start----------
const materialDelivery = ref([]);
const materialWithdraw = ref([]);

async function getMaterialDelivery() {
  const res = await getMaterialDeliveryApi(currentStoreId.value);
  materialDelivery.value = res.data?.result;
}

async function getMaterialWithdraw() {
  const res = await getMaterialWithdrawApi(currentStoreId.value);
  materialWithdraw.value = res.data?.result;
}
// ---------物料投放/撤回end----------

async function select(e) {
  console.log("e", e);
  const data = e === "近30天" ? [0, 0] : e.split("-");
  getStoreGoodsInfo(Number(data[1]), Number(data[0]));
  getStoreSsaleroom(Number(data[1]), Number(data[0]));
  getStoreEditHistory(Number(data[1]), Number(data[0]));
  getMaterialDetail(Number(data[1]), Number(data[0]));
  getStoreBasketDetail(Number(data[1]), Number(data[0]));
  getVisitSituation(Number(data[1]), Number(data[0]));
}

onMounted(() => {
  currentStoreId.value = sessionStorage.getItem("storeId");
  getStoreInfo();
  getStoreGoodsInfo(0, 0);
  getStoreSsaleroom(0, 0);
  getAllStoreMMI();
  getStoreEditHistory();
  getMaterialDetail(0, 0);
  getStoreBasketDetail(0, 0);
  getVisitSituation(0, 0);
  getSalesImgs();
  getMaterialDelivery();
  getMaterialWithdraw();
});
</script>

<style lang="scss" scoped>
.box {
  border-radius: 20rpx;
  padding: 0 10rpx;
  width: 70%;
  box-shadow: 1px 11px 80px rgba(0, 0, 0, 0.15);
  .store-info-list {
    padding: 10rpx 0;
    padding-right: 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    justify-content: space-between;
  }
}
.top-info {
  display: flex;
  gap: 5px;
  flex-wrap: wrap;

  .message-list {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 5px;
    .message-item {
      display: flex;
      height: 30px;
      border: 1px solid #dde3e9;
    }
  }
  .img-list {
    width: 310px;
    display: grid;
    grid-template-columns: repeat(3, minmax(0, 1fr));
    gap: 10px;
    .list-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      .item-title {
        font-weight: 700;
        text-align: center;
        justify-content: center;
      }
      .no-png {
        width: 70px;
        height: 70px;
        line-height: 70px;
        text-align: center;
      }
    }
  }
  .key-name {
    font-weight: 700;
    width: 120px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #dde3e9;
  }
  .key-value {
    flex: 1;
    display: flex;
    padding: 0 10px;
    align-items: center;
  }
}
.col-item {
  display: flex;
  justify-content: space-between;
}

@media (width<1077px) {
  .col-item {
    display: contents;
  }

  .message-item {
    width: 49%;
    min-width: 250px;
  }

  .top-info {
    display: contents;
  }
}
</style>
